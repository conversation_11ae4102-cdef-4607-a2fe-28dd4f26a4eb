{"version": 4, "configurations": [{"name": "Linux", "compileCommands": "${workspaceFolder}/build/compile_commands.json", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/apps/client/include", "${workspaceFolder}/apps/client/src", "${workspaceFolder}/apps/server/include", "${workspaceFolder}/libraries/shared/include", "${workspaceFolder}/build/_deps/httplib-src", "/usr/include/**", "/usr/local/include/**", "/usr/include/x86_64-linux-gnu/qt5", "/usr/include/x86_64-linux-gnu/qt5/QtCore", "/usr/include/x86_64-linux-gnu/qt5/QtGui", "/usr/include/x86_64-linux-gnu/qt5/QtWidgets", "/usr/include/x86_64-linux-gnu/qt5/QtNetwork", "/usr/include/x86_64-linux-gnu/qt5/QtWebEngine", "/usr/include/x86_64-linux-gnu/qt5/QtWebEngineCore", "/usr/include/x86_64-linux-gnu/qt5/QtWebEngineWidgets", "/usr/include/x86_64-linux-gnu/qt5/QtWebChannel", "/usr/include/x86_64-linux-gnu/qt5/QtPositioning", "/usr/include/x86_64-linux-gnu/qt5/QtQuick", "/usr/include/x86_64-linux-gnu/qt5/QtQml", "/usr/include/x86_64-linux-gnu/qt5/QtQmlModels", "/usr/include/x86_64-linux-gnu/qt5/QtPrintSupport", "/usr/include/x86_64-linux-gnu/qt5/QtMultimedia"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "QT_CORE_LIB", "QT_GUI_LIB", "QT_WIDGETS_LIB", "QT_NETWORK_LIB", "QT_WEBENGINE_LIB", "QT_WEBENGINECORE_LIB", "QT_WEBENGINEWIDGETS_LIB", "QT_WEBCHANNEL_LIB", "QT_POSITIONING_LIB", "QT_QUICK_LIB", "QT_QML_LIB", "QT_QMLMODELS_LIB", "QT_PRINTSUPPORT_LIB", "QT_MULTIMEDIA_LIB", "QT_NO_DEBUG"], "compilerPath": "/usr/bin/g++", "cStandard": "c17", "cppStandard": "c++20", "intelliSenseMode": "linux-gcc-x64", "configurationProvider": "ms-vscode.cmake-tools", "browse": {"path": ["${workspaceFolder}", "/usr/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu/qt5"], "limitSymbolsToIncludedHeaders": true, "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"}}]}