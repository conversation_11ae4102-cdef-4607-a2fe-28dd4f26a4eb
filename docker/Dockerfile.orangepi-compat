# Docker image for Orange Pi compatible cross-compilation
# Uses Ubuntu 22.04 which has GLIBC 2.35/2.36 compatible toolchain

FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install basic dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    pkg-config \
    wget \
    curl \
    ca-certificates \
    software-properties-common \
    && rm -rf /var/lib/apt/lists/*

# Install cross-compilation toolchain for ARM64
RUN apt-get update && apt-get install -y \
    gcc-aarch64-linux-gnu \
    g++-aarch64-linux-gnu \
    libc6-dev-arm64-cross \
    && rm -rf /var/lib/apt/lists/*

# Install additional development tools
RUN apt-get update && apt-get install -y \
    binutils-aarch64-linux-gnu \
    file \
    libc-bin \
    && rm -rf /var/lib/apt/lists/*

# Set up cross-compilation environment
ENV CC=aarch64-linux-gnu-gcc
ENV CXX=aarch64-linux-gnu-g++
ENV AR=aarch64-linux-gnu-ar
ENV STRIP=aarch64-linux-gnu-strip
ENV PKG_CONFIG_PATH=/usr/lib/aarch64-linux-gnu/pkgconfig

# Create working directory
WORKDIR /workspace

# Copy source code and build files
COPY . /workspace/
# Remove any existing build directories to ensure clean build
RUN rm -rf /workspace/build* /workspace/.git

# Set executable permissions
RUN chmod +x /workspace/scripts/cross-build/*.sh

# Default command
CMD ["/bin/bash"]

# Copy build script to a location that won't be overridden by volume mount
COPY docker/build-in-container.sh /usr/local/bin/build-in-container.sh
RUN chmod +x /usr/local/bin/build-in-container.sh

# Set entrypoint
ENTRYPOINT ["/usr/local/bin/build-in-container.sh"]
