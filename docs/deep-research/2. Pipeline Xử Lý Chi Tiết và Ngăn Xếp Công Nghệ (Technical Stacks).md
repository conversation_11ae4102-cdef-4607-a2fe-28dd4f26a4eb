Pipeline Xử Lý Chi Tiết và Ngăn Xếp <PERSON> (Technical Stacks)

Dưới đây là phân tích chi tiết từng giai đoạn của pipeline, cùng với các công nghệ và API liên quan:

**Giai đoạn A: <PERSON>hu <PERSON>ận Luồng RTSP và Tách <PERSON> (Demuxing)**

- **Mục tiêu:** Kết nối với nhiều luồng camera RTSP, nhận dữ liệu video đã mã hóa (thường là H.264 hoặc H.265) và tách các đơn vị NAL (Network Abstraction Layer) thô.
- **Ngăn xếp công nghệ:**
  - **Thư viện:** FFmpeg (libavformat, libavcodec) hoặc GStreamer (rtspsrc, rtph264depay/rtph265depay, các trình phân tích cú pháp như h264parse).
  - **<PERSON><PERSON>n ngữ:** C++.
- **<PERSON> tiết triển khai:**
  - Mỗi luồng camera sẽ được quản lý bởi một luồng (thread) C++ riêng biệt.
  - Luồng này sẽ sử dụng API của FFmpeg hoặc GStreamer để:
    - Thiết lập kết nối RTSP (TCP hoặc UDP).
    - Xử lý các vấn đề về mạng như jitter, mất gói (ở một mức độ nhất định).
    - Tách (demux) luồng video thành các gói NAL units H.264/H.265.
- **Đầu ra:** Các gói NAL units H.264/H.265 thô cho mỗi luồng camera, sẵn sàng để giải mã.

**Giai đoạn B: Giải Mã Video (Sử dụng MPP)**

- **Mục tiêu:** Giải mã các gói NAL units thành các khung hình video thô (ví dụ: định dạng YUV như NV12) bằng cách sử dụng bộ giải mã phần cứng của RK3588 (VPU).
- **Ngăn xếp công nghệ:**
  - **API:** Rockchip Media Process Platform (MPP) C API (rk_mpi.h).
  - **Thư viện liên quan:** libmpp.so.
- **Chi tiết triển khai:**
  - Mỗi luồng camera sẽ có một MppCtx (context) riêng cho việc giải mã.
  - **Khởi tạo:**
    - `mpp_create()`: Tạo context MPP và lấy cấu trúc API.
    - `mpp_init()`: Khởi tạo context cho tác vụ giải mã (MPP_CTX_DEC) và chỉ định codec (ví dụ: MPP_VIDEO_CodingAVC cho H.264, MPP_VIDEO_CodingHEVC cho H.265).
  - **Cấu hình (Quan trọng cho Zero-Copy):**
    - Sử dụng `mpi->control()` với các lệnh phù hợp để yêu cầu MPP xuất các khung hình đã giải mã vào bộ đệm DMABUF. Điều này rất quan trọng để tránh sao chép dữ liệu không cần thiết.
  - **Vòng lặp giải mã:**
    - `mpp_packet_init()`: Chuẩn bị MppPacket từ dữ liệu NAL unit.
    - `mpi->decode_put_packet()`: Gửi gói NAL unit vào bộ giải mã.
    - `mpi->decode_get_frame()`: Lấy khung hình đã giải mã (MppFrame). Khung hình này nên được cấp phát dưới dạng DMABUF.
    - Từ MppFrame, lấy file descriptor (FD) của DMABUF bằng `mpp_frame_get_fd(frame)`.
- **Đầu ra:** Các file descriptor (FD) của DMABUF, mỗi FD trỏ đến một khung hình YUV (ví dụ: NV12) đã được giải mã.

**Giai đoạn C: Tiền Xử Lý cho Phát Hiện Khuôn Mặt (Sử dụng RGA)**

- **Mục tiêu:** Chuyển đổi các khung hình YUV (từ DMABUF của MPP) thành định dạng và kích thước mà mô hình phát hiện khuôn mặt yêu cầu (ví dụ: BGR, 640x640).
- **Ngăn xếp công nghệ:**
  - **API:** librga C/C++ API (im2d.h cho C, im2d.hpp cho C++).
  - **Thư viện liên quan:** librga.so.
- **Chi tiết triển khai (Zero-Copy):**
  - **Nhập DMABUF:** Sử dụng `imimportbuffer_fd()` (hoặc API tương tự trong im2d.h/im2d.hpp) để RGA có thể truy cập bộ đệm DMABUF từ MPP thông qua FD của nó. Thao tác này tạo ra một `rga_buffer_handle_t`.
  - **Tạo DMABUF đích:** Cấp phát một DMABUF mới cho đầu ra của RGA (sẽ được NPU sử dụng). Lấy FD của nó và nhập vào RGA làm bộ đệm đích.
  - **Thực hiện thao tác:**
    - `imresize()`: Thay đổi kích thước khung hình NV12 thành kích thước đầu vào của mô hình phát hiện (ví dụ: 320x320 hoặc 640x640 cho RetinaFace).
    - `imcvt_color_space()`: Chuyển đổi không gian màu từ NV12 sang định dạng yêu cầu của NPU (thường là BGR hoặc RGB, ví dụ: RK_FORMAT_BGR_888).
  - **Đồng bộ hóa:** Sử dụng `im2d_sync()` hoặc `imsync()` để đảm bảo thao tác RGA hoàn tất trước khi NPU sử dụng dữ liệu.
- **Đầu ra:** Các FD của DMABUF mới, mỗi FD trỏ đến một khung hình đã được tiền xử lý (ví dụ: BGR, kích thước phù hợp), sẵn sàng cho mô hình phát hiện khuôn mặt.

**Giai đoạn D: Phát Hiện Khuôn Mặt (Sử dụng NPU)**

- **Mục tiêu:** Chạy mô hình phát hiện khuôn mặt (ví dụ: RetinaFace) trên NPU để xác định vị trí các khuôn mặt trong khung hình.
- **Ngăn xếp công nghệ:**
  - **API:** RKNN Runtime C API (rknn_api.h).
  - **Thư viện liên quan:** librknnrt.so.
  - **Mô hình:** RetinaFace (ví dụ: RetinaFace_mobile320.rknn đã được lượng tử hóa INT8).
- **Chi tiết triển khai (Tối ưu hóa NPU):**
  - **Khởi tạo Context:**
    - Tải mô hình .rknn từ bộ nhớ.
    - `rknn_init()`: Khởi tạo `rknn_context` cho mô hình phát hiện. Sử dụng các cờ quan trọng:
      - `RKNN_FLAG_MEM_ALLOC_OUTSIDE`: Bắt buộc cho zero-copy DMABUF.
      - `RKNN_FLAG_ASYNC_MODE`: Cho phép suy luận bất đồng bộ, `rknn_run` trả về ngay lập tức.
      - `RKNN_FLAG_BATCH_MODE` (nếu áp dụng và được xác minh từ rknn_api.h): Có thể kích hoạt cơ chế xử lý lô nội bộ.
  - **Quản lý Đa Lõi NPU:**
    - RK3588 có 3 lõi NPU.
    - Sử dụng `rknn_set_core_mask()` để gán context cho các lõi cụ thể (ví dụ: `RKNN_NPU_CORE_0`, `RKNN_NPU_CORE_1`, `RKNN_NPU_CORE_2`) hoặc cho phép runtime tự động phân bổ trên nhiều lõi (`RKNN_NPU_CORE_AUTO`, hoặc lý tưởng nhất là `RKNN_NPU_CORE_0_1_2` nếu mô hình và SDK hỗ trợ "multi-batch multi-core mode" hiệu quả cho một context).
  - **Thiết lập Đầu vào (Zero-Copy DMABUF):**
    - `rknn_query()`: Lấy thông tin thuộc tính đầu vào/đầu ra (rknn_tensor_attr, rknn_input_output_num).
    - `rknn_create_mem_from_fd()`: Tạo `rknn_tensor_mem` từ FD của DMABUF đầu ra của RGA.
    - `rknn_set_io_mem()`: Liên kết `rknn_tensor_mem` này với tensor đầu vào của mô hình. Đặt `pass_through = true` trong `rknn_input` nếu dữ liệu đã sẵn sàng.
  - **Xử lý theo lô (Batching):**
    - Gom nhiều khung hình đã tiền xử lý (từ nhiều camera hoặc các khung hình liên tiếp của một camera) thành một lô.
    - Nếu sử dụng `rknn_inputs_set`, chuẩn bị một mảng các cấu trúc `rknn_input`, mỗi cấu trúc trỏ đến dữ liệu của một hình ảnh trong lô. Kích thước lô (chiều N trong NHWC) phải được mô hình hỗ trợ khi chuyển đổi.
  - **Thực thi và Lấy Kết quả:**
    - `rknn_run()`: Thực hiện suy luận.
    - `rknn_outputs_get()`: Lấy kết quả đầu ra (tọa độ bounding box, điểm tin cậy).
    - `rknn_outputs_release()`: Giải phóng bộ đệm đầu ra do runtime cấp phát (nếu `is_prealloc = false`).
  - **Giải phóng:** `rknn_destroy_mem()`, `rknn_destroy()`.
- **Đầu ra:** Mảng các bounding box (tọa độ), điểm tin cậy cho mỗi khuôn mặt được phát hiện trong mỗi khung hình (hoặc lô khung hình).

**Giai đoạn E: Cắt Khuôn Mặt và Tiền Xử Lý cho Nhận Dạng (Sử dụng RGA)**

- **Mục tiêu:** Từ các bounding box đã phát hiện, cắt các vùng chứa khuôn mặt từ khung hình gốc (đã giải mã bởi MPP) và tiền xử lý chúng cho mô hình nhận dạng.
- **Ngăn xếp công nghệ:** librga C/C++ API.
- **Chi tiết triển khai (Zero-Copy):**
  - Đối với mỗi khuôn mặt được phát hiện:
    - **Nhập DMABUF (khung hình gốc):** Sử dụng `imimportbuffer_fd()` với FD của DMABUF gốc từ MPP (hoặc một bản sao đã được thay đổi kích thước phù hợp nếu cần).
    - **Tạo DMABUF đích:** Cấp phát DMABUF mới cho khuôn mặt đã cắt và tiền xử lý.
    - **Thực hiện thao tác:**
      - `imcrop()`: Cắt vùng khuôn mặt dựa trên tọa độ bounding box.
      - `imresize()`: Thay đổi kích thước vùng khuôn mặt đã cắt thành kích thước đầu vào của mô hình nhận dạng (ví dụ: 112x112 cho FaceNet/ArcFace).
      - `imcvt_color_space()`: Chuyển đổi không gian màu nếu cần (ví dụ: sang BGR).
    - **Đồng bộ hóa:** `im2d_sync()` hoặc `imsync()`.
- **Đầu ra:** Các FD của DMABUF, mỗi FD trỏ đến một hình ảnh khuôn mặt đã được cắt và tiền xử lý, sẵn sàng cho mô hình nhận dạng.

**Giai đoạn F: Nhận Dạng Khuôn Mặt (Sử dụng NPU)**

- **Mục tiêu:** Chạy mô hình nhận dạng khuôn mặt (ví dụ: ArcFace, FaceNet) trên NPU để trích xuất vector đặc trưng (embedding) cho mỗi khuôn mặt đã cắt.
- **Ngăn xếp công nghệ:** RKNN Runtime C API.
  - **Mô hình:** ArcFace hoặc FaceNet (ví dụ: facenet.rknn đã được lượng tử hóa INT8).
- **Chi tiết triển khai:** Tương tự như Giai đoạn D (Phát Hiện Khuôn Mặt), nhưng với context NPU và mô hình khác.
  - Tạo `rknn_context` riêng cho mô hình nhận dạng.
  - Sử dụng DMABUF từ Giai đoạn E làm đầu vào.
  - **Xử lý theo lô (Rất quan trọng):** Gom nhiều hình ảnh khuôn mặt đã cắt (từ cùng một khung hình hoặc nhiều khung hình/camera) thành một lô để tối đa hóa hiệu suất NPU.
  - Sử dụng `rknn_set_core_mask()` để phân bổ lõi NPU (có thể khác với lõi dùng cho phát hiện để đạt được sự song song thực sự).
  - Sử dụng `RKNN_FLAG_ASYNC_MODE`.
- **Đầu ra:** Các vector đặc trưng (embeddings) cho mỗi khuôn mặt được xử lý.

**Giai đoạn G: Hậu Xử Lý và Logic Ứng Dụng (Sử dụng CPU)**

- **Mục tiêu:** Xử lý kết quả từ NPU và thực hiện logic nghiệp vụ.
- **Ngăn xếp công nghệ:** C++, có thể sử dụng OpenCV cho một số tiện ích.
- **Chi tiết triển khai:**
  - **Hậu xử lý phát hiện:**
    - Giải mã đầu ra của mô hình phát hiện (nếu cần).
    - Áp dụng ngưỡng tin cậy (confidence thresholding).
    - Thực hiện Non-Maximum Suppression (NMS) để loại bỏ các bounding box trùng lặp hoặc kém tin cậy.
  - **Hậu xử lý nhận dạng:**
    - Chuẩn hóa L2 cho các vector đặc trưng (nếu mô hình chưa thực hiện, ví dụ như trong một số trường hợp của FaceNet ).
    - So sánh vector đặc trưng mới với cơ sở dữ liệu các vector đặc trưng đã biết bằng một thước đo khoảng cách (ví dụ: cosine similarity, Euclidean distance).
  - **Logic ứng dụng:**
    - Liên kết kết quả phát hiện và nhận dạng.
    - Theo dõi khuôn mặt qua các khung hình (nếu cần).
    - Ghi log, gửi thông báo, cập nhật giao diện người dùng, v.v.
- **Đầu ra:** Thông tin về các khuôn mặt được phát hiện và nhận dạng, các sự kiện liên quan.

**Tối Đa Hóa Số Lượng Luồng RTSP và Đảm Bảo Độ Chính Xác**

- **Triệt Để Zero-Copy (DMABUF):** Đây là yếu tố then chốt. Luồng dữ liệu MPP -> RGA -> NPU phải hoàn toàn zero-copy để giảm thiểu việc sử dụng CPU và băng thông bộ nhớ, giải phóng tài nguyên cho việc xử lý nhiều luồng hơn.
- **Tận Dụng Tối Đa Ba Lõi NPU:**
  - **Phân bổ tác vụ thông minh:** Cân nhắc việc gán các mô hình khác nhau (phát hiện, nhận dạng) hoặc các luồng camera khác nhau cho các lõi NPU riêng biệt bằng `rknn_set_core_mask`.
  - **"Multi-batch multi-core mode":** Nếu SDK cho phép một context NPU duy nhất (được cấu hình với `RKNN_NPU_CORE_0_1_2`) xử lý một lô dữ liệu hiệu quả trên cả ba lõi, đây sẽ là lựa chọn tối ưu về thông lượng.
  - **Nhiều context song song:** Tạo nhiều `rknn_context` (ví dụ, một vài cho phát hiện, một vài cho nhận dạng) và quản lý chúng trong một nhóm luồng (thread pool) NPU. Mỗi luồng worker lấy một context và một tác vụ. Điều này cho phép các suy luận khác nhau chạy song song trên các lõi NPU khác nhau.
- **Xử Lý Theo Lô (Batch Processing) Mạnh Mẽ:**
  - **Phát hiện:** Gom các khung hình từ nhiều camera (hoặc các khung hình liên tiếp từ một camera) thành một lô trước khi đưa vào NPU phát hiện.
  - **Nhận dạng:** Sau khi phát hiện, gom tất cả các khuôn mặt đã cắt từ một hoặc nhiều khung hình thành một lô lớn để đưa vào NPU nhận dạng. Đây là nơi có thể đạt được lợi ích lớn từ việc xử lý theo lô.
  - Kích thước lô tối ưu cần được xác định thông qua thực nghiệm.
- **Hoạt Động Bất Đồng Bộ (Asynchronous Operations):**
  - Sử dụng `RKNN_FLAG_ASYNC_MODE` cho `rknn_init` để NPU không chặn CPU.
  - Thiết kế toàn bộ pipeline với các hàng đợi (queues) giữa các giai đoạn (MPP, RGA, NPU, CPU post-processing) để các thành phần có thể hoạt động song song và không đồng bộ.
- **Quản Lý Luồng (Threading) Hiệu Quả:**
  - Sử dụng các nhóm luồng (thread pools) cho từng loại tác vụ chính (đầu vào, giải mã, tiền xử lý RGA, suy luận NPU, hậu xử lý CPU).
  - Số lượng luồng trong mỗi nhóm cần được điều chỉnh dựa trên tải và khả năng của từng thành phần phần cứng.
  - Sử dụng các cơ chế đồng bộ hóa hiệu quả (ví dụ: std::condition_variable, std::mutex, hoặc hàng đợi không khóa nếu có thể).
- **Lựa Chọn và Tối Ưu Hóa Mô Hình:**
  - **Độ chính xác:** Chọn các mô hình như RetinaFace và ArcFace/FaceNet đã được chứng minh về độ chính xác.
  - **Lượng tử hóa INT8:** Sử dụng RKNN-Toolkit2 để lượng tử hóa các mô hình sang INT8. Điều này tăng tốc độ NPU đáng kể. Cung cấp một tập dữ liệu hiệu chỉnh (calibration dataset) đa dạng và đại diện cho dữ liệu thực tế là rất quan trọng để giảm thiểu suy hao độ chính xác.
  - Nếu độ chính xác INT8 không đủ, cân nhắc sử dụng FP16 (với hiệu suất thấp hơn) hoặc các kỹ thuật Quantization-Aware Training (QAT) trước khi chuyển đổi.
- **Quản Lý Độ Phân Giải và Tốc Độ Khung Hình Đầu Vào:**
  - Xử lý ở độ phân giải thấp nhất có thể mà vẫn đảm bảo độ chính xác chấp nhận được cho việc phát hiện.
  - Cân nhắc việc bỏ qua khung hình (frame skipping) một cách có chọn lọc cho các luồng ít quan trọng hơn hoặc khi hệ thống bị quá tải.
- **Hậu Xử Lý Tối Ưu trên CPU:** Các thuật toán như NMS và so khớp embedding cần được triển khai hiệu quả để không trở thành điểm nghẽn của CPU.
- **Theo Dõi và Phân Tích Hiệu Suất (Profiling):** Liên tục theo dõi việc sử dụng CPU, NPU, RGA, MPP và băng thông bộ nhớ. Sử dụng các công cụ như perf, htop, và các API truy vấn hiệu suất của RKNN (ví dụ: `rknn_query` với `RKNN_QUERY_PERF_DETAIL` hoặc `RKNN_QUERY_PERF_RUN`) để xác định và giải quyết các điểm nghẽn.
