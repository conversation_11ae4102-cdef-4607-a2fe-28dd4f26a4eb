#include "handlers/base_handler.hpp"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <optional>

namespace server::handlers {

BaseHandler::BaseHandler(std::shared_ptr<shared::ServerConfig> config)
    : config_(config) {
}

// JSON response builders using shared utilities
utils::JsonValue BaseHandler::createSuccessResponse(const std::string& message, const utils::JsonValue& data) {
    utils::JsonObject response;
    response["success"] = utils::JsonValue(true);
    response["message"] = utils::JsonValue(message);

    if (!data.isNull()) {
        response["data"] = data;
    }

    return utils::JsonValue(response);
}

utils::JsonValue BaseHandler::createErrorResponse(const std::string& error, const std::string& error_code, int http_code) {
    utils::JsonObject response;
    response["success"] = utils::JsonValue(false);
    response["error"] = utils::JsonValue(error);
    response["code"] = utils::JsonValue(http_code);

    if (!error_code.empty()) {
        response["error_code"] = utils::JsonValue(error_code);
    }

    return utils::JsonValue(response);
}

// Data transformation helpers
utils::JsonValue BaseHandler::createTimestamp(const std::chrono::system_clock::time_point& time) {
    auto time_t = std::chrono::system_clock::to_time_t(time);
    std::ostringstream oss;
    oss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
    return utils::JsonValue(oss.str());
}

utils::JsonValue BaseHandler::createDuration(const std::chrono::milliseconds& duration) {
    return utils::JsonValue(static_cast<double>(duration.count()));
}

// Validation helpers
bool BaseHandler::validateJsonRequest(const utils::JsonValue& json, const std::vector<std::string>& required_fields) {
    if (!json.isObject()) {
        return false;
    }

    for (const auto& field : required_fields) {
        if (!json.hasKey(field)) {
            return false;
        }
    }

    return true;
}

std::string BaseHandler::extractStringField(const utils::JsonValue& json, const std::string& field, const std::string& default_value) {
    if (!json.hasKey(field)) {
        return default_value;
    }

    try {
        return json[field].asString();
    } catch (const std::exception&) {
        return default_value;
    }
}

std::optional<float> BaseHandler::extractFloatField(const utils::JsonValue& json, const std::string& field) {
    if (!json.hasKey(field)) {
        return std::nullopt;
    }

    try {
        return static_cast<float>(json[field].asNumber());
    } catch (const std::exception&) {
        return std::nullopt;
    }
}

std::optional<int> BaseHandler::extractIntField(const utils::JsonValue& json, const std::string& field) {
    if (!json.hasKey(field)) {
        return std::nullopt;
    }

    try {
        return json[field].asInt();
    } catch (const std::exception&) {
        return std::nullopt;
    }
}

std::optional<bool> BaseHandler::extractBoolField(const utils::JsonValue& json, const std::string& field) {
    if (!json.hasKey(field)) {
        return std::nullopt;
    }

    try {
        return json[field].asBool();
    } catch (const std::exception&) {
        return std::nullopt;
    }
}

// Logging helpers
void BaseHandler::logInfo(const std::string& message, const std::string& handler_name) {
    if (config_->enable_request_logging) {
        std::cout << "[" << getCurrentTimestamp() << "] [INFO] "
                  << handler_name << " - " << message << std::endl;
    }
}

void BaseHandler::logError(const std::string& error, const std::string& handler_name) {
    std::cout << "[" << getCurrentTimestamp() << "] [ERROR] "
              << handler_name << " - " << error << std::endl;
}

void BaseHandler::logDebug(const std::string& message, const std::string& handler_name) {
    if (config_->enable_debug_logging) {
        std::cout << "[" << getCurrentTimestamp() << "] [DEBUG] "
                  << handler_name << " - " << message << std::endl;
    }
}

// Private helpers
std::string BaseHandler::getCurrentTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

} // namespace server::handlers
