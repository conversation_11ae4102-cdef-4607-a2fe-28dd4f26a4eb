#include <gtest/gtest.h>
#include "rtsp/thread_safe_queue.hpp"
#include "rtsp/rtsp_types.hpp"

#include <thread>
#include <vector>
#include <chrono>
#include <atomic>
#include <random>

using namespace aibox::rtsp;

class ThreadSafeQueueAdvancedTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.max_size = 1000;
        config_.enable_statistics = true;
        config_.enable_dmabuf = true;
        config_.dmabuf_pool_size = 16;
        config_.dmabuf_buffer_size = 1024;
    }
    
    QueueConfig config_;
};

// DMABUF functionality tests
TEST_F(ThreadSafeQueueAdvancedTest, DMABufBasicOperations) {
    ThreadSafeQueue<int> queue(config_);
    
    // Create a mock DMABUF buffer (using -1 as fd for testing)
    auto buffer = std::make_shared<DMABufBuffer>(-1, 1024);
    int metadata = 42;
    
    // Test DMABUF enqueue/dequeue
    EXPECT_TRUE(queue.enqueueDMABuf(buffer, metadata));
    
    std::shared_ptr<DMABufBuffer> result_buffer;
    int result_metadata;
    EXPECT_TRUE(queue.dequeueDMABuf(result_buffer, result_metadata));
    
    EXPECT_EQ(result_buffer, buffer);
    EXPECT_EQ(result_metadata, metadata);
}

TEST_F(ThreadSafeQueueAdvancedTest, DMABufPoolLimit) {
    config_.dmabuf_pool_size = 2;
    ThreadSafeQueue<int> queue(config_);
    
    auto buffer1 = std::make_shared<DMABufBuffer>(-1, 1024);
    auto buffer2 = std::make_shared<DMABufBuffer>(-1, 1024);
    auto buffer3 = std::make_shared<DMABufBuffer>(-1, 1024);
    
    // Fill pool to capacity
    EXPECT_TRUE(queue.enqueueDMABuf(buffer1, 1));
    EXPECT_TRUE(queue.enqueueDMABuf(buffer2, 2));
    
    // Should fail when pool is full
    EXPECT_FALSE(queue.enqueueDMABuf(buffer3, 3));
    
    // Dequeue one and try again
    std::shared_ptr<DMABufBuffer> result_buffer;
    int result_metadata;
    EXPECT_TRUE(queue.dequeueDMABuf(result_buffer, result_metadata));
    EXPECT_TRUE(queue.enqueueDMABuf(buffer3, 3));
}

TEST_F(ThreadSafeQueueAdvancedTest, DMABufDisabled) {
    config_.enable_dmabuf = false;
    ThreadSafeQueue<int> queue(config_);
    
    auto buffer = std::make_shared<DMABufBuffer>(-1, 1024);
    
    // Operations should fail when DMABUF is disabled
    EXPECT_FALSE(queue.enqueueDMABuf(buffer, 42));
    
    std::shared_ptr<DMABufBuffer> result_buffer;
    int result_metadata;
    EXPECT_FALSE(queue.dequeueDMABuf(result_buffer, result_metadata));
}

// Bulk operations tests
TEST_F(ThreadSafeQueueAdvancedTest, BulkEnqueue) {
    ThreadSafeQueue<int> queue(config_);
    
    std::vector<int> items = {1, 2, 3, 4, 5};
    size_t enqueued = queue.enqueueBatch(items);
    
    EXPECT_EQ(enqueued, 5);
    EXPECT_EQ(queue.size(), 5);
}

TEST_F(ThreadSafeQueueAdvancedTest, BulkDequeue) {
    ThreadSafeQueue<int> queue(config_);
    
    // Fill queue
    for (int i = 1; i <= 10; ++i) {
        queue.enqueue(i);
    }
    
    std::vector<int> items;
    size_t dequeued = queue.dequeueBatch(items, 5);
    
    EXPECT_EQ(dequeued, 5);
    EXPECT_EQ(items.size(), 5);
    EXPECT_EQ(queue.size(), 5);
    
    // Verify order
    for (int i = 0; i < 5; ++i) {
        EXPECT_EQ(items[i], i + 1);
    }
}

TEST_F(ThreadSafeQueueAdvancedTest, BulkOperationsWithTimeout) {
    config_.max_size = 3;
    ThreadSafeQueue<int> queue(config_);
    
    // Fill queue partially
    queue.enqueue(1);
    
    std::vector<int> items = {2, 3, 4, 5}; // More than remaining capacity
    size_t enqueued = queue.enqueueBatch(items, 100); // 100ms timeout
    
    EXPECT_EQ(enqueued, 2); // Only 2 more can fit
    EXPECT_TRUE(queue.full());
}

// Lock-free queue stress tests
TEST_F(ThreadSafeQueueAdvancedTest, LockFreeStressTest) {
    config_.use_lock_free = true;
    config_.max_size = 10000;
    ThreadSafeQueue<int> queue(config_);
    
    const int num_producers = 4;
    const int num_consumers = 4;
    const int items_per_producer = 1000;
    
    std::atomic<int> total_produced(0);
    std::atomic<int> total_consumed(0);
    std::vector<std::thread> threads;
    
    // Start producers
    for (int p = 0; p < num_producers; ++p) {
        threads.emplace_back([&queue, &total_produced, items_per_producer, p]() {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(1, 10);
            
            for (int i = 0; i < items_per_producer; ++i) {
                int value = p * items_per_producer + i;
                while (!queue.enqueue(value)) {
                    std::this_thread::sleep_for(std::chrono::microseconds(dis(gen)));
                }
                total_produced++;
            }
        });
    }
    
    // Start consumers
    for (int c = 0; c < num_consumers; ++c) {
        threads.emplace_back([&queue, &total_consumed, &total_produced, items_per_producer, num_producers]() {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(1, 10);
            
            int value;
            while (total_consumed < num_producers * items_per_producer) {
                if (queue.dequeue(value)) {
                    total_consumed++;
                } else {
                    std::this_thread::sleep_for(std::chrono::microseconds(dis(gen)));
                }
            }
        });
    }
    
    // Wait for completion
    for (auto& t : threads) {
        t.join();
    }
    
    EXPECT_EQ(total_produced.load(), num_producers * items_per_producer);
    EXPECT_EQ(total_consumed.load(), num_producers * items_per_producer);
}

// Performance comparison tests
TEST_F(ThreadSafeQueueAdvancedTest, PerformanceComparison) {
    const int num_operations = 100000;
    
    // Test lock-based performance
    config_.use_lock_free = false;
    ThreadSafeQueue<int> lock_based_queue(config_);
    
    auto start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_operations; ++i) {
        lock_based_queue.enqueue(i);
        int value;
        lock_based_queue.dequeue(value);
    }
    auto lock_based_time = std::chrono::high_resolution_clock::now() - start;
    
    // Test lock-free performance
    config_.use_lock_free = true;
    ThreadSafeQueue<int> lock_free_queue(config_);
    
    start = std::chrono::high_resolution_clock::now();
    for (int i = 0; i < num_operations; ++i) {
        lock_free_queue.enqueue(i);
        int value;
        lock_free_queue.dequeue(value);
    }
    auto lock_free_time = std::chrono::high_resolution_clock::now() - start;
    
    // Lock-free should generally be faster or comparable
    // Note: This is a rough test and results may vary
    std::cout << "Lock-based time: " 
              << std::chrono::duration_cast<std::chrono::microseconds>(lock_based_time).count() 
              << " μs" << std::endl;
    std::cout << "Lock-free time: " 
              << std::chrono::duration_cast<std::chrono::microseconds>(lock_free_time).count() 
              << " μs" << std::endl;
}

// Memory usage tests
TEST_F(ThreadSafeQueueAdvancedTest, MemoryUsageTracking) {
    ThreadSafeQueue<std::vector<int>> queue(config_);
    
    // Enqueue large objects
    for (int i = 0; i < 10; ++i) {
        std::vector<int> large_vector(1000, i);
        queue.enqueue(std::move(large_vector));
    }
    
    auto stats = queue.getStatistics();
    EXPECT_GT(stats.total_memory_usage, 0);
    
    // Clear queue and check memory is released
    queue.clear();
    stats = queue.getStatistics();
    EXPECT_EQ(stats.current_size, 0);
}

// Utilization tests
TEST_F(ThreadSafeQueueAdvancedTest, UtilizationCalculation) {
    config_.max_size = 100;
    ThreadSafeQueue<int> queue(config_);
    
    EXPECT_DOUBLE_EQ(queue.getUtilization(), 0.0);
    
    // Fill to 50%
    for (int i = 0; i < 50; ++i) {
        queue.enqueue(i);
    }
    
    EXPECT_DOUBLE_EQ(queue.getUtilization(), 0.5);
    
    // Fill to 100%
    for (int i = 50; i < 100; ++i) {
        queue.enqueue(i);
    }
    
    EXPECT_DOUBLE_EQ(queue.getUtilization(), 1.0);
}

// Error handling tests
TEST_F(ThreadSafeQueueAdvancedTest, ErrorHandling) {
    config_.max_size = 1;
    ThreadSafeQueue<int> queue(config_);
    
    // Fill queue
    EXPECT_TRUE(queue.enqueue(1));
    
    // Test timeout failures
    auto stats_before = queue.getStatistics();
    EXPECT_FALSE(queue.enqueue(2, 10)); // Should timeout
    auto stats_after = queue.getStatistics();
    
    EXPECT_GT(stats_after.enqueue_failures, stats_before.enqueue_failures);
    
    // Clear queue
    int value;
    queue.dequeue(value);
    
    // Test dequeue timeout
    stats_before = queue.getStatistics();
    EXPECT_FALSE(queue.dequeue(value, 10)); // Should timeout
    stats_after = queue.getStatistics();
    
    EXPECT_GT(stats_after.dequeue_timeouts, stats_before.dequeue_timeouts);
}

// Configuration change tests
TEST_F(ThreadSafeQueueAdvancedTest, DynamicConfiguration) {
    ThreadSafeQueue<int> queue(config_);
    
    // Initial configuration
    EXPECT_EQ(queue.capacity(), config_.max_size);
    EXPECT_FALSE(queue.isBackPressureActive());
    
    // Change max size
    queue.setMaxSize(50);
    EXPECT_EQ(queue.capacity(), 50);
    
    // Enable back pressure
    queue.enableBackPressure(true);
    queue.setBackPressureThreshold(40);
    
    // Fill beyond threshold
    for (int i = 0; i < 45; ++i) {
        queue.enqueue(i);
    }
    
    EXPECT_TRUE(queue.isBackPressureActive());
    
    // Test adaptive sizing
    queue.enableAdaptiveSizing(true);
    // Note: Adaptive sizing requires time-based conditions and activity
}

// Thread safety verification
TEST_F(ThreadSafeQueueAdvancedTest, ThreadSafetyVerification) {
    ThreadSafeQueue<int> queue(config_);
    std::atomic<bool> stop_flag(false);
    std::atomic<int> operation_count(0);
    
    const int num_threads = 8;
    std::vector<std::thread> threads;
    
    // Start threads that perform random operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&queue, &stop_flag, &operation_count]() {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> op_dis(0, 3);
            std::uniform_int_distribution<> val_dis(1, 1000);
            
            while (!stop_flag.load()) {
                int op = op_dis(gen);
                int value = val_dis(gen);
                
                switch (op) {
                    case 0: // enqueue
                        queue.tryEnqueue(value);
                        break;
                    case 1: // dequeue
                        queue.tryDequeue(value);
                        break;
                    case 2: // check size
                        queue.size();
                        break;
                    case 3: // get statistics
                        queue.getStatistics();
                        break;
                }
                
                operation_count++;
                std::this_thread::yield();
            }
        });
    }
    
    // Run for a short time
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    stop_flag.store(true);
    
    // Wait for threads to finish
    for (auto& t : threads) {
        t.join();
    }
    
    EXPECT_GT(operation_count.load(), 0);
    
    // Queue should still be in a valid state
    auto stats = queue.getStatistics();
    EXPECT_GE(stats.enqueue_count + stats.dequeue_count, 0);
}
