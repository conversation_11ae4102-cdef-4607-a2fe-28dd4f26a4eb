#pragma once

#include <atomic>
#include <memory>
#include <chrono>
#include <condition_variable>
#include <mutex>
#include <queue>
#include <vector>
#include <functional>

#ifdef HAVE_SHARED_LIBRARY
#include "shared/result.hpp"
#include "shared/error_category.hpp"
#endif

namespace aibox {
namespace rtsp {

/**
 * @brief DMABUF buffer wrapper for zero-copy operations
 * 
 * Provides reference-counted DMABUF management optimized for RK3588.
 */
class DMABufBuffer {
public:
    DMABufBuffer(int fd, size_t size, void* mapped_addr = nullptr);
    ~DMABufBuffer();
    
    // Non-copyable but movable
    DMABufBuffer(const DMABufBuffer&) = delete;
    DMABufBuffer& operator=(const DMABufBuffer&) = delete;
    DMABufBuffer(DMABufBuffer&& other) noexcept;
    DMABufBuffer& operator=(DMABufBuffer&& other) noexcept;
    
    // Buffer access
    int getFd() const { return fd_; }
    size_t getSize() const { return size_; }
    void* getMappedAddress() const { return mapped_addr_; }
    bool isMapped() const { return mapped_addr_ != nullptr; }
    
    // Memory mapping
    bool map(int prot = 0x1 | 0x2); // PROT_READ | PROT_WRITE
    void unmap();
    
    // Reference counting
    std::shared_ptr<DMABufBuffer> share();
    
private:
    int fd_;
    size_t size_;
    void* mapped_addr_;
    std::atomic<int> ref_count_;
};

/**
 * @brief Lock-free queue node for high-performance operations
 */
template<typename T>
struct LockFreeNode {
    std::atomic<T*> data;
    std::atomic<LockFreeNode*> next;
    
    LockFreeNode() : data(nullptr), next(nullptr) {}
};

/**
 * @brief High-performance lock-free queue implementation
 * 
 * Uses Michael & Scott algorithm with hazard pointers for memory safety.
 * Optimized for RK3588 with memory ordering considerations.
 */
template<typename T>
class LockFreeQueue {
public:
    LockFreeQueue();
    ~LockFreeQueue();
    
    // Queue operations
    bool enqueue(T&& item);
    bool enqueue(const T& item);
    bool dequeue(T& item);
    
    // Queue status
    size_t size() const;
    bool empty() const;
    
    // Statistics
    uint64_t getEnqueueCount() const { return enqueue_count_.load(); }
    uint64_t getDequeueCount() const { return dequeue_count_.load(); }
    
private:
    using Node = LockFreeNode<T>;
    
    std::atomic<Node*> head_;
    std::atomic<Node*> tail_;
    
    // Statistics
    std::atomic<uint64_t> enqueue_count_;
    std::atomic<uint64_t> dequeue_count_;
    std::atomic<size_t> size_;
    
    // Memory management
    std::atomic<Node*> free_list_;
    
    Node* allocateNode();
    void deallocateNode(Node* node);
};

/**
 * @brief Queue configuration for different priority levels
 */
struct QueueConfig {
    size_t max_size = 1000;
    bool enable_back_pressure = true;
    size_t back_pressure_threshold = 800;  // 80% of max_size
    bool enable_adaptive_sizing = false;
    size_t min_size = 100;
    size_t growth_factor = 2;
    
    // DMABUF specific settings
    bool enable_dmabuf = false;
    size_t dmabuf_pool_size = 32;
    size_t dmabuf_buffer_size = 1024 * 1024;  // 1MB default
    
    // Performance tuning
    bool use_lock_free = true;
    int timeout_ms = 1000;
    bool enable_statistics = true;
};

/**
 * @brief Queue statistics for monitoring and debugging
 */
struct QueueStatistics {
    uint64_t enqueue_count{0};
    uint64_t dequeue_count{0};
    uint64_t enqueue_failures{0};
    uint64_t dequeue_timeouts{0};
    uint64_t back_pressure_events{0};
    uint64_t adaptive_resize_events{0};
    size_t current_size{0};
    size_t peak_size{0};
    size_t total_memory_usage{0};

    void reset() {
        enqueue_count = 0;
        dequeue_count = 0;
        enqueue_failures = 0;
        dequeue_timeouts = 0;
        back_pressure_events = 0;
        adaptive_resize_events = 0;
        current_size = 0;
        peak_size = 0;
        total_memory_usage = 0;
    }
};

/**
 * @brief Enhanced thread-safe queue with lock-free option and DMABUF support
 * 
 * Provides both lock-based and lock-free implementations with automatic
 * fallback. Optimized for RK3588 hardware with DMABUF zero-copy support.
 */
template<typename T>
class ThreadSafeQueue {
public:
    explicit ThreadSafeQueue(const QueueConfig& config = QueueConfig{});
    ~ThreadSafeQueue();
    
    // Queue operations
    bool enqueue(const T& item, int timeout_ms = -1);
    bool enqueue(T&& item, int timeout_ms = -1);
    bool dequeue(T& item, int timeout_ms = -1);
    bool tryEnqueue(const T& item);
    bool tryEnqueue(T&& item);
    bool tryDequeue(T& item);
    
    // Bulk operations
    size_t enqueueBatch(const std::vector<T>& items, int timeout_ms = -1);
    size_t dequeueBatch(std::vector<T>& items, size_t max_count, int timeout_ms = -1);
    
    // Queue status
    size_t size() const;
    size_t capacity() const;
    bool empty() const;
    bool full() const;
    void clear();
    
    // Configuration
    void setMaxSize(size_t max_size);
    void enableAdaptiveSizing(bool enable);
    void enableBackPressure(bool enable);
    void setBackPressureThreshold(size_t threshold);
    void stop();
    void resume();
    
    // DMABUF operations
    bool enqueueDMABuf(std::shared_ptr<DMABufBuffer> buffer, const T& metadata);
    bool dequeueDMABuf(std::shared_ptr<DMABufBuffer>& buffer, T& metadata);
    
    // Statistics and monitoring
    QueueStatistics getStatistics() const;
    void resetStatistics();
    bool isBackPressureActive() const;
    double getUtilization() const;
    
    // Performance tuning
    void setLockFreeMode(bool enable);
    bool isLockFreeMode() const;
    void optimizeForLatency();
    void optimizeForThroughput();
    
private:
    QueueConfig config_;

    // Internal atomic statistics
    mutable std::atomic<uint64_t> enqueue_count_{0};
    mutable std::atomic<uint64_t> dequeue_count_{0};
    mutable std::atomic<uint64_t> enqueue_failures_{0};
    mutable std::atomic<uint64_t> dequeue_timeouts_{0};
    mutable std::atomic<uint64_t> back_pressure_events_{0};
    mutable std::atomic<uint64_t> adaptive_resize_events_{0};
    mutable std::atomic<size_t> peak_size_{0};
    mutable std::atomic<size_t> total_memory_usage_{0};
    
    // Lock-based implementation
    std::queue<T> queue_;
    mutable std::mutex mutex_;
    std::condition_variable condition_not_empty_;
    std::condition_variable condition_not_full_;
    
    // Lock-free implementation
    std::unique_ptr<LockFreeQueue<T>> lock_free_queue_;
    
    // Control flags
    std::atomic<bool> should_stop_;
    std::atomic<bool> back_pressure_active_;
    std::atomic<bool> use_lock_free_;
    
    // DMABUF management
    struct DMABufEntry {
        std::shared_ptr<DMABufBuffer> buffer;
        T metadata;
    };
    std::queue<DMABufEntry> dmabuf_queue_;
    std::mutex dmabuf_mutex_;
    
    // Adaptive sizing
    std::atomic<size_t> current_max_size_;
    std::chrono::steady_clock::time_point last_resize_time_;
    
    // Helper methods
    void updateStatistics(bool enqueue_success, bool is_enqueue);
    void checkAndHandleBackPressure();
    void adaptiveResize();
    bool shouldUseAdaptiveSizing() const;
    
    // Lock-free helpers
    bool enqueueInternal(T&& item, int timeout_ms);
    bool dequeueInternal(T& item, int timeout_ms);
};

// Type aliases for common queue types
using NALUnitQueue = ThreadSafeQueue<struct NALUnit>;
using PacketQueue = ThreadSafeQueue<struct RTPPacket>;
using FrameQueue = ThreadSafeQueue<struct VideoFrame>;

} // namespace rtsp
} // namespace aibox
