# X11 Server Setup Scripts

This directory contains scripts to install and configure X11 servers on different host operating systems, enabling GUI applications to run in containers and display on the host.

## Overview

The C-AIBOX Qt5 client application can run with a graphical interface when an X11 server is available. These scripts help set up X11 forwarding from containers to the host display.

## Quick Start

### Auto-Detection Script
```bash
# Run the main setup script (detects OS automatically)
./scripts/setup_x11_host.sh
```

### Platform-Specific Scripts

#### Linux
```bash
# Linux with native X11 (automatic)
./scripts/setup_x11_host.sh
```

#### macOS
```bash
# Install and configure XQuartz
./scripts/setup_xquartz_macos.sh
```

#### Windows
```powershell
# Run in PowerShell as Administrator
./scripts/setup_vcxsrv_windows.ps1
```

## Script Details

### `setup_x11_host.sh`
**Main setup script with OS auto-detection**

- Detects Linux, macOS, Windows/WSL2
- Installs appropriate X11 server
- Configures container access
- Creates environment files
- Tests X11 connection

**Usage:**
```bash
./scripts/setup_x11_host.sh
```

### `setup_xquartz_macos.sh`
**Comprehensive macOS XQuartz setup**

Features:
- Installs Homebrew if needed
- Downloads and installs XQuartz
- Configures network connections
- Sets up authentication
- Creates helper scripts
- Supports both Intel and Apple Silicon

**Usage:**
```bash
./scripts/setup_xquartz_macos.sh
```

**Manual steps required:**
1. Open XQuartz
2. Go to Preferences > Security
3. Check "Allow connections from network clients"
4. Restart XQuartz

### `setup_vcxsrv_windows.ps1`
**Windows VcXsrv installation and configuration**

Features:
- Downloads and installs VcXsrv
- Configures Windows Firewall
- Creates desktop shortcuts
- Sets up optimal container settings
- Creates startup scripts

**Usage (PowerShell as Administrator):**
```powershell
# Basic installation
./scripts/setup_vcxsrv_windows.ps1

# With auto-start
./scripts/setup_vcxsrv_windows.ps1 -AutoStart

# Skip firewall configuration
./scripts/setup_vcxsrv_windows.ps1 -ConfigureFirewall:$false
```

## Container Environment

After running the host setup, use these commands in your container:

### Linux Host
```bash
source ./scripts/container_x11_env.sh
export DISPLAY=:0
./scripts/run_client.sh --gui
```

### macOS Host
```bash
source ./scripts/container_x11_env_macos.sh
export DISPLAY=host.docker.internal:0
./scripts/run_client.sh --gui
```

### Windows Host (WSL2)
```bash
source ./scripts/container_x11_env.sh
export DISPLAY=host.docker.internal:0
./scripts/run_client.sh --gui
```

## Troubleshooting

### Common Issues

**1. "Authorization required" Error**
```bash
# On host (Linux/macOS)
xhost +local:docker
xhost +localhost

# Or disable access control temporarily
xhost +
```

**2. "Can't open display" Error**
```bash
# Check DISPLAY variable
echo $DISPLAY

# Test X11 connection
xdpyinfo
xset q

# Try different DISPLAY formats
export DISPLAY=:0
export DISPLAY=localhost:0
export DISPLAY=host.docker.internal:0
```

**3. VcXsrv Not Starting (Windows)**
- Check Windows Firewall settings
- Run VcXsrv as Administrator
- Verify port 6000 is not blocked
- Use the created desktop shortcuts

**4. XQuartz Issues (macOS)**
- Restart XQuartz completely
- Check "Allow connections from network clients"
- Verify firewall allows XQuartz
- Try the helper script: `~/start_xquartz_for_containers.sh`

### Debug Commands

```bash
# Test X11 server
xdpyinfo
xwininfo -root
xclock  # Simple GUI test

# Check processes
ps aux | grep -i x11
ps aux | grep -i xquartz
ps aux | grep -i vcxsrv

# Network connectivity
netstat -an | grep 6000  # X11 port
telnet localhost 6000    # Test connection
```

## Security Considerations

### Development vs Production

**Development (Recommended Settings):**
- Access control disabled (`-ac` flag)
- Network connections allowed
- Simplified authentication

**Production (Enhanced Security):**
- Enable access control
- Use proper X11 authentication
- Restrict network access
- Use SSH X11 forwarding

### Firewall Configuration

**Linux:**
```bash
# Allow X11 port
sudo ufw allow 6000/tcp
```

**macOS:**
```bash
# XQuartz usually handles firewall automatically
# Manual configuration in System Preferences > Security & Privacy
```

**Windows:**
```powershell
# Automatic configuration in setup script
# Manual: Allow VcXsrv through Windows Firewall
```

## Performance Tips

### Optimize for Containers
```bash
# Reduce X11 overhead
export QT_X11_NO_MITSHM=1
export LIBGL_ALWAYS_INDIRECT=1

# Disable unnecessary features
export QT_AUTO_SCREEN_SCALE_FACTOR=0
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false"
```

### Network Optimization
- Use `--net=host` for better performance (Linux)
- Consider VNC for remote access
- Use hardware acceleration when available

## Integration with C-AIBOX

### Automated Workflow
```bash
# 1. Setup host X11 server
./scripts/setup_x11_host.sh

# 2. Start dev container (VSCode will handle this)
# 3. In container, run the application
./scripts/run_client.sh --gui
```

### CI/CD Integration
```bash
# Always use headless mode in CI
./scripts/run_client.sh --headless

# Or set environment
export QT_QPA_PLATFORM=offscreen
./build/apps/client/client_app
```

## Files Created

### Host Files
- `~/start_xquartz_for_containers.sh` (macOS)
- `~/Desktop/VcXsrv for C-AIBOX.lnk` (Windows)
- `~/Desktop/Start VcXsrv for C-AIBOX.bat` (Windows)

### Container Files
- `./scripts/container_x11_env.sh`
- `./scripts/container_x11_env_macos.sh`

## Support

For issues with these scripts:

1. Check the troubleshooting section above
2. Verify your OS and version compatibility
3. Test with simple X11 applications first (`xclock`, `xeyes`)
4. Check container and host network connectivity
5. Review firewall and security settings

## References

- [XQuartz Documentation](https://www.xquartz.org/)
- [VcXsrv Project](https://sourceforge.net/projects/vcxsrv/)
- [X11 Forwarding Guide](https://wiki.archlinux.org/title/X11_forwarding)
- [Docker GUI Applications](https://docs.docker.com/desktop/faqs/linuxfaqs/#can-i-use-docker-desktop-on-linux-to-run-gui-applications)
