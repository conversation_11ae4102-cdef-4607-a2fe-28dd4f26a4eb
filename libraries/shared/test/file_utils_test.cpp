#include <gtest/gtest.h>
#include <utils/file_utils.hpp>
#include <filesystem>

class FileUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test directory
        testDir = "test_file_utils";
        utils::FileUtils::createDirectory(testDir);
    }

    void TearDown() override {
        // Clean up test directory
        if (utils::FileUtils::exists(testDir)) {
            utils::FileUtils::deleteDirectory(testDir);
        }
    }

    std::string testDir;
    
    std::string getTestPath(const std::string& filename) {
        return utils::FileUtils::joinPath(testDir, filename);
    }
};

TEST_F(FileUtilsTest, FileReadWrite) {
    std::string testFile = getTestPath("test.txt");
    std::string content = "Hello, World!\nThis is a test file.";
    
    // Test write file
    EXPECT_TRUE(utils::FileUtils::writeFile(testFile, content));
    EXPECT_TRUE(utils::FileUtils::exists(testFile));
    EXPECT_TRUE(utils::FileUtils::isFile(testFile));
    
    // Test read file
    auto readContent = utils::FileUtils::readFile(testFile);
    EXPECT_TRUE(readContent.has_value());
    EXPECT_EQ(readContent.value(), content);
    
    // Test convenience functions
    EXPECT_TRUE(utils::writeFile(testFile + "_conv", content));
    auto convRead = utils::readFile(testFile + "_conv");
    EXPECT_TRUE(convRead.has_value());
    EXPECT_EQ(convRead.value(), content);
    
    EXPECT_TRUE(utils::fileExists(testFile));
}

TEST_F(FileUtilsTest, FileLines) {
    std::string testFile = getTestPath("lines.txt");
    std::vector<std::string> lines = {"Line 1", "Line 2", "Line 3", ""};
    
    // Test write lines
    EXPECT_TRUE(utils::FileUtils::writeLines(testFile, lines));
    
    // Test read lines
    auto readLines = utils::FileUtils::readLines(testFile);
    EXPECT_TRUE(readLines.has_value());
    EXPECT_EQ(readLines.value().size(), 4);
    EXPECT_EQ(readLines.value()[0], "Line 1");
    EXPECT_EQ(readLines.value()[1], "Line 2");
    EXPECT_EQ(readLines.value()[2], "Line 3");
    EXPECT_EQ(readLines.value()[3], "");
}

TEST_F(FileUtilsTest, BinaryReadWrite) {
    std::string testFile = getTestPath("binary.dat");
    std::vector<uint8_t> data = {0x00, 0x01, 0x02, 0xFF, 0xFE, 0xFD};
    
    // Test write binary
    EXPECT_TRUE(utils::FileUtils::writeBinary(testFile, data));
    
    // Test read binary
    auto readData = utils::FileUtils::readBinary(testFile);
    EXPECT_TRUE(readData.has_value());
    EXPECT_EQ(readData.value(), data);
}

TEST_F(FileUtilsTest, FileAppend) {
    std::string testFile = getTestPath("append.txt");
    
    // Write initial content
    EXPECT_TRUE(utils::FileUtils::writeFile(testFile, "Initial content\n"));
    
    // Append more content
    EXPECT_TRUE(utils::FileUtils::writeFile(testFile, "Appended content\n", true));
    
    // Read and verify
    auto content = utils::FileUtils::readFile(testFile);
    EXPECT_TRUE(content.has_value());
    EXPECT_EQ(content.value(), "Initial content\nAppended content\n");
}

TEST_F(FileUtilsTest, FileProperties) {
    std::string testFile = getTestPath("properties.txt");
    std::string content = "Test content for properties";
    
    EXPECT_TRUE(utils::FileUtils::writeFile(testFile, content));
    
    // Test file size
    auto size = utils::FileUtils::getFileSize(testFile);
    EXPECT_TRUE(size.has_value());
    EXPECT_EQ(size.value(), content.length());
    
    // Test modification time
    auto modTime = utils::FileUtils::getModificationTime(testFile);
    EXPECT_TRUE(modTime.has_value());
    
    // Test file type checks
    EXPECT_TRUE(utils::FileUtils::isFile(testFile));
    EXPECT_FALSE(utils::FileUtils::isDirectory(testFile));
    EXPECT_TRUE(utils::FileUtils::isReadable(testFile));
    EXPECT_TRUE(utils::FileUtils::isWritable(testFile));
}

TEST_F(FileUtilsTest, FileCopyMoveDelete) {
    std::string sourceFile = getTestPath("source.txt");
    std::string copyFile = getTestPath("copy.txt");
    std::string moveFile = getTestPath("moved.txt");
    std::string content = "Content to copy and move";
    
    // Create source file
    EXPECT_TRUE(utils::FileUtils::writeFile(sourceFile, content));
    
    // Test copy
    EXPECT_TRUE(utils::FileUtils::copyFile(sourceFile, copyFile));
    EXPECT_TRUE(utils::FileUtils::exists(copyFile));
    auto copyContent = utils::FileUtils::readFile(copyFile);
    EXPECT_TRUE(copyContent.has_value());
    EXPECT_EQ(copyContent.value(), content);
    
    // Test move
    EXPECT_TRUE(utils::FileUtils::moveFile(copyFile, moveFile));
    EXPECT_FALSE(utils::FileUtils::exists(copyFile));
    EXPECT_TRUE(utils::FileUtils::exists(moveFile));
    
    // Test delete
    EXPECT_TRUE(utils::FileUtils::deleteFile(sourceFile));
    EXPECT_TRUE(utils::FileUtils::deleteFile(moveFile));
    EXPECT_FALSE(utils::FileUtils::exists(sourceFile));
    EXPECT_FALSE(utils::FileUtils::exists(moveFile));
}

TEST_F(FileUtilsTest, DirectoryOperations) {
    std::string subDir = getTestPath("subdir");
    std::string nestedDir = utils::FileUtils::joinPath(subDir, "nested");
    
    // Test create directory
    EXPECT_TRUE(utils::FileUtils::createDirectory(nestedDir));
    EXPECT_TRUE(utils::FileUtils::exists(nestedDir));
    EXPECT_TRUE(utils::FileUtils::isDirectory(nestedDir));
    
    // Test convenience function
    std::string convDir = getTestPath("conv_dir");
    EXPECT_TRUE(utils::createDir(convDir));
    
    // Create some test files
    EXPECT_TRUE(utils::FileUtils::writeFile(utils::FileUtils::joinPath(subDir, "file1.txt"), "content1"));
    EXPECT_TRUE(utils::FileUtils::writeFile(utils::FileUtils::joinPath(subDir, "file2.txt"), "content2"));
    EXPECT_TRUE(utils::FileUtils::writeFile(utils::FileUtils::joinPath(nestedDir, "file3.txt"), "content3"));
    
    // Test list files
    auto files = utils::FileUtils::listFiles(subDir, false);
    EXPECT_TRUE(files.has_value());
    EXPECT_EQ(files.value().size(), 2);
    
    auto filesRecursive = utils::FileUtils::listFiles(subDir, true);
    EXPECT_TRUE(filesRecursive.has_value());
    EXPECT_EQ(filesRecursive.value().size(), 3);
    
    // Test list directories
    auto dirs = utils::FileUtils::listDirectories(subDir, false);
    EXPECT_TRUE(dirs.has_value());
    EXPECT_EQ(dirs.value().size(), 1);
    
    // Test list all
    auto all = utils::FileUtils::listAll(subDir, false);
    EXPECT_TRUE(all.has_value());
    EXPECT_EQ(all.value().size(), 3); // 2 files + 1 directory
    
    // Test delete directory
    EXPECT_TRUE(utils::FileUtils::deleteDirectory(subDir));
    EXPECT_FALSE(utils::FileUtils::exists(subDir));
}

TEST_F(FileUtilsTest, PathOperations) {
    // Test path components
    std::string path = "/home/<USER>/documents/file.txt";
    
    EXPECT_EQ(utils::FileUtils::getFilename(path), "file.txt");
    EXPECT_EQ(utils::FileUtils::getBasename(path), "file");
    EXPECT_EQ(utils::FileUtils::getExtension(path), "txt");
    EXPECT_EQ(utils::FileUtils::getDirectory(path), "/home/<USER>/documents");
    
    // Test path joining
    std::vector<std::string> components = {"home", "user", "documents", "file.txt"};
    std::string joined = utils::FileUtils::joinPath(components);
    EXPECT_FALSE(joined.empty());
    
    std::string joined2 = utils::FileUtils::joinPath("home/user", "documents/file.txt");
    EXPECT_FALSE(joined2.empty());
    
    // Test convenience function
    std::string convJoined = utils::joinPath("path1", "path2");
    EXPECT_FALSE(convJoined.empty());
    
    // Test normalize path
    std::string normalized = utils::FileUtils::normalizePath("./test/../test/file.txt");
    EXPECT_FALSE(normalized.empty());
    
    // Test absolute path
    std::string absolute = utils::FileUtils::getAbsolutePath("relative/path");
    EXPECT_FALSE(absolute.empty());
}

TEST_F(FileUtilsTest, WorkingDirectory) {
    std::string originalDir = utils::FileUtils::getCurrentDirectory();
    EXPECT_FALSE(originalDir.empty());
    
    // Create a test subdirectory
    std::string newDir = getTestPath("workdir");
    EXPECT_TRUE(utils::FileUtils::createDirectory(newDir));
    
    // Change to new directory
    EXPECT_TRUE(utils::FileUtils::setCurrentDirectory(newDir));
    
    std::string currentDir = utils::FileUtils::getCurrentDirectory();
    EXPECT_TRUE(currentDir.find("workdir") != std::string::npos);
    
    // Change back to original directory
    EXPECT_TRUE(utils::FileUtils::setCurrentDirectory(originalDir));
}

TEST_F(FileUtilsTest, TemporaryFiles) {
    // Test temporary directory
    std::string tempDir = utils::FileUtils::getTempDirectory();
    EXPECT_FALSE(tempDir.empty());
    EXPECT_TRUE(utils::FileUtils::exists(tempDir));
    EXPECT_TRUE(utils::FileUtils::isDirectory(tempDir));
    
    // Test create temporary file
    auto tempFile = utils::FileUtils::createTempFile("test_", ".tmp");
    EXPECT_TRUE(tempFile.has_value());
    EXPECT_TRUE(utils::FileUtils::exists(tempFile.value()));
    EXPECT_TRUE(utils::FileUtils::isFile(tempFile.value()));
    
    // Clean up temp file
    EXPECT_TRUE(utils::FileUtils::deleteFile(tempFile.value()));
    
    // Test create temporary directory
    auto tempDirPath = utils::FileUtils::createTempDirectory("test_dir_");
    EXPECT_TRUE(tempDirPath.has_value());
    EXPECT_TRUE(utils::FileUtils::exists(tempDirPath.value()));
    EXPECT_TRUE(utils::FileUtils::isDirectory(tempDirPath.value()));
    
    // Clean up temp directory
    EXPECT_TRUE(utils::FileUtils::deleteDirectory(tempDirPath.value()));
}

TEST_F(FileUtilsTest, FileSearch) {
    // Create test files with different extensions
    EXPECT_TRUE(utils::FileUtils::writeFile(getTestPath("file1.txt"), "content"));
    EXPECT_TRUE(utils::FileUtils::writeFile(getTestPath("file2.txt"), "content"));
    EXPECT_TRUE(utils::FileUtils::writeFile(getTestPath("file3.log"), "content"));
    EXPECT_TRUE(utils::FileUtils::writeFile(getTestPath("document.pdf"), "content"));
    
    // Test find files with pattern
    auto txtFiles = utils::FileUtils::findFiles(testDir, "*.txt", false);
    EXPECT_TRUE(txtFiles.has_value());
    EXPECT_EQ(txtFiles.value().size(), 2);
    
    auto allFiles = utils::FileUtils::findFiles(testDir, "*", false);
    EXPECT_TRUE(allFiles.has_value());
    EXPECT_EQ(allFiles.value().size(), 4);
    
    auto logFiles = utils::FileUtils::findFiles(testDir, "*.log", false);
    EXPECT_TRUE(logFiles.has_value());
    EXPECT_EQ(logFiles.value().size(), 1);
}

TEST_F(FileUtilsTest, DirectorySize) {
    // Create files with known content
    std::string content1 = "Hello";  // 5 bytes
    std::string content2 = "World!"; // 6 bytes
    
    EXPECT_TRUE(utils::FileUtils::writeFile(getTestPath("file1.txt"), content1));
    EXPECT_TRUE(utils::FileUtils::writeFile(getTestPath("file2.txt"), content2));
    
    auto dirSize = utils::FileUtils::getDirectorySize(testDir);
    EXPECT_TRUE(dirSize.has_value());
    EXPECT_EQ(dirSize.value(), 11); // 5 + 6 bytes
}

TEST_F(FileUtilsTest, FileComparison) {
    std::string file1 = getTestPath("file1.txt");
    std::string file2 = getTestPath("file2.txt");
    std::string file3 = getTestPath("file3.txt");
    std::string content = "Same content";
    
    EXPECT_TRUE(utils::FileUtils::writeFile(file1, content));
    EXPECT_TRUE(utils::FileUtils::writeFile(file2, content));
    EXPECT_TRUE(utils::FileUtils::writeFile(file3, "Different content"));
    
    // Test file comparison
    auto comparison1 = utils::FileUtils::compareFiles(file1, file2);
    EXPECT_TRUE(comparison1.has_value());
    EXPECT_TRUE(comparison1.value());
    
    auto comparison2 = utils::FileUtils::compareFiles(file1, file3);
    EXPECT_TRUE(comparison2.has_value());
    EXPECT_FALSE(comparison2.value());
}

TEST_F(FileUtilsTest, FileHash) {
    std::string testFile = getTestPath("hash.txt");
    std::string content = "Content for hashing";
    
    EXPECT_TRUE(utils::FileUtils::writeFile(testFile, content));
    
    auto hash1 = utils::FileUtils::getFileHash(testFile);
    EXPECT_TRUE(hash1.has_value());
    EXPECT_FALSE(hash1.value().empty());
    
    // Same content should produce same hash
    std::string testFile2 = getTestPath("hash2.txt");
    EXPECT_TRUE(utils::FileUtils::writeFile(testFile2, content));
    
    auto hash2 = utils::FileUtils::getFileHash(testFile2);
    EXPECT_TRUE(hash2.has_value());
    EXPECT_EQ(hash1.value(), hash2.value());
}

TEST_F(FileUtilsTest, ErrorHandling) {
    std::string nonExistentFile = getTestPath("nonexistent.txt");
    std::string nonExistentDir = getTestPath("nonexistent_dir");
    
    // Test operations on non-existent files
    EXPECT_FALSE(utils::FileUtils::exists(nonExistentFile));
    EXPECT_FALSE(utils::FileUtils::readFile(nonExistentFile).has_value());
    EXPECT_FALSE(utils::FileUtils::readLines(nonExistentFile).has_value());
    EXPECT_FALSE(utils::FileUtils::readBinary(nonExistentFile).has_value());
    EXPECT_FALSE(utils::FileUtils::getFileSize(nonExistentFile).has_value());
    EXPECT_FALSE(utils::FileUtils::getModificationTime(nonExistentFile).has_value());
    EXPECT_FALSE(utils::FileUtils::copyFile(nonExistentFile, getTestPath("copy.txt")));
    EXPECT_FALSE(utils::FileUtils::deleteFile(nonExistentFile));
    
    // Test operations on non-existent directories
    EXPECT_FALSE(utils::FileUtils::listFiles(nonExistentDir).has_value());
    EXPECT_FALSE(utils::FileUtils::listDirectories(nonExistentDir).has_value());
    EXPECT_FALSE(utils::FileUtils::listAll(nonExistentDir).has_value());
    EXPECT_FALSE(utils::FileUtils::getDirectorySize(nonExistentDir).has_value());
}
