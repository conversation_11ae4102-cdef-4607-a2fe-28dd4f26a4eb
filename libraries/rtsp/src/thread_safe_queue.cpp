#include "rtsp/thread_safe_queue.hpp"
#include "rtsp/rtsp_types.hpp"

#include <sys/mman.h>
#include <unistd.h>
#include <fcntl.h>
#include <iostream>
#include <algorithm>

namespace aibox {
namespace rtsp {

// DMABufBuffer implementation
DMABufBuffer::DMABufBuffer(int fd, size_t size, void* mapped_addr)
    : fd_(fd), size_(size), mapped_addr_(mapped_addr), ref_count_(1) {
}

DMABufBuffer::~DMABufBuffer() {
    unmap();
    if (fd_ >= 0) {
        close(fd_);
    }
}

DMABufBuffer::DMABufBuffer(DMABufBuffer&& other) noexcept
    : fd_(other.fd_), size_(other.size_), mapped_addr_(other.mapped_addr_), 
      ref_count_(other.ref_count_.load()) {
    other.fd_ = -1;
    other.size_ = 0;
    other.mapped_addr_ = nullptr;
    other.ref_count_ = 0;
}

DMABufBuffer& DMABufBuffer::operator=(DMABufBuffer&& other) noexcept {
    if (this != &other) {
        unmap();
        if (fd_ >= 0) {
            close(fd_);
        }
        
        fd_ = other.fd_;
        size_ = other.size_;
        mapped_addr_ = other.mapped_addr_;
        ref_count_ = other.ref_count_.load();
        
        other.fd_ = -1;
        other.size_ = 0;
        other.mapped_addr_ = nullptr;
        other.ref_count_ = 0;
    }
    return *this;
}

bool DMABufBuffer::map(int prot) {
    if (mapped_addr_ != nullptr || fd_ < 0) {
        return false;
    }
    
    mapped_addr_ = mmap(nullptr, size_, prot, MAP_SHARED, fd_, 0);
    if (mapped_addr_ == MAP_FAILED) {
        mapped_addr_ = nullptr;
        return false;
    }
    
    return true;
}

void DMABufBuffer::unmap() {
    if (mapped_addr_ != nullptr) {
        munmap(mapped_addr_, size_);
        mapped_addr_ = nullptr;
    }
}

std::shared_ptr<DMABufBuffer> DMABufBuffer::share() {
    ref_count_++;
    return std::shared_ptr<DMABufBuffer>(this, [](DMABufBuffer* buf) {
        if (--buf->ref_count_ == 0) {
            delete buf;
        }
    });
}

// LockFreeQueue implementation
template<typename T>
LockFreeQueue<T>::LockFreeQueue() 
    : enqueue_count_(0), dequeue_count_(0), size_(0), free_list_(nullptr) {
    Node* dummy = new Node;
    head_.store(dummy);
    tail_.store(dummy);
}

template<typename T>
LockFreeQueue<T>::~LockFreeQueue() {
    // Clean up remaining items
    T item;
    while (dequeue(item)) {
        // Items are automatically destroyed
    }
    
    // Clean up nodes
    Node* head = head_.load();
    while (head != nullptr) {
        Node* next = head->next.load();
        delete head;
        head = next;
    }
    
    // Clean up free list
    Node* free_node = free_list_.load();
    while (free_node != nullptr) {
        Node* next = free_node->next.load();
        delete free_node;
        free_node = next;
    }
}

template<typename T>
bool LockFreeQueue<T>::enqueue(T&& item) {
    Node* new_node = allocateNode();
    if (!new_node) {
        return false;
    }
    
    T* data = new T(std::move(item));
    new_node->data.store(data);
    
    while (true) {
        Node* last = tail_.load();
        Node* next = last->next.load();
        
        if (last == tail_.load()) {
            if (next == nullptr) {
                if (last->next.compare_exchange_weak(next, new_node)) {
                    break;
                }
            } else {
                tail_.compare_exchange_weak(last, next);
            }
        }
    }
    
    Node* current_tail = tail_.load();
    tail_.compare_exchange_weak(current_tail, new_node);
    enqueue_count_++;
    size_++;
    return true;
}

template<typename T>
bool LockFreeQueue<T>::enqueue(const T& item) {
    T copy = item;
    return enqueue(std::move(copy));
}

template<typename T>
bool LockFreeQueue<T>::dequeue(T& item) {
    while (true) {
        Node* first = head_.load();
        Node* last = tail_.load();
        Node* next = first->next.load();
        
        if (first == head_.load()) {
            if (first == last) {
                if (next == nullptr) {
                    return false; // Queue is empty
                }
                tail_.compare_exchange_weak(last, next);
            } else {
                if (next == nullptr) {
                    continue;
                }
                
                T* data = next->data.load();
                if (data == nullptr) {
                    continue;
                }
                
                if (head_.compare_exchange_weak(first, next)) {
                    item = *data;
                    delete data;
                    deallocateNode(first);
                    dequeue_count_++;
                    size_--;
                    return true;
                }
            }
        }
    }
}

template<typename T>
size_t LockFreeQueue<T>::size() const {
    return size_.load();
}

template<typename T>
bool LockFreeQueue<T>::empty() const {
    return size_.load() == 0;
}

template<typename T>
typename LockFreeQueue<T>::Node* LockFreeQueue<T>::allocateNode() {
    Node* node = free_list_.load();
    while (node != nullptr) {
        Node* next = node->next.load();
        if (free_list_.compare_exchange_weak(node, next)) {
            node->data.store(nullptr);
            node->next.store(nullptr);
            return node;
        }
        node = free_list_.load();
    }
    
    return new Node;
}

template<typename T>
void LockFreeQueue<T>::deallocateNode(Node* node) {
    node->data.store(nullptr);
    Node* free_head = free_list_.load();
    do {
        node->next.store(free_head);
    } while (!free_list_.compare_exchange_weak(free_head, node));
}

// ThreadSafeQueue implementation
template<typename T>
ThreadSafeQueue<T>::ThreadSafeQueue(const QueueConfig& config)
    : config_(config), should_stop_(false), back_pressure_active_(false),
      use_lock_free_(config.use_lock_free), current_max_size_(config.max_size),
      last_resize_time_(std::chrono::steady_clock::now()) {
    
    if (config_.use_lock_free) {
        lock_free_queue_ = std::make_unique<LockFreeQueue<T>>();
    }
}

template<typename T>
ThreadSafeQueue<T>::~ThreadSafeQueue() {
    stop();
    clear();
}

template<typename T>
bool ThreadSafeQueue<T>::enqueue(const T& item, int timeout_ms) {
    if (should_stop_.load()) {
        return false;
    }
    
    if (use_lock_free_.load() && lock_free_queue_) {
        bool success = lock_free_queue_->enqueue(item);
        updateStatistics(success, true);
        if (success) {
            checkAndHandleBackPressure();
        }
        return success;
    }
    
    return enqueueInternal(T(item), timeout_ms);
}

template<typename T>
bool ThreadSafeQueue<T>::enqueue(T&& item, int timeout_ms) {
    if (should_stop_.load()) {
        return false;
    }
    
    if (use_lock_free_.load() && lock_free_queue_) {
        bool success = lock_free_queue_->enqueue(std::move(item));
        updateStatistics(success, true);
        if (success) {
            checkAndHandleBackPressure();
        }
        return success;
    }
    
    return enqueueInternal(std::move(item), timeout_ms);
}

template<typename T>
bool ThreadSafeQueue<T>::dequeue(T& item, int timeout_ms) {
    if (use_lock_free_.load() && lock_free_queue_) {
        bool success = lock_free_queue_->dequeue(item);
        updateStatistics(success, false);
        return success;
    }
    
    return dequeueInternal(item, timeout_ms);
}

template<typename T>
bool ThreadSafeQueue<T>::tryEnqueue(const T& item) {
    return enqueue(item, 0);
}

template<typename T>
bool ThreadSafeQueue<T>::tryEnqueue(T&& item) {
    return enqueue(std::move(item), 0);
}

template<typename T>
bool ThreadSafeQueue<T>::tryDequeue(T& item) {
    return dequeue(item, 0);
}

template<typename T>
size_t ThreadSafeQueue<T>::size() const {
    if (use_lock_free_.load() && lock_free_queue_) {
        return lock_free_queue_->size();
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    return queue_.size();
}

template<typename T>
size_t ThreadSafeQueue<T>::capacity() const {
    return current_max_size_.load();
}

template<typename T>
bool ThreadSafeQueue<T>::empty() const {
    return size() == 0;
}

template<typename T>
bool ThreadSafeQueue<T>::full() const {
    return size() >= capacity();
}

template<typename T>
void ThreadSafeQueue<T>::clear() {
    if (use_lock_free_.load() && lock_free_queue_) {
        T item;
        while (lock_free_queue_->dequeue(item)) {
            // Items are automatically destroyed
        }
    } else {
        std::lock_guard<std::mutex> lock(mutex_);
        while (!queue_.empty()) {
            queue_.pop();
        }
    }
    
    // Clear DMABUF queue
    std::lock_guard<std::mutex> dmabuf_lock(dmabuf_mutex_);
    while (!dmabuf_queue_.empty()) {
        dmabuf_queue_.pop();
    }
    
    // Current size is automatically updated by size() method
}

template<typename T>
void ThreadSafeQueue<T>::setMaxSize(size_t max_size) {
    current_max_size_.store(max_size);
    config_.max_size = max_size;
}

template<typename T>
void ThreadSafeQueue<T>::enableAdaptiveSizing(bool enable) {
    config_.enable_adaptive_sizing = enable;
}

template<typename T>
void ThreadSafeQueue<T>::enableBackPressure(bool enable) {
    config_.enable_back_pressure = enable;
}

template<typename T>
void ThreadSafeQueue<T>::setBackPressureThreshold(size_t threshold) {
    config_.back_pressure_threshold = threshold;
}

template<typename T>
void ThreadSafeQueue<T>::stop() {
    should_stop_.store(true);
    condition_not_empty_.notify_all();
    condition_not_full_.notify_all();
}

template<typename T>
void ThreadSafeQueue<T>::resume() {
    should_stop_.store(false);
}

template<typename T>
bool ThreadSafeQueue<T>::enqueueDMABuf(std::shared_ptr<DMABufBuffer> buffer, const T& metadata) {
    if (!config_.enable_dmabuf || should_stop_.load()) {
        return false;
    }

    std::lock_guard<std::mutex> lock(dmabuf_mutex_);
    if (dmabuf_queue_.size() >= config_.dmabuf_pool_size) {
        return false;
    }

    dmabuf_queue_.push({buffer, metadata});
    return true;
}

template<typename T>
bool ThreadSafeQueue<T>::dequeueDMABuf(std::shared_ptr<DMABufBuffer>& buffer, T& metadata) {
    if (!config_.enable_dmabuf) {
        return false;
    }

    std::lock_guard<std::mutex> lock(dmabuf_mutex_);
    if (dmabuf_queue_.empty()) {
        return false;
    }

    auto entry = dmabuf_queue_.front();
    dmabuf_queue_.pop();
    buffer = entry.buffer;
    metadata = entry.metadata;
    return true;
}

template<typename T>
QueueStatistics ThreadSafeQueue<T>::getStatistics() const {
    QueueStatistics stats;
    stats.enqueue_count = enqueue_count_.load();
    stats.dequeue_count = dequeue_count_.load();
    stats.enqueue_failures = enqueue_failures_.load();
    stats.dequeue_timeouts = dequeue_timeouts_.load();
    stats.back_pressure_events = back_pressure_events_.load();
    stats.adaptive_resize_events = adaptive_resize_events_.load();
    stats.current_size = size();
    stats.peak_size = peak_size_.load();
    stats.total_memory_usage = total_memory_usage_.load();
    return stats;
}

template<typename T>
void ThreadSafeQueue<T>::resetStatistics() {
    enqueue_count_.store(0);
    dequeue_count_.store(0);
    enqueue_failures_.store(0);
    dequeue_timeouts_.store(0);
    back_pressure_events_.store(0);
    adaptive_resize_events_.store(0);
    peak_size_.store(0);
    total_memory_usage_.store(0);
}

template<typename T>
bool ThreadSafeQueue<T>::isBackPressureActive() const {
    return back_pressure_active_.load();
}

template<typename T>
double ThreadSafeQueue<T>::getUtilization() const {
    size_t current = size();
    size_t max = capacity();
    return max > 0 ? static_cast<double>(current) / static_cast<double>(max) : 0.0;
}

template<typename T>
void ThreadSafeQueue<T>::setLockFreeMode(bool enable) {
    if (enable && !lock_free_queue_) {
        lock_free_queue_ = std::make_unique<LockFreeQueue<T>>();
    }
    use_lock_free_.store(enable);
}

template<typename T>
bool ThreadSafeQueue<T>::isLockFreeMode() const {
    return use_lock_free_.load();
}

template<typename T>
void ThreadSafeQueue<T>::optimizeForLatency() {
    setLockFreeMode(true);
    config_.timeout_ms = 0;
    config_.enable_back_pressure = false;
}

template<typename T>
void ThreadSafeQueue<T>::optimizeForThroughput() {
    setLockFreeMode(false);
    config_.enable_back_pressure = true;
    config_.enable_adaptive_sizing = true;
}

template<typename T>
bool ThreadSafeQueue<T>::enqueueInternal(T&& item, int timeout_ms) {
    std::unique_lock<std::mutex> lock(mutex_);

    if (timeout_ms >= 0) {
        auto timeout = std::chrono::milliseconds(timeout_ms);
        if (!condition_not_full_.wait_for(lock, timeout,
            [this] { return queue_.size() < current_max_size_.load() || should_stop_.load(); })) {
            updateStatistics(false, true);
            return false;
        }
    } else {
        condition_not_full_.wait(lock,
            [this] { return queue_.size() < current_max_size_.load() || should_stop_.load(); });
    }

    if (should_stop_.load()) {
        updateStatistics(false, true);
        return false;
    }

    queue_.push(std::move(item));
    condition_not_empty_.notify_one();
    updateStatistics(true, true);

    if (shouldUseAdaptiveSizing()) {
        adaptiveResize();
    }

    checkAndHandleBackPressure();
    return true;
}

template<typename T>
bool ThreadSafeQueue<T>::dequeueInternal(T& item, int timeout_ms) {
    std::unique_lock<std::mutex> lock(mutex_);

    if (timeout_ms >= 0) {
        auto timeout = std::chrono::milliseconds(timeout_ms);
        if (!condition_not_empty_.wait_for(lock, timeout,
            [this] { return !queue_.empty() || should_stop_.load(); })) {
            updateStatistics(false, false);
            return false;
        }
    } else {
        condition_not_empty_.wait(lock,
            [this] { return !queue_.empty() || should_stop_.load(); });
    }

    if (should_stop_.load() && queue_.empty()) {
        updateStatistics(false, false);
        return false;
    }

    item = std::move(queue_.front());
    queue_.pop();
    condition_not_full_.notify_one();
    updateStatistics(true, false);
    return true;
}

template<typename T>
void ThreadSafeQueue<T>::updateStatistics(bool success, bool is_enqueue) {
    if (!config_.enable_statistics) {
        return;
    }

    if (is_enqueue) {
        if (success) {
            enqueue_count_++;
        } else {
            enqueue_failures_++;
        }
    } else {
        if (success) {
            dequeue_count_++;
        } else {
            dequeue_timeouts_++;
        }
    }

    size_t current = size();

    size_t peak = peak_size_.load();
    while (current > peak && !peak_size_.compare_exchange_weak(peak, current)) {
        peak = peak_size_.load();
    }
}

template<typename T>
void ThreadSafeQueue<T>::checkAndHandleBackPressure() {
    if (!config_.enable_back_pressure) {
        return;
    }

    size_t current = size();
    bool should_activate = current >= config_.back_pressure_threshold;
    bool was_active = back_pressure_active_.exchange(should_activate);

    if (should_activate && !was_active) {
        back_pressure_events_++;
    }
}

template<typename T>
void ThreadSafeQueue<T>::adaptiveResize() {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_resize_time_);

    if (elapsed.count() < 5) { // Don't resize too frequently
        return;
    }

    double utilization = getUtilization();
    size_t current_max = current_max_size_.load();

    if (utilization > 0.9 && current_max < config_.max_size) {
        // Increase size
        size_t new_size = std::min(current_max * config_.growth_factor, config_.max_size);
        current_max_size_.store(new_size);
        adaptive_resize_events_++;
        last_resize_time_ = now;
    } else if (utilization < 0.3 && current_max > config_.min_size) {
        // Decrease size
        size_t new_size = std::max(current_max / config_.growth_factor, config_.min_size);
        current_max_size_.store(new_size);
        adaptive_resize_events_++;
        last_resize_time_ = now;
    }
}

template<typename T>
size_t ThreadSafeQueue<T>::enqueueBatch(const std::vector<T>& items, int timeout_ms) {
    size_t enqueued = 0;
    for (const auto& item : items) {
        if (enqueue(item, timeout_ms)) {
            enqueued++;
        } else {
            break; // Stop on first failure
        }
    }
    return enqueued;
}

template<typename T>
size_t ThreadSafeQueue<T>::dequeueBatch(std::vector<T>& items, size_t max_count, int timeout_ms) {
    items.clear();
    items.reserve(max_count);

    size_t dequeued = 0;
    T item;
    for (size_t i = 0; i < max_count; ++i) {
        if (dequeue(item, timeout_ms)) {
            items.push_back(std::move(item));
            dequeued++;
        } else {
            break; // Stop on first failure
        }
    }
    return dequeued;
}

template<typename T>
bool ThreadSafeQueue<T>::shouldUseAdaptiveSizing() const {
    return config_.enable_adaptive_sizing &&
           enqueue_count_.load() > 1000; // Only after some activity
}

// Explicit template instantiations
template class LockFreeQueue<NALUnit>;
template class LockFreeQueue<RTPPacket>;
template class LockFreeQueue<int>;
template class ThreadSafeQueue<NALUnit>;
template class ThreadSafeQueue<RTPPacket>;
template class ThreadSafeQueue<int>;

} // namespace rtsp
} // namespace aibox
