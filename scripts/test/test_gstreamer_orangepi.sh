#!/bin/bash

# GStreamer Hardware Acceleration Test Script for Orange Pi 5 Plus
# Tests the key issues identified in issues.md

echo "=== Orange Pi 5 Plus GStreamer Hardware Acceleration Test ==="
echo "Date: $(date)"
echo "Hostname: $(hostname)"
echo "Kernel: $(uname -r)"
echo "Architecture: $(uname -m)"
echo ""

# Test 1: GStreamer Version and Basic Functionality
echo "=== Test 1: GStreamer Version and Basic Info ==="
gst-inspect-1.0 --version
echo ""

# Test 2: RockChip MPP Hardware Acceleration
echo "=== Test 2: RockChip MPP Hardware Acceleration ==="
echo "Available MPP plugins:"
gst-inspect-1.0 | grep -i mpp
echo ""

echo "MPP Video Decoder details:"
gst-inspect-1.0 mppvideodec 2>/dev/null || echo "mppvideodec not available"
echo ""

# Test 3: RTSP Support
echo "=== Test 3: RTSP Support ==="
echo "Available RTSP plugins:"
gst-inspect-1.0 | grep -i rtsp
echo ""

echo "RTSP Source details:"
gst-inspect-1.0 rtspsrc 2>/dev/null || echo "rtspsrc not available"
echo ""

# Test 4: RGA Hardware Scaling
echo "=== Test 4: RGA Hardware Scaling ==="
echo "RGA device status:"
ls -la /dev/rga* 2>/dev/null || echo "RGA device not found"
echo ""

echo "Available RGA plugins:"
gst-inspect-1.0 | grep -i rga || echo "No RGA plugins found"
echo ""

# Test 5: Video Conversion and Scaling
echo "=== Test 5: Video Conversion and Scaling ==="
echo "Available video converters:"
gst-inspect-1.0 | grep -E "(videoconvert|videoscale)"
echo ""

# Test 6: Hardware Decoder Test (Simple Pipeline)
echo "=== Test 6: Hardware Decoder Pipeline Test ==="
echo "Testing simple pipeline creation (no actual stream)..."

# Test pipeline creation without actual stream
echo "Testing MPP H.264 decoder pipeline:"
timeout 5 gst-launch-1.0 fakesrc num-buffers=1 ! \
    "video/x-h264,width=1920,height=1080,framerate=30/1" ! \
    mppvideodec ! fakesink 2>&1 | head -10 || echo "MPP decoder test failed"
echo ""

# Test 7: RTSP Pipeline Test (Mock)
echo "=== Test 7: RTSP Pipeline Test (Mock) ==="
echo "Testing RTSP pipeline creation (will fail due to no server, but tests plugin loading):"
timeout 10 gst-launch-1.0 rtspsrc location=rtsp://fake.server/stream ! \
    mppvideodec ! videoconvert ! fakesink 2>&1 | head -10 || echo "RTSP pipeline test completed (expected failure)"
echo ""

# Test 8: System Resources
echo "=== Test 8: System Resources ==="
echo "Memory usage:"
free -h
echo ""

echo "CPU info:"
lscpu | grep -E "(Architecture|CPU\(s\)|Model name|MHz)"
echo ""

echo "Temperature:"
cat /sys/class/thermal/thermal_zone*/temp 2>/dev/null | while read temp; do
    echo "Zone: $((temp/1000))°C"
done || echo "Temperature sensors not accessible"
echo ""

# Test 9: GStreamer Environment
echo "=== Test 9: GStreamer Environment ==="
echo "GST_PLUGIN_PATH: ${GST_PLUGIN_PATH:-not set}"
echo "GST_PLUGIN_SYSTEM_PATH: ${GST_PLUGIN_SYSTEM_PATH:-not set}"
echo ""

echo "Plugin directories:"
find /usr/lib -name "*gstreamer*" -type d 2>/dev/null | head -5
echo ""

# Test 10: Hardware Acceleration Capabilities Summary
echo "=== Test 10: Hardware Acceleration Summary ==="
echo "MPP Decoder: $(gst-inspect-1.0 mppvideodec >/dev/null 2>&1 && echo 'Available' || echo 'Not Available')"
echo "RGA Scaler: $(ls /dev/rga >/dev/null 2>&1 && echo 'Device Available' || echo 'Device Not Available')"
echo "RTSP Source: $(gst-inspect-1.0 rtspsrc >/dev/null 2>&1 && echo 'Available' || echo 'Not Available')"
echo "Video Convert: $(gst-inspect-1.0 videoconvert >/dev/null 2>&1 && echo 'Available' || echo 'Not Available')"
echo ""

echo "=== Test Complete ==="
echo "This test validates the key hardware acceleration issues identified in issues.md"
echo "Next steps: Test with real RTSP streams and measure performance"
