#!/bin/bash

# Test on Orange Pi 5 Plus Script
# Runs comprehensive tests on deployed applications

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
DEPLOY_DIR="/home/<USER>/c-aibox"
SSH_KEY=""
RUN_PERFORMANCE_TESTS=false
RUN_HARDWARE_TESTS=false
RUN_STRESS_TESTS=false
TEST_DURATION=60

# Load SSH configuration if available
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
    source "$PROJECT_ROOT/.orangepi_ssh"
    SSH_KEY="$ORANGE_PI_SSH_KEY"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --port)
            ORANGE_PI_PORT="$2"
            shift 2
            ;;
        --deploy-dir)
            DEPLOY_DIR="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --performance)
            RUN_PERFORMANCE_TESTS=true
            shift
            ;;
        --hardware)
            RUN_HARDWARE_TESTS=true
            shift
            ;;
        --stress)
            RUN_STRESS_TESTS=true
            shift
            ;;
        --duration)
            TEST_DURATION="$2"
            shift 2
            ;;
        --all)
            RUN_PERFORMANCE_TESTS=true
            RUN_HARDWARE_TESTS=true
            RUN_STRESS_TESTS=true
            shift
            ;;
        --help)
            cat << EOF
Test on Orange Pi 5 Plus Script

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP         Orange Pi IP address

OPTIONS:
    --user USER     SSH username (default: orangepi)
    --port PORT     SSH port (default: 22)
    --deploy-dir DIR Remote deployment directory (default: /home/<USER>/c-aibox)
    --ssh-key PATH  SSH private key file
    --performance   Run performance benchmarks
    --hardware      Run hardware acceleration tests
    --stress        Run stress tests
    --duration SEC  Test duration in seconds (default: 60)
    --all           Run all test types
    --help          Show this help

EXAMPLES:
    $0 --ip *************                    # Basic functionality tests
    $0 --ip ************* --all              # All tests
    $0 --ip ************* --performance      # Performance benchmarks only
    $0 --ip ************* --hardware         # Hardware acceleration tests

TEST TYPES:
    Basic:       Application startup, basic functionality
    Performance: CPU, memory, GPU utilization benchmarks
    Hardware:    RK3588 MPP, RGA, GPU acceleration tests
    Stress:      Long-running stability tests
EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    log_info "Usage: $0 --ip IP_ADDRESS"
    exit 1
fi

# SSH command builder
build_ssh_cmd() {
    local ssh_cmd="ssh"
    
    if [[ -n "$SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY"
    fi
    
    ssh_cmd="$ssh_cmd -p $ORANGE_PI_PORT $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "$ssh_cmd"
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
        exit 1
    fi
}

# Check deployment
check_deployment() {
    log_step "Checking Deployment"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Check deployment directory
    if ! $ssh_cmd "test -d $DEPLOY_DIR"; then
        log_error "Deployment directory not found: $DEPLOY_DIR"
        log_info "Run deployment first: ./scripts/deploy/deploy-to-orangepi.sh --ip $ORANGE_PI_IP"
        exit 1
    fi
    
    # Check executables
    local executables=($($ssh_cmd "cd $DEPLOY_DIR && find . -type f -executable -name '*' | grep -E '(client_app|server)'" 2>/dev/null || echo ""))
    if [[ ${#executables[@]} -eq 0 ]]; then
        log_error "No executables found in deployment directory"
        exit 1
    fi
    
    log_success "Found ${#executables[@]} executable(s) in deployment"
}

# System information
get_system_info() {
    log_step "Orange Pi System Information"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Hardware information:"
    $ssh_cmd "cat /proc/cpuinfo | grep -E '(processor|model name|cpu cores)' | head -10"
    
    log_info "Memory information:"
    $ssh_cmd "free -h"
    
    log_info "Storage information:"
    $ssh_cmd "df -h | grep -E '(Filesystem|/dev/mmcblk|/dev/nvme)'"
    
    log_info "GPU information:"
    $ssh_cmd "ls -la /dev/dri/ 2>/dev/null || echo 'DRI devices not found'"
    
    log_info "OS information:"
    $ssh_cmd "cat /etc/os-release | grep PRETTY_NAME"
    $ssh_cmd "uname -r"
}

# Basic functionality tests
run_basic_tests() {
    log_step "Running Basic Functionality Tests"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Test server startup
    log_info "Testing server startup..."
    if $ssh_cmd "cd $DEPLOY_DIR && timeout 10 ./server --version" 2>/dev/null; then
        log_success "Server startup test passed"
    else
        log_error "Server startup test failed"
    fi
    
    # Test client startup
    log_info "Testing client startup..."
    if $ssh_cmd "cd $DEPLOY_DIR && timeout 10 ./client_app --version" 2>/dev/null; then
        log_success "Client startup test passed"
    else
        log_warning "Client startup test failed (may require display)"
    fi
    
    # Test library dependencies
    log_info "Testing library dependencies..."
    if $ssh_cmd "cd $DEPLOY_DIR && ldd ./server | grep -q 'not found'"; then
        log_error "Missing library dependencies found"
        $ssh_cmd "cd $DEPLOY_DIR && ldd ./server | grep 'not found'" || true
    else
        log_success "All library dependencies satisfied"
    fi
}

# Performance tests
run_performance_tests() {
    if [[ "$RUN_PERFORMANCE_TESTS" != "true" ]]; then
        return
    fi
    
    log_step "Running Performance Tests"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # CPU performance test
    log_info "Testing CPU performance..."
    $ssh_cmd "cd $DEPLOY_DIR && timeout $TEST_DURATION stress-ng --cpu 8 --metrics-brief 2>/dev/null || echo 'stress-ng not available'"
    
    # Memory performance test
    log_info "Testing memory performance..."
    $ssh_cmd "cd $DEPLOY_DIR && timeout 30 stress-ng --vm 2 --vm-bytes 1G --metrics-brief 2>/dev/null || echo 'Memory stress test skipped'"
    
    # Application performance test
    log_info "Testing application performance..."
    $ssh_cmd "cd $DEPLOY_DIR && timeout $TEST_DURATION ./server --benchmark 2>/dev/null || echo 'Application benchmark not available'"
    
    # System resource monitoring
    log_info "System resource usage during tests:"
    $ssh_cmd "top -bn1 | head -20"
}

# Hardware acceleration tests
run_hardware_tests() {
    if [[ "$RUN_HARDWARE_TESTS" != "true" ]]; then
        return
    fi
    
    log_step "Running Hardware Acceleration Tests"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Test GPU acceleration
    log_info "Testing GPU acceleration..."
    if $ssh_cmd "command -v glxinfo >/dev/null 2>&1"; then
        $ssh_cmd "glxinfo | grep -E '(OpenGL renderer|OpenGL version)'" || true
    else
        log_warning "glxinfo not available for GPU testing"
    fi
    
    # Test GStreamer hardware plugins
    log_info "Testing GStreamer hardware plugins..."
    $ssh_cmd "gst-inspect-1.0 | grep -i rockchip || echo 'RockChip plugins not found'"
    $ssh_cmd "gst-inspect-1.0 | grep -i mpp || echo 'MPP plugins not found'"
    
    # Test video decoding
    log_info "Testing hardware video decoding..."
    $ssh_cmd "cd $DEPLOY_DIR && timeout 30 ./server --test-decode 2>/dev/null || echo 'Hardware decode test not available'"
    
    # Test RGA scaling
    log_info "Testing RGA hardware scaling..."
    $ssh_cmd "ls -la /dev/rga 2>/dev/null && echo 'RGA device found' || echo 'RGA device not found'"
}

# Stress tests
run_stress_tests() {
    if [[ "$RUN_STRESS_TESTS" != "true" ]]; then
        return
    fi
    
    log_step "Running Stress Tests"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Running stress tests for $TEST_DURATION seconds..."
    log_warning "This may impact system performance"
    
    # CPU + Memory stress
    log_info "CPU and memory stress test..."
    $ssh_cmd "timeout $TEST_DURATION stress-ng --cpu 4 --vm 2 --vm-bytes 512M --metrics-brief 2>/dev/null || echo 'Stress test completed'"
    
    # Application stress test
    log_info "Application stress test..."
    $ssh_cmd "cd $DEPLOY_DIR && timeout $TEST_DURATION ./server --stress-test 2>/dev/null || echo 'Application stress test not available'"
    
    # Monitor system during stress
    log_info "System status after stress test:"
    $ssh_cmd "free -h && uptime"
}

# Network tests
run_network_tests() {
    log_step "Running Network Tests"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Test network connectivity
    log_info "Testing network connectivity..."
    if $ssh_cmd "ping -c 3 8.8.8.8 >/dev/null 2>&1"; then
        log_success "Internet connectivity working"
    else
        log_warning "Internet connectivity issues"
    fi
    
    # Test RTSP streaming (if available)
    log_info "Testing RTSP capabilities..."
    $ssh_cmd "cd $DEPLOY_DIR && timeout 30 ./server --test-rtsp 2>/dev/null || echo 'RTSP test not available'"
}

# Generate test report
generate_report() {
    log_step "Generating Test Report"
    
    local ssh_cmd=$(build_ssh_cmd)
    local report_file="orangepi-test-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
Orange Pi 5 Plus Test Report
===========================
Date: $(date)
Orange Pi: $ORANGE_PI_USER@$ORANGE_PI_IP
Deployment Directory: $DEPLOY_DIR

System Information:
$($ssh_cmd "uname -a")
$($ssh_cmd "cat /etc/os-release | grep PRETTY_NAME")
$($ssh_cmd "free -h")

Test Results:
- Basic Tests: $(if [[ -f /tmp/basic_tests_passed ]]; then echo "PASSED"; else echo "See output above"; fi)
- Performance Tests: $(if [[ "$RUN_PERFORMANCE_TESTS" == "true" ]]; then echo "COMPLETED"; else echo "SKIPPED"; fi)
- Hardware Tests: $(if [[ "$RUN_HARDWARE_TESTS" == "true" ]]; then echo "COMPLETED"; else echo "SKIPPED"; fi)
- Stress Tests: $(if [[ "$RUN_STRESS_TESTS" == "true" ]]; then echo "COMPLETED"; else echo "SKIPPED"; fi)

Application Status:
$($ssh_cmd "cd $DEPLOY_DIR && ls -la")

Dependencies:
$($ssh_cmd "cd $DEPLOY_DIR && ldd ./server 2>/dev/null | head -10 || echo 'ldd not available'")

EOF
    
    log_success "Test report generated: $report_file"
}

# Show test summary
show_summary() {
    log_step "Test Summary"
    
    echo "Orange Pi: $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "Deployment Directory: $DEPLOY_DIR"
    echo "Test Duration: $TEST_DURATION seconds"
    echo ""
    echo "Tests Run:"
    echo "  Basic Functionality: ✓"
    echo "  Performance Tests: $(if [[ "$RUN_PERFORMANCE_TESTS" == "true" ]]; then echo "✓"; else echo "✗"; fi)"
    echo "  Hardware Tests: $(if [[ "$RUN_HARDWARE_TESTS" == "true" ]]; then echo "✓"; else echo "✗"; fi)"
    echo "  Stress Tests: $(if [[ "$RUN_STRESS_TESTS" == "true" ]]; then echo "✓"; else echo "✗"; fi)"
    echo ""
    
    log_success "Orange Pi testing completed!"
    log_info "Check the test report for detailed results"
}

# Main execution
main() {
    log_step "Orange Pi 5 Plus Testing"
    
    test_ssh_connection
    check_deployment
    get_system_info
    run_basic_tests
    run_performance_tests
    run_hardware_tests
    run_stress_tests
    run_network_tests
    generate_report
    show_summary
}

# Run main function
main "$@"
