#include <gtest/gtest.h>
#include "rtsp/packet_receiver.hpp"
#include <thread>
#include <chrono>
#include <vector>
#include <random>

using namespace aibox::rtsp;

class PacketReceiverTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration
        config_.rtsp_url = "rtsp://test.example.com:554/stream";
        config_.username = "testuser";
        config_.password = "testpass";
        config_.timeout_ms = 5000;
        config_.retry_count = 3;
        config_.transport = TransportProtocol::TCP;
        config_.use_mpp_decoder = true;
        config_.use_rga_scaler = true;
        config_.use_dmabuf_zerocopy = true;
        config_.buffer_size_bytes = 1024 * 1024;  // 1MB
        config_.queue_size = 100;
        config_.priority = StreamPriority::HIGH;
        
        // Ensure configuration is valid
        ASSERT_TRUE(config_.isValid()) << "Test configuration should be valid";
    }
    
    void TearDown() override {
        // Cleanup any resources
    }
    
    RTSPConnectionConfig config_;
    
    // Helper function to create a valid RTP packet
    std::vector<uint8_t> createRTPPacket(uint16_t sequence_number, uint32_t timestamp, 
                                        uint8_t payload_type = 96, bool marker = false) {
        std::vector<uint8_t> packet;
        
        // RTP header (12 bytes minimum)
        packet.resize(12);
        
        // Version (2), Padding (0), Extension (0), CSRC count (0)
        packet[0] = 0x80;  // Version 2, no padding, no extension, no CSRC
        
        // Marker bit and payload type
        packet[1] = (marker ? 0x80 : 0x00) | (payload_type & 0x7F);
        
        // Sequence number (big endian)
        packet[2] = static_cast<uint8_t>((sequence_number >> 8) & 0xFF);
        packet[3] = static_cast<uint8_t>(sequence_number & 0xFF);

        // Timestamp (big endian)
        packet[4] = static_cast<uint8_t>((timestamp >> 24) & 0xFF);
        packet[5] = static_cast<uint8_t>((timestamp >> 16) & 0xFF);
        packet[6] = static_cast<uint8_t>((timestamp >> 8) & 0xFF);
        packet[7] = static_cast<uint8_t>(timestamp & 0xFF);

        // SSRC (big endian) - use fixed value for testing
        uint32_t ssrc = 0x12345678;
        packet[8] = static_cast<uint8_t>((ssrc >> 24) & 0xFF);
        packet[9] = static_cast<uint8_t>((ssrc >> 16) & 0xFF);
        packet[10] = static_cast<uint8_t>((ssrc >> 8) & 0xFF);
        packet[11] = static_cast<uint8_t>(ssrc & 0xFF);
        
        // Add some dummy payload
        std::vector<uint8_t> payload = {0x00, 0x00, 0x00, 0x01, 0x67, 0x42, 0x80, 0x1E}; // H.264 SPS start
        packet.insert(packet.end(), payload.begin(), payload.end());
        
        return packet;
    }
    
    // Helper function to create RTCP packet
    std::vector<uint8_t> createRTCPPacket(RTCPPacketType type) {
        std::vector<uint8_t> packet;
        packet.resize(8);  // Minimum RTCP header
        
        // Version (2), Padding (0), RC (0)
        packet[0] = 0x80;
        
        // Packet type
        packet[1] = static_cast<uint8_t>(type);
        
        // Length (in 32-bit words minus 1)
        packet[2] = 0x00;
        packet[3] = 0x01;  // 2 words total (8 bytes)
        
        // SSRC
        uint32_t ssrc = 0x12345678;
        packet[4] = static_cast<uint8_t>((ssrc >> 24) & 0xFF);
        packet[5] = static_cast<uint8_t>((ssrc >> 16) & 0xFF);
        packet[6] = static_cast<uint8_t>((ssrc >> 8) & 0xFF);
        packet[7] = static_cast<uint8_t>(ssrc & 0xFF);
        
        return packet;
    }
};

// Test basic construction and destruction
TEST_F(PacketReceiverTest, ConstructionDestruction) {
    EXPECT_NO_THROW({
        PacketReceiver receiver(config_);
    });
}

// Test configuration validation
TEST_F(PacketReceiverTest, ConfigurationValidation) {
    // Valid configuration should work
    EXPECT_NO_THROW({
        PacketReceiver receiver(config_);
    });
    
    // Invalid configuration should throw
    RTSPConnectionConfig invalid_config;
    invalid_config.rtsp_url = "";  // Empty URL is invalid
    
    EXPECT_THROW({
        PacketReceiver receiver(invalid_config);
    }, std::invalid_argument);
}

// Test transport mode configuration
TEST_F(PacketReceiverTest, TransportModeConfiguration) {
    PacketReceiver receiver(config_);
    
    // Test initial transport mode
    EXPECT_EQ(receiver.getTransportMode(), TransportProtocol::TCP);
    
    // Test changing transport mode
    receiver.setTransportMode(TransportProtocol::UDP);
    EXPECT_EQ(receiver.getTransportMode(), TransportProtocol::UDP);
    
    receiver.setTransportMode(TransportProtocol::TCP);
    EXPECT_EQ(receiver.getTransportMode(), TransportProtocol::TCP);
}

// Test hardware acceleration control
TEST_F(PacketReceiverTest, HardwareAccelerationControl) {
    PacketReceiver receiver(config_);
    
    // Test RGA scaling control
    EXPECT_NO_THROW({
        bool result = receiver.enableRGAScaling(true);
        (void)result; // Suppress unused variable warning
        // Result depends on hardware availability, but should not throw
    });

    // Test DMABUF zero-copy control
    EXPECT_NO_THROW({
        bool result = receiver.enableDMABufZeroCopy(true);
        (void)result; // Suppress unused variable warning
        // Result depends on hardware availability, but should not throw
    });
    
    // Test status queries
    HardwareAccelStatus rga_status = receiver.getRGAStatus();
    EXPECT_TRUE(rga_status == HardwareAccelStatus::DISABLED ||
                rga_status == HardwareAccelStatus::HARDWARE_ACTIVE ||
                rga_status == HardwareAccelStatus::SOFTWARE_FALLBACK ||
                rga_status == HardwareAccelStatus::HARDWARE_ERROR);
}

// Test statistics functionality
TEST_F(PacketReceiverTest, StatisticsFunctionality) {
    PacketReceiver receiver(config_);
    
    // Get initial statistics
    PacketStatistics stats = receiver.getStatistics();
    
    // Initial statistics should be zero
    EXPECT_EQ(stats.packets_received, 0);
    EXPECT_EQ(stats.packets_lost, 0);
    EXPECT_EQ(stats.bytes_received, 0);
    EXPECT_EQ(stats.duplicate_packets, 0);
    EXPECT_EQ(stats.out_of_order_packets, 0);
    
    // Test packet loss rate calculation
    EXPECT_EQ(stats.getPacketLossRate(), 0.0);
}

// Test quality control settings
TEST_F(PacketReceiverTest, QualityControlSettings) {
    PacketReceiver receiver(config_);
    
    // Test setting quality thresholds
    EXPECT_NO_THROW({
        receiver.setQualityThresholds(0.05f, 200);
    });
    
    // Test adaptive quality control
    EXPECT_NO_THROW({
        receiver.enableAdaptiveQuality(true);
        EXPECT_TRUE(receiver.isAdaptiveQualityEnabled());
        
        receiver.enableAdaptiveQuality(false);
        EXPECT_FALSE(receiver.isAdaptiveQualityEnabled());
    });
}

// Test thermal management
TEST_F(PacketReceiverTest, ThermalManagement) {
    PacketReceiver receiver(config_);
    
    // Test thermal throttling handling
    EXPECT_NO_THROW({
        receiver.handleThermalThrottling(75);  // Normal temperature
        receiver.handleThermalThrottling(85);  // High temperature
    });
    
    // Test performance mode setting
    EXPECT_NO_THROW({
        receiver.setPerformanceMode(true);
        receiver.setPerformanceMode(false);
    });
}

// Test callback functionality
TEST_F(PacketReceiverTest, CallbackFunctionality) {
    PacketReceiver receiver(config_);
    
    bool packet_callback_called = false;
    bool nal_callback_called = false;
    bool error_callback_called = false;
    
    // Set packet callback
    receiver.setPacketCallback([&](const std::vector<uint8_t>& packet, Timestamp timestamp) {
        (void)timestamp; // Suppress unused parameter warning
        packet_callback_called = true;
        EXPECT_FALSE(packet.empty());
    });
    
    // Set NAL unit callback
    receiver.setNALUnitCallback([&](const NALUnit& nal_unit) {
        nal_callback_called = true;
        EXPECT_FALSE(nal_unit.empty());
    });
    
    // Set error callback
    receiver.setErrorCallback([&](const StreamId& stream_id, ErrorCategory category, const std::string& message) {
        error_callback_called = true;
        EXPECT_FALSE(message.empty());
        EXPECT_FALSE(stream_id.empty());
    });
    
    // Callbacks should be set without throwing
    EXPECT_NO_THROW({
        // The callbacks are now set and will be called when appropriate events occur
    });
}

// Test memory usage monitoring
TEST_F(PacketReceiverTest, MemoryUsageMonitoring) {
    PacketReceiver receiver(config_);
    
    // Get memory usage
    size_t memory_usage = receiver.getMemoryUsage();
    
    // Memory usage should be reasonable (not zero, not excessive)
    EXPECT_GT(memory_usage, 0);
    EXPECT_LT(memory_usage, 100 * 1024 * 1024);  // Less than 100MB for empty receiver
}

// Test jitter buffer configuration
TEST_F(PacketReceiverTest, JitterBufferConfiguration) {
    PacketReceiver receiver(config_);
    
    // Create jitter buffer configuration
    JitterBufferConfig jitter_config;
    jitter_config.initial_size_ms = 150;
    jitter_config.max_size_ms = 800;
    jitter_config.min_size_ms = 50;
    jitter_config.adaptive_sizing = true;
    jitter_config.target_latency_ms = 100;
    
    // Configure jitter buffer
    EXPECT_NO_THROW({
        receiver.configureJitterBuffer(jitter_config);
    });
    
    // Get configuration back
    const JitterBufferConfig& retrieved_config = receiver.getJitterBufferConfig();
    EXPECT_EQ(retrieved_config.initial_size_ms, jitter_config.initial_size_ms);
    EXPECT_EQ(retrieved_config.max_size_ms, jitter_config.max_size_ms);
    EXPECT_EQ(retrieved_config.adaptive_sizing, jitter_config.adaptive_sizing);
}

// Test RTP packet parsing
TEST_F(PacketReceiverTest, RTPPacketParsing) {
    PacketReceiver receiver(config_);

    // Create test RTP packet
    auto rtp_packet = createRTPPacket(1234, 567890, 96, true);

    // Test that packet processing doesn't crash
    EXPECT_NO_THROW({
        // We can't directly test processRTPPacket as it's private,
        // but we can test the overall packet handling through callbacks
        bool callback_called = false;
        receiver.setPacketCallback([&](const std::vector<uint8_t>& packet, Timestamp timestamp) {
            (void)timestamp; // Suppress unused parameter warning
            callback_called = true;
            EXPECT_FALSE(packet.empty());
        });

        // The actual packet processing would happen in the receiver worker thread
        // For unit testing, we focus on the public interface
    });
}

// Test RTCP packet handling
TEST_F(PacketReceiverTest, RTCPPacketHandling) {
    PacketReceiver receiver(config_);

    // Create test RTCP packets
    auto sender_report = createRTCPPacket(RTCPPacketType::SENDER_REPORT);
    auto receiver_report = createRTCPPacket(RTCPPacketType::RECEIVER_REPORT);

    // Test that RTCP processing doesn't crash
    EXPECT_NO_THROW({
        // RTCP processing is internal, but we can verify the receiver handles it
        // by checking that statistics are updated appropriately
        PacketStatistics initial_stats = receiver.getStatistics();
        EXPECT_EQ(initial_stats.rtcp_packets_received, 0);
    });
}

// Test duplicate packet detection
TEST_F(PacketReceiverTest, DuplicatePacketDetection) {
    PacketReceiver receiver(config_);

    // Test with same sequence numbers
    auto packet1 = createRTPPacket(100, 1000);
    auto packet2 = createRTPPacket(100, 1000);  // Same sequence number

    // The duplicate detection logic is internal, but we can test
    // that the receiver handles duplicate packets gracefully
    EXPECT_NO_THROW({
        // Duplicate detection would be tested through statistics
        PacketStatistics stats = receiver.getStatistics();
        EXPECT_EQ(stats.duplicate_packets, 0);  // Initially zero
    });
}

// Test out-of-order packet handling
TEST_F(PacketReceiverTest, OutOfOrderPacketHandling) {
    PacketReceiver receiver(config_);

    // Create packets with out-of-order sequence numbers
    auto packet1 = createRTPPacket(102, 1000);  // Later packet first
    auto packet2 = createRTPPacket(101, 900);   // Earlier packet second

    EXPECT_NO_THROW({
        // Out-of-order detection would be reflected in statistics
        PacketStatistics stats = receiver.getStatistics();
        EXPECT_EQ(stats.out_of_order_packets, 0);  // Initially zero
    });
}

// Test hardware acceleration fallback
TEST_F(PacketReceiverTest, HardwareAccelerationFallback) {
    PacketReceiver receiver(config_);

    // Test that hardware acceleration can be disabled and enabled
    EXPECT_NO_THROW({
        receiver.enableRGAScaling(false);
        receiver.enableDMABufZeroCopy(false);

        // Should fall back to software processing
        HardwareAccelStatus rga_status = receiver.getRGAStatus();
        bool dmabuf_enabled = receiver.isDMABufEnabled();

        // Status should reflect the disabled state
        EXPECT_TRUE(rga_status == HardwareAccelStatus::DISABLED ||
                    rga_status == HardwareAccelStatus::SOFTWARE_FALLBACK);
        EXPECT_FALSE(dmabuf_enabled);
    });
}

// Test error handling
TEST_F(PacketReceiverTest, ErrorHandling) {
    PacketReceiver receiver(config_);

    bool error_callback_called = false;
    std::string error_message;
    ErrorCategory error_category;

    receiver.setErrorCallback([&](const StreamId& stream_id, ErrorCategory category, const std::string& message) {
        error_callback_called = true;
        error_category = category;
        error_message = message;
        EXPECT_FALSE(stream_id.empty());
    });

    // Test that error handling doesn't crash
    EXPECT_NO_THROW({
        // Error conditions would be triggered by invalid packets or hardware failures
        // For unit testing, we verify the error callback mechanism works
    });
}

// Test performance under load
TEST_F(PacketReceiverTest, PerformanceUnderLoad) {
    PacketReceiver receiver(config_);

    // Test that the receiver can handle rapid configuration changes
    EXPECT_NO_THROW({
        for (int i = 0; i < 100; ++i) {
            receiver.setQualityThresholds(0.01f * static_cast<float>(i), static_cast<uint32_t>(100 + i));
            receiver.enableAdaptiveQuality(i % 2 == 0);

            // Get statistics to ensure internal state is consistent
            PacketStatistics stats = receiver.getStatistics();
            EXPECT_GE(stats.packets_received, 0);
        }
    });
}

// Test concurrent access safety
TEST_F(PacketReceiverTest, ConcurrentAccessSafety) {
    PacketReceiver receiver(config_);

    std::atomic<bool> stop_threads{false};
    std::vector<std::thread> threads;

    // Start multiple threads accessing the receiver
    for (int i = 0; i < 4; ++i) {
        threads.emplace_back([&receiver, &stop_threads, i]() {
            while (!stop_threads) {
                switch (i % 4) {
                    case 0:
                        receiver.getStatistics();
                        break;
                    case 1:
                        receiver.getMemoryUsage();
                        break;
                    case 2:
                        receiver.getCurrentJitterMs();
                        break;
                    case 3:
                        receiver.getQueueDepth();
                        break;
                }
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        });
    }

    // Let threads run for a short time
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // Stop threads
    stop_threads = true;
    for (auto& thread : threads) {
        thread.join();
    }

    // Verify receiver is still functional
    EXPECT_NO_THROW({
        PacketStatistics stats = receiver.getStatistics();
        EXPECT_GE(stats.packets_received, 0);
    });
}

// Test NAL unit extraction
TEST_F(PacketReceiverTest, NALUnitExtraction) {
    PacketReceiver receiver(config_);

    bool nal_callback_called = false;
    NALUnitType detected_type = NALUnitType::UNKNOWN;

    receiver.setNALUnitCallback([&](const NALUnit& nal_unit) {
        nal_callback_called = true;
        detected_type = nal_unit.type;
        EXPECT_FALSE(nal_unit.empty());
        EXPECT_GT(nal_unit.size(), 0);
    });

    // Create H.264 packet with SPS NAL unit
    auto h264_packet = createRTPPacket(1, 1000, 96);  // H.264 payload type

    EXPECT_NO_THROW({
        // NAL unit extraction would happen during packet processing
        // The callback mechanism is tested here
    });
}

// Test configuration update
TEST_F(PacketReceiverTest, ConfigurationUpdate) {
    PacketReceiver receiver(config_);

    // Create new configuration
    RTSPConnectionConfig new_config = config_;
    new_config.timeout_ms = 10000;
    new_config.retry_count = 5;
    new_config.transport = TransportProtocol::UDP;

    EXPECT_NO_THROW({
        receiver.updateConfig(new_config);
    });

    // Verify transport mode was updated
    EXPECT_EQ(receiver.getTransportMode(), TransportProtocol::UDP);
}
