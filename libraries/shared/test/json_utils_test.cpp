#include <gtest/gtest.h>
#include <utils/json_utils.hpp>

class JsonUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up test fixtures if needed
    }

    void TearDown() override {
        // Clean up after tests
    }
};

TEST_F(JsonUtilsTest, JsonValueConstruction) {
    // Test null value
    utils::JsonValue nullVal;
    EXPECT_TRUE(nullVal.isNull());
    EXPECT_EQ(nullVal.getType(), utils::JsonType::Null);

    // Test boolean values
    utils::JsonValue trueVal(true);
    utils::JsonValue falseVal(false);
    EXPECT_TRUE(trueVal.isBool());
    EXPECT_TRUE(falseVal.isBool());
    EXPECT_TRUE(trueVal.asBool());
    EXPECT_FALSE(falseVal.asBool());

    // Test numeric values
    utils::JsonValue intVal(42);
    utils::JsonValue doubleVal(3.14);
    EXPECT_TRUE(intVal.isNumber());
    EXPECT_TRUE(doubleVal.isNumber());
    EXPECT_EQ(intVal.asInt(), 42);
    EXPECT_DOUBLE_EQ(doubleVal.asNumber(), 3.14);

    // Test string values
    utils::JsonValue stringVal("hello");
    utils::JsonValue cStringVal("world");
    EXPECT_TRUE(stringVal.isString());
    EXPECT_TRUE(cStringVal.isString());
    EXPECT_EQ(stringVal.asString(), "hello");
    EXPECT_EQ(cStringVal.asString(), "world");

    // Test array values
    utils::JsonArray arr = {utils::JsonValue(1), utils::JsonValue(2), utils::JsonValue(3)};
    utils::JsonValue arrayVal(arr);
    EXPECT_TRUE(arrayVal.isArray());
    EXPECT_EQ(arrayVal.size(), 3);
    EXPECT_EQ(arrayVal[0].asInt(), 1);
    EXPECT_EQ(arrayVal[1].asInt(), 2);
    EXPECT_EQ(arrayVal[2].asInt(), 3);

    // Test object values
    utils::JsonObject obj = {{"name", utils::JsonValue("John")}, {"age", utils::JsonValue(30)}};
    utils::JsonValue objectVal(obj);
    EXPECT_TRUE(objectVal.isObject());
    EXPECT_EQ(objectVal.size(), 2);
    EXPECT_TRUE(objectVal.hasKey("name"));
    EXPECT_TRUE(objectVal.hasKey("age"));
    EXPECT_EQ(objectVal["name"].asString(), "John");
    EXPECT_EQ(objectVal["age"].asInt(), 30);
}

TEST_F(JsonUtilsTest, JsonValueOperations) {
    // Test object operations
    utils::JsonValue obj = utils::createObject();
    obj["name"] = utils::JsonValue("Alice");
    obj["age"] = utils::JsonValue(25);
    obj["active"] = utils::JsonValue(true);

    EXPECT_TRUE(obj.isObject());
    EXPECT_EQ(obj.size(), 3);
    EXPECT_TRUE(obj.hasKey("name"));
    EXPECT_FALSE(obj.hasKey("email"));

    auto keys = obj.getKeys();
    EXPECT_EQ(keys.size(), 3);
    EXPECT_TRUE(std::find(keys.begin(), keys.end(), "name") != keys.end());
    EXPECT_TRUE(std::find(keys.begin(), keys.end(), "age") != keys.end());
    EXPECT_TRUE(std::find(keys.begin(), keys.end(), "active") != keys.end());

    // Test array operations
    utils::JsonValue arr = utils::createArray();
    arr.asArray().push_back(utils::JsonValue("first"));
    arr.asArray().push_back(utils::JsonValue("second"));
    arr.asArray().push_back(utils::JsonValue("third"));

    EXPECT_TRUE(arr.isArray());
    EXPECT_EQ(arr.size(), 3);
    EXPECT_EQ(arr[0].asString(), "first");
    EXPECT_EQ(arr[1].asString(), "second");
    EXPECT_EQ(arr[2].asString(), "third");
}

TEST_F(JsonUtilsTest, JsonParsing) {
    // Test parsing simple values
    auto nullResult = utils::JsonUtils::parse("null");
    EXPECT_TRUE(nullResult.has_value());
    EXPECT_TRUE(nullResult->isNull());

    auto boolResult = utils::JsonUtils::parse("true");
    EXPECT_TRUE(boolResult.has_value());
    EXPECT_TRUE(boolResult->isBool());
    EXPECT_TRUE(boolResult->asBool());

    auto numberResult = utils::JsonUtils::parse("42.5");
    EXPECT_TRUE(numberResult.has_value());
    EXPECT_TRUE(numberResult->isNumber());
    EXPECT_DOUBLE_EQ(numberResult->asNumber(), 42.5);

    auto stringResult = utils::JsonUtils::parse("\"hello world\"");
    EXPECT_TRUE(stringResult.has_value());
    EXPECT_TRUE(stringResult->isString());
    EXPECT_EQ(stringResult->asString(), "hello world");

    // Test convenience function
    auto convResult = utils::parseJson("123");
    EXPECT_TRUE(convResult.has_value());
    EXPECT_EQ(convResult->asInt(), 123);
}

TEST_F(JsonUtilsTest, JsonArrayParsing) {
    std::string arrayJson = "[1, 2, 3, \"hello\", true, null]";
    auto result = utils::JsonUtils::parse(arrayJson);

    EXPECT_TRUE(result.has_value());
    EXPECT_TRUE(result->isArray());
    EXPECT_EQ(result->size(), 6);

    EXPECT_EQ((*result)[0].asInt(), 1);
    EXPECT_EQ((*result)[1].asInt(), 2);
    EXPECT_EQ((*result)[2].asInt(), 3);
    EXPECT_EQ((*result)[3].asString(), "hello");
    EXPECT_TRUE((*result)[4].asBool());
    EXPECT_TRUE((*result)[5].isNull());

    // Test empty array
    auto emptyResult = utils::JsonUtils::parse("[]");
    EXPECT_TRUE(emptyResult.has_value());
    EXPECT_TRUE(emptyResult->isArray());
    EXPECT_EQ(emptyResult->size(), 0);
}

TEST_F(JsonUtilsTest, JsonObjectParsing) {
    std::string objectJson = R"({
        "name": "John Doe",
        "age": 30,
        "active": true,
        "salary": 50000.5,
        "address": null
    })";

    auto result = utils::JsonUtils::parse(objectJson);

    EXPECT_TRUE(result.has_value());
    EXPECT_TRUE(result->isObject());
    EXPECT_EQ(result->size(), 5);

    EXPECT_TRUE(result->hasKey("name"));
    EXPECT_EQ((*result)["name"].asString(), "John Doe");
    EXPECT_EQ((*result)["age"].asInt(), 30);
    EXPECT_TRUE((*result)["active"].asBool());
    EXPECT_DOUBLE_EQ((*result)["salary"].asNumber(), 50000.5);
    EXPECT_TRUE((*result)["address"].isNull());

    // Test empty object
    auto emptyResult = utils::JsonUtils::parse("{}");
    EXPECT_TRUE(emptyResult.has_value());
    EXPECT_TRUE(emptyResult->isObject());
    EXPECT_EQ(emptyResult->size(), 0);
}

TEST_F(JsonUtilsTest, NestedJsonParsing) {
    std::string nestedJson = R"({
        "user": {
            "name": "Alice",
            "details": {
                "age": 25,
                "email": "<EMAIL>"
            }
        },
        "items": [
            {"id": 1, "name": "Item 1"},
            {"id": 2, "name": "Item 2"}
        ]
    })";

    auto result = utils::JsonUtils::parse(nestedJson);

    EXPECT_TRUE(result.has_value());
    EXPECT_TRUE(result->isObject());

    // Test nested object access
    EXPECT_TRUE((*result)["user"].isObject());
    EXPECT_EQ((*result)["user"]["name"].asString(), "Alice");
    EXPECT_TRUE((*result)["user"]["details"].isObject());
    EXPECT_EQ((*result)["user"]["details"]["age"].asInt(), 25);
    EXPECT_EQ((*result)["user"]["details"]["email"].asString(), "<EMAIL>");

    // Test nested array access
    EXPECT_TRUE((*result)["items"].isArray());
    EXPECT_EQ((*result)["items"].size(), 2);
    EXPECT_EQ((*result)["items"][0]["id"].asInt(), 1);
    EXPECT_EQ((*result)["items"][0]["name"].asString(), "Item 1");
    EXPECT_EQ((*result)["items"][1]["id"].asInt(), 2);
    EXPECT_EQ((*result)["items"][1]["name"].asString(), "Item 2");
}

TEST_F(JsonUtilsTest, JsonStringification) {
    // Test simple values
    utils::JsonValue nullVal;
    EXPECT_EQ(utils::JsonUtils::stringify(nullVal), "null");

    utils::JsonValue boolVal(true);
    EXPECT_EQ(utils::JsonUtils::stringify(boolVal), "true");

    utils::JsonValue numberVal(42);
    EXPECT_EQ(utils::JsonUtils::stringify(numberVal), "42");

    utils::JsonValue stringVal("hello");
    EXPECT_EQ(utils::JsonUtils::stringify(stringVal), "\"hello\"");

    // Test convenience function
    EXPECT_EQ(utils::toJson(numberVal), "42");

    // Test array
    utils::JsonValue arrayVal = utils::createArray();
    arrayVal.asArray().push_back(utils::JsonValue(1));
    arrayVal.asArray().push_back(utils::JsonValue("test"));
    arrayVal.asArray().push_back(utils::JsonValue(true));

    std::string arrayStr = utils::JsonUtils::stringify(arrayVal);
    EXPECT_EQ(arrayStr, "[1,\"test\",true]");

    // Test object
    utils::JsonValue objectVal = utils::createObject();
    objectVal["name"] = utils::JsonValue("John");
    objectVal["age"] = utils::JsonValue(30);

    std::string objectStr = utils::JsonUtils::stringify(objectVal);
    // Note: order might vary, so we check if it contains expected parts
    EXPECT_TRUE(objectStr.find("\"name\":\"John\"") != std::string::npos);
    EXPECT_TRUE(objectStr.find("\"age\":30") != std::string::npos);
}

TEST_F(JsonUtilsTest, PrettyPrinting) {
    utils::JsonValue obj = utils::createObject();
    obj["name"] = utils::JsonValue("John");
    obj["age"] = utils::JsonValue(30);
    obj["items"] = utils::createArray();
    obj["items"].asArray().push_back(utils::JsonValue("item1"));
    obj["items"].asArray().push_back(utils::JsonValue("item2"));

    std::string pretty = utils::JsonUtils::stringify(obj, true);
    EXPECT_TRUE(pretty.find("\n") != std::string::npos); // Should contain newlines
    EXPECT_TRUE(pretty.find("  ") != std::string::npos); // Should contain indentation
}

TEST_F(JsonUtilsTest, InvalidJsonParsing) {
    // Test invalid JSON strings
    EXPECT_FALSE(utils::JsonUtils::parse("").has_value());
    EXPECT_FALSE(utils::JsonUtils::parse("invalid").has_value());
    EXPECT_FALSE(utils::JsonUtils::parse("{invalid}").has_value());
    EXPECT_FALSE(utils::JsonUtils::parse("[1,2,]").has_value()); // Trailing comma
    EXPECT_FALSE(utils::JsonUtils::parse("{\"key\":}").has_value()); // Missing value
    EXPECT_FALSE(utils::JsonUtils::parse("\"unclosed string").has_value());

    // Test isValid function
    EXPECT_TRUE(utils::JsonUtils::isValid("null"));
    EXPECT_TRUE(utils::JsonUtils::isValid("true"));
    EXPECT_TRUE(utils::JsonUtils::isValid("42"));
    EXPECT_TRUE(utils::JsonUtils::isValid("\"string\""));
    EXPECT_TRUE(utils::JsonUtils::isValid("[]"));
    EXPECT_TRUE(utils::JsonUtils::isValid("{}"));
    EXPECT_FALSE(utils::JsonUtils::isValid("invalid"));
}

TEST_F(JsonUtilsTest, StringEscaping) {
    // Test string with escape characters
    std::string jsonStr = R"("Hello\nWorld\t\"Quote\"\\Backslash")";
    auto result = utils::JsonUtils::parse(jsonStr);

    EXPECT_TRUE(result.has_value());
    EXPECT_TRUE(result->isString());

    std::string expected = "Hello\nWorld\t\"Quote\"\\Backslash";
    EXPECT_EQ(result->asString(), expected);

    // Test stringification with escaping
    utils::JsonValue stringVal("Line1\nLine2\tTab\"Quote\"");
    std::string stringified = utils::JsonUtils::stringify(stringVal);
    EXPECT_TRUE(stringified.find("\\n") != std::string::npos);
    EXPECT_TRUE(stringified.find("\\t") != std::string::npos);
    EXPECT_TRUE(stringified.find("\\\"") != std::string::npos);
}

TEST_F(JsonUtilsTest, JsonMerge) {
    utils::JsonValue base = utils::createObject();
    base["name"] = utils::JsonValue("John");
    base["age"] = utils::JsonValue(30);
    base["settings"] = utils::createObject();
    base["settings"]["theme"] = utils::JsonValue("dark");

    utils::JsonValue overlay = utils::createObject();
    overlay["age"] = utils::JsonValue(31); // Override
    overlay["email"] = utils::JsonValue("<EMAIL>"); // New field
    overlay["settings"] = utils::createObject();
    overlay["settings"]["language"] = utils::JsonValue("en"); // New nested field

    auto merged = utils::JsonUtils::merge(base, overlay);

    EXPECT_TRUE(merged.isObject());
    EXPECT_EQ(merged["name"].asString(), "John"); // Preserved
    EXPECT_EQ(merged["age"].asInt(), 31); // Overridden
    EXPECT_EQ(merged["email"].asString(), "<EMAIL>"); // Added
    EXPECT_TRUE(merged["settings"].isObject());
    EXPECT_EQ(merged["settings"]["theme"].asString(), "dark"); // Preserved nested
    EXPECT_EQ(merged["settings"]["language"].asString(), "en"); // Added nested
}

TEST_F(JsonUtilsTest, TypeChecking) {
    utils::JsonValue nullVal;
    utils::JsonValue boolVal(true);
    utils::JsonValue numberVal(42);
    utils::JsonValue stringVal("test");
    utils::JsonValue arrayVal = utils::createArray();
    utils::JsonValue objectVal = utils::createObject();

    // Test type checking
    EXPECT_TRUE(nullVal.isNull());
    EXPECT_FALSE(nullVal.isBool());
    EXPECT_FALSE(nullVal.isNumber());
    EXPECT_FALSE(nullVal.isString());
    EXPECT_FALSE(nullVal.isArray());
    EXPECT_FALSE(nullVal.isObject());

    EXPECT_TRUE(boolVal.isBool());
    EXPECT_FALSE(boolVal.isNull());

    EXPECT_TRUE(numberVal.isNumber());
    EXPECT_FALSE(numberVal.isBool());

    EXPECT_TRUE(stringVal.isString());
    EXPECT_FALSE(stringVal.isNumber());

    EXPECT_TRUE(arrayVal.isArray());
    EXPECT_FALSE(arrayVal.isObject());

    EXPECT_TRUE(objectVal.isObject());
    EXPECT_FALSE(objectVal.isArray());
}

TEST_F(JsonUtilsTest, ErrorHandling) {
    utils::JsonValue stringVal("test");

    // Test type mismatch exceptions
    EXPECT_THROW(stringVal.asBool(), std::runtime_error);
    EXPECT_THROW(stringVal.asNumber(), std::runtime_error);
    EXPECT_THROW(stringVal.asArray(), std::runtime_error);
    EXPECT_THROW(stringVal.asObject(), std::runtime_error);

    // Test array index out of bounds
    utils::JsonValue arrayVal = utils::createArray();
    arrayVal.asArray().push_back(utils::JsonValue(1));

    EXPECT_THROW(arrayVal[1], std::out_of_range);

    // Test object key not found (const version throws)
    utils::JsonValue objectVal = utils::createObject();
    objectVal["key1"] = utils::JsonValue("value1");

    const utils::JsonValue& constObjectVal = objectVal;
    EXPECT_THROW(constObjectVal["nonexistent"], std::out_of_range);
}
