# Dependencies management for the project

# Find required packages
find_package(Threads REQUIRED)

# Function to find and configure common dependencies
function(setup_common_dependencies)
    # Threading support
    if(Threads_FOUND)
        message(STATUS "Threading support found")
    endif()

    # Optional: Find Boost (uncomment if needed)
    # find_package(Boost REQUIRED COMPONENTS system filesystem thread)
    # if(Boost_FOUND)
    #     message(STATUS "Boost found: ${Boost_VERSION}")
    # endif()

    # Optional: Find OpenSSL (enabled for crypto utils)
    option(USE_OPENSSL "Use OpenSSL for crypto operations" OFF)
    if(USE_OPENSSL)
        find_package(OpenSSL)
        if(OpenSSL_FOUND)
            message(STATUS "OpenSSL found: ${OPENSSL_VERSION}")
            add_compile_definitions(CRYPTO_USE_OPENSSL)
        else()
            message(WARNING "OpenSSL not found. Crypto utils will use fallback implementations.")
        endif()
    else()
        message(STATUS "OpenSSL disabled. Crypto utils will use fallback implementations.")
    endif()

    # Optional: Find protobuf (uncomment if needed)
    # find_package(Protobuf REQUIRED)
    # if(Protobuf_FOUND)
    #     message(STATUS "Protobuf found: ${Protobuf_VERSION}")
    # endif()

    # Find CURL for HTTP utils
    option(USE_CURL "Use CURL for HTTP operations" ON)
    if(USE_CURL)
        find_package(PkgConfig QUIET)
        if(PKG_CONFIG_FOUND)
            pkg_check_modules(CURL QUIET libcurl)
            if(CURL_FOUND)
                message(STATUS "CURL found via pkg-config: ${CURL_VERSION}")
                add_compile_definitions(HTTP_USE_CURL)
            else()
                # Fallback to find_package
                find_package(CURL)
                if(CURL_FOUND)
                    message(STATUS "CURL found: ${CURL_VERSION_STRING}")
                    add_compile_definitions(HTTP_USE_CURL)
                else()
                    message(WARNING "CURL not found. HTTP utils will not be available.")
                endif()
            endif()
        else()
            # Fallback to find_package
            find_package(CURL)
            if(CURL_FOUND)
                message(STATUS "CURL found: ${CURL_VERSION_STRING}")
                add_compile_definitions(HTTP_USE_CURL)
            else()
                message(WARNING "CURL not found. HTTP utils will not be available.")
            endif()
        endif()
    else()
        message(STATUS "CURL disabled. HTTP utils will not be available.")
    endif()
endfunction()

# Function to setup testing framework
function(setup_testing_framework)
    # Enable testing
    enable_testing()

    # Find Google Test
    find_package(GTest QUIET)
    if(NOT GTest_FOUND)
        message(STATUS "Google Test not found, using FetchContent to download")
        include(FetchContent)
        FetchContent_Declare(
            googletest
            GIT_REPOSITORY https://github.com/google/googletest.git
            GIT_TAG release-1.12.1
        )
        # For Windows: Prevent overriding the parent project's compiler/linker settings
        set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
        FetchContent_MakeAvailable(googletest)
    else()
        message(STATUS "Google Test found: ${GTest_VERSION}")
    endif()
endfunction()

# Function to setup cpp-httplib dependency
function(setup_httplib_dependency)
    include(FetchContent)

    # Check if httplib is already available
    find_package(httplib QUIET)
    if(NOT httplib_FOUND)
        message(STATUS "cpp-httplib not found, using FetchContent to download")
        FetchContent_Declare(
            httplib
            GIT_REPOSITORY https://github.com/yhirose/cpp-httplib.git
            GIT_TAG v0.14.3
        )
        FetchContent_MakeAvailable(httplib)
    else()
        message(STATUS "cpp-httplib found: ${httplib_VERSION}")
    endif()
endfunction()

# Function to setup RTSP and multimedia dependencies
function(setup_rtsp_dependencies)
    find_package(PkgConfig REQUIRED)

    # Core GStreamer components
    pkg_check_modules(GSTREAMER QUIET
        gstreamer-1.0>=1.18.0
        gstreamer-rtsp-1.0>=1.18.0
        gstreamer-app-1.0>=1.18.0
        gstreamer-video-1.0>=1.18.0
        gstreamer-rtp-1.0>=1.18.0
    )

    # RockChip specific plugins (optional for hardware acceleration)
    pkg_check_modules(GSTREAMER_ROCKCHIP QUIET
        gstreamer-rockchip-1.0
        gstreamer-mpp-1.0
    )

    # FFmpeg as fallback for video processing
    pkg_check_modules(FFMPEG QUIET
        libavformat>=58.0.0
        libavcodec>=58.0.0
        libavutil>=56.0.0
        libswscale>=5.0.0
        libavfilter>=7.0.0
    )

    # Set global variables for use in other CMake files
    if(GSTREAMER_FOUND)
        set(GSTREAMER_FOUND TRUE PARENT_SCOPE)
        set(GSTREAMER_INCLUDE_DIRS ${GSTREAMER_INCLUDE_DIRS} PARENT_SCOPE)
        set(GSTREAMER_LIBRARIES ${GSTREAMER_LIBRARIES} PARENT_SCOPE)
        set(GSTREAMER_CFLAGS_OTHER ${GSTREAMER_CFLAGS_OTHER} PARENT_SCOPE)
        message(STATUS "GStreamer found: ${GSTREAMER_VERSION}")
    else()
        set(GSTREAMER_FOUND FALSE PARENT_SCOPE)
        message(WARNING "GStreamer not found. RTSP functionality will be limited.")
    endif()

    if(GSTREAMER_ROCKCHIP_FOUND)
        set(GSTREAMER_ROCKCHIP_FOUND TRUE PARENT_SCOPE)
        set(GSTREAMER_ROCKCHIP_INCLUDE_DIRS ${GSTREAMER_ROCKCHIP_INCLUDE_DIRS} PARENT_SCOPE)
        set(GSTREAMER_ROCKCHIP_LIBRARIES ${GSTREAMER_ROCKCHIP_LIBRARIES} PARENT_SCOPE)
        message(STATUS "RockChip GStreamer plugins found: ${GSTREAMER_ROCKCHIP_VERSION}")
    else()
        set(GSTREAMER_ROCKCHIP_FOUND FALSE PARENT_SCOPE)
        message(STATUS "RockChip GStreamer plugins not found. Hardware acceleration may be limited.")
    endif()

    if(FFMPEG_FOUND)
        set(FFMPEG_FOUND TRUE PARENT_SCOPE)
        set(FFMPEG_INCLUDE_DIRS ${FFMPEG_INCLUDE_DIRS} PARENT_SCOPE)
        set(FFMPEG_LIBRARIES ${FFMPEG_LIBRARIES} PARENT_SCOPE)
        set(FFMPEG_CFLAGS_OTHER ${FFMPEG_CFLAGS_OTHER} PARENT_SCOPE)
        message(STATUS "FFmpeg found: ${FFMPEG_VERSION}")
    else()
        set(FFMPEG_FOUND FALSE PARENT_SCOPE)
        message(STATUS "FFmpeg not found. Video processing fallback not available.")
    endif()
endfunction()

# Function to setup Qt5 dependencies for GUI applications
function(setup_qt5_dependencies)
    # Find Qt5 components
    find_package(Qt5 QUIET COMPONENTS Core Widgets Network WebEngine WebEngineWidgets)

    if(Qt5_FOUND)
        set(Qt5_FOUND TRUE PARENT_SCOPE)
        message(STATUS "Qt5 found: ${Qt5_VERSION}")

        # Set Qt5 variables for parent scope
        set(Qt5Core_FOUND ${Qt5Core_FOUND} PARENT_SCOPE)
        set(Qt5Widgets_FOUND ${Qt5Widgets_FOUND} PARENT_SCOPE)
        set(Qt5Network_FOUND ${Qt5Network_FOUND} PARENT_SCOPE)
        set(Qt5WebEngine_FOUND ${Qt5WebEngine_FOUND} PARENT_SCOPE)
        set(Qt5WebEngineWidgets_FOUND ${Qt5WebEngineWidgets_FOUND} PARENT_SCOPE)
    else()
        set(Qt5_FOUND FALSE PARENT_SCOPE)
        message(WARNING "Qt5 not found. GUI applications will not be available.")
    endif()
endfunction()

# Function to link common dependencies to a target
function(link_common_dependencies target_name)
    target_link_libraries(${target_name} PRIVATE Threads::Threads)

    # Link other dependencies as needed
    # target_link_libraries(${target_name} PRIVATE ${Boost_LIBRARIES})
    # target_link_libraries(${target_name} PRIVATE OpenSSL::SSL OpenSSL::Crypto)
    # target_link_libraries(${target_name} PRIVATE protobuf::libprotobuf)

    # Link CURL if available
    if(CURL_FOUND)
        if(TARGET CURL::libcurl)
            # Use the imported target if available (from find_package)
            target_link_libraries(${target_name} PRIVATE CURL::libcurl)
        else()
            # Use pkg-config variables
            target_include_directories(${target_name} PRIVATE ${CURL_INCLUDE_DIRS})
            target_link_libraries(${target_name} PRIVATE ${CURL_LIBRARIES})
            target_compile_options(${target_name} PRIVATE ${CURL_CFLAGS_OTHER})
        endif()
    endif()
endfunction()

# Function to link RTSP dependencies to a target
function(link_rtsp_dependencies target_name)
    # Link GStreamer if available
    if(GSTREAMER_FOUND)
        target_include_directories(${target_name} PRIVATE ${GSTREAMER_INCLUDE_DIRS})
        target_link_libraries(${target_name} PRIVATE ${GSTREAMER_LIBRARIES})
        target_compile_options(${target_name} PRIVATE ${GSTREAMER_CFLAGS_OTHER})
        target_compile_definitions(${target_name} PRIVATE HAVE_GSTREAMER)
    endif()

    # Link RockChip plugins if available
    if(GSTREAMER_ROCKCHIP_FOUND)
        target_include_directories(${target_name} PRIVATE ${GSTREAMER_ROCKCHIP_INCLUDE_DIRS})
        target_link_libraries(${target_name} PRIVATE ${GSTREAMER_ROCKCHIP_LIBRARIES})
        target_compile_definitions(${target_name} PRIVATE HAVE_GSTREAMER_ROCKCHIP)
    endif()

    # Link FFmpeg if available
    if(FFMPEG_FOUND)
        target_include_directories(${target_name} PRIVATE ${FFMPEG_INCLUDE_DIRS})
        target_link_libraries(${target_name} PRIVATE ${FFMPEG_LIBRARIES})
        target_compile_options(${target_name} PRIVATE ${FFMPEG_CFLAGS_OTHER})
        target_compile_definitions(${target_name} PRIVATE HAVE_FFMPEG)
    endif()
endfunction()

# Function to link Qt5 dependencies to a target
function(link_qt5_dependencies target_name)
    if(Qt5_FOUND)
        # Link Qt5 Core (always needed)
        if(Qt5Core_FOUND)
            target_link_libraries(${target_name} PRIVATE Qt5::Core)
        endif()

        # Link Qt5 Widgets for GUI
        if(Qt5Widgets_FOUND)
            target_link_libraries(${target_name} PRIVATE Qt5::Widgets)
        endif()

        # Link Qt5 Network for network operations
        if(Qt5Network_FOUND)
            target_link_libraries(${target_name} PRIVATE Qt5::Network)
        endif()

        # Link Qt5 WebEngine for web content
        if(Qt5WebEngine_FOUND)
            target_link_libraries(${target_name} PRIVATE Qt5::WebEngine)
        endif()

        # Link Qt5 WebEngineWidgets for web widgets
        if(Qt5WebEngineWidgets_FOUND)
            target_link_libraries(${target_name} PRIVATE Qt5::WebEngineWidgets)
        endif()

        # Enable Qt5 MOC, UIC, and RCC
        set_target_properties(${target_name} PROPERTIES
            AUTOMOC ON
            AUTOUIC ON
            AUTORCC ON
        )
    endif()
endfunction()

# Function to link testing dependencies to a test target
function(link_testing_dependencies target_name)
    if(TARGET gtest_main)
        target_link_libraries(${target_name} PRIVATE gtest_main gtest)
    elseif(TARGET GTest::gtest_main)
        target_link_libraries(${target_name} PRIVATE GTest::gtest_main GTest::gtest)
    endif()
endfunction()

# Function to add a test executable
function(add_test_executable test_name)
    set(options "")
    set(oneValueArgs "")
    set(multiValueArgs SOURCES LIBRARIES)
    cmake_parse_arguments(TEST "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})

    add_executable(${test_name} ${TEST_SOURCES})
    set_target_properties_common(${test_name})
    link_testing_dependencies(${test_name})

    if(TEST_LIBRARIES)
        target_link_libraries(${test_name} PRIVATE ${TEST_LIBRARIES})
    endif()

    add_test(NAME ${test_name} COMMAND ${test_name})
endfunction()
