#pragma once

#include "rtsp_types.hpp"
#include <string>
#include <vector>
#include <unordered_map>
#include <memory>

namespace aibox {
namespace rtsp {

// RTSP connection configuration
struct RTSPConnectionConfig {
    // Basic connection settings
    std::string stream_id;
    std::string rtsp_url;
    std::string username;
    std::string password;
    
    // Network settings
    TransportProtocol transport = TransportProtocol::TCP;
    int timeout_ms = 5000;
    int connection_timeout_ms = 10000;
    int read_timeout_ms = 5000;
    
    // Retry settings
    int retry_count = 3;
    int retry_delay_ms = 1000;
    int retry_delay_max_ms = 60000;
    int retry_jitter_ms = 500;
    
    // Stream settings
    StreamPriority priority = StreamPriority::MEDIUM;
    bool enabled = true;
    
    // Video settings
    Resolution target_resolution{1920, 1080};
    FrameRate target_framerate{30, 1};
    uint32_t target_bitrate_kbps = 4000;
    VideoCodec preferred_codec = VideoCodec::H264;
    
    // Buffer settings
    size_t buffer_size_bytes = 1024 * 1024;  // 1MB
    size_t queue_size = 100;
    int jitter_buffer_size_ms = 200;
    
    // Hardware acceleration settings
    bool use_mpp_decoder = true;
    bool use_rga_scaler = true;
    bool use_dmabuf_zerocopy = true;
    
    // Audio settings (future use)
    bool audio_enabled = false;
    
    // Metadata
    std::unordered_map<std::string, std::string> metadata;
    
    // Validation
    bool isValid() const {
        return !rtsp_url.empty() && 
               rtsp_url.find("rtsp://") == 0 &&
               timeout_ms > 0 &&
               retry_count >= 0 &&
               buffer_size_bytes > 0 &&
               queue_size > 0;
    }
    
    // Get memory footprint estimate
    size_t getEstimatedMemoryUsage() const {
        // Rough estimate based on buffer sizes and queue depth
        size_t base_memory = buffer_size_bytes * 2;  // Input + output buffers
        size_t queue_memory = queue_size * 64 * 1024;  // Assume 64KB per NAL unit
        size_t overhead = 10 * 1024 * 1024;  // 10MB overhead for GStreamer
        return base_memory + queue_memory + overhead;
    }
};

// Performance configuration for RK3588 platform
struct RTSPPerformanceConfig {
    // Thread configuration
    int thread_pool_size = 4;
    int io_thread_count = 2;
    int worker_thread_count = 2;
    std::vector<int> cpu_affinity = {2, 3};  // RK3588 cores 2-3
    
    // Memory configuration
    size_t max_memory_usage_mb = platform::MEMORY_LIMIT_4GB_MB;
    size_t buffer_pool_size = 32;
    size_t shared_buffer_size_mb = 100;
    
    // Hardware acceleration
    bool enable_mpp_decoder = true;
    bool enable_rga_scaler = true;
    bool enable_dmabuf_zerocopy = true;
    int mpp_decoder_instances = 6;
    
    // Performance tuning
    bool adaptive_quality = true;
    bool thermal_management = true;
    int target_latency_ms = 150;
    
    // Resource limits
    int max_concurrent_streams = 6;  // Default for 4GB
    float cpu_usage_limit_percent = 25.0f;
    
    // Thermal settings
    int thermal_throttle_temperature = platform::THROTTLE_TEMPERATURE;
    int thermal_shutdown_temperature = platform::MAX_SOC_TEMPERATURE;
    
    void configureFor4GB() {
        max_memory_usage_mb = platform::MEMORY_LIMIT_4GB_MB;
        max_concurrent_streams = 6;
        cpu_usage_limit_percent = 25.0f;
        buffer_pool_size = 32;
        mpp_decoder_instances = 6;
    }

    void configureFor8GB() {
        max_memory_usage_mb = platform::MEMORY_LIMIT_8GB_MB;
        max_concurrent_streams = 12;
        cpu_usage_limit_percent = 30.0f;
        buffer_pool_size = 64;
        mpp_decoder_instances = 12;
    }

    void configureFor16GB() {
        max_memory_usage_mb = platform::MEMORY_LIMIT_16GB_MB;
        max_concurrent_streams = 24;
        cpu_usage_limit_percent = 35.0f;
        buffer_pool_size = 128;
        mpp_decoder_instances = 24;
    }
};

// Network configuration
struct RTSPNetworkConfig {
    // Transport settings
    bool prefer_tcp = true;
    bool tcp_nodelay = true;
    size_t udp_buffer_size = 65536;
    size_t tcp_buffer_size = 65536;
    
    // RTP settings
    int rtp_port_range_start = 10000;
    int rtp_port_range_end = 20000;
    
    // Keep-alive settings
    int keep_alive_interval_ms = 30000;
    bool enable_keep_alive = true;
    
    // Security settings
    bool ssl_verify_peer = true;
    bool ssl_verify_hostname = true;
    std::string ssl_ca_file = "/etc/ssl/certs/ca-certificates.crt";
    
    // Timeout settings
    int dns_timeout_ms = 5000;
    int connect_timeout_ms = 10000;
    int socket_timeout_ms = 30000;
};

// Logging and monitoring configuration
struct RTSPMonitoringConfig {
    // Statistics collection
    bool enable_statistics = true;
    int statistics_interval_ms = 1000;
    size_t statistics_history_size = 3600;  // 1 hour at 1s intervals
    
    // Performance monitoring
    bool enable_performance_monitoring = true;
    bool enable_thermal_monitoring = true;
    bool enable_memory_monitoring = true;
    
    // Logging settings
    std::string log_level = "INFO";
    std::string log_file_path = "/var/log/aibox/rtsp.log";
    size_t max_log_size_mb = 100;
    int log_rotation_count = 5;
    
    // Debug settings
    bool enable_packet_tracing = false;
    bool enable_nal_unit_dumping = false;
    bool enable_gstreamer_debug = false;
    
    // Export settings
    bool export_prometheus = false;
    int prometheus_port = 9090;
};

// Main RTSP module configuration
struct RTSPModuleConfig {
    // Module information
    std::string version = "1.0.0";
    bool enabled = true;
    
    // Sub-configurations
    RTSPPerformanceConfig performance;
    RTSPNetworkConfig network;
    RTSPMonitoringConfig monitoring;
    
    // Stream configurations
    std::vector<RTSPConnectionConfig> streams;
    
    // Platform detection
    bool auto_detect_platform = true;
    std::string platform_override;  // "4gb" or "8gb"
    
    // Validation
    bool isValid() const {
        if (!enabled) return true;  // Disabled module is valid
        
        // Check performance limits
        size_t total_memory = 0;
        for (const auto& stream : streams) {
            if (!stream.isValid()) return false;
            total_memory += stream.getEstimatedMemoryUsage();
        }
        
        return total_memory <= (performance.max_memory_usage_mb * 1024 * 1024) &&
               streams.size() <= static_cast<size_t>(performance.max_concurrent_streams);
    }
    
    // Auto-configure based on detected platform
    void autoConfigurePlatform();
    
    // Get total estimated memory usage
    size_t getTotalEstimatedMemoryUsage() const {
        size_t total = 0;
        for (const auto& stream : streams) {
            total += stream.getEstimatedMemoryUsage();
        }
        return total;
    }
    
    // Get active stream count
    size_t getActiveStreamCount() const {
        size_t count = 0;
        for (const auto& stream : streams) {
            if (stream.enabled) count++;
        }
        return count;
    }
};

// Configuration loader/saver
class RTSPConfigManager {
public:
    // Load configuration from JSON file
    static Result<RTSPModuleConfig> loadFromFile(const std::string& file_path);
    
    // Save configuration to JSON file
    static Result<bool> saveToFile(const RTSPModuleConfig& config, const std::string& file_path);
    
    // Load configuration from JSON string
    static Result<RTSPModuleConfig> loadFromJson(const std::string& json_string);
    
    // Convert configuration to JSON string
    static Result<std::string> toJson(const RTSPModuleConfig& config);
    
    // Validate configuration
    static Result<bool> validate(const RTSPModuleConfig& config);
    
    // Create default configuration
    static RTSPModuleConfig createDefault();
    
    // Create configuration for specific platform
    static RTSPModuleConfig createFor4GB();
    static RTSPModuleConfig createFor8GB();
    static RTSPModuleConfig createFor16GB();
    
    // Merge configurations (useful for updates)
    static RTSPModuleConfig merge(const RTSPModuleConfig& base, const RTSPModuleConfig& override);
    
private:
    // Internal helper methods
    static void detectPlatform(RTSPModuleConfig& config);
    static bool validateStreamConfig(const RTSPConnectionConfig& stream);
    static bool validatePerformanceConfig(const RTSPPerformanceConfig& perf);
};

} // namespace rtsp
} // namespace aibox
