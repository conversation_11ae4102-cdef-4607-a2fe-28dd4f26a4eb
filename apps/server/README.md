# C-AIBOX HTTP API Server

HTTP API server for C-AIBOX using cpp-httplib, providing REST endpoints for AI model inference.

## Features

- **RESTful API** for AI model inference
- **ArcFace** face recognition inference
- **YOLO** object detection
- **CORS support** for web applications
- **JSON responses** with proper error handling
- **Request logging** and health monitoring
- **Command-line configuration**

## Building

From the project root directory:

```bash
# Build the entire project
./scripts/build.sh

# Or build just the server
cd build
make server
```

## Running the Server

### Basic Usage

```bash
# Run with default settings (host: 0.0.0.0, port: 8080)
./build/bin/server

# Run with custom port
./build/bin/server --port 3000

# Run with custom host and port
./build/bin/server --host 127.0.0.1 --port 3000

# Show help
./build/bin/server --help
```

### Command Line Options

- `--port <port>`: Server port (default: 8080)
- `--host <host>`: Server host (default: 0.0.0.0)
- `--help`: Show help message

## API Endpoints

### Health Check

**GET** `/health`

Check if the server is running.

**Response:**
```json
{
  "success": true,
  "message": "Server is healthy"
}
```

### API Information

**GET** `/api/v1/info`

Get API version and available endpoints.

**Response:**
```json
{
  "success": true,
  "message": "API information",
  "data": {
    "version": "1.0.0",
    "endpoints": ["/health", "/api/v1/arcface/infer", "/api/v1/yolo/detect"]
  }
}
```

### ArcFace Inference

**POST** `/api/v1/arcface/infer`

Perform face recognition inference using ArcFace model.

**Request Body:**
```json
{
  "image_path": "/path/to/face/image.jpg"
}
```

**Response:**
```json
{
  "success": true,
  "message": "ArcFace inference completed",
  "data": {
    "image_path": "/path/to/face/image.jpg",
    "status": "processed"
  }
}
```

### YOLO Detection

**POST** `/api/v1/yolo/detect`

Perform object detection using YOLO model.

**Request Body:**
```json
{
  "image_path": "/path/to/image.jpg"
}
```

**Response:**
```json
{
  "success": true,
  "message": "YOLO detection completed",
  "data": {
    "image_path": "/path/to/image.jpg",
    "status": "processed"
  }
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "success": false,
  "error": "Error description",
  "code": 400
}
```

Common error codes:
- `400`: Bad Request (missing parameters, invalid input)
- `404`: Not Found (endpoint not found)
- `500`: Internal Server Error

## Example Usage

### Using curl

```bash
# Health check
curl http://localhost:8080/health

# ArcFace inference
curl -X POST http://localhost:8080/api/v1/arcface/infer \
  -H "Content-Type: application/json" \
  -d '{"image_path": "face.jpg"}'

# YOLO detection
curl -X POST http://localhost:8080/api/v1/yolo/detect \
  -H "Content-Type: application/json" \
  -d '{"image_path": "image.jpg"}'
```

### Using JavaScript (fetch)

```javascript
// Health check
const healthResponse = await fetch('http://localhost:8080/health');
const healthData = await healthResponse.json();

// ArcFace inference
const arcfaceResponse = await fetch('http://localhost:8080/api/v1/arcface/infer', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    image_path: 'face.jpg'
  })
});
const arcfaceData = await arcfaceResponse.json();
```

## Dependencies

- **cpp-httplib**: HTTP server library (automatically downloaded via CMake)
- **models library**: Internal AI models (arcface, yolo)
- **shared library**: Internal utilities
- **C++17**: Required for compilation

## Development

The server is built using:
- **cpp-httplib** for HTTP server functionality
- **CMake FetchContent** for dependency management
- **JSON** responses with manual string construction (lightweight)
- **CORS** support for web application integration

## Configuration

Server configuration can be modified in the `ServerConfig` struct in `main.cpp`:

```cpp
struct ServerConfig {
    std::string host = "0.0.0.0";
    int port = 8080;
    int timeout_seconds = 30;
    bool enable_cors = true;
};
```

## Logging

The server logs all requests in the format:
```
[timestamp] METHOD /path - status_code
```

Example:
```
[**********] POST /api/v1/arcface/infer - 200
[**********] GET /health - 200
```
