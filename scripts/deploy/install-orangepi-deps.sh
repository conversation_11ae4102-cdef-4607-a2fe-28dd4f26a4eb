#!/bin/bash

# Orange Pi 5 Plus Dependencies Installation Script
# Installs runtime dependencies and RK3588 specific libraries

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Detect Orange Pi variant
detect_orangepi_variant() {
    log_step "Detecting Orange Pi Variant"
    
    # Check for RK3588 SoC
    if grep -q "rk3588" /proc/device-tree/compatible 2>/dev/null; then
        ORANGEPI_SOC="RK3588"
        log_success "Detected RK3588 SoC"
    else
        log_warning "Could not detect RK3588 SoC"
        ORANGEPI_SOC="Unknown"
    fi
    
    # Check memory size
    local mem_kb=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local mem_gb=$((mem_kb / 1024 / 1024))
    
    if [[ $mem_gb -ge 15 ]]; then
        ORANGEPI_RAM="16GB"
    elif [[ $mem_gb -ge 7 ]]; then
        ORANGEPI_RAM="8GB"
    elif [[ $mem_gb -ge 3 ]]; then
        ORANGEPI_RAM="4GB"
    else
        ORANGEPI_RAM="Unknown"
    fi
    
    log_info "Orange Pi Configuration: $ORANGEPI_SOC with $ORANGEPI_RAM RAM"
}

# Update system packages
update_system() {
    log_step "Updating System Packages"
    
    log_info "Updating package lists..."
    apt-get update
    
    log_info "Upgrading system packages..."
    apt-get upgrade -y
    
    log_success "System update completed"
}

# Install basic runtime dependencies
install_basic_deps() {
    log_step "Installing Basic Runtime Dependencies"
    
    log_info "Installing essential libraries..."
    apt-get install -y \
        libc6 \
        libstdc++6 \
        libgcc-s1 \
        libssl3 \
        libcrypto3 \
        zlib1g \
        libpthread-stubs0-dev
    
    log_success "Basic dependencies installed"
}

# Install GStreamer runtime
install_gstreamer() {
    log_step "Installing GStreamer Runtime"
    
    log_info "Installing GStreamer core..."
    apt-get install -y \
        libgstreamer1.0-0 \
        gstreamer1.0-tools \
        gstreamer1.0-plugins-base \
        gstreamer1.0-plugins-good \
        gstreamer1.0-plugins-bad \
        gstreamer1.0-plugins-ugly \
        gstreamer1.0-libav
    
    log_info "Installing GStreamer RTSP support..."
    apt-get install -y \
        gstreamer1.0-rtsp \
        libgstreamer-plugins-base1.0-0 \
        libgstreamer-plugins-good1.0-0 \
        libgstreamer-plugins-bad1.0-0
    
    log_success "GStreamer runtime installed"
}

# Install Qt5 runtime
install_qt5() {
    log_step "Installing Qt5 Runtime"
    
    log_info "Installing Qt5 core libraries..."
    apt-get install -y \
        libqt5core5a \
        libqt5gui5 \
        libqt5widgets5 \
        libqt5network5 \
        libqt5dbus5
    
    log_info "Installing Qt5 WebEngine..."
    apt-get install -y \
        libqt5webengine5 \
        libqt5webenginewidgets5 \
        libqt5webenginecore5
    
    log_info "Installing Qt5 platform plugins..."
    apt-get install -y \
        qt5-gtk-platformtheme \
        libqt5svg5
    
    log_success "Qt5 runtime installed"
}

# Install multimedia libraries
install_multimedia() {
    log_step "Installing Multimedia Libraries"
    
    log_info "Installing FFmpeg runtime..."
    apt-get install -y \
        libavformat58 \
        libavcodec58 \
        libavutil56 \
        libswscale5 \
        libavfilter7
    
    log_info "Installing audio libraries..."
    apt-get install -y \
        libasound2 \
        libpulse0 \
        libasound2-plugins
    
    log_success "Multimedia libraries installed"
}

# Install RK3588 specific libraries
install_rk3588_libs() {
    log_step "Installing RK3588 Hardware Acceleration Libraries"
    
    # Check for RockChip repositories
    if ! grep -q "rockchip" /etc/apt/sources.list.d/* 2>/dev/null; then
        log_warning "RockChip repositories not found"
        log_info "Hardware acceleration libraries may not be available"
        log_info "Please add RockChip repositories manually for full hardware support"
        return
    fi
    
    log_info "Installing MPP (Media Process Platform)..."
    apt-get install -y \
        librockchip-mpp1 \
        librockchip-mpp-dev \
        rockchip-mpp-demos || {
        log_warning "MPP libraries not available in repositories"
    }
    
    log_info "Installing RGA (Raster Graphic Acceleration)..."
    apt-get install -y \
        librga2 \
        librga-dev || {
        log_warning "RGA libraries not available in repositories"
    }
    
    log_info "Installing Mali GPU libraries..."
    apt-get install -y \
        libmali-g610-x11 \
        mali-g610-firmware || {
        log_warning "Mali GPU libraries not available in repositories"
    }
    
    log_success "RK3588 libraries installation attempted"
}

# Install development tools (optional)
install_dev_tools() {
    log_step "Installing Development Tools (Optional)"
    
    log_info "Installing debugging tools..."
    apt-get install -y \
        gdb \
        strace \
        htop \
        iotop \
        stress-ng \
        glmark2-es2 \
        mesa-utils || {
        log_warning "Some development tools not available"
    }
    
    log_success "Development tools installed"
}

# Configure system for optimal performance
configure_system() {
    log_step "Configuring System for Optimal Performance"
    
    # Set CPU governor to performance
    log_info "Setting CPU governor to performance..."
    echo "performance" > /sys/devices/system/cpu/cpu0/cpufreq/scaling_governor 2>/dev/null || {
        log_warning "Could not set CPU governor"
    }
    
    # Configure memory settings
    log_info "Configuring memory settings..."
    echo "vm.swappiness=10" >> /etc/sysctl.conf
    echo "vm.vfs_cache_pressure=50" >> /etc/sysctl.conf
    
    # Configure GPU settings
    log_info "Configuring GPU settings..."
    echo "MALI_SHARED_MEMORY=1" >> /etc/environment
    
    # Set library paths
    log_info "Configuring library paths..."
    echo "/usr/local/lib" > /etc/ld.so.conf.d/local.conf
    echo "/opt/rockchip/lib" > /etc/ld.so.conf.d/rockchip.conf
    ldconfig
    
    log_success "System configuration completed"
}

# Verify installation
verify_installation() {
    log_step "Verifying Installation"
    
    # Check GStreamer
    if gst-inspect-1.0 --version >/dev/null 2>&1; then
        local gst_version=$(gst-inspect-1.0 --version | head -n1)
        log_success "GStreamer verified: $gst_version"
    else
        log_error "GStreamer verification failed"
    fi
    
    # Check Qt5
    if pkg-config --exists Qt5Core; then
        local qt_version=$(pkg-config --modversion Qt5Core)
        log_success "Qt5 verified: $qt_version"
    else
        log_warning "Qt5 verification failed"
    fi
    
    # Check hardware devices
    log_info "Hardware devices:"
    ls -la /dev/dri/ 2>/dev/null && log_success "DRI devices found" || log_warning "DRI devices not found"
    ls -la /dev/rga 2>/dev/null && log_success "RGA device found" || log_warning "RGA device not found"
    ls -la /dev/mpp* 2>/dev/null && log_success "MPP devices found" || log_warning "MPP devices not found"
    
    # Check memory
    local available_mem=$(free -h | grep "Mem:" | awk '{print $7}')
    log_info "Available memory: $available_mem"
    
    log_success "Installation verification completed"
}

# Show installation summary
show_summary() {
    log_step "Installation Summary"
    
    echo "Orange Pi Configuration: $ORANGEPI_SOC with $ORANGEPI_RAM RAM"
    echo ""
    echo "Installed Components:"
    echo "  ✓ Basic runtime libraries"
    echo "  ✓ GStreamer multimedia framework"
    echo "  ✓ Qt5 GUI framework"
    echo "  ✓ FFmpeg multimedia libraries"
    echo "  ✓ Audio libraries (ALSA, PulseAudio)"
    echo "  ✓ RK3588 hardware libraries (if available)"
    echo "  ✓ Development tools"
    echo "  ✓ System optimization"
    echo ""
    
    log_success "Orange Pi dependencies installation completed!"
    log_info "System is ready for c-aibox applications"
    log_info "Reboot recommended for optimal performance: sudo reboot"
}

# Main installation function
main() {
    log_step "Orange Pi 5 Plus Dependencies Installation"
    
    check_root
    detect_orangepi_variant
    update_system
    install_basic_deps
    install_gstreamer
    install_qt5
    install_multimedia
    install_rk3588_libs
    install_dev_tools
    configure_system
    verify_installation
    show_summary
}

# Handle command line arguments
case "${1:-install}" in
    --help|-h|help)
        cat << EOF
Orange Pi 5 Plus Dependencies Installation Script
================================================

This script installs all runtime dependencies for c-aibox applications
on Orange Pi 5 Plus with RK3588 SoC.

USAGE:
    sudo $0

COMPONENTS INSTALLED:
    - Basic runtime libraries (libc, libstdc++, etc.)
    - GStreamer multimedia framework with RTSP support
    - Qt5 GUI framework with WebEngine
    - FFmpeg multimedia libraries
    - Audio libraries (ALSA, PulseAudio)
    - RK3588 hardware acceleration libraries (MPP, RGA, Mali)
    - Development and debugging tools
    - System performance optimizations

REQUIREMENTS:
    - Orange Pi 5 Plus with RK3588 SoC
    - Ubuntu 22.04/24.04 ARM64
    - Root privileges (sudo)
    - Internet connection

NOTES:
    - RK3588 specific libraries require RockChip repositories
    - Some hardware acceleration features may need additional setup
    - Reboot recommended after installation
EOF
        ;;
    install|"")
        main
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
