#pragma once

#include "rtsp_types.hpp"
#include <memory>
#include <string>
#include <unordered_map>
#include <atomic>
#include <mutex>
#include <chrono>
#include <functional>
#include <vector>
#include <queue>

namespace aibox {
namespace rtsp {

// Forward declarations
class Logger;

/**
 * @brief Error severity levels for prioritization
 */
enum class ErrorSeverity {
    TRACE = 0,      // Trace information
    DEBUG_LEVEL = 1,      // Debug information
    INFO = 2,       // Informational messages
    WARNING = 3,    // Warning conditions
    ERROR = 4,      // Error conditions
    CRITICAL = 5,   // Critical conditions
    FATAL = 6       // Fatal errors requiring immediate attention
};

/**
 * @brief Error recovery actions
 */
enum class RecoveryAction {
    NONE,                   // No action needed
    RETRY,                  // Retry the operation
    RESET_CONNECTION,       // Reset the connection
    RESTART_STREAM,         // Restart the stream
    FALLBACK_SOFTWARE,      // Fallback to software processing
    REDUCE_QUALITY,         // Reduce stream quality
    THERMAL_THROTTLE,       // Apply thermal throttling
    SHUTDOWN_STREAM,        // Shutdown the stream
    SYSTEM_RESTART          // Restart the entire system
};

/**
 * @brief Detailed error information
 */
struct ErrorInfo {
    ErrorCategory category;
    ErrorSeverity severity;
    std::string message;
    std::string component;
    std::string stream_id;
    std::chrono::steady_clock::time_point timestamp;
    std::unordered_map<std::string, std::string> context;
    RecoveryAction suggested_action;
    bool is_recoverable;
    int error_code;
    
    ErrorInfo() :
        category(ErrorCategory::CONFIGURATION_ERROR),
        severity(ErrorSeverity::ERROR),
        timestamp(std::chrono::steady_clock::now()),
        suggested_action(RecoveryAction::NONE),
        is_recoverable(true),
        error_code(0) {}
};

/**
 * @brief Error statistics for monitoring
 */
struct ErrorStatistics {
    uint64_t total_errors{0};
    uint64_t network_errors{0};
    uint64_t protocol_errors{0};
    uint64_t codec_errors{0};
    uint64_t hardware_errors{0};
    uint64_t resource_errors{0};
    uint64_t configuration_errors{0};
    uint64_t thermal_errors{0};

    uint64_t recoverable_errors{0};
    uint64_t fatal_errors{0};
    uint64_t recovery_attempts{0};
    uint64_t successful_recoveries{0};

    std::chrono::steady_clock::time_point last_error_time;
    std::chrono::steady_clock::time_point start_time;

    ErrorStatistics() :
        last_error_time(std::chrono::steady_clock::now()),
        start_time(std::chrono::steady_clock::now()) {}
};

/**
 * @brief Error recovery strategy configuration
 */
struct RecoveryConfig {
    int max_retry_attempts = 3;
    std::chrono::milliseconds retry_delay = std::chrono::milliseconds(1000);
    std::chrono::milliseconds backoff_multiplier = std::chrono::milliseconds(2);
    std::chrono::milliseconds max_retry_delay = std::chrono::milliseconds(30000);
    
    bool enable_automatic_recovery = true;
    bool enable_thermal_protection = true;
    bool enable_resource_monitoring = true;
    
    int thermal_threshold_celsius = 80;
    int critical_thermal_threshold_celsius = 85;
    double memory_pressure_threshold = 0.85; // 85% memory usage
    double cpu_pressure_threshold = 0.90;    // 90% CPU usage
};

/**
 * @brief Callback function types for error handling
 */
using ErrorCallback = std::function<void(const ErrorInfo&)>;
using RecoveryCallback = std::function<bool(const ErrorInfo&, RecoveryAction)>;
using MetricsCallback = std::function<void(const ErrorStatistics&)>;

/**
 * @brief Centralized error handling and recovery manager for RTSP module
 * 
 * This class provides comprehensive error handling, logging, and recovery
 * capabilities optimized for the RK3588 platform and Orange Pi hardware.
 */
class ErrorHandler {
public:
    /**
     * @brief Constructor
     * @param logger Shared logger instance
     * @param config Recovery configuration
     */
    explicit ErrorHandler(std::shared_ptr<Logger> logger = nullptr,
                         const RecoveryConfig& config = RecoveryConfig{});
    
    /**
     * @brief Destructor
     */
    ~ErrorHandler();
    
    // Core error handling methods
    
    /**
     * @brief Handle an error with automatic recovery
     * @param category Error category
     * @param severity Error severity
     * @param message Error message
     * @param component Component that generated the error
     * @param stream_id Stream identifier (optional)
     * @param context Additional context information
     * @return Recovery action taken
     */
    RecoveryAction handleError(ErrorCategory category,
                              ErrorSeverity severity,
                              const std::string& message,
                              const std::string& component,
                              const std::string& stream_id = "",
                              const std::unordered_map<std::string, std::string>& context = {});
    
    /**
     * @brief Handle an error with custom error info
     * @param error_info Detailed error information
     * @return Recovery action taken
     */
    RecoveryAction handleError(const ErrorInfo& error_info);
    
    /**
     * @brief Report a successful recovery
     * @param stream_id Stream identifier
     * @param action Recovery action that succeeded
     * @param details Recovery details
     */
    void reportRecoverySuccess(const std::string& stream_id,
                              RecoveryAction action,
                              const std::string& details = "");
    
    /**
     * @brief Report a failed recovery
     * @param stream_id Stream identifier
     * @param action Recovery action that failed
     * @param details Failure details
     */
    void reportRecoveryFailure(const std::string& stream_id,
                              RecoveryAction action,
                              const std::string& details = "");
    
    // Configuration and management
    
    /**
     * @brief Update recovery configuration
     * @param config New recovery configuration
     */
    void updateConfig(const RecoveryConfig& config);
    
    /**
     * @brief Get current recovery configuration
     * @return Current recovery configuration
     */
    RecoveryConfig getConfig() const;
    
    /**
     * @brief Set logger instance
     * @param logger Logger instance
     */
    void setLogger(std::shared_ptr<Logger> logger);
    
    // Callback management
    
    /**
     * @brief Set error callback
     * @param callback Error callback function
     */
    void setErrorCallback(ErrorCallback callback);
    
    /**
     * @brief Set recovery callback
     * @param callback Recovery callback function
     */
    void setRecoveryCallback(RecoveryCallback callback);
    
    /**
     * @brief Set metrics callback
     * @param callback Metrics callback function
     */
    void setMetricsCallback(MetricsCallback callback);
    
    // Statistics and monitoring
    
    /**
     * @brief Get error statistics
     * @return Current error statistics
     */
    ErrorStatistics getStatistics() const;
    
    /**
     * @brief Reset error statistics
     */
    void resetStatistics();
    
    /**
     * @brief Get error rate (errors per second)
     * @return Current error rate
     */
    double getErrorRate() const;
    
    /**
     * @brief Get recovery success rate
     * @return Recovery success rate (0.0 to 1.0)
     */
    double getRecoverySuccessRate() const;
    
    // System health monitoring
    
    /**
     * @brief Check system health
     * @return True if system is healthy
     */
    bool isSystemHealthy() const;
    
    /**
     * @brief Get thermal status
     * @return Current thermal status
     */
    std::string getThermalStatus() const;
    
    /**
     * @brief Get memory pressure status
     * @return Current memory pressure status
     */
    std::string getMemoryPressureStatus() const;

private:
    // Internal implementation details
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

} // namespace rtsp
} // namespace aibox
