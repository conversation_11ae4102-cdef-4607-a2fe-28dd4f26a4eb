#include <gtest/gtest.h>
#include <utils/http_utils.hpp>
#include <utils/json_utils.hpp>

class HttpUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Skip tests if HTTP utils not available
        if (!utils::HttpUtils::isAvailable()) {
            GTEST_SKIP() << "HTTP utils not available - CURL not compiled";
        }
    }
};

TEST_F(HttpUtilsTest, IsAvailable) {
    // This test will only run if HTTP utils is available
    EXPECT_TRUE(utils::HttpUtils::isAvailable());
}

TEST_F(HttpUtilsTest, MethodToString) {
    EXPECT_EQ(utils::HttpUtils::methodToString(utils::HttpMethod::GET), "GET");
    EXPECT_EQ(utils::HttpUtils::methodToString(utils::HttpMethod::POST), "POST");
    EXPECT_EQ(utils::HttpUtils::methodToString(utils::HttpMethod::PUT), "PUT");
    EXPECT_EQ(utils::HttpUtils::methodToString(utils::HttpMethod::DELETE), "DELETE");
    EXPECT_EQ(utils::HttpUtils::methodToString(utils::HttpMethod::PATCH), "PATCH");
    EXPECT_EQ(utils::HttpUtils::methodToString(utils::HttpMethod::HEAD), "HEAD");
    EXPECT_EQ(utils::HttpUtils::methodToString(utils::HttpMethod::OPTIONS), "OPTIONS");
}

TEST_F(HttpUtilsTest, StringToMethod) {
    EXPECT_EQ(utils::HttpUtils::stringToMethod("GET"), utils::HttpMethod::GET);
    EXPECT_EQ(utils::HttpUtils::stringToMethod("get"), utils::HttpMethod::GET);
    EXPECT_EQ(utils::HttpUtils::stringToMethod("POST"), utils::HttpMethod::POST);
    EXPECT_EQ(utils::HttpUtils::stringToMethod("post"), utils::HttpMethod::POST);
    EXPECT_EQ(utils::HttpUtils::stringToMethod("PUT"), utils::HttpMethod::PUT);
    EXPECT_EQ(utils::HttpUtils::stringToMethod("DELETE"), utils::HttpMethod::DELETE);
    EXPECT_EQ(utils::HttpUtils::stringToMethod("PATCH"), utils::HttpMethod::PATCH);
    EXPECT_EQ(utils::HttpUtils::stringToMethod("HEAD"), utils::HttpMethod::HEAD);
    EXPECT_EQ(utils::HttpUtils::stringToMethod("OPTIONS"), utils::HttpMethod::OPTIONS);

    // Invalid method
    EXPECT_FALSE(utils::HttpUtils::stringToMethod("INVALID").has_value());
}

TEST_F(HttpUtilsTest, UrlEncode) {
    EXPECT_EQ(utils::HttpUtils::urlEncode("hello world"), "hello%20world");
    EXPECT_EQ(utils::HttpUtils::urlEncode("<EMAIL>"), "test%40example.com");
    EXPECT_EQ(utils::HttpUtils::urlEncode("a+b=c"), "a%2Bb%3Dc");
    EXPECT_EQ(utils::HttpUtils::urlEncode("simple"), "simple");
    EXPECT_EQ(utils::HttpUtils::urlEncode(""), "");
}

TEST_F(HttpUtilsTest, UrlDecode) {
    EXPECT_EQ(utils::HttpUtils::urlDecode("hello%20world"), "hello world");
    EXPECT_EQ(utils::HttpUtils::urlDecode("test%40example.com"), "<EMAIL>");
    EXPECT_EQ(utils::HttpUtils::urlDecode("a%2Bb%3Dc"), "a+b=c");
    EXPECT_EQ(utils::HttpUtils::urlDecode("simple"), "simple");
    EXPECT_EQ(utils::HttpUtils::urlDecode(""), "");
    // Note: CURL doesn't decode '+' to space by default, that's form-specific behavior
    // EXPECT_EQ(utils::HttpUtils::urlDecode("hello+world"), "hello world");
}

TEST_F(HttpUtilsTest, BuildQueryString) {
    std::unordered_map<std::string, std::string> params;
    EXPECT_EQ(utils::HttpUtils::buildQueryString(params), "");

    params["key1"] = "value1";
    std::string result = utils::HttpUtils::buildQueryString(params);
    EXPECT_EQ(result, "key1=value1");

    params["key2"] = "value with spaces";
    result = utils::HttpUtils::buildQueryString(params);
    // Note: order might vary due to unordered_map
    EXPECT_TRUE(result.find("key1=value1") != std::string::npos);
    EXPECT_TRUE(result.find("key2=value%20with%20spaces") != std::string::npos);
    EXPECT_TRUE(result.find("&") != std::string::npos);
}

TEST_F(HttpUtilsTest, ParseUrl) {
    std::string scheme, host, path, query;
    int port;

    // Basic HTTPS URL
    EXPECT_TRUE(utils::HttpUtils::parseUrl("https://example.com/path?query=value",
                                         scheme, host, port, path, query));
    EXPECT_EQ(scheme, "https");
    EXPECT_EQ(host, "example.com");
    EXPECT_EQ(port, 443);
    EXPECT_EQ(path, "/path");
    EXPECT_EQ(query, "query=value");

    // HTTP URL with custom port
    EXPECT_TRUE(utils::HttpUtils::parseUrl("http://localhost:8080/api/v1",
                                         scheme, host, port, path, query));
    EXPECT_EQ(scheme, "http");
    EXPECT_EQ(host, "localhost");
    EXPECT_EQ(port, 8080);
    EXPECT_EQ(path, "/api/v1");
    EXPECT_EQ(query, "");

    // URL without path
    EXPECT_TRUE(utils::HttpUtils::parseUrl("https://example.com",
                                         scheme, host, port, path, query));
    EXPECT_EQ(scheme, "https");
    EXPECT_EQ(host, "example.com");
    EXPECT_EQ(port, 443);
    EXPECT_EQ(path, "/");
    EXPECT_EQ(query, "");

    // Invalid URL
    EXPECT_FALSE(utils::HttpUtils::parseUrl("invalid-url", scheme, host, port, path, query));
}

TEST_F(HttpUtilsTest, HttpConfig) {
    utils::HttpConfig config;

    // Test content type setters
    config.setContentTypeJson();
    EXPECT_EQ(config.headers["Content-Type"], "application/json");

    config.setContentTypeForm();
    EXPECT_EQ(config.headers["Content-Type"], "application/x-www-form-urlencoded");

    config.setContentTypeMultipart();
    EXPECT_EQ(config.headers["Content-Type"], "multipart/form-data");

    // Test authentication setters
    config.setBearerAuth("test-token");
    EXPECT_EQ(config.headers["Authorization"], "Bearer test-token");

    config.setBasicAuth("user", "pass");
    EXPECT_EQ(config.basicAuth, "user:pass");
}

TEST_F(HttpUtilsTest, HttpResponse) {
    utils::HttpResponse response;

    // Test status code checks
    response.statusCode = 200;
    EXPECT_TRUE(response.isSuccess());
    EXPECT_FALSE(response.isClientError());
    EXPECT_FALSE(response.isServerError());

    response.statusCode = 404;
    EXPECT_FALSE(response.isSuccess());
    EXPECT_TRUE(response.isClientError());
    EXPECT_FALSE(response.isServerError());

    response.statusCode = 500;
    EXPECT_FALSE(response.isSuccess());
    EXPECT_FALSE(response.isClientError());
    EXPECT_TRUE(response.isServerError());
}

// Integration tests (require internet connection)
class HttpUtilsIntegrationTest : public HttpUtilsTest {
protected:
    void SetUp() override {
        HttpUtilsTest::SetUp();
        // These tests require internet connection
        // Skip if running in CI without network access
    }
};

TEST_F(HttpUtilsIntegrationTest, SimpleGetRequest) {
    auto response = utils::HttpUtils::get("https://httpbin.org/get");

    EXPECT_TRUE(response.success);
    EXPECT_EQ(response.statusCode, 200);
    EXPECT_FALSE(response.body.empty());
    EXPECT_GT(response.responseTime, 0.0);

    // Parse response as JSON
    auto jsonResponse = utils::JsonUtils::parse(response.body);
    EXPECT_TRUE(jsonResponse.has_value());
    EXPECT_TRUE(jsonResponse->isObject());
}

TEST_F(HttpUtilsIntegrationTest, PostRequestWithJson) {
    utils::HttpConfig config;
    config.setContentTypeJson();

    utils::JsonValue data = utils::createObject();
    data["test"] = utils::JsonValue("value");
    data["number"] = utils::JsonValue(42);

    std::string jsonBody = utils::JsonUtils::stringify(data);
    auto response = utils::HttpUtils::post("https://httpbin.org/post", jsonBody, config);

    EXPECT_TRUE(response.success);
    EXPECT_EQ(response.statusCode, 200);
    EXPECT_FALSE(response.body.empty());

    // Verify the server received our JSON
    auto jsonResponse = utils::JsonUtils::parse(response.body);
    EXPECT_TRUE(jsonResponse.has_value());
    EXPECT_TRUE(jsonResponse->isObject());

    if (jsonResponse->asObject().find("json") != jsonResponse->asObject().end()) {
        auto receivedData = jsonResponse->asObject()["json"];
        EXPECT_TRUE(receivedData.isObject());
        EXPECT_EQ(receivedData.asObject()["test"].asString(), "value");
        EXPECT_EQ(receivedData.asObject()["number"].asNumber(), 42);
    }
}

TEST_F(HttpUtilsIntegrationTest, ConvenienceFunctions) {
    auto response = utils::httpGet("https://httpbin.org/get");
    EXPECT_TRUE(response.success);
    EXPECT_EQ(response.statusCode, 200);

    utils::HttpConfig config;
    config.setContentTypeJson();
    auto postResponse = utils::httpPost("https://httpbin.org/post", "{\"test\":\"data\"}", config);
    EXPECT_TRUE(postResponse.success);
    EXPECT_EQ(postResponse.statusCode, 200);
}

TEST_F(HttpUtilsIntegrationTest, ErrorHandling) {
    // Test with invalid URL
    auto response = utils::HttpUtils::get("https://invalid-domain-that-does-not-exist.com");
    EXPECT_FALSE(response.success);
    EXPECT_FALSE(response.errorMessage.empty());

    // Test 404 response
    auto response404 = utils::HttpUtils::get("https://httpbin.org/status/404");
    EXPECT_TRUE(response404.success); // Request succeeded, but got 404 status
    EXPECT_EQ(response404.statusCode, 404);
    EXPECT_TRUE(response404.isClientError());
}

TEST_F(HttpUtilsIntegrationTest, CustomHeaders) {
    utils::HttpConfig config;
    config.headers["X-Custom-Header"] = "TestValue";
    config.headers["User-Agent"] = "HttpUtilsTest/1.0";

    auto response = utils::HttpUtils::get("https://httpbin.org/headers", config);
    EXPECT_TRUE(response.success);
    EXPECT_EQ(response.statusCode, 200);

    // Parse response and check if our headers were sent
    auto jsonResponse = utils::JsonUtils::parse(response.body);
    EXPECT_TRUE(jsonResponse.has_value());

    if (jsonResponse->isObject() &&
        jsonResponse->asObject().find("headers") != jsonResponse->asObject().end()) {
        auto headers = jsonResponse->asObject()["headers"];
        if (headers.isObject()) {
            auto headerObj = headers.asObject();
            if (headerObj.find("X-Custom-Header") != headerObj.end()) {
                EXPECT_EQ(headerObj["X-Custom-Header"].asString(), "TestValue");
            }
        }
    }
}
