# X11 Server Scripts Usage Guide

This directory contains scripts to manage X11 server for GUI applications in your development environment.

## Scripts Overview

### 1. `start-x11.sh` - X11 Server Manager
Main script to start, stop, and manage the X11 server (Xvfb).

### 2. `install-x11-deps.sh` - Dependencies Installer
Installs required X11 packages for your system.

### 3. `create-render-node.sh` - GPU Render Node Creator
Creates missing DRI render nodes for GPU acceleration.

## Quick Start

### Option 1: Using Host X11 (Recommended)
If you're running on a system with X11 already available:

```bash
# Check if X11 is available
echo $DISPLAY
xset q

# If available, you can directly run GUI applications
# Your devcontainer is already configured to forward X11
```

### Option 2: Using Virtual X11 Server
If you need a virtual X11 server (headless environment):

```bash
# Start the X11 server
./start-x11.sh start

# Check status
./start-x11.sh status

# Test with a GUI application
./start-x11.sh test

# Stop the server
./start-x11.sh stop
```

## Detailed Usage

### start-x11.sh Commands

```bash
# Start X11 server
./start-x11.sh start

# Stop X11 server
./start-x11.sh stop

# Restart X11 server
./start-x11.sh restart

# Show status
./start-x11.sh status

# Test X11 functionality
./start-x11.sh test
```

### Environment Variables

You can customize the X11 server behavior:

```bash
# Set custom display number
export DISPLAY_NUM=:100
./start-x11.sh start

# Set custom screen resolution
export SCREEN_RESOLUTION=1280x720x24
./start-x11.sh start

# Both together
DISPLAY_NUM=:101 SCREEN_RESOLUTION=2560x1440x24 ./start-x11.sh start
```

### Installing Dependencies

If you need to install X11 dependencies:

```bash
# Install all required packages
sudo ./install-x11-deps.sh

# Show help
./install-x11-deps.sh --help
```

## Running GUI Applications

### With Host X11 (Default Setup)
```bash
# Your devcontainer should already have DISPLAY set
echo $DISPLAY  # Should show something like :0

# Run Qt applications
./your-qt-app

# Run other GUI applications
firefox
gedit
```

### With Virtual X11 Server
```bash
# Start X11 server first
./start-x11.sh start

# Set display in your terminal
export DISPLAY=:99

# Run GUI applications
./your-qt-app
xeyes
xclock
```

## Troubleshooting

### Common Issues

1. **"cannot connect to X server"**
   ```bash
   # Check if X11 is running
   ./start-x11.sh status
   
   # Check DISPLAY variable
   echo $DISPLAY
   
   # Start X11 if needed
   ./start-x11.sh start
   ```

2. **"No protocol specified"**
   ```bash
   # Fix X11 authentication
   xhost +local:
   # Or restart X11 server
   ./start-x11.sh restart
   ```

3. **Missing render node for GPU**
   ```bash
   # Create missing render node
   ./create-render-node.sh
   ```

4. **Window manager not working**
   ```bash
   # Start window manager manually
   fluxbox &
   # Or
   openbox &
   ```

### Checking GPU Acceleration

```bash
# Check OpenGL info
glxinfo | grep "OpenGL renderer"

# Check DRI devices
ls -la /dev/dri/

# Test GPU acceleration
glxgears
```

## Integration with DevContainer

Your `.devcontainer/devcontainer.json` is configured to:
- Forward X11 display from host
- Mount X11 socket
- Provide GPU access through DRI
- Set proper environment variables

The container should automatically have GUI support when built.

## Files Created

- `/tmp/x11-server.log` - X11 server log file
- `/tmp/xvfb.pid` - Xvfb process ID file

## Tips

1. **For development**: Use host X11 forwarding (default setup)
2. **For CI/CD**: Use virtual X11 server with `start-x11.sh`
3. **For testing**: Use `./start-x11.sh test` to verify setup
4. **For debugging**: Check logs in `/tmp/x11-server.log`

## Window Managers Available

- **Fluxbox**: Lightweight, fast
- **Openbox**: Minimal, configurable
- **Xfwm4**: Full-featured (if installed)

Choose based on your needs. Fluxbox is recommended for development.
