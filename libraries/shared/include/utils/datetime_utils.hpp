#pragma once
#include <string>
#include <chrono>
#include <optional>
#include <ctime>

namespace utils {
    /**
     * @brief DateTime utilities for parsing, formatting, and manipulating date/time values
     */
    class DateTimeUtils {
    public:
        // Type aliases for convenience
        using TimePoint = std::chrono::system_clock::time_point;
        using Duration = std::chrono::system_clock::duration;
        using Milliseconds = std::chrono::milliseconds;
        using Seconds = std::chrono::seconds;
        using Minutes = std::chrono::minutes;
        using Hours = std::chrono::hours;

        /**
         * @brief Get current timestamp as time_point
         * @return Current system time
         */
        static TimePoint now();

        /**
         * @brief Get current timestamp as Unix timestamp (seconds since epoch)
         * @return Unix timestamp in seconds
         */
        static int64_t nowUnix();

        /**
         * @brief Get current timestamp as Unix timestamp in milliseconds
         * @return Unix timestamp in milliseconds
         */
        static int64_t nowUnixMs();

        /**
         * @brief Convert time_point to Unix timestamp (seconds)
         * @param tp Time point to convert
         * @return Unix timestamp in seconds
         */
        static int64_t toUnix(const TimePoint& tp);

        /**
         * @brief Convert time_point to Unix timestamp (milliseconds)
         * @param tp Time point to convert
         * @return Unix timestamp in milliseconds
         */
        static int64_t toUnixMs(const TimePoint& tp);

        /**
         * @brief Convert Unix timestamp to time_point
         * @param timestamp Unix timestamp in seconds
         * @return Time point
         */
        static TimePoint fromUnix(int64_t timestamp);

        /**
         * @brief Convert Unix timestamp in milliseconds to time_point
         * @param timestamp Unix timestamp in milliseconds
         * @return Time point
         */
        static TimePoint fromUnixMs(int64_t timestamp);

        /**
         * @brief Format time_point to string using specified format
         * @param tp Time point to format
         * @param format Format string (strftime format)
         * @return Formatted string
         */
        static std::string format(const TimePoint& tp, const std::string& format = "%Y-%m-%d %H:%M:%S");

        /**
         * @brief Format current time to string using specified format
         * @param format Format string (strftime format)
         * @return Formatted string
         */
        static std::string formatNow(const std::string& format = "%Y-%m-%d %H:%M:%S");

        /**
         * @brief Parse string to time_point using specified format
         * @param timeStr Time string to parse
         * @param format Format string (strptime format)
         * @return Optional time point (nullopt if parsing fails)
         */
        static std::optional<TimePoint> parse(const std::string& timeStr, const std::string& format = "%Y-%m-%d %H:%M:%S");

        /**
         * @brief Parse ISO 8601 string to time_point
         * @param isoStr ISO 8601 string (e.g., "2023-12-25T10:30:45Z" or "2023-12-25T10:30:45+00:00")
         * @return Optional time point (nullopt if parsing fails)
         */
        static std::optional<TimePoint> parseISO8601(const std::string& isoStr);

        /**
         * @brief Format time_point to ISO 8601 string
         * @param tp Time point to format
         * @param includeMs Whether to include milliseconds
         * @return ISO 8601 formatted string
         */
        static std::string toISO8601(const TimePoint& tp, bool includeMs = false);

        /**
         * @brief Format current time to ISO 8601 string
         * @param includeMs Whether to include milliseconds
         * @return ISO 8601 formatted string
         */
        static std::string nowISO8601(bool includeMs = false);

        /**
         * @brief Add duration to time_point
         * @param tp Base time point
         * @param duration Duration to add
         * @return New time point
         */
        static TimePoint add(const TimePoint& tp, const Duration& duration);

        /**
         * @brief Subtract duration from time_point
         * @param tp Base time point
         * @param duration Duration to subtract
         * @return New time point
         */
        static TimePoint subtract(const TimePoint& tp, const Duration& duration);

        /**
         * @brief Calculate difference between two time points
         * @param tp1 First time point
         * @param tp2 Second time point
         * @return Duration (tp1 - tp2)
         */
        static Duration diff(const TimePoint& tp1, const TimePoint& tp2);

        /**
         * @brief Calculate difference in seconds
         * @param tp1 First time point
         * @param tp2 Second time point
         * @return Difference in seconds
         */
        static int64_t diffSeconds(const TimePoint& tp1, const TimePoint& tp2);

        /**
         * @brief Calculate difference in milliseconds
         * @param tp1 First time point
         * @param tp2 Second time point
         * @return Difference in milliseconds
         */
        static int64_t diffMs(const TimePoint& tp1, const TimePoint& tp2);

        /**
         * @brief Check if a time point is before another
         * @param tp1 First time point
         * @param tp2 Second time point
         * @return true if tp1 is before tp2
         */
        static bool isBefore(const TimePoint& tp1, const TimePoint& tp2);

        /**
         * @brief Check if a time point is after another
         * @param tp1 First time point
         * @param tp2 Second time point
         * @return true if tp1 is after tp2
         */
        static bool isAfter(const TimePoint& tp1, const TimePoint& tp2);

        /**
         * @brief Sleep for specified duration
         * @param duration Duration to sleep
         */
        static void sleep(const Duration& duration);

        /**
         * @brief Sleep for specified milliseconds
         * @param ms Milliseconds to sleep
         */
        static void sleepMs(int64_t ms);

        /**
         * @brief Sleep for specified seconds
         * @param seconds Seconds to sleep
         */
        static void sleepSeconds(int64_t seconds);

        /**
         * @brief Get start of day for given time point
         * @param tp Time point
         * @return Time point at start of day (00:00:00)
         */
        static TimePoint startOfDay(const TimePoint& tp);

        /**
         * @brief Get end of day for given time point
         * @param tp Time point
         * @return Time point at end of day (23:59:59.999)
         */
        static TimePoint endOfDay(const TimePoint& tp);

        /**
         * @brief Check if two time points are on the same day
         * @param tp1 First time point
         * @param tp2 Second time point
         * @return true if both time points are on the same day
         */
        static bool isSameDay(const TimePoint& tp1, const TimePoint& tp2);

        /**
         * @brief Get elapsed time since a time point in human readable format
         * @param tp Time point to compare with current time
         * @return Human readable elapsed time (e.g., "2 hours ago", "3 days ago")
         */
        static std::string getElapsedTime(const TimePoint& tp);

        /**
         * @brief Validate if a string matches a date/time format
         * @param timeStr Time string to validate
         * @param format Format string to validate against
         * @return true if string matches format
         */
        static bool isValidFormat(const std::string& timeStr, const std::string& format);

    private:
        /**
         * @brief Helper function to convert tm to time_point
         * @param tm Time structure
         * @return Time point
         */
        static TimePoint tmToTimePoint(const std::tm& tm);

        /**
         * @brief Helper function to convert time_point to tm
         * @param tp Time point
         * @return Time structure
         */
        static std::tm timePointToTm(const TimePoint& tp);
    };

    // Convenience functions for ease of use

    /**
     * @brief Get current timestamp (convenience function)
     * @return Current system time
     */
    DateTimeUtils::TimePoint now();

    /**
     * @brief Get current Unix timestamp (convenience function)
     * @return Unix timestamp in seconds
     */
    int64_t nowUnix();

    /**
     * @brief Format current time (convenience function)
     * @param format Format string
     * @return Formatted string
     */
    std::string formatNow(const std::string& format = "%Y-%m-%d %H:%M:%S");

    /**
     * @brief Parse time string (convenience function)
     * @param timeStr Time string to parse
     * @param format Format string
     * @return Optional time point
     */
    std::optional<DateTimeUtils::TimePoint> parseTime(const std::string& timeStr, const std::string& format = "%Y-%m-%d %H:%M:%S");

    /**
     * @brief Sleep for milliseconds (convenience function)
     * @param ms Milliseconds to sleep
     */
    void sleepMs(int64_t ms);
}
