#pragma once

#include <string>
#include <vector>
#include <chrono>
#include <optional>

namespace server::models {

/**
 * @brief Base response model for AI inference
 */
struct InferenceResponse {
    bool success;
    std::string message;
    std::optional<std::string> error_code;
    std::chrono::milliseconds processing_time;
    std::string model_version;

    InferenceResponse() : success(false), processing_time(0) {}
    virtual ~InferenceResponse() = default;
};

/**
 * @brief Bounding box for object detection
 */
struct BoundingBox {
    float x;
    float y;
    float width;
    float height;
    float confidence;

    BoundingBox(float x = 0, float y = 0, float w = 0, float h = 0, float conf = 0)
        : x(x), y(y), width(w), height(h), confidence(conf) {}
};

/**
 * @brief Face detection result
 */
struct FaceDetection {
    BoundingBox bbox;
    std::vector<float> landmarks;  // Facial landmarks if available
    float quality_score;           // Face quality score

    FaceDetection() : quality_score(0.0f) {}
};

/**
 * @brief Face embedding/features
 */
struct FaceEmbedding {
    std::vector<float> features;
    float confidence;
    std::string face_id;  // Optional face ID if recognized

    FaceEmbedding() : confidence(0.0f) {}
};

/**
 * @brief ArcFace inference response
 */
struct ArcFaceResponse : public InferenceResponse {
    std::vector<FaceDetection> faces;
    std::vector<FaceEmbedding> embeddings;
    int total_faces_detected;

    ArcFaceResponse() : total_faces_detected(0) {}
};

/**
 * @brief Object detection result
 */
struct ObjectDetection {
    BoundingBox bbox;
    std::string class_name;
    int class_id;

    ObjectDetection() : class_id(-1) {}
};

/**
 * @brief YOLO detection response
 */
struct YoloResponse : public InferenceResponse {
    std::vector<ObjectDetection> detections;
    int total_objects_detected;
    std::vector<std::string> detected_classes;

    YoloResponse() : total_objects_detected(0) {}
};

/**
 * @brief Health check response
 */
struct HealthResponse {
    bool healthy;
    std::string status;
    std::chrono::system_clock::time_point timestamp;
    std::string version;

    HealthResponse() : healthy(false), timestamp(std::chrono::system_clock::now()) {}
};

/**
 * @brief API information response
 */
struct ApiInfoResponse {
    std::string version;
    std::vector<std::string> endpoints;
    std::string description;
    std::chrono::system_clock::time_point server_start_time;

    ApiInfoResponse() : server_start_time(std::chrono::system_clock::now()) {}
};

} // namespace server::models
