# RGA Pre-Processing Module - Detailed Implementation Plan

## AI Context & System Requirements

### Platform Context (RK3588 RGA Optimized)

```
Platform: Orange Pi 5 Plus/Ultra with RK3588 SoC
- CPU: 4x Cortex-A76 @ 2.4GHz + 4x Cortex-A55 @ 1.8GHz  
- Memory: 4GB/8GB LPDDR4X-4224 (CRITICAL: Memory-constrained environment)
- Storage: 240GB SSD
- Hardware Accelerators: 
  * RGA (2D Raster Graphics Accelerator) - PRIMARY for image processing
  * VPU (Video Processing Unit) - Upstream integration (MPP decoder)
  * NPU (6 TOPS) - Downstream target (face detection/recognition)
- OS: Ubuntu 22.04 LTS ARM64
- Cross-compilation: aarch64-linux-gnu toolchain required
- Thermal Management: 85°C max operating, throttle at 80°C
```

### Technology Stack Context (Hardware-First Approach)

```
RGA Pre-Processing Technology Choices (Mandatory for RK3588):
- Image Processing: RockChip RGA (2D Graphics Accelerator) - MANDATORY
- Memory Management: DMABUF zero-copy operations - CRITICAL
- Hardware Integration: RGA + NPU coordination
- Build System: CMake with RK3588-specific optimizations
- Threading: Hardware-aware worker pools (coordinate with 8-core architecture)
- Image Operations: Hardware-accelerated scaling and format conversion
- Performance: RGA utilization >80% target, minimize CPU overhead

FORBIDDEN Technologies:
- Software-only image processing (CPU usage too high)
- Memory copying operations (bandwidth limited)
- Generic image processing libraries (OpenCV for processing)
- CPU-based scaling algorithms (hardware acceleration mandatory)
```

### Resource Allocation Context (Critical Constraints)

```
RGA Pre-Processing Resource Assignment:
- CPU Cores: Coordinate with existing allocations
  * Core 0-1 (A55): UI Client + System Services (AVOID)
  * Core 2-3 (A55): RTSP + MPP coordination
  * Core 4-7 (A76): Available for RGA worker threads (lightweight)
  
- Memory Allocation (STRICT LIMITS):
  * 4GB Config: RGA Processing ≤600MB (includes DMABUF pools)
  * 8GB Config: RGA Processing ≤1.2GB (includes DMABUF pools)
  * Buffer Pool Sizing: Dynamic based on stream count and AI model requirements
  
- Hardware Resource Coordination:
  * RGA: Primary user (image scaling and format conversion)
  * VPU: Upstream integration (receive DMABUF from MPP)
  * NPU: Downstream integration (provide prepared input buffers)
  
- Performance Targets (Platform Limits):
  * 4GB: 8 concurrent streams → AI-ready format
  * 8GB: 16 concurrent streams → AI-ready format
  * Latency: <20ms processing time per frame
  * RGA Utilization: 80-90% optimal range
```

### Integration Context (AI Box Pipeline)

```
Module Dependencies & Interfaces:
- INPUT: MPP Decoder Module (DMABUF YUV frames via queues)
- OUTPUT: Face Detection Module (DMABUF BGR/RGB frames for NPU)
- COORDINATION: Other modules (memory management, thermal control)

Data Flow Pipeline:
MPP Decode → YUV DMABUF → RGA Scale/Convert → BGR DMABUF → NPU Face AI

Resource Sharing Strategy:
- Hardware: RGA primary, coordinate with VPU and NPU scheduling
- Memory: DMABUF pools shared between MPP → RGA → NPU
- Timing: Frame synchronization across pipeline stages
- Error Handling: Cascade error recovery upstream/downstream

Critical Integration Points:
1. DMABUF import from MPP decoder module
2. RGA hardware operation coordination
3. DMABUF export to NPU face detection module
4. Multi-resolution output for different AI models
5. Frame rate adaptation based on NPU processing capacity
```

### Development Constraints Context

```
Mandatory Requirements:
- Hardware-in-the-Loop: All testing on actual Orange Pi 5 Plus/Ultra
- RK3588-First Development: All decisions prioritize RGA capabilities
- Zero-Copy Architecture: DMABUF mandatory, no frame copying
- AI Model Optimization: Output format must match NPU requirements exactly
- Resource Cooperation: Coordinate with MPP decoder and NPU modules
- Hardware Validation: RGA, memory bandwidth, NPU handoff testing required

Performance Constraints:
- Memory Bandwidth: Limited, zero-copy operations critical
- Thermal Budget: Shared across all modules, adaptive scaling required
- Power Efficiency: Embedded platform, optimize for sustained operation
- AI Pipeline Latency: Total preprocessing <20ms for real-time face detection
- Hardware Coordination: RGA operations must not block NPU processing

Quality Gates:
- Orange Pi hardware validation for every major component
- AI model accuracy validation with processed frames
- Thermal cycling tests (idle → max load → thermal throttling)
- Memory leak detection under 72-hour stress testing
- Integration testing with full AI Box pipeline (RTSP → MPP → RGA → NPU)
```

### Current Project Status Context

```
RGA Pre-Processing Module Status:
- Phase: Implementation Planning (Phase 1 pending)
- Dependencies: MPP Decoder Module (DMABUF YUV output interface)
- Critical Path: Foundation → RGA Interface → Image Processing → NPU Integration
- Hardware Access: Orange Pi 5 Plus/Ultra required for validation

Key Decisions Made:
1. RockChip RGA library as primary image processor (hardware acceleration)
2. DMABUF zero-copy memory architecture between MPP → RGA → NPU
3. Multi-resolution output for different AI model requirements
4. Hardware resource coordination framework
5. Thermal-aware adaptive performance scaling

Integration Requirements:
- MPP Module: DMABUF YUV frame input interface design
- NPU Module: DMABUF BGR/RGB frame output protocol for face detection
- AI Models: Exact input format specifications (resolution, color space)
- System Monitor: Thermal and performance telemetry integration
```

### Risk Context (Critical for RK3588)

```
Critical Risks (Address Immediately):
- R2: RGA hardware driver compatibility and DMABUF integration complexity
- R1: NPU input format requirements vs RGA output capabilities mismatch
- R3: Memory bandwidth limitations affecting pipeline throughput

High Priority Risks:
- R4: Thermal throttling under sustained RGA + NPU operation
- R5: Image quality degradation affecting AI model accuracy
- R6: Frame synchronization issues in multi-stream scenarios

Mitigation Strategies (Mandatory):
- Early prototype testing on Orange Pi with real AI models
- RGA capability validation with exact NPU input requirements
- Hardware resource scheduling with thermal monitoring
- Image quality validation with face detection accuracy metrics
- Software fallback mechanisms for hardware failures (degraded performance)

Testing Requirements:
- All components tested on target Orange Pi hardware
- AI model accuracy validation with RGA-processed frames
- Thermal cycling validation (cold boot → sustained AI processing)
- Memory pressure testing at platform limits
- Hardware failure simulation and recovery validation
```

## Task Overview Summary

| Task ID                            | Task Name                      | Priority | Status    | Estimated Hours | Dependencies | Assignee     | Platform Focus      |
| ---------------------------------- | ------------------------------ | -------- | --------- | --------------- | ------------ | ------------ | ------------------- |
| **Phase 1: Foundation Setup**      |                                |          |           | **3-6 hours**   |              |              |                     |
| 1.1                                | Project Structure Creation     | Critical | ⏳ Pending | 2-3h            | MPP Module   | Lead Dev     | RK3588 RGA System   |
| 1.2                                | CMake & RGA Dependencies Setup | Critical | ⏳ Pending | 1-3h            | 1.1          | DevOps       | RockChip + DMABUF   |
| **Phase 2: Core Implementation**   |                                |          |           | **38-54 hours** |              |              |                     |
| 2.1                                | RGA Interface Manager          | Critical | ⏳ Pending | 8-12h           | 1.2          | Graphics Dev | RGA Optimization    |
| 2.2                                | DMABUF Buffer Manager          | Critical | ⏳ Pending | 6-8h            | 2.1          | System Dev   | Zero-Copy Pipeline  |
| 2.3                                | Image Scaler                   | Critical | ⏳ Pending | 8-12h           | 2.1,2.2      | Image Dev    | AI Model Targets    |
| 2.4                                | Format Converter               | Critical | ⏳ Pending | 8-12h           | 2.1,2.2      | Image Dev    | NPU Format Prep     |
| 2.5                                | Batch Processor                | High     | ⏳ Pending | 8-10h           | 2.3,2.4      | Performance  | Multi-Stream Coord  |
| **Phase 3: Integration & Testing** |                                |          |           | **16-24 hours** |              |              |                     |
| 3.1                                | MPP Integration                | Critical | ⏳ Pending | 6-8h            | Phase 2      | Integration  | Pipeline Connection |
| 3.2                                | NPU Format Validation          | Critical | ⏳ Pending | 6-8h            | 3.1          | AI Engineer  | Model Compatibility |
| 3.3                                | Unit Testing Suite             | High     | ⏳ Pending | 4-8h            | Phase 2      | QA + Devs    | Hardware Validation |
| **Phase 4: AI Pipeline Features**  |                                |          |           | **14-20 hours** |              |              |                     |
| 4.1                                | Multi-Resolution Processing    | Medium   | ⏳ Pending | 6-8h            | Phase 3      | AI Engineer  | Model Optimization  |
| 4.2                                | Quality Optimization           | Medium   | ⏳ Pending | 8-12h           | 4.1          | Image Dev    | AI Accuracy Tuning  |
| **Phase 5: Production Readiness**  |                                |          |           | **10-16 hours** |              |              |                     |
| 5.1                                | AI Accuracy Validation         | High     | ⏳ Pending | 6-10h           | Phase 4      | AI Engineer  | Model Performance   |
| 5.2                                | Documentation & Examples       | Medium   | ⏳ Pending | 4-6h            | Phase 4      | Tech Writer  | RGA Integration     |

### Status Legend

- ⏳ **Pending**: Not started
- 🔄 **In Progress**: Currently being worked on
- ✅ **Completed**: Task finished and tested
- ⚠️ **Blocked**: Waiting for dependencies or resources
- ❌ **Failed**: Task failed and needs rework

### Resource Allocation Summary

- **Total Estimated Time**: 81-120 hours (6-9 weeks)
- **Critical Path**: Tasks 1.1 → 1.2 → 2.1 → 2.2 → 2.3,2.4 → 3.1 → 3.2 → 5.1
- **RK3588 RGA Focus**: 85% of tasks include hardware-specific optimizations
- **Hardware Testing Required**: Tasks 2.1, 2.2, 2.3, 2.4, 3.2, 4.2, 5.1

## Milestone Tracking

| Milestone                     | Target Date | Status    | Completion % | Key Deliverables                                    | Risk Level |
| ----------------------------- | ----------- | --------- | ------------ | --------------------------------------------------- | ---------- |
| **M1: Foundation Complete**   | Week 1      | ⏳ Pending | 0%           | Build system, RGA integration, basic structure      | 🟢 Low      |
| **M2: Core Processing Ready** | Week 3      | ⏳ Pending | 0%           | RGA interface, scaling, format conversion working   | 🟠 High     |
| **M3: Pipeline Integration**  | Week 5      | ⏳ Pending | 0%           | MPP→RGA→NPU pipeline, multi-stream support          | 🟡 Medium   |
| **M4: AI Pipeline Optimized** | Week 7      | ⏳ Pending | 0%           | Multi-resolution, quality optimization, AI accuracy | 🟡 Medium   |
| **M5: Production Ready**      | Week 9      | ⏳ Pending | 0%           | AI validation, stress testing, documentation        | 🟢 Low      |

### Milestone Success Criteria

#### M1: Foundation Complete

- [ ] CMake builds successfully with RGA dependencies
- [ ] RockChip RGA library linked and functional
- [ ] DMABUF support detected and configured
- [ ] Basic project structure created following patterns

#### M2: Core Processing Ready

- [ ] Single frame scaling and format conversion using RGA
- [ ] DMABUF zero-copy pipeline working
- [ ] RGA utilization >60% for test operations
- [ ] Memory usage within platform limits

#### M3: Pipeline Integration

- [ ] MPP YUV frames processed into NPU-ready format
- [ ] Multiple concurrent streams (8 for 4GB, 16 for 8GB)
- [ ] Integration with downstream NPU module
- [ ] Frame timing and synchronization working

#### M4: AI Pipeline Optimized

- [ ] Multi-resolution output for different AI models
- [ ] Image quality optimization for face detection accuracy
- [ ] Performance optimization targets met
- [ ] Resource coordination with NPU processing

#### M5: Production Ready

- [ ] AI model accuracy validation passed
- [ ] 72-hour stress test with full pipeline
- [ ] Complete documentation and integration guides
- [ ] RK3588 RGA compliance validation 100%

## Risk Assessment & Mitigation

| Risk ID | Risk Description                             | Probability | Impact | Risk Level | Mitigation Strategy                                     | Owner        |
| ------- | -------------------------------------------- | ----------- | ------ | ---------- | ------------------------------------------------------- | ------------ |
| **R1**  | NPU input format vs RGA output mismatch      | Medium      | High   | 🟠 High     | Early format validation, NPU model requirements testing | AI Engineer  |
| **R2**  | RGA hardware driver and DMABUF complexity    | High        | High   | 🔴 Critical | Incremental implementation, copy fallback mode          | Graphics Dev |
| **R3**  | Memory bandwidth limitations pipeline impact | High        | Medium | 🟡 Medium   | Memory optimization, adaptive quality scaling           | System Dev   |
| **R4**  | Thermal throttling under RGA + NPU load      | Medium      | Medium | 🟡 Medium   | Thermal monitoring, adaptive performance scaling        | System Dev   |
| **R5**  | Image quality degradation affecting AI       | Medium      | High   | 🟠 High     | Quality validation, AI accuracy testing                 | Image Dev    |
| **R6**  | Frame sync issues in multi-stream scenarios  | Low         | High   | 🟡 Medium   | Robust timing implementation, comprehensive testing     | Pipeline Dev |
| **R7**  | RGA hardware failure and recovery            | Low         | High   | 🟡 Medium   | Error detection, software fallback mechanisms           | System Dev   |
| **R8**  | Integration complexity with AI pipeline      | Medium      | Medium | 🟡 Medium   | Clear interface design, incremental integration testing | Integration  |

### Risk Mitigation Actions

#### Critical Priority (Address Immediately)

- **R2**: Design RGA architecture with software fallback from day one
- **R1**: Set up NPU format validation environment early
- **R5**: Establish AI accuracy testing framework

#### High Priority (Address in Phase 1-2)

- **R3**: Implement memory pressure monitoring and adaptive algorithms
- **R4**: Develop thermal-aware performance scaling system
- **R6**: Create comprehensive timing and synchronization test suite

#### Medium Priority (Monitor and Address as Needed)

- **R7**: Design robust error detection and recovery mechanisms
- **R8**: Establish clear module interfaces and integration protocols

## Quick Reference

### Current Status Dashboard

```
📊 Overall Progress: 0% (0/12 tasks completed)
🎯 Current Phase: Phase 1 - Foundation Setup
⏰ Next Milestone: M1 - Foundation Complete (Week 1)
🔥 Critical Path: Task 1.1 (Project Structure Creation)
⚠️ Top Risk: RGA hardware driver and DMABUF complexity
🏗️ Platform Focus: RK3588 RGA and AI pipeline optimization
```

### Key Contacts

- **Technical Lead**: [Name] - Overall architecture and RK3588 optimization
- **Graphics Developer**: [Name] - RGA integration and hardware acceleration
- **System Developer**: [Name] - DMABUF and hardware integration
- **Image Developer**: [Name] - Scaling, format conversion, quality optimization
- **AI Engineer**: [Name] - NPU format validation and AI accuracy testing

### Quick Commands

```bash
# Check task status
grep -E "⏳|🔄|✅|⚠️|❌" implementation-plan.md

# Update task status (example)
sed -i 's/| 2.1 | .* | ⏳ Pending |/| 2.1 | ... | 🔄 In Progress |/' implementation-plan.md

# View critical path
grep -A1 -B1 "Critical\|High" implementation-plan.md
```

## Executive Summary

This document provides a comprehensive implementation plan for the RGA Pre-Processing Module optimized for Orange Pi 5 Plus/Ultra with RK3588 SoC. The module will leverage hardware image processing capabilities through RockChip's 2D Raster Graphics Accelerator (RGA) to prepare video frames for AI processing with zero-copy DMABUF integration.

## Project Structure Analysis

### Current Architecture (RK3588 RGA Optimized)

- **Platform**: Orange Pi 5 Plus/Ultra with RK3588 SoC and RGA
- **Integration Point**: `libraries/rga-preprocessor` (new module)
- **Hardware Acceleration**: RK3588 RGA, DMABUF, DRM allocator
- **Primary Dependencies**: RockChip RGA library, DRM/DRI, Linux DMABUF
- **Memory Architecture**: Zero-copy pipeline with DMABUF sharing
- **Threading Model**: Lightweight worker threads coordinating with RGA hardware

### Integration Points

- Input from MPP decoder's DMABUF YUV frames
- Output to NPU face detection module via DMABUF BGR/RGB frames
- Hardware resource coordination with VPU and NPU
- Integration with existing error handling and logging systems
- Configuration management following project patterns

## Detailed Task Breakdown

### Phase 1: Project Structure Setup

#### Task 1.1: Create Library Structure

**Priority**: Critical  
**Estimated Time**: 2-3 hours  
**Dependencies**: MPP Module DMABUF output

**Files to Create:**

```
libraries/rga-preprocessor/
├── CMakeLists.txt
├── include/
│   └── rga_preprocessor/
│       ├── rga_interface_manager.hpp
│       ├── dmabuf_buffer_manager.hpp
│       ├── image_scaler.hpp
│       ├── format_converter.hpp
│       ├── batch_processor.hpp
│       ├── rga_preprocessor.hpp
│       └── rga_types.hpp
├── src/
│   ├── rga_interface_manager.cpp
│   ├── dmabuf_buffer_manager.cpp
│   ├── image_scaler.cpp
│   ├── format_converter.cpp
│   ├── batch_processor.cpp
│   └── rga_preprocessor.cpp
└── tests/
    ├── CMakeLists.txt
    ├── test_rga_interface.cpp
    ├── test_scaling.cpp
    ├── test_format_conversion.cpp
    └── test_integration.cpp
```

**Configuration Requirements:**

- CMake integration with RockChip RGA library detection
- DMABUF and DRM library linking  
- ARM64 cross-compilation support optimized for RK3588
- RGA hardware feature detection and configuration
- Memory management optimized for embedded platform constraints
- Integration with existing shared utilities and logging

#### Task 1.2: Update Root CMakeLists.txt and Dependencies

**Priority**: Critical  
**Estimated Time**: 1-3 hours  
**Dependencies**: Task 1.1

**Changes Required:**

- Add `add_subdirectory(libraries/rga-preprocessor)` to root CMakeLists.txt
- Configure RockChip RGA library detection and linking
- Set up DMABUF and DRM dependencies
- Configure RGA hardware feature detection
- Ensure proper linking with MPP decoder module
- Add RK3588-specific compiler optimizations
- Configure memory management for 4GB/8GB RAM variants

### Phase 2: Core Components Implementation

#### Task 2.1: RGA Interface Manager Implementation

**Priority**: Critical  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 1.2

**Implementation Details:**

```cpp
// rga_interface_manager.hpp
struct RGAConfig {
    int rga_version;           // RGA3 for RK3588
    bool enable_async_mode;
    uint32_t max_operations;
    uint32_t timeout_ms;
};

class RGAInterfaceManager {
public:
    struct RGAOperation {
        rga_buffer_t src_buffer;
        rga_buffer_t dst_buffer;
        im_rect src_rect;
        im_rect dst_rect;
        int usage;  // Scaling, rotation, format conversion flags
        int job_id;
        std::chrono::steady_clock::time_point submit_time;
    };
    
    bool initialize(const RGAConfig& config);
    void shutdown();
    
    int submitOperation(const RGAOperation& operation);
    bool waitForCompletion(int job_id, std::chrono::milliseconds timeout);
    void synchronizeAll();
    
    // Capability queries
    bool supportsFormat(int format);
    bool supportsOperation(int src_format, int dst_format, int usage);
    std::vector<int> getSupportedFormats();
    
private:
    RGAConfig config_;
    std::atomic<int> next_job_id_;
    std::unordered_map<int, RGAOperation> active_operations_;
    std::mutex operations_mutex_;
};
```

**Features:**

- RGA hardware initialization and configuration
- Operation submission and tracking
- Capability detection and validation
- Hardware resource management and monitoring
- Asynchronous operation support
- Error detection and recovery

#### Task 2.2: DMABUF Buffer Manager Implementation

**Priority**: Critical  
**Estimated Time**: 6-8 hours  
**Dependencies**: Task 2.1

**Core Functionality:**

```cpp
// dmabuf_buffer_manager.hpp
struct RGABuffer {
    int dmabuf_fd;
    uint32_t width;
    uint32_t height;
    uint32_t format;
    uint32_t stride;
    size_t size;
    rga_buffer_handle_t rga_handle;
    std::atomic<int> ref_count;
    bool is_input;  // Input from MPP or output to NPU
};

class DMABUFBufferManager {
public:
    std::shared_ptr<RGABuffer> importBuffer(int dmabuf_fd, 
                                           uint32_t width, uint32_t height, 
                                           uint32_t format);
    
    std::shared_ptr<RGABuffer> allocateBuffer(uint32_t width, uint32_t height, 
                                             uint32_t format);
    
    int exportBuffer(const std::shared_ptr<RGABuffer>& buffer);
    void releaseBuffer(const std::shared_ptr<RGABuffer>& buffer);
    
    // Buffer pool management
    void createBufferPool(uint32_t width, uint32_t height, 
                         uint32_t format, size_t pool_size);
    std::shared_ptr<RGABuffer> getFromPool(uint32_t width, uint32_t height, 
                                          uint32_t format);
    
private:
    struct BufferPool {
        std::queue<std::shared_ptr<RGABuffer>> available;
        std::vector<std::shared_ptr<RGABuffer>> allocated;
        std::mutex mutex;
        uint32_t width, height, format;
    };
    
    std::unordered_map<std::string, BufferPool> buffer_pools_;
    int drm_fd_;
    std::shared_ptr<RGAInterfaceManager> rga_manager_;
};
```

**Key Features:**

- DMABUF import from MPP decoder
- DMABUF export to NPU module  
- Reference counting for safe buffer sharing
- Buffer pools for different formats and sizes
- RGA buffer handle management
- Memory pressure monitoring

#### Task 2.3: Image Scaler Implementation

**Priority**: Critical  
**Estimated Time**: 8-12 hours  
**Dependencies**: Tasks 2.1, 2.2

**Core Functionality:**

```cpp
// image_scaler.hpp
struct ScalingParams {
    uint32_t src_width, src_height;
    uint32_t dst_width, dst_height;
    int scaling_algorithm;  // Nearest, bilinear, bicubic
    bool preserve_aspect_ratio;
    int background_color;  // For letterboxing
};

class ImageScaler {
public:
    struct ScaleTask {
        std::shared_ptr<RGABuffer> input_buffer;
        std::shared_ptr<RGABuffer> output_buffer;
        ScalingParams params;
        std::string camera_id;
        uint64_t timestamp;
    };
    
    bool scaleImage(const ScaleTask& task);
    bool scaleImageAsync(const ScaleTask& task, int& job_id);
    
    // Multi-resolution scaling
    bool scaleToMultipleResolutions(
        const std::shared_ptr<RGABuffer>& input,
        const std::vector<ScalingParams>& target_resolutions,
        std::vector<std::shared_ptr<RGABuffer>>& outputs);
    
    // Aspect ratio utilities
    ScalingParams calculateLetterboxParams(uint32_t src_width, uint32_t src_height,
                                          uint32_t dst_width, uint32_t dst_height);
    
private:
    std::shared_ptr<RGAInterfaceManager> rga_manager_;
    std::shared_ptr<DMABUFBufferManager> buffer_manager_;
    
    bool validateScalingParams(const ScalingParams& params);
    int selectOptimalAlgorithm(const ScalingParams& params);
};
```

**Features:**

- Hardware-accelerated scaling using RGA
- Multiple interpolation algorithms
- Aspect ratio preservation with letterboxing
- Multi-resolution output for different AI models
- Batch scaling operations
- Quality optimization for AI input

#### Task 2.4: Format Converter Implementation

**Priority**: Critical  
**Estimated Time**: 8-12 hours  
**Dependencies**: Tasks 2.1, 2.2

**Core Functionality:**

```cpp
// format_converter.hpp
enum class ColorSpace {
    YUV420_NV12,   // Input from MPP
    BGR888,        // NPU input format
    RGB888,        // Alternative NPU format
    YUV444,
    RGBA8888
};

struct ConversionParams {
    ColorSpace src_format;
    ColorSpace dst_format;
    bool apply_normalization;
    float scale_factor;
    float offset_value;
};

class FormatConverter {
public:
    struct ConversionTask {
        std::shared_ptr<RGABuffer> input_buffer;
        std::shared_ptr<RGABuffer> output_buffer;
        ConversionParams params;
        std::string camera_id;
        uint64_t timestamp;
    };
    
    bool convertFormat(const ConversionTask& task);
    bool convertFormatAsync(const ConversionTask& task, int& job_id);
    
    // Combined operations
    bool scaleAndConvert(const std::shared_ptr<RGABuffer>& input,
                        const std::shared_ptr<RGABuffer>& output,
                        const ScalingParams& scale_params,
                        const ConversionParams& conv_params);
    
    // NPU-specific conversions
    bool prepareForNPU(const std::shared_ptr<RGABuffer>& input,
                      const std::shared_ptr<RGABuffer>& output,
                      const std::string& model_name);
    
private:
    std::shared_ptr<RGAInterfaceManager> rga_manager_;
    std::shared_ptr<DMABUFBufferManager> buffer_manager_;
    
    std::unordered_map<std::string, ConversionParams> model_requirements_;
    
    bool validateConversionParams(const ConversionParams& params);
    uint32_t getRGAFormat(ColorSpace color_space);
};
```

**Features:**

- Hardware-accelerated format conversion using RGA
- YUV to RGB/BGR conversion for NPU input
- Normalization and scaling during conversion
- NPU model-specific format preparation
- Combined scaling and conversion operations
- Quality validation for AI processing

#### Task 2.5: Batch Processor Implementation

**Priority**: High  
**Estimated Time**: 8-10 hours  
**Dependencies**: Tasks 2.3, 2.4

**Core Functionality:**

```cpp
// batch_processor.hpp
class BatchProcessor {
public:
    struct ProcessingTask {
        std::shared_ptr<RGABuffer> input_buffer;
        std::string camera_id;
        uint64_t timestamp;
        std::vector<std::string> target_models;  // AI models to prepare for
    };
    
    struct ProcessedFrame {
        std::string camera_id;
        uint64_t timestamp;
        std::unordered_map<std::string, std::shared_ptr<RGABuffer>> model_outputs;
    };
    
    void start(size_t worker_count);
    void stop();
    
    void submitTask(const ProcessingTask& task);
    bool getProcessedFrame(ProcessedFrame& frame, std::chrono::milliseconds timeout);
    
    // Configuration
    void configureModelRequirements(const std::string& model_name,
                                   const ScalingParams& scale_params,
                                   const ConversionParams& conv_params);
    
private:
    void workerThreadFunction(size_t worker_id);
    bool processTask(const ProcessingTask& task, ProcessedFrame& result);
    
    std::vector<std::thread> worker_threads_;
    ThreadSafeQueue<ProcessingTask> input_queue_;
    ThreadSafeQueue<ProcessedFrame> output_queue_;
    std::atomic<bool> should_stop_;
    
    std::shared_ptr<ImageScaler> scaler_;
    std::shared_ptr<FormatConverter> converter_;
    std::shared_ptr<DMABUFBufferManager> buffer_manager_;
    
    struct ModelConfig {
        ScalingParams scale_params;
        ConversionParams conv_params;
    };
    std::unordered_map<std::string, ModelConfig> model_configs_;
};
```

**Features:**

- Multi-threaded batch processing
- Multi-model output preparation
- Work queue management
- Dynamic model configuration
- Performance monitoring and statistics
- Load balancing across worker threads

### Phase 3: Integration and Testing

#### Task 3.1: MPP Integration

**Priority**: Critical  
**Estimated Time**: 6-8 hours  
**Dependencies**: Phase 2 completion

**Integration Points:**

- Connect to MPP decoder's DMABUF YUV output queue
- Implement frame metadata preservation
- Handle stream format changes and reconfiguration
- Coordinate timing and synchronization between modules
- Implement backpressure handling from RGA to MPP

#### Task 3.2: NPU Format Validation

**Priority**: Critical  
**Estimated Time**: 6-8 hours  
**Dependencies**: Task 3.1

**Validation Requirements:**

- Test format compatibility with actual NPU face detection models
- Validate image quality and AI accuracy with processed frames
- Benchmark processing latency and throughput
- Test multi-resolution output for different model requirements
- Validate DMABUF handoff to NPU module

#### Task 3.3: Unit Testing Suite

**Priority**: High  
**Estimated Time**: 4-8 hours  
**Dependencies**: Phase 2 completion

**Test Coverage:**

- RGA interface and operation management
- DMABUF buffer management and sharing
- Image scaling functionality
- Format conversion accuracy
- Batch processing performance
- Integration with MPP and NPU modules

### Phase 4: AI Pipeline Features

#### Task 4.1: Multi-Resolution Processing

**Priority**: Medium  
**Estimated Time**: 6-8 hours  
**Dependencies**: Phase 3 completion

**Features:**

- Simultaneous output at multiple resolutions for different AI models
- Efficient resource utilization for multi-target processing
- Dynamic resolution adaptation based on AI model requirements
- Load balancing for optimal performance

#### Task 4.2: Quality Optimization

**Priority**: Medium  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 4.1

**Implementation:**

- Image quality assessment and optimization for AI accuracy
- Adaptive processing parameters based on image content
- Quality vs performance tradeoff algorithms
- AI model accuracy validation and tuning

### Phase 5: Production Readiness

#### Task 5.1: AI Accuracy Validation

**Priority**: High  
**Estimated Time**: 6-10 hours  
**Dependencies**: Phase 4 completion

**Testing Requirements:**

- Face detection accuracy with RGA-processed frames
- Performance validation under sustained load
- Quality regression testing
- AI model compatibility validation

#### Task 5.2: Documentation & Examples

**Priority**: Medium  
**Estimated Time**: 4-6 hours  
**Dependencies**: Phase 4 completion

**Documentation Requirements:**

- RGA integration and configuration guide
- Image processing best practices
- Performance tuning and optimization guide
- AI model integration examples
- Troubleshooting guide

## Configuration Requirements

### Build Dependencies

```cmake
# RockChip RGA library
find_library(RGA_LIBRARY
    NAMES rga
    PATHS /usr/lib/aarch64-linux-gnu
    REQUIRED)

# DMABUF and DRM libraries
find_package(PkgConfig REQUIRED)
pkg_check_modules(DRM REQUIRED libdrm)

# Threading support
find_package(Threads REQUIRED)

# Hardware detection
check_include_file("linux/dma-buf.h" HAVE_DMABUF)
check_include_file("RockchipRga.h" HAVE_RGA)
```

### Runtime Configuration

```json
{
  "rga_preprocessor": {
    "max_concurrent_operations": 16,
    "worker_thread_count": 4,
    "buffer_pool_config": {
      "input_pools": [
        {"width": 1920, "height": 1080, "format": "NV12", "count": 16},
        {"width": 1280, "height": 720, "format": "NV12", "count": 8}
      ],
      "output_pools": [
        {"width": 640, "height": 640, "format": "BGR888", "count": 32},
        {"width": 320, "height": 320, "format": "BGR888", "count": 16}
      ]
    },
    "model_requirements": {
      "face_detection_v1": {
        "input_width": 640,
        "input_height": 640,
        "input_format": "BGR888",
        "preserve_aspect": true
      },
      "face_recognition_v1": {
        "input_width": 112,
        "input_height": 112,
        "input_format": "RGB888",
        "preserve_aspect": false
      }
    },
    "performance": {
      "enable_batch_processing": true,
      "max_batch_size": 8,
      "quality_priority": "balanced"
    }
  }
}
```

## Success Criteria

### Functional Requirements

- [ ] Hardware-accelerated image processing using RK3588 RGA
- [ ] Zero-copy DMABUF pipeline from MPP to NPU
- [ ] Support for 16+ concurrent streams on 8GB platform
- [ ] Multi-resolution output for different AI models
- [ ] Format conversion from YUV to BGR/RGB for NPU input

### Performance Requirements

- [ ] RGA utilization >80% for optimal throughput
- [ ] Processing latency <20ms for 640x640 output
- [ ] Memory usage <1.2GB for 16 concurrent streams (8GB config)
- [ ] Support 16 streams @ 1080p input → 640x640 NPU format
- [ ] AI accuracy maintained >95% compared to reference processing

### Quality Requirements

- [ ] Comprehensive unit and integration test coverage (>90%)
- [ ] Hardware validation on Orange Pi 5 Plus/Ultra
- [ ] AI accuracy validation with face detection models
- [ ] Documentation completeness for RGA integration
- [ ] Code review and RK3588 compliance validation

## Timeline Estimation

| Phase                          | Duration  | Dependencies |
| ------------------------------ | --------- | ------------ |
| Phase 1: Foundation Setup      | 1 week    | MPP Module   |
| Phase 2: Core Implementation   | 3-4 weeks | Phase 1      |
| Phase 3: Integration & Testing | 2-3 weeks | Phase 2      |
| Phase 4: AI Pipeline Features  | 1-2 weeks | Phase 3      |
| Phase 5: Production Readiness  | 1-2 weeks | Phase 4      |

**Total Estimated Duration: 8-12 weeks**

## Next Steps

1. **Immediate Actions (Week 1)**
   - Set up project structure and RGA dependencies (Tasks 1.1, 1.2)
   - Begin RGA interface manager implementation (Task 2.1)
   - Set up hardware testing environment with NPU models

2. **Short-term Goals (Weeks 2-4)**
   - Complete core component implementation
   - Implement DMABUF zero-copy pipeline
   - Begin MPP integration testing

3. **Medium-term Goals (Weeks 5-8)**
   - Complete integration with MPP and NPU modules
   - Implement AI pipeline features and optimization
   - Comprehensive testing and validation

4. **Long-term Goals (Weeks 9-12)**
   - AI accuracy validation and tuning
   - Documentation completion
   - Production deployment validation

This implementation plan provides a comprehensive roadmap for developing the RGA Pre-Processing Module with hardware acceleration, zero-copy DMABUF integration, and AI model optimization tailored for RK3588 platform capabilities. 