#include <utils/http_utils.hpp>
#include <utils/json_utils.hpp>
#include <iostream>
#include <iomanip>

void printResponse(const utils::HttpResponse& response) {
    std::cout << "Status: " << response.statusCode << " " << response.statusText << std::endl;
    std::cout << "Success: " << (response.success ? "Yes" : "No") << std::endl;
    std::cout << "Response Time: " << std::fixed << std::setprecision(3) << response.responseTime << "s" << std::endl;
    
    if (!response.errorMessage.empty()) {
        std::cout << "Error: " << response.errorMessage << std::endl;
    }
    
    std::cout << "Headers:" << std::endl;
    for (const auto& header : response.headers) {
        std::cout << "  " << header.first << ": " << header.second << std::endl;
    }
    
    std::cout << "Body: " << response.body.substr(0, 200);
    if (response.body.length() > 200) {
        std::cout << "... (truncated)";
    }
    std::cout << std::endl << std::endl;
}

int main() {
    std::cout << "=== HTTP Utils Demo ===" << std::endl;
    
    // Check if HTTP utils is available
    if (!utils::HttpUtils::isAvailable()) {
        std::cout << "HTTP utils not available - CURL not compiled" << std::endl;
        return 1;
    }
    
    std::cout << "HTTP utils is available!" << std::endl << std::endl;

    // 1. Simple GET request
    std::cout << "1. Simple GET Request:" << std::endl;
    auto response1 = utils::HttpUtils::get("https://httpbin.org/get");
    printResponse(response1);

    // 2. GET request with query parameters
    std::cout << "2. GET with Query Parameters:" << std::endl;
    std::unordered_map<std::string, std::string> params = {
        {"param1", "value1"},
        {"param2", "value with spaces"}
    };
    std::string queryString = utils::HttpUtils::buildQueryString(params);
    auto response2 = utils::HttpUtils::get("https://httpbin.org/get?" + queryString);
    printResponse(response2);

    // 3. POST request with JSON data
    std::cout << "3. POST with JSON Data:" << std::endl;
    utils::HttpConfig postConfig;
    postConfig.setContentTypeJson();
    
    // Create JSON data using json_utils
    utils::JsonValue jsonData = utils::createObject();
    jsonData["name"] = utils::JsonValue("John Doe");
    jsonData["age"] = utils::JsonValue(30);
    jsonData["email"] = utils::JsonValue("<EMAIL>");
    
    std::string jsonBody = utils::JsonUtils::stringify(jsonData);
    auto response3 = utils::HttpUtils::post("https://httpbin.org/post", jsonBody, postConfig);
    printResponse(response3);

    // 4. PUT request with authentication
    std::cout << "4. PUT with Bearer Authentication:" << std::endl;
    utils::HttpConfig authConfig;
    authConfig.setContentTypeJson();
    authConfig.setBearerAuth("your-token-here");
    
    utils::JsonValue updateData = utils::createObject();
    updateData["status"] = utils::JsonValue("updated");
    updateData["timestamp"] = utils::JsonValue(1640995200);
    
    std::string updateBody = utils::JsonUtils::stringify(updateData);
    auto response4 = utils::HttpUtils::put("https://httpbin.org/put", updateBody, authConfig);
    printResponse(response4);

    // 5. DELETE request
    std::cout << "5. DELETE Request:" << std::endl;
    auto response5 = utils::HttpUtils::del("https://httpbin.org/delete");
    printResponse(response5);

    // 6. Custom headers
    std::cout << "6. Request with Custom Headers:" << std::endl;
    utils::HttpConfig customConfig;
    customConfig.headers["X-Custom-Header"] = "CustomValue";
    customConfig.headers["X-API-Version"] = "v1";
    customConfig.userAgent = "C-AIBOX-Demo/1.0";
    
    auto response6 = utils::HttpUtils::get("https://httpbin.org/headers", customConfig);
    printResponse(response6);

    // 7. Error handling - invalid URL
    std::cout << "7. Error Handling (Invalid URL):" << std::endl;
    auto response7 = utils::HttpUtils::get("https://invalid-url-that-does-not-exist.com");
    printResponse(response7);

    // 8. Timeout configuration
    std::cout << "8. Timeout Configuration (5 seconds):" << std::endl;
    utils::HttpConfig timeoutConfig;
    timeoutConfig.timeoutMs = 5000; // 5 seconds
    
    auto response8 = utils::HttpUtils::get("https://httpbin.org/delay/3", timeoutConfig);
    printResponse(response8);

    // 9. URL encoding/decoding
    std::cout << "9. URL Encoding/Decoding:" << std::endl;
    std::string original = "Hello World! @#$%^&*()";
    std::string encoded = utils::HttpUtils::urlEncode(original);
    std::string decoded = utils::HttpUtils::urlDecode(encoded);
    
    std::cout << "Original: " << original << std::endl;
    std::cout << "Encoded:  " << encoded << std::endl;
    std::cout << "Decoded:  " << decoded << std::endl;
    std::cout << "Match:    " << (original == decoded ? "Yes" : "No") << std::endl << std::endl;

    // 10. URL parsing
    std::cout << "10. URL Parsing:" << std::endl;
    std::string testUrl = "https://api.example.com:8080/v1/users?page=1&limit=10";
    std::string scheme, host, path, query;
    int port;
    
    if (utils::HttpUtils::parseUrl(testUrl, scheme, host, port, path, query)) {
        std::cout << "URL: " << testUrl << std::endl;
        std::cout << "Scheme: " << scheme << std::endl;
        std::cout << "Host: " << host << std::endl;
        std::cout << "Port: " << port << std::endl;
        std::cout << "Path: " << path << std::endl;
        std::cout << "Query: " << query << std::endl;
    } else {
        std::cout << "Failed to parse URL" << std::endl;
    }
    std::cout << std::endl;

    // 11. Convenience functions
    std::cout << "11. Using Convenience Functions:" << std::endl;
    auto response11 = utils::httpGet("https://httpbin.org/user-agent");
    printResponse(response11);

    // 12. Response status checks
    std::cout << "12. Response Status Checks:" << std::endl;
    auto response12 = utils::HttpUtils::get("https://httpbin.org/status/404");
    std::cout << "Status Code: " << response12.statusCode << std::endl;
    std::cout << "Is Success: " << (response12.isSuccess() ? "Yes" : "No") << std::endl;
    std::cout << "Is Client Error: " << (response12.isClientError() ? "Yes" : "No") << std::endl;
    std::cout << "Is Server Error: " << (response12.isServerError() ? "Yes" : "No") << std::endl;
    std::cout << std::endl;

    std::cout << "=== Demo Complete ===" << std::endl;
    return 0;
}
