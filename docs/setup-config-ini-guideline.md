# Environment Utils Migration: .env to config.ini

## Overview

The `env_utils` library has been updated to support INI configuration files (`config.ini`) as the primary configuration format, while maintaining backward compatibility with `.env` files.

## What Changed

### New Features

1. **INI File Support**: Added support for structured configuration using INI format with sections
2. **Section-based Configuration**: Variables are now organized in sections (e.g., `[database]`, `[api]`, `[logging]`)
3. **Dot Notation Access**: Access variables using dot notation (e.g., `database.host`, `api.port`)

### New Methods

- `EnvUtils::loadConfigFromFile(filepath)` - Load from INI file
- `EnvUtils::loadConfigFromString(content)` - Load INI content from string
- `utils::loadConfig(filepath)` - Convenience function for INI files

### Deprecated Methods (Still Working)

- `EnvUtils::loadEnvFromFile()` - Use `loadConfigFromFile()` instead
- `EnvUtils::loadEnvFromString()` - Use `loadConfigFromString()` instead
- `utils::loadEnv()` - Use `utils::loadConfig()` instead

## Configuration Format Comparison

### Old Format (.env)
```bash
# .env file
DATABASE_HOST=localhost
DATABASE_PORT=5432
API_HOST=0.0.0.0
API_PORT=8080
DEBUG=false
```

### New Format (config.ini)
```ini
# config.ini file
[database]
host=localhost
port=5432

[api]
host=0.0.0.0
port=8080
debug=false
```

## Usage Examples

### Loading Configuration

```cpp
#include <utils/env_utils.hpp>

// New way (recommended)
if (utils::loadConfig("config.ini")) {
    std::string dbHost = utils::getEnv("database.host", "localhost");
    std::string apiPort = utils::getEnv("api.port", "8080");
}

// Old way (still works but deprecated)
if (utils::loadEnv(".env")) {
    std::string dbHost = utils::getEnv("DATABASE_HOST", "localhost");
}
```

### Accessing Configuration Values

```cpp
// Section-based access (new)
std::string dbHost = utils::getEnv("database.host", "localhost");
std::string dbPort = utils::getEnv("database.port", "5432");
std::string apiKey = utils::getEnv("api.key", "");

// Traditional access (old, still works)
std::string dbHost = utils::getEnv("DATABASE_HOST", "localhost");
```

## Migration Guide

### Step 1: Create config.ini

Convert your existing `.env` file to `config.ini` format:

```bash
# From .env
DATABASE_HOST=localhost
DATABASE_PORT=5432
API_KEY=secret

# To config.ini
[database]
host=localhost
port=5432

[api]
key=secret
```

### Step 2: Update Code

```cpp
// Old code
utils::loadEnv(".env");
std::string host = utils::getEnv("DATABASE_HOST");

// New code
utils::loadConfig("config.ini");
std::string host = utils::getEnv("database.host");
```

### Step 3: Test

Run your application to ensure all configuration values are loaded correctly.

## Benefits of INI Format

1. **Better Organization**: Group related settings in sections
2. **Clearer Structure**: Hierarchical organization of configuration
3. **Standard Format**: INI is a widely recognized configuration format
4. **Comments Support**: Both `#` and `;` comment styles supported
5. **Backward Compatibility**: Old `.env` files still work

## Testing

All existing tests pass, plus new tests for INI functionality:

```bash
cd build
./libraries/shared/shared_env_utils_test
```

## Example Files

- `config.ini` - Sample INI configuration file
- `test_config_demo.cpp` - Demo program showing INI usage

## Backward Compatibility

- All existing `.env` functionality continues to work
- Deprecated methods show compiler warnings but still function
- No breaking changes to existing APIs
- Mixed usage is supported (can load both .env and .ini files)

## Future Plans

- The `.env` support will be maintained for backward compatibility
- New projects should use `config.ini` format
- Consider migrating existing projects when convenient
