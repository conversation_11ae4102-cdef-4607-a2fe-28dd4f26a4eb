# MPP Video Decoding Module Task Definition

## Overview
This module is responsible for decoding the H.264/H.265 NAL units received from the RTSP input module into raw video frames using RK3588's hardware video decoding capabilities through the Media Process Platform (MPP).

## Requirements
- Decode H.264/H.265 video streams using hardware acceleration
- Support multiple concurrent decoding processes for different camera streams
- Implement zero-copy data flow using DMABUF
- Manage decoder contexts for each camera stream
- Handle various video resolutions and formats

## Technical Approach
1. **MPP Initialization and Configuration**:
   - Create and initialize MPP contexts for each camera stream
   - Configure MPP for hardware decoding of H.264/H.265
   - Set up MPP to use DMABUF for zero-copy operations

2. **Decoding Pipeline**:
   - Implement a decoding worker thread/pool
   - Process NAL units from the input queue
   - Configure decoder for appropriate codec type (H.264/H.265)
   - Set up proper buffer management for efficient memory use

3. **DMABUF Integration**:
   - Configure MPP to output decoded frames as DMABUF
   - Implement DMABUF handling for zero-copy operations
   - Export DMABUF file descriptors for subsequent processing stages

4. **Performance Optimization**:
   - Implement asynchronous decoding to maximize throughput
   - Balance decoding resources across multiple streams
   - Monitor and handle decoding errors

## Interfaces
- **Input**: Queue of H.264/H.265 NAL units from RTSP module
- **Output**: DMABUF file descriptors pointing to decoded YUV frames (typically NV12 format)

## Dependencies
- Rockchip MPP library (libmpp.so)
- C++ standard library
- DMABUF/DRM libraries for buffer management

## Performance Considerations
- Efficient use of VPU hardware to maximize the number of streams
- Proper error handling to prevent hardware lockups
- Monitoring of decoding performance and latency
- Balance between throughput and memory usage
