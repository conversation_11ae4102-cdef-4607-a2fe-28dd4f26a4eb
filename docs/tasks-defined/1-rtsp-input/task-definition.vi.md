# Đ<PERSON>nh Nghĩa Tác Vụ Module Đầu Vào RTSP

## Tổng Quan
Tác vụ này bao gồm việc tạo một module để kết nối và nhận dữ liệu video từ nhiều luồng camera RTSP. Module sẽ chịu trách nhiệm thiết lập kết nối đến các camera, xử lý các vấn đề mạng, và chuẩn bị các gói video thô cho việc xử lý tiếp theo.

## Yêu Cầu
- Thiết lập và duy trì kết nối đến nhiều luồng camera RTSP đồng thời
- Xử lý các vấn đề mạng và logic kết nối lại
- Trích xuất các đơn vị NAL H.264/H.265 từ các luồng RTSP
- Triển khai hệ thống hàng đợi thread-safe cho mỗi luồng camera
- Hỗ trợ nhiều luồng camera với độ phân giải và tốc độ khung hình khác nhau

## Phương Pháp Kỹ Thuật
1. **Quản Lý Kết Nối RTSP**:
   - Sử dụng FFmpeg (libavformat, libavcodec) hoặc GStreamer (rtspsrc, rtph264depay/rtph265depay) để xử lý luồng RTSP
   - Triển khai lớp quản lý kết nối có thể xử lý nhiều URL RTSP
   - Thêm cơ chế thử lại để xử lý gián đoạn mạng

2. **Quản Lý Thread**:
   - Tạo thread riêng cho mỗi luồng camera
   - Triển khai quản lý thread pool để kiểm soát việc sử dụng tài nguyên

3. **Trích Xuất Gói**:
   - Trích xuất các đơn vị NAL H.264/H.265 từ các luồng
   - Tạo hệ thống buffer cho các gói đã trích xuất
   - Liên kết metadata (ID camera, timestamp) với mỗi gói

4. **Quản Lý Hàng Đợi**:
   - Triển khai hàng đợi thread-safe cho mỗi luồng camera
   - Thêm giới hạn kích thước hàng đợi có thể cấu hình để ngăn tràn bộ nhớ
   - Bao gồm giám sát độ đầy hàng đợi để phát hiện nghẽn xử lý

## Giao Diện
- **Đầu vào**: URL RTSP, tham số kết nối
- **Đầu ra**: Hàng đợi các đơn vị NAL với metadata liên quan

## Phụ Thuộc
- Thư viện FFmpeg hoặc GStreamer
- Thư viện chuẩn C++ (threads, mutexes, condition variables)

## Cân Nhắc Hiệu Suất
- Giảm thiểu sử dụng CPU cho các thao tác mạng
- Xử lý hiệu quả việc đệm gói để ngăn các vấn đề bộ nhớ
- Giám sát và ghi log các chỉ số chất lượng kết nối
