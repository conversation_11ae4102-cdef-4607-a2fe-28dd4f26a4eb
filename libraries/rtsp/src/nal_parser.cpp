#include "rtsp/nal_parser.hpp"
#include <sched.h>
#include <unistd.h>
#include <iostream>
#include <algorithm>

namespace aibox {
namespace rtsp {

// NALParser implementation
NALParser::NALParser(const NALParsingConfig& config)
    : config_(config)
    , hardware_status_(HardwareAccelStatus::DISABLED)
    , should_stop_(false) {
    
    // Optimize for RK3588
    optimizeForRK3588();
    
    // Initialize hardware acceleration if enabled
    if (config_.enable_mpp_parsing) {
        initializeHardwareAcceleration();
    }
    
    // Validate configuration
    if (!validateConfig()) {
        throw std::invalid_argument("Invalid NAL parsing configuration");
    }
    
    std::cout << "[NALParser] Initialized with hardware acceleration: " 
              << (hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE ? "enabled" : "disabled") << std::endl;
}

NALParser::~NALParser() {
    should_stop_ = true;
    
    // Wait for worker threads
    for (auto& thread : worker_threads_) {
        if (thread && thread->joinable()) {
            thread->join();
        }
    }
    
    cleanupHardwareAcceleration();
    std::cout << "[NALParser] Destroyed" << std::endl;
}

void NALParser::updateConfig(const NALParsingConfig& new_config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_ = new_config;
    
    // Reinitialize hardware if needed
    if (new_config.enable_mpp_parsing && hardware_status_ == HardwareAccelStatus::DISABLED) {
        initializeHardwareAcceleration();
    } else if (!new_config.enable_mpp_parsing && hardware_status_ != HardwareAccelStatus::DISABLED) {
        cleanupHardwareAcceleration();
    }
}

const NALParsingConfig& NALParser::getConfig() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return config_;
}

std::vector<NALUnit> NALParser::parseRTPPayload(const std::vector<uint8_t>& payload, 
                                               const StreamId& stream_id,
                                               Timestamp timestamp) {
    std::vector<NALUnit> nal_units;
    
    if (payload.empty()) {
        return nal_units;
    }
    
    // Check for NAL unit start codes (0x00 0x00 0x00 0x01 or 0x00 0x00 0x01)
    size_t pos = 0;
    while (pos < payload.size()) {
        // Find next start code
        size_t start_code_pos = findNextStartCode(payload, pos);
        if (start_code_pos == std::string::npos) {
            break;
        }
        
        // Find end of NAL unit
        size_t next_start_code = findNextStartCode(payload, start_code_pos + 4);
        size_t nal_end = (next_start_code != std::string::npos) ? next_start_code : payload.size();
        
        // Extract NAL unit data
        std::vector<uint8_t> nal_data(payload.begin() + start_code_pos + 4, payload.begin() + nal_end);
        
        if (!nal_data.empty()) {
            NALUnit nal_unit;
            if (parseNALUnit(nal_data, nal_unit)) {
                nal_unit.stream_id = stream_id;
                nal_unit.timestamp = timestamp;
                nal_units.push_back(nal_unit);
                
                // Update statistics
                updateParsingStatistics(nal_unit, hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE);
                
                // Invoke callback
                std::lock_guard<std::mutex> lock(callback_mutex_);
                if (nal_callback_) {
                    nal_callback_(nal_unit);
                }
            }
        }
        
        pos = nal_end;
    }
    
    return nal_units;
}

bool NALParser::parseNALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    if (data.empty()) {
        return false;
    }
    
    // Validate NAL header if enabled
    if (config_.validate_nal_headers && !validateNALHeader(data)) {
        statistics_.invalid_nal_units++;
        return false;
    }
    
    // Detect NAL unit type
    nal_unit.type = detectNALUnitType(data);
    if (nal_unit.type == NALUnitType::UNKNOWN) {
        statistics_.invalid_nal_units++;
        return false;
    }
    
    // Copy data
    nal_unit.data = data;
    
    // Detect if this is a keyframe
    nal_unit.is_keyframe = isKeyframe(nal_unit);
    
    // Process with hardware if available
    bool hardware_processed = false;
    if (hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE && config_.enable_mpp_parsing) {
        hardware_processed = processWithMPP(data, nal_unit);
    }
    
    if (!hardware_processed) {
        // Fallback to software processing
        hardware_processed = processWithSoftware(data, nal_unit);
    }
    
    statistics_.nal_units_parsed++;
    return hardware_processed;
}

NALUnitType NALParser::detectNALUnitType(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return NALUnitType::UNKNOWN;
    }
    
    uint8_t nal_header = data[0];
    
    // Check if this is H.264 or H.265
    if ((nal_header & 0x80) == 0) {
        // H.264 NAL unit
        uint8_t nal_type = nal_header & 0x1F;
        switch (nal_type) {
            case 1: return NALUnitType::H264_NON_IDR;
            case 5: return NALUnitType::H264_IDR;
            case 7: return NALUnitType::H264_SPS;
            case 8: return NALUnitType::H264_PPS;
            default: return NALUnitType::UNKNOWN;
        }
    } else {
        // H.265 NAL unit
        uint8_t nal_type = (nal_header >> 1) & 0x3F;
        switch (nal_type) {
            case 19: return NALUnitType::H265_IDR_W_RADL;
            case 20: return NALUnitType::H265_IDR_N_LP;
            case 32: return NALUnitType::H265_VPS;
            case 33: return NALUnitType::H265_SPS;
            case 34: return NALUnitType::H265_PPS;
            default: return NALUnitType::UNKNOWN;
        }
    }
}

bool NALParser::isKeyframe(const NALUnit& nal_unit) {
    switch (nal_unit.type) {
        case NALUnitType::H264_IDR:
        case NALUnitType::H265_IDR_W_RADL:
        case NALUnitType::H265_IDR_N_LP:
            return true;
        default:
            return false;
    }
}

bool NALParser::extractStreamInfo(const std::vector<NALUnit>& nal_units, VideoStreamInfo& info) {
    bool info_updated = false;
    
    for (const auto& nal_unit : nal_units) {
        if (updateStreamInfo(nal_unit, info)) {
            info_updated = true;
        }
    }
    
    return info_updated;
}

bool NALParser::updateStreamInfo(const NALUnit& nal_unit, VideoStreamInfo& info) {
    bool updated = false;
    
    switch (nal_unit.type) {
        case NALUnitType::H264_SPS:
            if (parseSPS(nal_unit.data, info)) {
                info.codec = VideoCodec::H264;
                info.has_sps = true;
                info.sps_data = nal_unit.data;
                updated = true;
                statistics_.sps_units_found++;
            }
            break;
            
        case NALUnitType::H264_PPS:
            if (parsePPS(nal_unit.data, info)) {
                info.has_pps = true;
                info.pps_data = nal_unit.data;
                updated = true;
                statistics_.pps_units_found++;
            }
            break;
            
        case NALUnitType::H265_VPS:
            if (parseVPS(nal_unit.data, info)) {
                info.codec = VideoCodec::H265;
                info.has_vps = true;
                info.vps_data = nal_unit.data;
                updated = true;
            }
            break;
            
        case NALUnitType::H265_SPS:
            if (parseSPS(nal_unit.data, info)) {
                info.codec = VideoCodec::H265;
                info.has_sps = true;
                info.sps_data = nal_unit.data;
                updated = true;
                statistics_.sps_units_found++;
            }
            break;
            
        case NALUnitType::H265_PPS:
            if (parsePPS(nal_unit.data, info)) {
                info.has_pps = true;
                info.pps_data = nal_unit.data;
                updated = true;
                statistics_.pps_units_found++;
            }
            break;
            
        case NALUnitType::H264_IDR:
        case NALUnitType::H265_IDR_W_RADL:
        case NALUnitType::H265_IDR_N_LP:
            statistics_.idr_frames_found++;
            break;
            
        default:
            break;
    }
    
    return updated;
}

VideoStreamInfo NALParser::getCurrentStreamInfo(const StreamId& stream_id) const {
    std::lock_guard<std::mutex> lock(stream_info_mutex_);
    
    auto it = stream_info_map_.find(stream_id);
    if (it != stream_info_map_.end()) {
        return it->second;
    }
    
    return VideoStreamInfo{};
}

bool NALParser::enableHardwareAcceleration(bool enable) {
    if (enable && hardware_status_ == HardwareAccelStatus::DISABLED) {
        initializeHardwareAcceleration();
    } else if (!enable && hardware_status_ != HardwareAccelStatus::DISABLED) {
        cleanupHardwareAcceleration();
    }
    
    return hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE;
}

bool NALParser::isHardwareAccelerationEnabled() const {
    return hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE;
}

HardwareAccelStatus NALParser::getHardwareStatus() const {
    return hardware_status_;
}

void NALParser::setNALUnitCallback(std::function<void(const NALUnit&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    nal_callback_ = callback;
}

void NALParser::setStreamInfoCallback(std::function<void(const StreamId&, const VideoStreamInfo&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    stream_info_callback_ = callback;
}

void NALParser::setErrorCallback(StreamErrorCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    error_callback_ = callback;
}

NALParsingStatistics NALParser::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Create a copy manually since atomic types can't be copied
    NALParsingStatistics copy;
    copy.nal_units_parsed = statistics_.nal_units_parsed.load();
    copy.sps_units_found = statistics_.sps_units_found.load();
    copy.pps_units_found = statistics_.pps_units_found.load();
    copy.idr_frames_found = statistics_.idr_frames_found.load();
    copy.p_frames_found = statistics_.p_frames_found.load();
    copy.b_frames_found = statistics_.b_frames_found.load();
    copy.parsing_errors = statistics_.parsing_errors.load();
    copy.invalid_nal_units = statistics_.invalid_nal_units.load();
    copy.hardware_accelerated_count = statistics_.hardware_accelerated_count.load();
    copy.software_fallback_count = statistics_.software_fallback_count.load();

    return copy;
}

size_t NALParser::getMemoryUsage() const {
    size_t total = sizeof(*this);
    
    // Stream info map memory
    {
        std::lock_guard<std::mutex> lock(stream_info_mutex_);
        total += stream_info_map_.size() * (sizeof(StreamId) + sizeof(VideoStreamInfo));
    }
    
    // Hardware processor memory
    if (hardware_processor_) {
        total += hardware_processor_->getMemoryUsage();
    }
    
    return total;
}

uint32_t NALParser::getActiveStreamCount() const {
    std::lock_guard<std::mutex> lock(stream_info_mutex_);
    return static_cast<uint32_t>(stream_info_map_.size());
}

void NALParser::handleThermalThrottling(int temperature) {
    if (temperature >= 80) {
        // Disable hardware acceleration to reduce heat
        if (hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE) {
            hardware_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
            std::cout << "[NALParser] Thermal throttling: switching to software parsing" << std::endl;
        }
    } else if (temperature < 75) {
        // Re-enable hardware acceleration
        if (hardware_status_ == HardwareAccelStatus::SOFTWARE_FALLBACK && config_.enable_mpp_parsing) {
            hardware_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
            std::cout << "[NALParser] Thermal throttling: re-enabling hardware parsing" << std::endl;
        }
    }
}

void NALParser::setPerformanceMode(bool high_performance) {
    if (high_performance && config_.enable_mpp_parsing) {
        if (hardware_status_ == HardwareAccelStatus::DISABLED) {
            initializeHardwareAcceleration();
        }
    } else {
        if (hardware_status_ == HardwareAccelStatus::HARDWARE_ACTIVE) {
            hardware_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
        }
    }
}

// Private methods
void NALParser::initializeHardwareAcceleration() {
    try {
        hardware_processor_ = std::make_unique<HardwareNALProcessor>(config_);
        if (hardware_processor_->initialize()) {
            hardware_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
            std::cout << "[NALParser] Hardware acceleration initialized successfully" << std::endl;
        } else {
            hardware_status_ = HardwareAccelStatus::HARDWARE_ERROR;
            std::cout << "[NALParser] Hardware acceleration initialization failed" << std::endl;
        }
    } catch (const std::exception& e) {
        hardware_status_ = HardwareAccelStatus::HARDWARE_ERROR;
        std::cout << "[NALParser] Hardware acceleration error: " << e.what() << std::endl;
    }
}

void NALParser::cleanupHardwareAcceleration() {
    if (hardware_processor_) {
        hardware_processor_->cleanup();
        hardware_processor_.reset();
    }
    hardware_status_ = HardwareAccelStatus::DISABLED;
}

void NALParser::optimizeForRK3588() {
    setCPUAffinity();
    
    // Start worker threads if parallel parsing is enabled
    if (config_.enable_parallel_parsing && config_.worker_thread_count > 0) {
        worker_threads_.reserve(config_.worker_thread_count);
        for (int i = 0; i < config_.worker_thread_count; ++i) {
            // TODO: Implement worker threads for parallel parsing
        }
    }
}

void NALParser::setCPUAffinity() {
    // Set CPU affinity to RK3588 efficiency cores
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(2, &cpuset);  // Cortex-A55 core
    CPU_SET(3, &cpuset);  // Cortex-A55 core
    
    if (sched_setaffinity(0, sizeof(cpuset), &cpuset) != 0) {
        std::cout << "[NALParser] Warning: Failed to set CPU affinity" << std::endl;
    }
}

size_t NALParser::findNextStartCode(const std::vector<uint8_t>& data, size_t start_pos) {
    for (size_t i = start_pos; i < data.size() - 3; ++i) {
        if (data[i] == 0x00 && data[i+1] == 0x00) {
            if (data[i+2] == 0x00 && data[i+3] == 0x01) {
                return i;  // Found 4-byte start code
            } else if (data[i+2] == 0x01) {
                return i+1;  // Found 3-byte start code
            }
        }
    }
    return std::string::npos;
}

bool NALParser::parseH264NALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    if (data.empty()) {
        return false;
    }

    // H.264 NAL header is 1 byte
    uint8_t nal_header = data[0];

    // Extract fields from NAL header
    uint8_t forbidden_bit = (nal_header >> 7) & 0x01;
    uint8_t nal_ref_idc = (nal_header >> 5) & 0x03;
    uint8_t nal_unit_type = nal_header & 0x1F;

    // Validate forbidden bit
    if (forbidden_bit != 0) {
        statistics_.invalid_nal_units++;
        return false;
    }

    // Set NAL unit properties
    nal_unit.data = data;

    // Map H.264 NAL unit types
    switch (nal_unit_type) {
        case 1:  // Non-IDR slice
            nal_unit.type = NALUnitType::H264_NON_IDR;
            nal_unit.is_keyframe = false;
            break;
        case 5:  // IDR slice
            nal_unit.type = NALUnitType::H264_IDR;
            nal_unit.is_keyframe = true;
            break;
        case 7:  // SPS
            nal_unit.type = NALUnitType::H264_SPS;
            nal_unit.is_keyframe = false;
            break;
        case 8:  // PPS
            nal_unit.type = NALUnitType::H264_PPS;
            nal_unit.is_keyframe = false;
            break;
        case 28: // FU-A (Fragmentation Unit Type A)
            return parseH264FragmentationUnit(data, nal_unit);
        case 29: // FU-B (Fragmentation Unit Type B)
            return parseH264FragmentationUnit(data, nal_unit);
        default:
            nal_unit.type = NALUnitType::UNKNOWN;
            nal_unit.is_keyframe = false;
            break;
    }

    // Additional validation for reference frames
    if (nal_unit.type == NALUnitType::H264_IDR && nal_ref_idc == 0) {
        statistics_.invalid_nal_units++;
        return false;
    }

    statistics_.nal_units_parsed++;
    return true;
}

bool NALParser::parseH265NALUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    if (data.size() < 2) {
        return false;
    }

    // H.265 NAL header is 2 bytes
    uint16_t nal_header = (static_cast<uint16_t>(data[0]) << 8) | data[1];

    // Extract fields from NAL header
    uint8_t forbidden_bit = (nal_header >> 15) & 0x01;
    uint8_t nal_unit_type = (nal_header >> 9) & 0x3F;
    uint8_t nuh_layer_id = (nal_header >> 3) & 0x3F;
    uint8_t nuh_temporal_id_plus1 = nal_header & 0x07;

    // Validate forbidden bit
    if (forbidden_bit != 0) {
        statistics_.invalid_nal_units++;
        return false;
    }

    // Validate temporal ID
    if (nuh_temporal_id_plus1 == 0) {
        statistics_.invalid_nal_units++;
        return false;
    }

    // Set NAL unit properties
    nal_unit.data = data;

    // Map H.265 NAL unit types
    switch (nal_unit_type) {
        case 0: case 1:  // TRAIL_N, TRAIL_R
        case 2: case 3:  // TSA_N, TSA_R
        case 4: case 5:  // STSA_N, STSA_R
        case 6: case 7:  // RADL_N, RADL_R
        case 8: case 9:  // RASL_N, RASL_R
            nal_unit.type = NALUnitType::H264_NON_IDR; // Reuse for non-IDR
            nal_unit.is_keyframe = false;
            break;
        case 16: case 17: case 18: // BLA_W_LP, BLA_W_RADL, BLA_N_LP
        case 19: // IDR_W_RADL
            nal_unit.type = NALUnitType::H265_IDR_W_RADL;
            nal_unit.is_keyframe = true;
            break;
        case 20: // IDR_N_LP
            nal_unit.type = NALUnitType::H265_IDR_N_LP;
            nal_unit.is_keyframe = true;
            break;
        case 21: // CRA_NUT
            nal_unit.type = NALUnitType::H265_IDR_W_RADL; // Treat as IDR
            nal_unit.is_keyframe = true;
            break;
        case 32: // VPS
            nal_unit.type = NALUnitType::H265_VPS;
            nal_unit.is_keyframe = false;
            break;
        case 33: // SPS
            nal_unit.type = NALUnitType::H265_SPS;
            nal_unit.is_keyframe = false;
            break;
        case 34: // PPS
            nal_unit.type = NALUnitType::H265_PPS;
            nal_unit.is_keyframe = false;
            break;
        case 49: // FU (Fragmentation Unit)
            return parseH265FragmentationUnit(data, nal_unit);
        default:
            nal_unit.type = NALUnitType::UNKNOWN;
            nal_unit.is_keyframe = false;
            break;
    }

    statistics_.nal_units_parsed++;
    return true;
}

bool NALParser::validateNALHeader(const std::vector<uint8_t>& data) {
    if (data.empty()) return false;

    // Basic NAL header validation
    uint8_t header = data[0];

    // Check forbidden bit (should be 0)
    if (header & 0x80) return false;

    return true;
}

bool NALParser::parseH264FragmentationUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    if (data.size() < 2) {
        return false;
    }

    // FU indicator (first byte)
    uint8_t fu_indicator = data[0];
    uint8_t fu_header = data[1];

    // Extract FU header fields
    bool start_bit = (fu_header & 0x80) != 0;
    bool end_bit = (fu_header & 0x40) != 0;
    bool reserved_bit = (fu_header & 0x20) != 0;
    uint8_t nal_unit_type = fu_header & 0x1F;

    // Validate reserved bit (should be 0)
    if (reserved_bit) {
        statistics_.invalid_nal_units++;
        return false;
    }

    // For start fragments, reconstruct the original NAL header
    if (start_bit) {
        // Create original NAL header by combining FU indicator and NAL unit type
        uint8_t original_nal_header = (fu_indicator & 0xE0) | nal_unit_type;

        // Create new NAL unit with reconstructed header
        nal_unit.data.clear();
        nal_unit.data.push_back(original_nal_header);
        nal_unit.data.insert(nal_unit.data.end(), data.begin() + 2, data.end());

        // Set NAL unit type based on reconstructed header
        switch (nal_unit_type) {
            case 1:
                nal_unit.type = NALUnitType::H264_NON_IDR;
                nal_unit.is_keyframe = false;
                break;
            case 5:
                nal_unit.type = NALUnitType::H264_IDR;
                nal_unit.is_keyframe = true;
                break;
            default:
                nal_unit.type = NALUnitType::UNKNOWN;
                nal_unit.is_keyframe = false;
                break;
        }
    } else {
        // For middle and end fragments, just append payload data
        nal_unit.data.insert(nal_unit.data.end(), data.begin() + 2, data.end());
    }

    statistics_.nal_units_parsed++;
    return true;
}

bool NALParser::parseH265FragmentationUnit(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    if (data.size() < 3) {
        return false;
    }

    // H.265 FU has 3-byte header: PayloadHdr (2 bytes) + FU header (1 byte)
    uint16_t payload_hdr = (static_cast<uint16_t>(data[0]) << 8) | data[1];
    uint8_t fu_header = data[2];

    // Extract FU header fields
    bool start_bit = (fu_header & 0x80) != 0;
    bool end_bit = (fu_header & 0x40) != 0;
    uint8_t fu_type = fu_header & 0x3F;

    // For start fragments, reconstruct the original NAL header
    if (start_bit) {
        // Create original NAL header by replacing FU type with actual NAL unit type
        uint16_t original_nal_header = (payload_hdr & 0xFE01) | (static_cast<uint16_t>(fu_type) << 1);

        // Create new NAL unit with reconstructed header
        nal_unit.data.clear();
        nal_unit.data.push_back(static_cast<uint8_t>(original_nal_header >> 8));
        nal_unit.data.push_back(static_cast<uint8_t>(original_nal_header & 0xFF));
        nal_unit.data.insert(nal_unit.data.end(), data.begin() + 3, data.end());

        // Set NAL unit type based on reconstructed header
        switch (fu_type) {
            case 19:
                nal_unit.type = NALUnitType::H265_IDR_W_RADL;
                nal_unit.is_keyframe = true;
                break;
            case 20:
                nal_unit.type = NALUnitType::H265_IDR_N_LP;
                nal_unit.is_keyframe = true;
                break;
            case 32:
                nal_unit.type = NALUnitType::H265_VPS;
                nal_unit.is_keyframe = false;
                break;
            case 33:
                nal_unit.type = NALUnitType::H265_SPS;
                nal_unit.is_keyframe = false;
                break;
            case 34:
                nal_unit.type = NALUnitType::H265_PPS;
                nal_unit.is_keyframe = false;
                break;
            default:
                nal_unit.type = NALUnitType::UNKNOWN;
                nal_unit.is_keyframe = false;
                break;
        }
    } else {
        // For middle and end fragments, just append payload data
        nal_unit.data.insert(nal_unit.data.end(), data.begin() + 3, data.end());
    }

    statistics_.nal_units_parsed++;
    return true;
}

bool NALParser::parseSPS(const std::vector<uint8_t>& sps_data, VideoStreamInfo& info) {
    if (sps_data.size() < 4) return false;

    try {
        // Create a simple bit reader for SPS parsing
        size_t bit_pos = 8; // Skip NAL header

        // Read profile_idc
        if (bit_pos + 8 > sps_data.size() * 8) return false;
        uint8_t profile_idc = sps_data[bit_pos / 8];
        bit_pos += 8;

        // Skip constraint flags and level_idc
        bit_pos += 16;

        // Read seq_parameter_set_id (ue(v))
        uint32_t sps_id = readUnsignedExpGolomb(sps_data, bit_pos);
        if (sps_id > 31) return false; // Invalid SPS ID

        // For H.264 High profiles, read chroma format
        if (profile_idc == 100 || profile_idc == 110 || profile_idc == 122 ||
            profile_idc == 244 || profile_idc == 44 || profile_idc == 83 ||
            profile_idc == 86 || profile_idc == 118 || profile_idc == 128) {

            uint32_t chroma_format_idc = readUnsignedExpGolomb(sps_data, bit_pos);
            if (chroma_format_idc == 3) {
                bit_pos += 1; // separate_colour_plane_flag
            }

            // Skip bit_depth and qpprime_y_zero_transform_bypass_flag
            readUnsignedExpGolomb(sps_data, bit_pos); // bit_depth_luma_minus8
            readUnsignedExpGolomb(sps_data, bit_pos); // bit_depth_chroma_minus8
            bit_pos += 1; // qpprime_y_zero_transform_bypass_flag

            // Skip seq_scaling_matrix_present_flag and scaling lists
            if (readBit(sps_data, bit_pos)) {
                // Skip scaling matrix parsing for simplicity
                bit_pos += 48; // Approximate skip
            }
        }

        // Read log2_max_frame_num_minus4
        uint32_t log2_max_frame_num_minus4 = readUnsignedExpGolomb(sps_data, bit_pos);

        // Read pic_order_cnt_type
        uint32_t pic_order_cnt_type = readUnsignedExpGolomb(sps_data, bit_pos);

        if (pic_order_cnt_type == 0) {
            readUnsignedExpGolomb(sps_data, bit_pos); // log2_max_pic_order_cnt_lsb_minus4
        } else if (pic_order_cnt_type == 1) {
            // Skip POC type 1 parameters
            bit_pos += 1; // delta_pic_order_always_zero_flag
            readSignedExpGolomb(sps_data, bit_pos); // offset_for_non_ref_pic
            readSignedExpGolomb(sps_data, bit_pos); // offset_for_top_to_bottom_field
            uint32_t num_ref_frames_in_pic_order_cnt_cycle = readUnsignedExpGolomb(sps_data, bit_pos);
            for (uint32_t i = 0; i < num_ref_frames_in_pic_order_cnt_cycle; i++) {
                readSignedExpGolomb(sps_data, bit_pos); // offset_for_ref_frame[i]
            }
        }

        // Read max_num_ref_frames
        uint32_t max_num_ref_frames = readUnsignedExpGolomb(sps_data, bit_pos);

        // Skip gaps_in_frame_num_value_allowed_flag
        bit_pos += 1;

        // Read picture dimensions
        uint32_t pic_width_in_mbs_minus1 = readUnsignedExpGolomb(sps_data, bit_pos);
        uint32_t pic_height_in_map_units_minus1 = readUnsignedExpGolomb(sps_data, bit_pos);

        // Calculate actual resolution
        uint32_t width = (pic_width_in_mbs_minus1 + 1) * 16;
        uint32_t height = (pic_height_in_map_units_minus1 + 1) * 16;

        // Read frame_mbs_only_flag
        bool frame_mbs_only_flag = readBit(sps_data, bit_pos);
        if (!frame_mbs_only_flag) {
            height *= 2; // Interlaced
            bit_pos += 1; // mb_adaptive_frame_field_flag
        }

        // Read direct_8x8_inference_flag
        bit_pos += 1;

        // Read frame_cropping_flag and apply cropping if present
        if (readBit(sps_data, bit_pos)) {
            uint32_t crop_left = readUnsignedExpGolomb(sps_data, bit_pos);
            uint32_t crop_right = readUnsignedExpGolomb(sps_data, bit_pos);
            uint32_t crop_top = readUnsignedExpGolomb(sps_data, bit_pos);
            uint32_t crop_bottom = readUnsignedExpGolomb(sps_data, bit_pos);

            // Apply cropping
            width -= (crop_left + crop_right) * 2;
            height -= (crop_top + crop_bottom) * 2;
        }

        // Set extracted information
        info.resolution = Resolution{static_cast<int>(width), static_cast<int>(height)};
        info.framerate = FrameRate{30, 1}; // Default framerate, VUI parsing needed for actual value

        return true;

    } catch (...) {
        // Parsing failed
        return false;
    }
}

bool NALParser::parsePPS(const std::vector<uint8_t>& pps_data, VideoStreamInfo& info) {
    if (pps_data.size() < 2) return false;

    try {
        size_t bit_pos = 8; // Skip NAL header

        // Read pic_parameter_set_id
        uint32_t pps_id = readUnsignedExpGolomb(pps_data, bit_pos);
        if (pps_id > 255) return false; // Invalid PPS ID

        // Read seq_parameter_set_id
        uint32_t sps_id = readUnsignedExpGolomb(pps_data, bit_pos);
        if (sps_id > 31) return false; // Invalid SPS ID

        // Read entropy_coding_mode_flag
        bool entropy_coding_mode_flag = readBit(pps_data, bit_pos);

        // Read bottom_field_pic_order_in_frame_present_flag
        bool bottom_field_pic_order_in_frame_present_flag = readBit(pps_data, bit_pos);

        // Read num_slice_groups_minus1
        uint32_t num_slice_groups_minus1 = readUnsignedExpGolomb(pps_data, bit_pos);

        // Skip slice group mapping if present
        if (num_slice_groups_minus1 > 0) {
            // Simplified: skip slice group mapping parsing
            return true; // Basic validation passed
        }

        // Read num_ref_idx_l0_default_active_minus1
        uint32_t num_ref_idx_l0_default_active_minus1 = readUnsignedExpGolomb(pps_data, bit_pos);

        // Read num_ref_idx_l1_default_active_minus1
        uint32_t num_ref_idx_l1_default_active_minus1 = readUnsignedExpGolomb(pps_data, bit_pos);

        // Basic validation passed
        return true;

    } catch (...) {
        return false;
    }
}

bool NALParser::parseVPS(const std::vector<uint8_t>& vps_data, VideoStreamInfo& info) {
    if (vps_data.size() < 3) return false;

    try {
        size_t bit_pos = 16; // Skip NAL header (2 bytes for H.265)

        // Read vps_video_parameter_set_id (4 bits)
        uint8_t vps_id = 0;
        for (int i = 0; i < 4; i++) {
            vps_id = (vps_id << 1) | (readBit(vps_data, bit_pos) ? 1 : 0);
        }
        if (vps_id > 15) return false; // Invalid VPS ID

        // Read vps_base_layer_internal_flag (1 bit)
        bool vps_base_layer_internal_flag = readBit(vps_data, bit_pos);

        // Read vps_base_layer_available_flag (1 bit)
        bool vps_base_layer_available_flag = readBit(vps_data, bit_pos);

        // Read vps_max_layers_minus1 (6 bits)
        uint8_t vps_max_layers_minus1 = 0;
        for (int i = 0; i < 6; i++) {
            vps_max_layers_minus1 = (vps_max_layers_minus1 << 1) | (readBit(vps_data, bit_pos) ? 1 : 0);
        }

        // Read vps_max_sub_layers_minus1 (3 bits)
        uint8_t vps_max_sub_layers_minus1 = 0;
        for (int i = 0; i < 3; i++) {
            vps_max_sub_layers_minus1 = (vps_max_sub_layers_minus1 << 1) | (readBit(vps_data, bit_pos) ? 1 : 0);
        }

        // Read vps_temporal_id_nesting_flag (1 bit)
        bool vps_temporal_id_nesting_flag = readBit(vps_data, bit_pos);

        // Skip vps_reserved_0xffff_16bits (16 bits)
        bit_pos += 16;

        // Basic validation passed - VPS contains profile/tier/level info
        // but for basic functionality, we just validate structure
        return true;

    } catch (...) {
        return false;
    }
}

bool NALParser::processWithMPP(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    if (!hardware_processor_ || hardware_status_ != HardwareAccelStatus::HARDWARE_ACTIVE) {
        return false;
    }

    return hardware_processor_->processNALUnit(data, nal_unit);
}

bool NALParser::processWithSoftware(const std::vector<uint8_t>& data, NALUnit& nal_unit) {
    // Software fallback processing
    nal_unit.data = data;
    nal_unit.type = detectNALUnitType(data);
    nal_unit.is_keyframe = isKeyframe(nal_unit);

    statistics_.software_fallback_count++;
    return true;
}

void NALParser::handleParsingError(ErrorCategory category, const std::string& message) {
    statistics_.parsing_errors++;

    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (error_callback_) {
        error_callback_("", category, message);
    }
}

void NALParser::handleHardwareError(const std::string& component, const std::string& error) {
    std::cout << "[NALParser] Hardware error in " << component << ": " << error << std::endl;

    if (component == "MPP") {
        hardware_status_ = HardwareAccelStatus::HARDWARE_ERROR;
    }
}

void NALParser::updateParsingStatistics(const NALUnit& nal_unit, bool hardware_accelerated) {
    if (hardware_accelerated) {
        statistics_.hardware_accelerated_count++;
    } else {
        statistics_.software_fallback_count++;
    }

    // Update frame type statistics
    switch (nal_unit.type) {
        case NALUnitType::H264_IDR:
        case NALUnitType::H265_IDR_W_RADL:
        case NALUnitType::H265_IDR_N_LP:
            statistics_.idr_frames_found++;
            break;
        default:
            break;
    }
}

void NALParser::updateStreamStatistics(const StreamId& stream_id, const VideoStreamInfo& info) {
    std::lock_guard<std::mutex> lock(stream_info_mutex_);
    stream_info_map_[stream_id] = info;

    // Invoke stream info callback
    std::lock_guard<std::mutex> cb_lock(callback_mutex_);
    if (stream_info_callback_) {
        stream_info_callback_(stream_id, info);
    }
}

bool NALParser::validateConfig() const {
    return config_.max_nal_size_bytes > 0 &&
           config_.parsing_timeout_ms > 0 &&
           config_.worker_thread_count >= 0 &&
           config_.max_consecutive_errors > 0;
}

bool NALParser::checkHardwareCapabilities() const {
    // TODO: Implement hardware capability checking
    return true;
}

// HardwareNALProcessor implementation
HardwareNALProcessor::HardwareNALProcessor(const NALParsingConfig& config)
    : config_(config)
    , initialized_(false)
    , status_(HardwareAccelStatus::DISABLED)
    , processed_count_(0)
    , mpp_context_(nullptr)
    , rga_context_(nullptr) {
}

HardwareNALProcessor::~HardwareNALProcessor() {
    cleanup();
}

bool HardwareNALProcessor::initialize() {
    if (initialized_) return true;

    // Initialize MPP decoder
    if (!initializeMPP()) {
        status_ = HardwareAccelStatus::HARDWARE_ERROR;
        return false;
    }

    // Initialize RGA if needed
    if (config_.enable_zero_copy && !initializeRGA()) {
        std::cout << "[HardwareNALProcessor] RGA initialization failed, continuing without zero-copy" << std::endl;
    }

    initialized_ = true;
    status_ = HardwareAccelStatus::HARDWARE_ACTIVE;

    std::cout << "[HardwareNALProcessor] Hardware acceleration initialized" << std::endl;
    return true;
}

void HardwareNALProcessor::cleanup() {
    if (!initialized_) return;

    cleanupMPP();
    cleanupRGA();

    initialized_ = false;
    status_ = HardwareAccelStatus::DISABLED;

    std::cout << "[HardwareNALProcessor] Hardware acceleration cleaned up" << std::endl;
}

bool HardwareNALProcessor::isInitialized() const {
    return initialized_;
}

bool HardwareNALProcessor::processNALUnit(const std::vector<uint8_t>& input, NALUnit& output) {
    if (!initialized_ || status_ != HardwareAccelStatus::HARDWARE_ACTIVE) {
        return false;
    }

    if (input.empty()) {
        return false;
    }

    try {
        // Hardware-accelerated NAL unit processing
        // In a real implementation, this would use MPP decoder for validation
        // and potentially RGA for memory operations

        // Basic validation using hardware capabilities
        if (config_.enable_hardware_validation) {
            // Simulate hardware validation
            // Real implementation would use MPP decoder to validate NAL structure
            if (input.size() > config_.max_nal_size_bytes) {
                return false;
            }
        }

        // Copy input to output (in real implementation, this might be zero-copy with DMABUF)
        output.data = input;

        // Simulate hardware processing time
        // Real implementation would have actual MPP processing here

        // Update statistics
        processed_count_++;

        return true;

    } catch (const std::exception& e) {
        std::cout << "[HardwareNALProcessor] Processing error: " << e.what() << std::endl;
        return false;
    }
}

bool HardwareNALProcessor::validateNALUnit(const std::vector<uint8_t>& data) {
    // TODO: Implement hardware-accelerated validation
    return !data.empty();
}

HardwareAccelStatus HardwareNALProcessor::getStatus() const {
    return status_;
}

size_t HardwareNALProcessor::getMemoryUsage() const {
    // TODO: Calculate actual memory usage
    return sizeof(*this) + 1024 * 1024;  // Estimate 1MB
}

uint32_t HardwareNALProcessor::getProcessedCount() const {
    return processed_count_;
}

bool HardwareNALProcessor::initializeMPP() {
    // Basic MPP initialization for RK3588
    // Note: This is a simplified implementation
    // Real implementation would use RockChip MPP API

    try {
        // Check if MPP is available on the system
        // In a real implementation, this would call mpp_create() and configure decoder

        // For now, simulate successful initialization
        // Real code would look like:
        // MppCtx ctx = NULL;
        // MppApi *mpi = NULL;
        // ret = mpp_create(&ctx, &mpi);
        // if (ret) return false;

        std::cout << "[HardwareNALProcessor] MPP initialization simulated (placeholder)" << std::endl;

        // Set placeholder context
        mpp_context_ = reinterpret_cast<void*>(0x12345678); // Placeholder

        return true; // Simulate success for now

    } catch (const std::exception& e) {
        std::cout << "[HardwareNALProcessor] MPP initialization failed: " << e.what() << std::endl;
        return false;
    }
}

void HardwareNALProcessor::cleanupMPP() {
    // TODO: Implement MPP cleanup
    mpp_context_ = nullptr;
}

bool HardwareNALProcessor::initializeRGA() {
    // TODO: Implement RGA initialization
    rga_context_ = nullptr;  // Placeholder
    return false;  // Not implemented yet
}

void HardwareNALProcessor::cleanupRGA() {
    // TODO: Implement RGA cleanup
    rga_context_ = nullptr;
}

// Bitstream reading helper implementations
bool NALParser::readBit(const std::vector<uint8_t>& data, size_t& bit_pos) {
    if (bit_pos >= data.size() * 8) {
        throw std::out_of_range("Bit position out of range");
    }

    size_t byte_pos = bit_pos / 8;
    size_t bit_offset = bit_pos % 8;

    bool bit_value = (data[byte_pos] >> (7 - bit_offset)) & 0x01;
    bit_pos++;

    return bit_value;
}

uint32_t NALParser::readUnsignedExpGolomb(const std::vector<uint8_t>& data, size_t& bit_pos) {
    // Count leading zeros
    uint32_t leading_zeros = 0;
    while (!readBit(data, bit_pos)) {
        leading_zeros++;
        if (leading_zeros > 32) {
            throw std::runtime_error("Invalid Exp-Golomb code");
        }
    }

    // Read the remaining bits
    uint32_t value = 0;
    for (uint32_t i = 0; i < leading_zeros; i++) {
        value = (value << 1) | (readBit(data, bit_pos) ? 1 : 0);
    }

    return (1 << leading_zeros) - 1 + value;
}

int32_t NALParser::readSignedExpGolomb(const std::vector<uint8_t>& data, size_t& bit_pos) {
    uint32_t unsigned_value = readUnsignedExpGolomb(data, bit_pos);

    if (unsigned_value == 0) {
        return 0;
    }

    // Convert to signed: positive values are even, negative values are odd
    if (unsigned_value % 2 == 1) {
        return static_cast<int32_t>((unsigned_value + 1) / 2);
    } else {
        return -static_cast<int32_t>(unsigned_value / 2);
    }
}

} // namespace rtsp
} // namespace aibox
