#include "rtsp/stream_multiplexer.hpp"
#include "rtsp/connection_manager.hpp"
#include "rtsp/packet_receiver.hpp"
#include "rtsp/nal_parser.hpp"

#ifdef HAVE_SHARED_LIBRARY
#include "shared/result.hpp"
#include "shared/error_category.hpp"
#endif

#include <algorithm>
#include <thread>
#include <chrono>
#include <iostream>
#include <sched.h>
#include <unistd.h>

namespace aibox {
namespace rtsp {

// Explicit template instantiations for ThreadSafeQueue
template class ThreadSafeQueue<NALUnit>;
template class ThreadSafeQueue<std::pair<StreamId, NALUnit>>;

// StreamMultiplexer implementation
StreamMultiplexer::StreamMultiplexer(const StreamManagementConfig& config)
    : config_(config), running_(false), load_balancing_enabled_(true) {
    
    // Initialize queues
    initializeQueues();
    
    // Optimize for RK3588 platform
    optimizeForRK3588();
    
    std::cout << "[StreamMultiplexer] Initialized with max " << config_.max_concurrent_streams 
              << " streams, " << config_.worker_thread_count << " workers" << std::endl;
}

StreamMultiplexer::~StreamMultiplexer() {
    stop();
    cleanupQueues();
}

bool StreamMultiplexer::start() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    if (running_) {
        return true;  // Already running
    }
    
    // Start worker threads
    should_stop_ = false;
    
    // Management worker
    management_thread_ = std::thread(&StreamMultiplexer::managementWorker, this);
    
    // Monitoring worker
    monitoring_thread_ = std::thread(&StreamMultiplexer::monitoringWorker, this);
    
    // Processing workers
    for (int i = 0; i < config_.worker_thread_count; ++i) {
        worker_threads_.emplace_back(&StreamMultiplexer::processingWorker, this, i);
    }
    
    // Set CPU affinity for RK3588
    setCPUAffinity();
    
    running_ = true;
    
    std::cout << "[StreamMultiplexer] Started with " << worker_threads_.size() 
              << " processing workers" << std::endl;
    
    return true;
}

void StreamMultiplexer::stop() {
    std::lock_guard<std::mutex> lock(state_mutex_);
    
    if (!running_) {
        return;  // Already stopped
    }
    
    std::cout << "[StreamMultiplexer] Stopping..." << std::endl;
    
    // Signal all threads to stop
    should_stop_ = true;
    
    // Stop all queues
    if (high_priority_queue_) high_priority_queue_->stop();
    if (medium_priority_queue_) medium_priority_queue_->stop();
    if (low_priority_queue_) low_priority_queue_->stop();
    
    // Disconnect all streams
    disconnectAllStreams();
    
    // Wait for worker threads to finish
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    worker_threads_.clear();
    
    // Wait for management and monitoring threads
    if (management_thread_.joinable()) {
        management_thread_.join();
    }
    if (monitoring_thread_.joinable()) {
        monitoring_thread_.join();
    }
    
    running_ = false;
    
    std::cout << "[StreamMultiplexer] Stopped" << std::endl;
}

bool StreamMultiplexer::isRunning() const {
    std::lock_guard<std::mutex> lock(state_mutex_);
    return running_;
}

// Configuration management
void StreamMultiplexer::updateConfig(const StreamManagementConfig& new_config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_ = new_config;

    // Update queue sizes if needed
    if (high_priority_queue_) {
        high_priority_queue_->setMaxSize(config_.queue_config.high_priority_size);
    }
    if (medium_priority_queue_) {
        medium_priority_queue_->setMaxSize(config_.queue_config.medium_priority_size);
    }
    if (low_priority_queue_) {
        low_priority_queue_->setMaxSize(config_.queue_config.low_priority_size);
    }

    std::cout << "[StreamMultiplexer] Configuration updated" << std::endl;
}

const StreamManagementConfig& StreamMultiplexer::getConfig() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return config_;
}

// Stream management
bool StreamMultiplexer::addStream(const StreamId& id, const RTSPConnectionConfig& config) {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    // Check if stream already exists
    if (stream_infos_.find(id) != stream_infos_.end()) {
        std::cout << "[StreamMultiplexer] Stream " << id << " already exists" << std::endl;
        return false;
    }

    // Check resource availability
    if (!checkResourceAvailability(config)) {
        std::cout << "[StreamMultiplexer] Insufficient resources for stream " << id << std::endl;
        return false;
    }

    // Check stream count limit
    if (stream_infos_.size() >= static_cast<size_t>(config_.max_concurrent_streams)) {
        std::cout << "[StreamMultiplexer] Maximum stream count reached" << std::endl;
        return false;
    }

    // Create stream components
    if (!createStreamComponents(id, config)) {
        std::cout << "[StreamMultiplexer] Failed to create components for stream " << id << std::endl;
        return false;
    }

    // Initialize stream info
    StreamInfo stream_info;
    stream_info.id = id;
    stream_info.config = config;
    stream_info.state = ConnectionState::DISCONNECTED;
    stream_info.priority = config.priority;
    stream_info.last_activity = std::chrono::steady_clock::now();
    stream_info.enabled = true;

    stream_infos_[id] = stream_info;

    std::cout << "[StreamMultiplexer] Added stream " << id << " (total: "
              << stream_infos_.size() << ")" << std::endl;

    return true;
}

bool StreamMultiplexer::removeStream(const StreamId& id) {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it == stream_infos_.end()) {
        return false;  // Stream not found
    }

    // Disconnect stream first
    disconnectStream(id);

    // Destroy stream components
    destroyStreamComponents(id);

    // Remove from stream info
    stream_infos_.erase(it);

    std::cout << "[StreamMultiplexer] Removed stream " << id << " (remaining: "
              << stream_infos_.size() << ")" << std::endl;

    return true;
}

bool StreamMultiplexer::enableStream(const StreamId& id, bool enable) {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it == stream_infos_.end()) {
        return false;
    }

    it->second.enabled = enable;

    if (!enable) {
        // Disconnect if disabling
        disconnectStream(id);
    }

    std::cout << "[StreamMultiplexer] Stream " << id << " "
              << (enable ? "enabled" : "disabled") << std::endl;

    return true;
}

bool StreamMultiplexer::updateStreamConfig(const StreamId& id, const RTSPConnectionConfig& config) {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it == stream_infos_.end()) {
        return false;
    }

    // Update configuration
    it->second.config = config;
    it->second.priority = config.priority;

    // Reconfigure stream components
    if (!configureStreamComponents(id, config)) {
        std::cout << "[StreamMultiplexer] Failed to reconfigure stream " << id << std::endl;
        return false;
    }

    std::cout << "[StreamMultiplexer] Updated configuration for stream " << id << std::endl;

    return true;
}

// Stream control
bool StreamMultiplexer::connectStream(const StreamId& id) {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it == stream_infos_.end() || !it->second.enabled) {
        return false;
    }

    auto conn_it = connections_.find(id);
    if (conn_it == connections_.end()) {
        return false;
    }

    // Update state
    it->second.state = ConnectionState::CONNECTING;

    // Attempt connection
    auto result = conn_it->second->connect();
    if (result.success) {
        it->second.state = ConnectionState::CONNECTED;
        it->second.last_activity = std::chrono::steady_clock::now();

        std::cout << "[StreamMultiplexer] Connected stream " << id << std::endl;
        return true;
    } else {
        it->second.state = ConnectionState::ERROR;
        it->second.error_count++;

        std::cout << "[StreamMultiplexer] Failed to connect stream " << id
                  << ": " << result.error_message << std::endl;
        return false;
    }
}

bool StreamMultiplexer::disconnectStream(const StreamId& id) {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it == stream_infos_.end()) {
        return false;
    }

    auto conn_it = connections_.find(id);
    if (conn_it != connections_.end()) {
        conn_it->second->disconnect();
    }

    it->second.state = ConnectionState::DISCONNECTED;

    std::cout << "[StreamMultiplexer] Disconnected stream " << id << std::endl;

    return true;
}

bool StreamMultiplexer::reconnectStream(const StreamId& id) {
    disconnectStream(id);
    std::this_thread::sleep_for(std::chrono::milliseconds(100));  // Brief delay
    return connectStream(id);
}

void StreamMultiplexer::connectAllStreams() {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    for (auto& [stream_id, stream_info] : stream_infos_) {
        if (stream_info.enabled && stream_info.state == ConnectionState::DISCONNECTED) {
            connectStream(stream_id);
        }
    }
}

void StreamMultiplexer::disconnectAllStreams() {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    for (auto& [stream_id, stream_info] : stream_infos_) {
        if (stream_info.state != ConnectionState::DISCONNECTED) {
            disconnectStream(stream_id);
        }
    }
}

// Stream information
std::vector<StreamId> StreamMultiplexer::getStreamIds() const {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    std::vector<StreamId> ids;
    ids.reserve(stream_infos_.size());

    for (const auto& [id, info] : stream_infos_) {
        ids.push_back(id);
    }

    return ids;
}

std::vector<StreamInfo> StreamMultiplexer::getStreamInfos() const {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    std::vector<StreamInfo> infos;
    infos.reserve(stream_infos_.size());

    for (const auto& [id, info] : stream_infos_) {
        infos.push_back(info);
    }

    return infos;
}

StreamInfo StreamMultiplexer::getStreamInfo(const StreamId& id) const {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it != stream_infos_.end()) {
        return it->second;
    }

    return StreamInfo{};  // Return default-constructed info if not found
}

ConnectionState StreamMultiplexer::getStreamState(const StreamId& id) const {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it != stream_infos_.end()) {
        return it->second.state;
    }

    return ConnectionState::DISCONNECTED;
}

// Priority management
bool StreamMultiplexer::setStreamPriority(const StreamId& id, StreamPriority priority) {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it == stream_infos_.end()) {
        return false;
    }

    it->second.priority = priority;
    it->second.config.priority = priority;

    std::cout << "[StreamMultiplexer] Set priority for stream " << id
              << " to " << static_cast<int>(priority) << std::endl;

    // Trigger rebalancing if enabled
    if (load_balancing_enabled_) {
        rebalanceStreams();
    }

    return true;
}

StreamPriority StreamMultiplexer::getStreamPriority(const StreamId& id) const {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    auto it = stream_infos_.find(id);
    if (it != stream_infos_.end()) {
        return it->second.priority;
    }

    return StreamPriority::MEDIUM;  // Default priority
}

void StreamMultiplexer::rebalanceStreams() {
    if (!load_balancing_enabled_) {
        return;
    }

    std::lock_guard<std::mutex> lock(streams_mutex_);

    // Adjust stream priorities based on current load
    adjustStreamPriorities();

    // Redistribute resources
    redistributeResources();

    std::cout << "[StreamMultiplexer] Rebalanced " << stream_infos_.size() << " streams" << std::endl;
}

// Data callbacks
void StreamMultiplexer::setNALUnitCallback(std::function<void(const StreamId&, const NALUnit&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    nal_callback_ = callback;
}

void StreamMultiplexer::setStreamEventCallback(std::function<void(const StreamId&, const std::string&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    event_callback_ = callback;
}

void StreamMultiplexer::setErrorCallback(std::function<void(const StreamId&, ErrorCategory, const std::string&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    error_callback_ = callback;
}

// Statistics and monitoring
MultiplexerStatistics StreamMultiplexer::getStatistics() const {
    MultiplexerStatistics stats;

    std::lock_guard<std::mutex> lock(streams_mutex_);

    stats.total_streams = stream_infos_.size();
    stats.active_streams = 0;
    stats.connected_streams = 0;
    stats.error_streams = 0;

    for (const auto& [id, info] : stream_infos_) {
        if (info.enabled) {
            stats.active_streams++;
        }

        switch (info.state) {
            case ConnectionState::CONNECTED:
            case ConnectionState::STREAMING:
                stats.connected_streams++;
                break;
            case ConnectionState::ERROR:
                stats.error_streams++;
                break;
            default:
                break;
        }

        stats.total_packets_processed += info.packets_processed;
        stats.total_errors += info.error_count;
    }

    // Queue statistics
    if (high_priority_queue_) {
        stats.high_priority_queue_size = high_priority_queue_->size();
        stats.high_priority_queue_capacity = high_priority_queue_->capacity();
    }
    if (medium_priority_queue_) {
        stats.medium_priority_queue_size = medium_priority_queue_->size();
        stats.medium_priority_queue_capacity = medium_priority_queue_->capacity();
    }
    if (low_priority_queue_) {
        stats.low_priority_queue_size = low_priority_queue_->size();
        stats.low_priority_queue_capacity = low_priority_queue_->capacity();
    }

    stats.total_memory_usage = getTotalMemoryUsage();
    stats.cpu_usage_percent = getCurrentCPUUsage();

    return stats;
}

MultiplexerHealth StreamMultiplexer::getSystemHealth() const {
    MultiplexerHealth health;

    auto stats = getStatistics();

    // Calculate health score based on various factors
    float health_score = 1.0f;

    // Stream health factor
    if (stats.total_streams > 0) {
        float stream_health = static_cast<float>(stats.connected_streams) / stats.total_streams;
        health_score *= stream_health;
    }

    // Memory usage factor
    float memory_usage_ratio = static_cast<float>(stats.total_memory_usage) / (config_.max_memory_usage_mb * 1024 * 1024);
    if (memory_usage_ratio > 0.9f) {
        health_score *= 0.5f;  // Severe penalty for high memory usage
    } else if (memory_usage_ratio > 0.7f) {
        health_score *= 0.8f;  // Moderate penalty
    }

    // CPU usage factor
    if (stats.cpu_usage_percent > 90) {
        health_score *= 0.6f;
    } else if (stats.cpu_usage_percent > 70) {
        health_score *= 0.9f;
    }

    // Queue health factor
    size_t total_queue_usage = stats.high_priority_queue_size + stats.medium_priority_queue_size + stats.low_priority_queue_size;
    size_t total_queue_capacity = stats.high_priority_queue_capacity + stats.medium_priority_queue_capacity + stats.low_priority_queue_capacity;

    if (total_queue_capacity > 0) {
        float queue_usage_ratio = static_cast<float>(total_queue_usage) / total_queue_capacity;
        if (queue_usage_ratio > 0.8f) {
            health_score *= 0.7f;
        }
    }

    // Set health status
    if (health_score >= 0.8f) {
        health.status = "HEALTHY";
    } else if (health_score >= 0.6f) {
        health.status = "WARNING";
    } else {
        health.status = "CRITICAL";
    }

    health.score = health_score;
    health.active_streams = stats.active_streams;
    health.error_count = stats.total_errors;

    // Fill system health information
    health.system_health.rtsp_memory_usage_mb = stats.total_memory_usage / (1024 * 1024);
    health.system_health.average_cpu_usage = static_cast<float>(stats.cpu_usage_percent);
    health.system_health.soc_temperature_celsius = getCurrentTemperature();
    health.system_health.thermal_throttling_active = (health.system_health.soc_temperature_celsius > 75);

    return health;
}

size_t StreamMultiplexer::getTotalMemoryUsage() const {
    size_t total_memory = 0;

    std::lock_guard<std::mutex> lock(streams_mutex_);

    // Estimate memory usage for each stream
    for (const auto& [id, info] : stream_infos_) {
        // Base memory per stream (estimated)
        total_memory += 1024 * 1024;  // 1MB base per stream

        // Queue memory
        total_memory += info.queue_depth * 1024;  // Estimate 1KB per queued item
    }

    // Queue memory
    if (high_priority_queue_) {
        total_memory += high_priority_queue_->size() * sizeof(NALUnit);
    }
    if (medium_priority_queue_) {
        total_memory += medium_priority_queue_->size() * sizeof(NALUnit);
    }
    if (low_priority_queue_) {
        total_memory += low_priority_queue_->size() * sizeof(NALUnit);
    }

    return total_memory;
}

uint32_t StreamMultiplexer::getActiveStreamCount() const {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    uint32_t count = 0;
    for (const auto& [id, info] : stream_infos_) {
        if (info.enabled && (info.state == ConnectionState::CONNECTED || info.state == ConnectionState::STREAMING)) {
            count++;
        }
    }

    return count;
}

// Resource management
bool StreamMultiplexer::checkResourceAvailability(const RTSPConnectionConfig& config) const {
    // Check memory limits
    size_t estimated_memory = 2 * 1024 * 1024;  // 2MB per stream estimate
    if (!checkMemoryLimit(estimated_memory)) {
        return false;
    }

    // Check CPU limits
    if (!checkCPULimit()) {
        return false;
    }

    return true;
}

void StreamMultiplexer::optimizeResourceUsage() {
    std::lock_guard<std::mutex> lock(streams_mutex_);

    // Optimize queue sizes based on current load
    auto stats = getStatistics();

    if (stats.total_memory_usage > config_.max_memory_usage_mb * 1024 * 1024 * 0.8) {
        // Reduce queue sizes if memory usage is high
        for (auto& [id, info] : stream_infos_) {
            if (info.priority == StreamPriority::LOW) {
                info.queue_depth = std::min(info.queue_depth, static_cast<size_t>(50));
            }
        }
    }

    std::cout << "[StreamMultiplexer] Optimized resource usage" << std::endl;
}

void StreamMultiplexer::handleMemoryPressure() {
    std::cout << "[StreamMultiplexer] Handling memory pressure" << std::endl;

    // Reduce queue sizes
    if (high_priority_queue_) {
        high_priority_queue_->setMaxSize(high_priority_queue_->capacity() / 2);
    }
    if (medium_priority_queue_) {
        medium_priority_queue_->setMaxSize(medium_priority_queue_->capacity() / 2);
    }
    if (low_priority_queue_) {
        low_priority_queue_->setMaxSize(low_priority_queue_->capacity() / 4);
    }

    // Disable low priority streams if necessary
    std::lock_guard<std::mutex> lock(streams_mutex_);
    for (auto& [id, info] : stream_infos_) {
        if (info.priority == StreamPriority::LOW && info.enabled) {
            enableStream(id, false);
            std::cout << "[StreamMultiplexer] Disabled low priority stream " << id
                      << " due to memory pressure" << std::endl;
        }
    }
}

// Thermal management
void StreamMultiplexer::handleThermalThrottling(int temperature) {
    std::cout << "[StreamMultiplexer] Thermal throttling at " << temperature << "°C" << std::endl;

    if (temperature >= platform::THROTTLE_TEMPERATURE) {
        // Reduce performance mode
        setPerformanceMode(false);

        // Disable some streams if temperature is critical
        if (temperature >= platform::MAX_SOC_TEMPERATURE - 5) {
            std::lock_guard<std::mutex> lock(streams_mutex_);

            int disabled_count = 0;
            for (auto& [id, info] : stream_infos_) {
                if (info.priority <= StreamPriority::MEDIUM && info.enabled && disabled_count < 2) {
                    enableStream(id, false);
                    disabled_count++;
                    std::cout << "[StreamMultiplexer] Disabled stream " << id
                              << " due to thermal throttling" << std::endl;
                }
            }
        }
    }
}

void StreamMultiplexer::setPerformanceMode(bool high_performance) {
    std::lock_guard<std::mutex> lock(config_mutex_);

    if (high_performance) {
        // Enable all optimizations
        config_.enable_load_balancing = true;
        config_.enable_adaptive_quality = true;
        config_.worker_thread_count = std::max(config_.worker_thread_count, 4);
    } else {
        // Reduce performance to save power/heat
        config_.enable_adaptive_quality = false;
        config_.worker_thread_count = std::min(config_.worker_thread_count, 2);
    }

    std::cout << "[StreamMultiplexer] Performance mode: "
              << (high_performance ? "HIGH" : "LOW") << std::endl;
}

// Load balancing
void StreamMultiplexer::enableLoadBalancing(bool enable) {
    load_balancing_enabled_ = enable;

    if (enable) {
        redistributeLoad();
    }

    std::cout << "[StreamMultiplexer] Load balancing "
              << (enable ? "enabled" : "disabled") << std::endl;
}

bool StreamMultiplexer::isLoadBalancingEnabled() const {
    return load_balancing_enabled_;
}

void StreamMultiplexer::redistributeLoad() {
    if (!load_balancing_enabled_) {
        return;
    }

    std::lock_guard<std::mutex> lock(streams_mutex_);

    // Balance load across streams
    balanceStreamLoad();

    std::cout << "[StreamMultiplexer] Redistributed load across "
              << stream_infos_.size() << " streams" << std::endl;
}

// Private helper methods
void StreamMultiplexer::initializeQueues() {
    // Initialize priority queues with RK3588-optimized sizes
    high_priority_queue_ = std::make_unique<ThreadSafeQueue<NALUnit>>(config_.queue_config.high_priority_size);
    medium_priority_queue_ = std::make_unique<ThreadSafeQueue<NALUnit>>(config_.queue_config.medium_priority_size);
    low_priority_queue_ = std::make_unique<ThreadSafeQueue<NALUnit>>(config_.queue_config.low_priority_size);

    std::cout << "[StreamMultiplexer] Initialized priority queues" << std::endl;
}

void StreamMultiplexer::cleanupQueues() {
    if (high_priority_queue_) {
        high_priority_queue_->clear();
        high_priority_queue_.reset();
    }
    if (medium_priority_queue_) {
        medium_priority_queue_->clear();
        medium_priority_queue_.reset();
    }
    if (low_priority_queue_) {
        low_priority_queue_->clear();
        low_priority_queue_.reset();
    }

    std::cout << "[StreamMultiplexer] Cleaned up queues" << std::endl;
}

void StreamMultiplexer::optimizeForRK3588() {
    // Set platform-specific optimizations
    std::cout << "[StreamMultiplexer] Applying RK3588 optimizations" << std::endl;

    // Adjust configuration for RK3588 capabilities
    if (config_.max_concurrent_streams > platform::MAX_MPP_DECODER_INSTANCES) {
        config_.max_concurrent_streams = platform::MAX_MPP_DECODER_INSTANCES;
        std::cout << "[StreamMultiplexer] Limited streams to " << config_.max_concurrent_streams
                  << " (MPP decoder limit)" << std::endl;
    }

    // Optimize worker thread count for RK3588 cores
    if (config_.worker_thread_count > 4) {
        config_.worker_thread_count = 4;  // Don't overwhelm the system
    }
}

void StreamMultiplexer::setCPUAffinity() {
    // Set CPU affinity for RK3588 cores (Cortex-A55 cores 2-3)
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);

    for (int core : platform::RTSP_CPU_CORES) {
        CPU_SET(core, &cpuset);
    }

    if (sched_setaffinity(0, sizeof(cpuset), &cpuset) == 0) {
        std::cout << "[StreamMultiplexer] Set CPU affinity to cores 2-3" << std::endl;
    } else {
        std::cout << "[StreamMultiplexer] Failed to set CPU affinity" << std::endl;
    }
}

// Worker thread implementations
void StreamMultiplexer::managementWorker() {
    std::cout << "[StreamMultiplexer] Management worker started" << std::endl;

    while (!should_stop_) {
        try {
            // Check stream health and reconnect if needed
            std::lock_guard<std::mutex> lock(streams_mutex_);

            for (auto& [id, info] : stream_infos_) {
                if (!info.enabled) {
                    continue;
                }

                // Check for stale connections
                auto now = std::chrono::steady_clock::now();
                auto time_since_activity = std::chrono::duration_cast<std::chrono::seconds>(now - info.last_activity);

                if (time_since_activity.count() > 30 && info.state == ConnectionState::CONNECTED) {
                    // Connection seems stale, try to reconnect
                    std::cout << "[StreamMultiplexer] Reconnecting stale stream " << id << std::endl;
                    reconnectStream(id);
                }

                // Auto-retry failed connections
                if (info.state == ConnectionState::ERROR &&
                    info.error_count < config_.max_connection_retries) {

                    std::cout << "[StreamMultiplexer] Retrying failed stream " << id
                              << " (attempt " << info.error_count + 1 << ")" << std::endl;

                    std::this_thread::sleep_for(std::chrono::milliseconds(config_.retry_delay_ms));
                    connectStream(id);
                }

                // Remove persistently failed streams if configured
                if (config_.auto_remove_failed_streams &&
                    info.error_count >= config_.max_connection_retries) {

                    std::cout << "[StreamMultiplexer] Removing persistently failed stream " << id << std::endl;
                    removeStream(id);
                    break;  // Break to avoid iterator invalidation
                }
            }

            // Rebalance streams if enabled
            if (load_balancing_enabled_) {
                rebalanceStreams();
            }

        } catch (const std::exception& e) {
            std::cout << "[StreamMultiplexer] Management worker error: " << e.what() << std::endl;
        }

        // Sleep for management interval
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }

    std::cout << "[StreamMultiplexer] Management worker stopped" << std::endl;
}

void StreamMultiplexer::monitoringWorker() {
    std::cout << "[StreamMultiplexer] Monitoring worker started" << std::endl;

    while (!should_stop_) {
        try {
            // Collect and update statistics
            auto stats = getStatistics();
            auto health = getSystemHealth();

            // Handle memory pressure
            if (stats.total_memory_usage > config_.max_memory_usage_mb * 1024 * 1024 * 0.9) {
                handleMemoryPressure();
            }

            // Log system status periodically
            static int log_counter = 0;
            if (++log_counter % 12 == 0) {  // Every minute (5s * 12)
                std::cout << "[StreamMultiplexer] Status: " << stats.active_streams
                          << "/" << stats.total_streams << " streams, "
                          << "Memory: " << health.system_health.rtsp_memory_usage_mb << "MB, "
                          << "CPU: " << health.system_health.average_cpu_usage << "%, "
                          << "Health: " << health.status << std::endl;
            }

            // Check for thermal issues (simulated - would read from actual sensors)
            int current_temp = getCurrentTemperature();
            if (current_temp >= platform::THROTTLE_TEMPERATURE) {
                handleThermalThrottling(current_temp);
            }

        } catch (const std::exception& e) {
            std::cout << "[StreamMultiplexer] Monitoring worker error: " << e.what() << std::endl;
        }

        // Sleep for monitoring interval
        std::this_thread::sleep_for(std::chrono::milliseconds(config_.statistics_update_interval_ms));
    }

    std::cout << "[StreamMultiplexer] Monitoring worker stopped" << std::endl;
}

void StreamMultiplexer::processingWorker(int worker_id) {
    std::cout << "[StreamMultiplexer] Processing worker " << worker_id << " started" << std::endl;

    // Set CPU affinity for this worker
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(platform::RTSP_CPU_CORES[worker_id % 2], &cpuset);
    sched_setaffinity(0, sizeof(cpuset), &cpuset);

    while (!should_stop_) {
        try {
            NALUnit nal_unit;
            StreamId stream_id;

            // Try to dequeue NAL units from priority queues
            bool processed = false;

            // Process high priority first
            if (high_priority_queue_ && high_priority_queue_->tryDequeue(nal_unit)) {
                stream_id = nal_unit.stream_id;
                processed = true;
            }
            // Then medium priority
            else if (medium_priority_queue_ && medium_priority_queue_->tryDequeue(nal_unit)) {
                stream_id = nal_unit.stream_id;
                processed = true;
            }
            // Finally low priority
            else if (low_priority_queue_ && low_priority_queue_->tryDequeue(nal_unit)) {
                stream_id = nal_unit.stream_id;
                processed = true;
            }

            if (processed) {
                // Update stream statistics
                {
                    std::lock_guard<std::mutex> lock(streams_mutex_);
                    auto it = stream_infos_.find(stream_id);
                    if (it != stream_infos_.end()) {
                        it->second.packets_processed++;
                        it->second.last_activity = std::chrono::steady_clock::now();
                        it->second.queue_depth = std::max(it->second.queue_depth, static_cast<size_t>(1)) - 1;
                    }
                }

                // Invoke callback if set
                {
                    std::lock_guard<std::mutex> lock(callback_mutex_);
                    if (nal_callback_) {
                        nal_callback_(stream_id, nal_unit);
                    }
                }
            } else {
                // No work available, sleep briefly
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }

        } catch (const std::exception& e) {
            std::cout << "[StreamMultiplexer] Processing worker " << worker_id
                      << " error: " << e.what() << std::endl;
        }
    }

    std::cout << "[StreamMultiplexer] Processing worker " << worker_id << " stopped" << std::endl;
}

// Stream lifecycle helper methods
bool StreamMultiplexer::createStreamComponents(const StreamId& id, const RTSPConnectionConfig& config) {
    try {
        // Create connection manager
        auto connection = std::make_unique<ConnectionManager>(config);

        // Set up callbacks for connection manager
        connection->setDataCallback([this, id](const StreamId& stream_id, const NALUnit& nal_unit) {
            enqueueNALUnit(stream_id, nal_unit);
        });

        connection->setErrorCallback([this, id](const StreamId& stream_id, ErrorCategory category, const std::string& message) {
            std::lock_guard<std::mutex> lock(callback_mutex_);
            if (error_callback_) {
                error_callback_(stream_id, category, message);
            }
        });

        // Create packet receiver
        auto receiver = std::make_unique<PacketReceiver>(config);

        // Create NAL parser
        NALParsingConfig nal_config;
        nal_config.enable_mpp_parsing = true;
        nal_config.enable_hardware_validation = true;
        auto parser = std::make_unique<NALParser>(nal_config);

        // Store components
        connections_[id] = std::move(connection);
        receivers_[id] = std::move(receiver);
        parsers_[id] = std::move(parser);

        return true;

    } catch (const std::exception& e) {
        std::cout << "[StreamMultiplexer] Failed to create components for stream " << id
                  << ": " << e.what() << std::endl;

        // Cleanup partial creation
        connections_.erase(id);
        receivers_.erase(id);
        parsers_.erase(id);

        return false;
    }
}

void StreamMultiplexer::destroyStreamComponents(const StreamId& id) {
    // Remove components in reverse order
    parsers_.erase(id);
    receivers_.erase(id);
    connections_.erase(id);

    std::cout << "[StreamMultiplexer] Destroyed components for stream " << id << std::endl;
}

bool StreamMultiplexer::configureStreamComponents(const StreamId& id, const RTSPConnectionConfig& config) {
    // Update connection manager configuration
    auto conn_it = connections_.find(id);
    if (conn_it != connections_.end()) {
        conn_it->second->updateConfig(config);
    }

    // Update packet receiver configuration
    auto recv_it = receivers_.find(id);
    if (recv_it != receivers_.end()) {
        recv_it->second->updateConfig(config);
    }

    return true;
}

// Queue management
bool StreamMultiplexer::enqueueNALUnit(const StreamId& stream_id, const NALUnit& nal_unit) {
    // Determine priority queue based on stream priority
    StreamPriority priority = StreamPriority::MEDIUM;

    {
        std::lock_guard<std::mutex> lock(streams_mutex_);
        auto it = stream_infos_.find(stream_id);
        if (it != stream_infos_.end()) {
            priority = it->second.priority;
            it->second.queue_depth++;
        }
    }

    // Enqueue to appropriate priority queue
    bool success = false;
    switch (priority) {
        case StreamPriority::CRITICAL:
        case StreamPriority::HIGH:
            if (high_priority_queue_) {
                success = high_priority_queue_->tryEnqueue(nal_unit);
                if (!success) {
                    processQueueOverrun(StreamPriority::HIGH);
                }
            }
            break;

        case StreamPriority::MEDIUM:
            if (medium_priority_queue_) {
                success = medium_priority_queue_->tryEnqueue(nal_unit);
                if (!success) {
                    processQueueOverrun(StreamPriority::MEDIUM);
                }
            }
            break;

        case StreamPriority::LOW:
            if (low_priority_queue_) {
                success = low_priority_queue_->tryEnqueue(nal_unit);
                if (!success) {
                    processQueueOverrun(StreamPriority::LOW);
                }
            }
            break;
    }

    return success;
}

bool StreamMultiplexer::dequeueNALUnit(NALUnit& nal_unit, StreamId& stream_id) {
    // Try high priority first
    if (high_priority_queue_ && high_priority_queue_->tryDequeue(nal_unit)) {
        stream_id = nal_unit.stream_id;
        return true;
    }

    // Then medium priority
    if (medium_priority_queue_ && medium_priority_queue_->tryDequeue(nal_unit)) {
        stream_id = nal_unit.stream_id;
        return true;
    }

    // Finally low priority
    if (low_priority_queue_ && low_priority_queue_->tryDequeue(nal_unit)) {
        stream_id = nal_unit.stream_id;
        return true;
    }

    return false;
}

void StreamMultiplexer::processQueueOverrun(StreamPriority priority) {
    std::cout << "[StreamMultiplexer] Queue overrun for priority "
              << static_cast<int>(priority) << std::endl;

    // Handle queue overrun by dropping oldest items or adjusting priorities
    switch (priority) {
        case StreamPriority::LOW:
            // Drop some low priority items
            if (low_priority_queue_) {
                NALUnit dummy;
                for (int i = 0; i < 10 && !low_priority_queue_->empty(); ++i) {
                    low_priority_queue_->tryDequeue(dummy);
                }
            }
            break;

        case StreamPriority::MEDIUM:
            // Drop some medium priority items
            if (medium_priority_queue_) {
                NALUnit dummy;
                for (int i = 0; i < 5 && !medium_priority_queue_->empty(); ++i) {
                    medium_priority_queue_->tryDequeue(dummy);
                }
            }
            break;

        case StreamPriority::HIGH:
        case StreamPriority::CRITICAL:
            // For high priority, try to make room by dropping low priority items
            if (low_priority_queue_) {
                low_priority_queue_->clear();
            }
            break;
    }
}

// Resource management helpers
bool StreamMultiplexer::allocateResources(const StreamId& id, const RTSPConnectionConfig& config) {
    // Check if we have enough resources
    if (!checkResourceAvailability(config)) {
        return false;
    }

    // Resources are allocated when components are created
    return createStreamComponents(id, config);
}

void StreamMultiplexer::deallocateResources(const StreamId& id) {
    destroyStreamComponents(id);
}

bool StreamMultiplexer::checkMemoryLimit(size_t additional_memory) const {
    size_t current_usage = getTotalMemoryUsage();
    size_t total_needed = current_usage + additional_memory;
    size_t limit = config_.max_memory_usage_mb * 1024 * 1024;

    return total_needed <= limit;
}

bool StreamMultiplexer::checkCPULimit() const {
    // Simple CPU check - in real implementation would check actual CPU usage
    uint32_t current_cpu = getCurrentCPUUsage();
    return current_cpu < config_.cpu_usage_limit_percent;
}

// Load balancing helpers
void StreamMultiplexer::balanceStreamLoad() {
    // Simple load balancing - distribute streams evenly
    // In a more sophisticated implementation, this would consider actual load metrics

    std::vector<StreamId> high_load_streams;
    std::vector<StreamId> low_load_streams;

    for (const auto& [id, info] : stream_infos_) {
        if (info.queue_depth > 100) {
            high_load_streams.push_back(id);
        } else if (info.queue_depth < 10) {
            low_load_streams.push_back(id);
        }
    }

    // Adjust priorities for load balancing
    for (const auto& id : high_load_streams) {
        auto it = stream_infos_.find(id);
        if (it != stream_infos_.end() && it->second.priority > StreamPriority::LOW) {
            it->second.priority = static_cast<StreamPriority>(static_cast<int>(it->second.priority) - 1);
        }
    }
}

void StreamMultiplexer::adjustStreamPriorities() {
    // Adjust priorities based on current system load
    auto stats = getStatistics();

    if (stats.cpu_usage_percent > 80) {
        // High CPU usage - lower priorities for non-critical streams
        for (auto& [id, info] : stream_infos_) {
            if (info.priority == StreamPriority::MEDIUM) {
                info.priority = StreamPriority::LOW;
            }
        }
    }
}

void StreamMultiplexer::redistributeResources() {
    // Redistribute resources based on current priorities and load
    // This is a simplified implementation

    size_t total_memory = getTotalMemoryUsage();
    size_t memory_per_stream = total_memory / std::max(static_cast<size_t>(1), stream_infos_.size());

    for (auto& [id, info] : stream_infos_) {
        // Adjust queue sizes based on priority
        switch (info.priority) {
            case StreamPriority::CRITICAL:
            case StreamPriority::HIGH:
                info.queue_depth = std::min(info.queue_depth, static_cast<size_t>(200));
                break;
            case StreamPriority::MEDIUM:
                info.queue_depth = std::min(info.queue_depth, static_cast<size_t>(100));
                break;
            case StreamPriority::LOW:
                info.queue_depth = std::min(info.queue_depth, static_cast<size_t>(50));
                break;
        }
    }
}

// Utility methods for monitoring
uint32_t StreamMultiplexer::getCurrentCPUUsage() const {
    // Simplified CPU usage calculation
    // In real implementation, would read from /proc/stat or similar
    return 25;  // Placeholder value
}

int StreamMultiplexer::getCurrentTemperature() const {
    // Simplified temperature reading
    // In real implementation, would read from thermal sensors
    return 45;  // Placeholder value in Celsius
}

} // namespace rtsp
} // namespace aibox
