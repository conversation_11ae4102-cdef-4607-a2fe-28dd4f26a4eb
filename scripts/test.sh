#!/bin/bash

# Test script for c-aibox-v1 project
# Usage: ./scripts/test.sh [options]

set -e  # Exit on any error

# Default values
BUILD_TYPE="Debug"
BUILD_DIR="build"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
VERBOSE=false
COVERAGE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "OPTIONS:"
    echo "  --build-type TYPE    Build type (Debug|Release) [default: Debug]"
    echo "  --verbose, -v        Verbose test output"
    echo "  --coverage           Generate code coverage report (Debug builds only)"
    echo "  --help, -h           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                           # Run tests in Debug mode"
    echo "  $0 --build-type Release      # Run tests in Release mode"
    echo "  $0 --verbose --coverage      # Run tests with verbose output and coverage"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --build-type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        --verbose|-v)
            VERBOSE=true
            shift
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        --help|-h)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate build type
if [[ "$BUILD_TYPE" != "Debug" && "$BUILD_TYPE" != "Release" ]]; then
    print_error "Invalid build type: $BUILD_TYPE. Must be Debug or Release."
    exit 1
fi

# Change to project root directory
cd "$PROJECT_ROOT"

print_info "Running tests for c-aibox-v1 project"
print_info "Build type: $BUILD_TYPE"
print_info "Project root: $PROJECT_ROOT"

# Check if build directory exists
if [ ! -d "$BUILD_DIR" ]; then
    print_warning "Build directory not found. Building project first..."
    ./scripts/build.sh "$BUILD_TYPE"
fi

cd "$BUILD_DIR"

# Check if tests were built
if [ ! -f "CTestTestfile.cmake" ]; then
    print_warning "Tests not configured. Rebuilding with tests enabled..."
    cd "$PROJECT_ROOT"
    ./scripts/build.sh "$BUILD_TYPE"
    cd "$BUILD_DIR"
fi

# Run tests
print_info "Running tests..."

CTEST_ARGS=""
if [ "$VERBOSE" = true ]; then
    CTEST_ARGS="$CTEST_ARGS --verbose"
fi

# Run CTest
if ctest $CTEST_ARGS --build-config "$BUILD_TYPE"; then
    print_success "All tests passed!"
else
    print_error "Some tests failed!"
    exit 1
fi

# Generate coverage report if requested
if [ "$COVERAGE" = true ]; then
    if [ "$BUILD_TYPE" != "Debug" ]; then
        print_warning "Coverage report is only available for Debug builds"
    elif command -v gcov >/dev/null 2>&1; then
        print_info "Generating coverage report..."
        
        # Create coverage directory
        mkdir -p coverage
        
        # Generate coverage data
        find . -name "*.gcda" -exec gcov {} \;
        
        # If lcov is available, generate HTML report
        if command -v lcov >/dev/null 2>&1 && command -v genhtml >/dev/null 2>&1; then
            print_info "Generating HTML coverage report..."
            lcov --capture --directory . --output-file coverage/coverage.info
            lcov --remove coverage/coverage.info '/usr/*' --output-file coverage/coverage.info
            lcov --remove coverage/coverage.info '*/third_party/*' --output-file coverage/coverage.info
            genhtml coverage/coverage.info --output-directory coverage/html
            print_success "Coverage report generated in coverage/html/"
        else
            print_warning "lcov not found. Install lcov for HTML coverage reports."
        fi
    else
        print_warning "gcov not found. Coverage report not available."
    fi
fi

print_success "Test execution completed!"
