#include <gtest/gtest.h>
#include "rtsp/thread_safe_queue.hpp"
#include "rtsp/rtsp_types.hpp"

#include <thread>
#include <vector>
#include <chrono>
#include <atomic>
#include <random>
#include <iostream>
#include <iomanip>

using namespace aibox::rtsp;

class ThreadSafeQueueBenchmarkTest : public ::testing::Test {
protected:
    void SetUp() override {
        config_.max_size = 100000;
        config_.enable_statistics = true;
        config_.enable_back_pressure = false; // Disable for pure performance
    }
    
    void printBenchmarkResults(const std::string& test_name, 
                              std::chrono::nanoseconds duration,
                              uint64_t operations) {
        auto ops_per_second = static_cast<double>(operations) / 
                             (static_cast<double>(duration.count()) / 1e9);
        auto ns_per_op = static_cast<double>(duration.count()) / operations;
        
        std::cout << std::fixed << std::setprecision(2);
        std::cout << "[BENCHMARK] " << test_name << ":\n";
        std::cout << "  Operations: " << operations << "\n";
        std::cout << "  Duration: " << duration.count() / 1e6 << " ms\n";
        std::cout << "  Ops/sec: " << ops_per_second << "\n";
        std::cout << "  ns/op: " << ns_per_op << "\n\n";
    }
    
    QueueConfig config_;
};

// Single-threaded performance benchmarks
TEST_F(ThreadSafeQueueBenchmarkTest, SingleThreadedLockBased) {
    config_.use_lock_free = false;
    ThreadSafeQueue<int> queue(config_);
    
    const uint64_t num_operations = 1000000;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Enqueue phase
    for (uint64_t i = 0; i < num_operations; ++i) {
        queue.enqueue(static_cast<int>(i));
    }
    
    // Dequeue phase
    int value;
    for (uint64_t i = 0; i < num_operations; ++i) {
        queue.dequeue(value);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    printBenchmarkResults("Single-threaded Lock-based", duration, num_operations * 2);
}

TEST_F(ThreadSafeQueueBenchmarkTest, SingleThreadedLockFree) {
    config_.use_lock_free = true;
    ThreadSafeQueue<int> queue(config_);
    
    const uint64_t num_operations = 1000000;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Enqueue phase
    for (uint64_t i = 0; i < num_operations; ++i) {
        queue.enqueue(static_cast<int>(i));
    }
    
    // Dequeue phase
    int value;
    for (uint64_t i = 0; i < num_operations; ++i) {
        queue.dequeue(value);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    printBenchmarkResults("Single-threaded Lock-free", duration, num_operations * 2);
}

// Multi-threaded producer-consumer benchmarks
TEST_F(ThreadSafeQueueBenchmarkTest, MultiThreadedLockBased) {
    config_.use_lock_free = false;
    ThreadSafeQueue<int> queue(config_);
    
    const int num_producers = 2;
    const int num_consumers = 2;
    const uint64_t items_per_producer = 500000;
    
    std::atomic<uint64_t> total_operations(0);
    std::vector<std::thread> threads;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Start producers
    for (int p = 0; p < num_producers; ++p) {
        threads.emplace_back([&queue, &total_operations, items_per_producer, p]() {
            for (uint64_t i = 0; i < items_per_producer; ++i) {
                int value = static_cast<int>(p * items_per_producer + i);
                while (!queue.enqueue(value)) {
                    std::this_thread::yield();
                }
                total_operations++;
            }
        });
    }
    
    // Start consumers
    for (int c = 0; c < num_consumers; ++c) {
        threads.emplace_back([&queue, &total_operations, items_per_producer, num_producers]() {
            int value;
            for (uint64_t i = 0; i < items_per_producer; ++i) {
                while (!queue.dequeue(value)) {
                    std::this_thread::yield();
                }
                total_operations++;
            }
        });
    }
    
    // Wait for completion
    for (auto& t : threads) {
        t.join();
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    printBenchmarkResults("Multi-threaded Lock-based (2P/2C)", duration, total_operations.load());
}

TEST_F(ThreadSafeQueueBenchmarkTest, MultiThreadedLockFree) {
    config_.use_lock_free = true;
    ThreadSafeQueue<int> queue(config_);
    
    const int num_producers = 2;
    const int num_consumers = 2;
    const uint64_t items_per_producer = 500000;
    
    std::atomic<uint64_t> total_operations(0);
    std::vector<std::thread> threads;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Start producers
    for (int p = 0; p < num_producers; ++p) {
        threads.emplace_back([&queue, &total_operations, items_per_producer, p]() {
            for (uint64_t i = 0; i < items_per_producer; ++i) {
                int value = static_cast<int>(p * items_per_producer + i);
                while (!queue.enqueue(value)) {
                    std::this_thread::yield();
                }
                total_operations++;
            }
        });
    }
    
    // Start consumers
    for (int c = 0; c < num_consumers; ++c) {
        threads.emplace_back([&queue, &total_operations, items_per_producer, num_producers]() {
            int value;
            for (uint64_t i = 0; i < items_per_producer; ++i) {
                while (!queue.dequeue(value)) {
                    std::this_thread::yield();
                }
                total_operations++;
            }
        });
    }
    
    // Wait for completion
    for (auto& t : threads) {
        t.join();
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    printBenchmarkResults("Multi-threaded Lock-free (2P/2C)", duration, total_operations.load());
}

// High contention benchmark
TEST_F(ThreadSafeQueueBenchmarkTest, HighContentionLockFree) {
    config_.use_lock_free = true;
    ThreadSafeQueue<int> queue(config_);
    
    const int num_threads = 8;
    const uint64_t operations_per_thread = 125000;
    
    std::atomic<uint64_t> total_operations(0);
    std::vector<std::thread> threads;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Each thread does both enqueue and dequeue operations
    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([&queue, &total_operations, operations_per_thread, t]() {
            std::random_device rd;
            std::mt19937 gen(rd() + t);
            std::uniform_int_distribution<> dis(0, 1);
            
            for (uint64_t i = 0; i < operations_per_thread; ++i) {
                if (dis(gen) == 0) {
                    // Enqueue
                    int value = static_cast<int>(t * operations_per_thread + i);
                    while (!queue.enqueue(value)) {
                        std::this_thread::yield();
                    }
                } else {
                    // Dequeue
                    int value;
                    if (queue.dequeue(value)) {
                        // Success
                    } else {
                        // Queue was empty, try enqueue instead
                        int enq_value = static_cast<int>(t * operations_per_thread + i);
                        while (!queue.enqueue(enq_value)) {
                            std::this_thread::yield();
                        }
                    }
                }
                total_operations++;
            }
        });
    }
    
    // Wait for completion
    for (auto& t : threads) {
        t.join();
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    printBenchmarkResults("High Contention Lock-free (8 threads)", duration, total_operations.load());
}

// Bulk operations benchmark
TEST_F(ThreadSafeQueueBenchmarkTest, BulkOperations) {
    ThreadSafeQueue<int> queue(config_);
    
    const int batch_size = 1000;
    const int num_batches = 1000;
    
    // Prepare data
    std::vector<int> batch_data(batch_size);
    std::iota(batch_data.begin(), batch_data.end(), 1);
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Bulk enqueue
    for (int b = 0; b < num_batches; ++b) {
        queue.enqueueBatch(batch_data);
    }
    
    // Bulk dequeue
    std::vector<int> result_batch;
    for (int b = 0; b < num_batches; ++b) {
        result_batch.clear();
        queue.dequeueBatch(result_batch, batch_size);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    printBenchmarkResults("Bulk Operations", duration, num_batches * batch_size * 2);
}

// DMABUF operations benchmark
TEST_F(ThreadSafeQueueBenchmarkTest, DMABufOperations) {
    config_.enable_dmabuf = true;
    config_.dmabuf_pool_size = 10000;
    ThreadSafeQueue<int> queue(config_);
    
    const uint64_t num_operations = 100000;
    
    // Create DMABUF buffers
    std::vector<std::shared_ptr<DMABufBuffer>> buffers;
    for (int i = 0; i < 100; ++i) {
        buffers.push_back(std::make_shared<DMABufBuffer>(-1, 1024));
    }
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // DMABUF enqueue/dequeue operations
    for (uint64_t i = 0; i < num_operations; ++i) {
        auto buffer = buffers[i % buffers.size()];
        int metadata = static_cast<int>(i);
        
        if (queue.enqueueDMABuf(buffer, metadata)) {
            std::shared_ptr<DMABufBuffer> result_buffer;
            int result_metadata;
            queue.dequeueDMABuf(result_buffer, result_metadata);
        }
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    printBenchmarkResults("DMABUF Operations", duration, num_operations * 2);
}

// Memory usage benchmark
TEST_F(ThreadSafeQueueBenchmarkTest, MemoryUsage) {
    ThreadSafeQueue<std::vector<int>> queue(config_);
    
    const int num_items = 10000;
    const int vector_size = 1000;
    
    auto start = std::chrono::high_resolution_clock::now();
    
    // Enqueue large objects
    for (int i = 0; i < num_items; ++i) {
        std::vector<int> large_vector(vector_size, i);
        queue.enqueue(std::move(large_vector));
    }
    
    // Dequeue all objects
    std::vector<int> result;
    for (int i = 0; i < num_items; ++i) {
        queue.dequeue(result);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(end - start);
    
    auto stats = queue.getStatistics();
    
    std::cout << "[BENCHMARK] Memory Usage:\n";
    std::cout << "  Operations: " << num_items * 2 << "\n";
    std::cout << "  Duration: " << duration.count() / 1e6 << " ms\n";
    std::cout << "  Peak memory usage: " << stats.total_memory_usage << " bytes\n";
    std::cout << "  Peak queue size: " << stats.peak_size << "\n\n";
}

// Latency measurement benchmark
TEST_F(ThreadSafeQueueBenchmarkTest, LatencyMeasurement) {
    config_.use_lock_free = true;
    ThreadSafeQueue<std::chrono::high_resolution_clock::time_point> queue(config_);
    
    const int num_measurements = 10000;
    std::vector<std::chrono::nanoseconds> latencies;
    latencies.reserve(num_measurements);
    
    // Producer thread
    std::thread producer([&queue, num_measurements]() {
        for (int i = 0; i < num_measurements; ++i) {
            auto timestamp = std::chrono::high_resolution_clock::now();
            queue.enqueue(timestamp);
            std::this_thread::sleep_for(std::chrono::microseconds(10));
        }
    });
    
    // Consumer thread (measure latency)
    std::thread consumer([&queue, &latencies, num_measurements]() {
        std::chrono::high_resolution_clock::time_point timestamp;
        for (int i = 0; i < num_measurements; ++i) {
            if (queue.dequeue(timestamp, 1000)) { // 1 second timeout
                auto now = std::chrono::high_resolution_clock::now();
                auto latency = std::chrono::duration_cast<std::chrono::nanoseconds>(now - timestamp);
                latencies.push_back(latency);
            }
        }
    });
    
    producer.join();
    consumer.join();
    
    // Calculate statistics
    if (!latencies.empty()) {
        std::sort(latencies.begin(), latencies.end());
        
        auto min_latency = latencies.front();
        auto max_latency = latencies.back();
        auto median_latency = latencies[latencies.size() / 2];
        auto p95_latency = latencies[static_cast<size_t>(latencies.size() * 0.95)];
        auto p99_latency = latencies[static_cast<size_t>(latencies.size() * 0.99)];
        
        std::cout << "[BENCHMARK] Latency Measurement:\n";
        std::cout << "  Samples: " << latencies.size() << "\n";
        std::cout << "  Min: " << min_latency.count() << " ns\n";
        std::cout << "  Median: " << median_latency.count() << " ns\n";
        std::cout << "  P95: " << p95_latency.count() << " ns\n";
        std::cout << "  P99: " << p99_latency.count() << " ns\n";
        std::cout << "  Max: " << max_latency.count() << " ns\n\n";
    }
}
