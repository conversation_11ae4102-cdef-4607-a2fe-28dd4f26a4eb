#pragma once

#include "error_handler.hpp"
#include <string>
#include <memory>
#include <fstream>
#include <mutex>
#include <atomic>
#include <chrono>
#include <queue>
#include <thread>
#include <condition_variable>

namespace aibox {
namespace rtsp {

/**
 * @brief Log levels for filtering messages
 */
enum class LogLevel {
    TRACE = 0,      // Detailed trace information
    DEBUG_LEVEL = 1,      // Debug information
    INFO = 2,       // Informational messages
    WARNING = 3,    // Warning conditions
    ERROR = 4,      // Error conditions
    CRITICAL = 5,   // Critical conditions
    FATAL = 6       // Fatal errors
};

/**
 * @brief Log output destinations
 */
enum class LogOutput {
    CONSOLE,        // Standard output/error
    FILE,           // Log file
    SYSLOG,         // System log
    BOTH            // Both console and file
};

/**
 * @brief Log entry structure
 */
struct LogEntry {
    LogLevel level;
    std::string message;
    std::string component;
    std::string stream_id;
    std::chrono::steady_clock::time_point timestamp;
    std::thread::id thread_id;
    std::unordered_map<std::string, std::string> context;
    
    LogEntry() :
        level(LogLevel::INFO),
        timestamp(std::chrono::steady_clock::now()),
        thread_id(std::this_thread::get_id()) {}
};

/**
 * @brief Logger configuration
 */
struct LoggerConfig {
    LogLevel min_level = LogLevel::INFO;
    LogOutput output = LogOutput::BOTH;
    std::string log_file_path = "/var/log/aibox/rtsp.log";
    size_t max_file_size_mb = 100;
    int max_backup_files = 5;
    bool enable_rotation = true;
    bool enable_async_logging = true;
    size_t async_queue_size = 10000;
    bool enable_timestamps = true;
    bool enable_thread_ids = true;
    bool enable_colors = true;
    std::string timestamp_format = "%Y-%m-%d %H:%M:%S";
    
    // Performance settings
    bool enable_buffering = true;
    size_t buffer_size = 8192;
    std::chrono::milliseconds flush_interval = std::chrono::milliseconds(1000);
    
    // RK3588 specific settings
    bool enable_thermal_logging = true;
    bool enable_performance_logging = true;
    bool enable_hardware_logging = true;
};

/**
 * @brief High-performance structured logger for RTSP module
 * 
 * This logger provides thread-safe, high-performance logging with support
 * for structured logging, log rotation, and RK3588-specific optimizations.
 */
class Logger {
public:
    /**
     * @brief Constructor
     * @param config Logger configuration
     */
    explicit Logger(const LoggerConfig& config = LoggerConfig{});
    
    /**
     * @brief Destructor
     */
    ~Logger();
    
    // Core logging methods
    
    /**
     * @brief Log a message with specified level
     * @param level Log level
     * @param message Log message
     * @param component Component name
     * @param stream_id Stream identifier (optional)
     * @param context Additional context (optional)
     */
    void log(LogLevel level,
             const std::string& message,
             const std::string& component,
             const std::string& stream_id = "",
             const std::unordered_map<std::string, std::string>& context = {});
    
    /**
     * @brief Log a structured entry
     * @param entry Log entry
     */
    void log(const LogEntry& entry);
    
    // Convenience methods for different log levels
    
    /**
     * @brief Log trace message
     */
    void trace(const std::string& message, const std::string& component, 
               const std::string& stream_id = "");
    
    /**
     * @brief Log debug message
     */
    void debug(const std::string& message, const std::string& component,
               const std::string& stream_id = "");
    
    /**
     * @brief Log info message
     */
    void info(const std::string& message, const std::string& component, 
              const std::string& stream_id = "");
    
    /**
     * @brief Log warning message
     */
    void warning(const std::string& message, const std::string& component, 
                 const std::string& stream_id = "");
    
    /**
     * @brief Log error message
     */
    void error(const std::string& message, const std::string& component, 
               const std::string& stream_id = "");
    
    /**
     * @brief Log critical message
     */
    void critical(const std::string& message, const std::string& component, 
                  const std::string& stream_id = "");
    
    /**
     * @brief Log fatal message
     */
    void fatal(const std::string& message, const std::string& component, 
               const std::string& stream_id = "");
    
    // Error integration
    
    /**
     * @brief Log error information
     * @param error_info Error information to log
     */
    void logError(const ErrorInfo& error_info);
    
    // Configuration management
    
    /**
     * @brief Update logger configuration
     * @param config New configuration
     */
    void updateConfig(const LoggerConfig& config);
    
    /**
     * @brief Get current configuration
     * @return Current configuration
     */
    LoggerConfig getConfig() const;
    
    /**
     * @brief Set minimum log level
     * @param level Minimum log level
     */
    void setLogLevel(LogLevel level);
    
    /**
     * @brief Get current log level
     * @return Current minimum log level
     */
    LogLevel getLogLevel() const;
    
    // File management
    
    /**
     * @brief Force log rotation
     */
    void rotateLog();
    
    /**
     * @brief Flush pending log entries
     */
    void flush();
    
    /**
     * @brief Get current log file size
     * @return Log file size in bytes
     */
    size_t getLogFileSize() const;
    
    // Statistics
    
    /**
     * @brief Get logging statistics
     * @return Logging statistics
     */
    struct LoggingStatistics {
        uint64_t total_entries{0};
        uint64_t trace_entries{0};
        uint64_t debug_entries{0};
        uint64_t info_entries{0};
        uint64_t warning_entries{0};
        uint64_t error_entries{0};
        uint64_t critical_entries{0};
        uint64_t fatal_entries{0};
        uint64_t dropped_entries{0};
        uint64_t bytes_written{0};
        std::chrono::steady_clock::time_point start_time;

        LoggingStatistics() : start_time(std::chrono::steady_clock::now()) {}
    };
    
    LoggingStatistics getStatistics() const;
    
    /**
     * @brief Reset logging statistics
     */
    void resetStatistics();
    
    // Utility methods
    
    /**
     * @brief Check if level is enabled
     * @param level Log level to check
     * @return True if level is enabled
     */
    bool isLevelEnabled(LogLevel level) const;
    
    /**
     * @brief Convert log level to string
     * @param level Log level
     * @return String representation
     */
    static std::string levelToString(LogLevel level);
    
    /**
     * @brief Convert string to log level
     * @param level_str String representation
     * @return Log level
     */
    static LogLevel stringToLevel(const std::string& level_str);
    
    /**
     * @brief Get current timestamp as string
     * @param format Timestamp format
     * @return Formatted timestamp
     */
    static std::string getCurrentTimestamp(const std::string& format = "%Y-%m-%d %H:%M:%S");

private:
    // Internal implementation details
    class Impl;
    std::unique_ptr<Impl> pImpl;
};

// Global logger instance for convenience
extern std::shared_ptr<Logger> g_logger;

/**
 * @brief Initialize global logger
 * @param config Logger configuration
 */
void initializeLogger(const LoggerConfig& config = LoggerConfig{});

/**
 * @brief Get global logger instance
 * @return Global logger instance
 */
std::shared_ptr<Logger> getLogger();

// Convenience macros for logging
#define RTSP_LOG_TRACE(component, message) \
    if (auto logger = aibox::rtsp::getLogger()) { \
        logger->trace(message, component); \
    }

#define RTSP_LOG_DEBUG(component, message) \
    if (auto logger = aibox::rtsp::getLogger()) { \
        logger->debug(message, component); \
    }

#define RTSP_LOG_INFO(component, message) \
    if (auto logger = aibox::rtsp::getLogger()) { \
        logger->info(message, component); \
    }

#define RTSP_LOG_WARNING(component, message) \
    if (auto logger = aibox::rtsp::getLogger()) { \
        logger->warning(message, component); \
    }

#define RTSP_LOG_ERROR(component, message) \
    if (auto logger = aibox::rtsp::getLogger()) { \
        logger->error(message, component); \
    }

#define RTSP_LOG_CRITICAL(component, message) \
    if (auto logger = aibox::rtsp::getLogger()) { \
        logger->critical(message, component); \
    }

#define RTSP_LOG_FATAL(component, message) \
    if (auto logger = aibox::rtsp::getLogger()) { \
        logger->fatal(message, component); \
    }

} // namespace rtsp
} // namespace aibox
