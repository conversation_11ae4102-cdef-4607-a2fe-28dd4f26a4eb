# Định Nghĩa Tác Vụ Module Tiền Xử Lý RGA

## Tổng Quan
Module này xử lý việc tiền xử lý các khung hình video đã được giải mã sử dụng 2D Raster Graphics Accelerator (RGA) của RK3588. <PERSON><PERSON> chuyển đổi các khung hình YUV từ bộ giải mã MPP thành định dạng và độ phân giải cần thiết cho mạng neural phát hiện khuôn mặt, duy trì các thao tác zero-copy trong toàn bộ pipeline.

## Yêu Cầu
- Chuyển đổi các khung hình YUV đã giải mã (thường là NV12) sang định dạng cần thiết cho mô hình phát hiện khuôn mặt (thường là BGR)
- Thay đổi kích thước khung hình theo kích thước đầu vào mong đợi của mô hình phát hiện
- <PERSON><PERSON> tr<PERSON> các thao tác zero-copy sử dụng DMABUF
- Xử lý khung hình từ nhiều luồng camera một cách hiệu quả
- Tích hợp với cả module giải mã MPP upstream và module NPU downstream

## Phương Pháp Kỹ Thuật
1. **Cấu Hình và Khởi Tạo RGA**:
   - Khởi tạo thư viện RGA cho xử lý hình ảnh tăng tốc phần cứng
   - Cấu hình RGA để hoạt động với DMABUF cho các thao tác zero-copy
   - Thiết lập các tham số chuyển đổi không gian màu phù hợp

2. **Tích Hợp DMABUF Zero-Copy**:
   - Import DMABUF handle từ module MPP
   - Tạo DMABUF đầu ra cho đầu vào NPU
   - Triển khai quản lý và đếm tham chiếu DMABUF phù hợp

3. **Các Thao Tác Xử Lý Hình Ảnh**:
   - Triển khai các thao tác thay đổi kích thước để khớp với kích thước đầu vào mạng neural
   - Cấu hình chuyển đổi không gian màu (NV12 sang BGR/RGB)
   - Đảm bảo đồng bộ hóa phù hợp của các thao tác RGA

4. **Quản Lý Thread Worker**:
   - Tạo thread pool cho các thao tác RGA
   - Cân bằng tải xử lý trên các worker thread
   - Triển khai xử lý ưu tiên cho các luồng camera khác nhau nếu cần

## Giao Diện
- **Đầu vào**: File descriptor DMABUF trỏ đến các khung hình YUV đã giải mã từ MPP
- **Đầu ra**: File descriptor DMABUF trỏ đến các hình ảnh đã tiền xử lý (đã thay đổi kích thước, chuyển đổi màu) sẵn sàng cho NPU

## Phụ Thuộc
- librga (thư viện RGA của Rockchip)
- Thư viện chuẩn C++
- Thư viện DMABUF/DRM cho quản lý buffer

## Cân Nhắc Hiệu Suất
- Lập lịch hiệu quả các thao tác RGA để tránh nghẽn cổ chai
- Đồng bộ hóa phù hợp giữa các thao tác
- Giảm thiểu sử dụng băng thông bộ nhớ
- Giám sát hiệu suất và sử dụng RGA
