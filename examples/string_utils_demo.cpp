#include <iostream>
#include <utils/string_utils.hpp>

int main() {
    std::cout << "=== C-AIBOX String Utils Demo ===" << std::endl;

    // 1. Split and Join Operations
    std::cout << "\n1. Split and Join Operations:" << std::endl;
    std::string text = "apple,banana,cherry";
    auto parts = utils::StringUtils::split(text, ',');
    std::cout << "Split '" << text << "' by ',' -> ";
    for (const auto& part : parts) {
        std::cout << "[" << part << "] ";
    }
    std::cout << std::endl;

    std::string joined = utils::StringUtils::join(parts, " | ");
    std::cout << "Join with ' | ' -> " << joined << std::endl;

    // Split by string delimiter
    std::string text2 = "hello::world::test";
    auto parts2 = utils::StringUtils::split(text2, "::");
    std::cout << "Split '" << text2 << "' by '::' -> ";
    for (const auto& part : parts2) {
        std::cout << "[" << part << "] ";
    }
    std::cout << std::endl;

    // 2. Trimming Operations
    std::cout << "\n2. Trimming Operations:" << std::endl;
    std::string messy = "   hello world   ";
    std::cout << "Original: '" << messy << "'" << std::endl;
    std::cout << "Trimmed: '" << utils::StringUtils::trim(messy) << "'" << std::endl;
    std::cout << "Left trim: '" << utils::StringUtils::ltrim(messy) << "'" << std::endl;
    std::cout << "Right trim: '" << utils::StringUtils::rtrim(messy) << "'" << std::endl;

    std::string custom = "...hello world...";
    std::cout << "Custom trim '" << custom << "' (remove '.'): '" << utils::StringUtils::trim(custom, ".") << "'" << std::endl;

    // 3. Case Operations
    std::cout << "\n3. Case Operations:" << std::endl;
    std::string sample = "Hello World";
    std::cout << "Original: " << sample << std::endl;
    std::cout << "Lowercase: " << utils::StringUtils::toLower(sample) << std::endl;
    std::cout << "Uppercase: " << utils::StringUtils::toUpper(sample) << std::endl;
    std::cout << "Capitalize: " << utils::StringUtils::capitalize("hello") << std::endl;
    std::cout << "Title case: " << utils::StringUtils::toTitleCase("hello-world test") << std::endl;

    // 4. Search Operations
    std::cout << "\n4. Search Operations:" << std::endl;
    std::string searchText = "Hello World Hello";
    std::cout << "Text: " << searchText << std::endl;
    std::cout << "Starts with 'Hello': " << (utils::StringUtils::startsWith(searchText, "Hello") ? "Yes" : "No") << std::endl;
    std::cout << "Ends with 'Hello': " << (utils::StringUtils::endsWith(searchText, "Hello") ? "Yes" : "No") << std::endl;
    std::cout << "Contains 'World': " << (utils::StringUtils::contains(searchText, "World") ? "Yes" : "No") << std::endl;
    std::cout << "Contains 'world' (ignore case): " << (utils::StringUtils::contains(searchText, "world", true) ? "Yes" : "No") << std::endl;

    // 5. Replace Operations
    std::cout << "\n5. Replace Operations:" << std::endl;
    std::cout << "Replace all 'Hello' with 'Hi': " << utils::StringUtils::replace(searchText, "Hello", "Hi") << std::endl;
    std::cout << "Replace first 'Hello' with 'Hi': " << utils::StringUtils::replaceFirst(searchText, "Hello", "Hi") << std::endl;
    std::cout << "Replace last 'Hello' with 'Hi': " << utils::StringUtils::replaceLast(searchText, "Hello", "Hi") << std::endl;
    std::cout << "Remove 'Hello': " << utils::StringUtils::remove(searchText, "Hello") << std::endl;

    // 6. Padding Operations
    std::cout << "\n6. Padding Operations:" << std::endl;
    std::string word = "hello";
    std::cout << "Original: '" << word << "'" << std::endl;
    std::cout << "Pad left (10): '" << utils::StringUtils::padLeft(word, 10) << "'" << std::endl;
    std::cout << "Pad right (10): '" << utils::StringUtils::padRight(word, 10) << "'" << std::endl;
    std::cout << "Pad center (11): '" << utils::StringUtils::padCenter(word, 11) << "'" << std::endl;
    std::cout << "Pad left with '*': '" << utils::StringUtils::padLeft(word, 10, '*') << "'" << std::endl;

    // 7. Utility Operations
    std::cout << "\n7. Utility Operations:" << std::endl;
    std::cout << "Reverse 'hello': " << utils::StringUtils::reverse("hello") << std::endl;
    std::cout << "Repeat 'abc' 3 times: " << utils::StringUtils::repeat("abc", 3) << std::endl;
    std::cout << "Is '   ' blank? " << (utils::StringUtils::isBlank("   ") ? "Yes" : "No") << std::endl;
    std::cout << "Is 'hello' blank? " << (utils::StringUtils::isBlank("hello") ? "Yes" : "No") << std::endl;

    // 8. Validation Operations
    std::cout << "\n8. Validation Operations:" << std::endl;
    std::cout << "Is '123' numeric? " << (utils::StringUtils::isNumeric("123") ? "Yes" : "No") << std::endl;
    std::cout << "Is '123.45' numeric? " << (utils::StringUtils::isNumeric("123.45") ? "Yes" : "No") << std::endl;
    std::cout << "Is 'abc' numeric? " << (utils::StringUtils::isNumeric("abc") ? "Yes" : "No") << std::endl;
    std::cout << "Is '123' integer? " << (utils::StringUtils::isInteger("123") ? "Yes" : "No") << std::endl;
    std::cout << "Is '123.45' integer? " << (utils::StringUtils::isInteger("123.45") ? "Yes" : "No") << std::endl;
    std::cout << "Is 'hello' alpha? " << (utils::StringUtils::isAlpha("hello") ? "Yes" : "No") << std::endl;
    std::cout << "Is 'hello123' alphanumeric? " << (utils::StringUtils::isAlphaNumeric("hello123") ? "Yes" : "No") << std::endl;

    // 9. Count and Find Operations
    std::cout << "\n9. Count and Find Operations:" << std::endl;
    std::cout << "Count 'Hello' in '" << searchText << "': " << utils::StringUtils::count(searchText, "Hello") << std::endl;
    std::cout << "Count 'hello' (ignore case): " << utils::StringUtils::count(searchText, "hello", true) << std::endl;
    
    auto positions = utils::StringUtils::findAll(searchText, "Hello");
    std::cout << "Positions of 'Hello': ";
    for (size_t pos : positions) {
        std::cout << pos << " ";
    }
    std::cout << std::endl;

    // 10. Extraction Operations
    std::cout << "\n10. Extraction Operations:" << std::endl;
    std::string bracketText = "Hello [World] Test";
    auto extracted = utils::StringUtils::extractBetween(bracketText, "[", "]");
    if (extracted.has_value()) {
        std::cout << "Extract between '[' and ']' from '" << bracketText << "': " << extracted.value() << std::endl;
    }

    std::cout << "Truncate 'Hello World' to 5 chars: " << utils::StringUtils::truncate("Hello World", 5) << std::endl;
    std::cout << "Truncate with custom suffix: " << utils::StringUtils::truncate("Hello World", 5, "***") << std::endl;

    // 11. Comparison Operations
    std::cout << "\n11. Comparison Operations:" << std::endl;
    std::cout << "Equals ignore case 'Hello' vs 'hello': " << (utils::StringUtils::equalsIgnoreCase("Hello", "hello") ? "Yes" : "No") << std::endl;
    std::cout << "Equals ignore case 'Hello' vs 'world': " << (utils::StringUtils::equalsIgnoreCase("Hello", "world") ? "Yes" : "No") << std::endl;

    // 12. Random String Generation
    std::cout << "\n12. Random String Generation:" << std::endl;
    std::cout << "Random string (10 chars): " << utils::StringUtils::random(10) << std::endl;
    std::cout << "Random string (5 chars, ABC only): " << utils::StringUtils::random(5, "ABC") << std::endl;
    std::cout << "Random string (8 chars, digits only): " << utils::StringUtils::random(8, "0123456789") << std::endl;

    // 13. Convenience Functions
    std::cout << "\n13. Convenience Functions:" << std::endl;
    std::cout << "Convenience split: ";
    auto convSplit = utils::split("a,b,c", ',');
    for (const auto& part : convSplit) {
        std::cout << "[" << part << "] ";
    }
    std::cout << std::endl;
    
    std::cout << "Convenience join: " << utils::join({"x", "y", "z"}, "-") << std::endl;
    std::cout << "Convenience trim: '" << utils::trim("  hello  ") << "'" << std::endl;
    std::cout << "Convenience toLower: " << utils::toLower("HELLO") << std::endl;
    std::cout << "Convenience toUpper: " << utils::toUpper("hello") << std::endl;

    std::cout << "\n=== Demo completed ===" << std::endl;
    return 0;
}
