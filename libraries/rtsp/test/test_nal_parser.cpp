#include <gtest/gtest.h>
#include <vector>
#include <memory>
#include "rtsp/nal_parser.hpp"
#include "rtsp/rtsp_types.hpp"

using namespace aibox::rtsp;

class NALParserTest : public ::testing::Test {
protected:
    void SetUp() override {
        NALParsingConfig config;
        config.enable_mpp_parsing = false; // Use software for testing
        config.validate_nal_headers = true;
        config.extract_metadata = true;
        config.detect_frame_types = true;
        config.parse_sps_pps = true;
        config.strict_validation = false;
        config.skip_invalid_nal_units = true;
        config.max_consecutive_errors = 10;
        config.buffer_pool_size = 32;
        config.max_memory_usage_mb = 100;
        
        parser_ = std::make_unique<NALParser>(config);
    }

    void TearDown() override {
        parser_.reset();
    }

    std::unique_ptr<NALParser> parser_;
};

// Test basic NAL parser construction and configuration
TEST_F(NALParserTest, Construction) {
    ASSERT_NE(parser_, nullptr);
    
    auto config = parser_->getConfig();
    EXPECT_FALSE(config.enable_mpp_parsing);
    EXPECT_TRUE(config.validate_nal_headers);
    EXPECT_TRUE(config.extract_metadata);
    EXPECT_TRUE(config.detect_frame_types);
    EXPECT_TRUE(config.parse_sps_pps);
}

// Test H.264 NAL unit type detection
TEST_F(NALParserTest, H264NALUnitTypeDetection) {
    // H.264 IDR slice (type 5)
    std::vector<uint8_t> h264_idr = {0x25, 0x88, 0x84, 0x00}; // NAL header + some data
    NALUnitType type = parser_->detectNALUnitType(h264_idr);
    EXPECT_EQ(type, NALUnitType::H264_IDR);
    
    // H.264 Non-IDR slice (type 1)
    std::vector<uint8_t> h264_non_idr = {0x21, 0x88, 0x84, 0x00};
    type = parser_->detectNALUnitType(h264_non_idr);
    EXPECT_EQ(type, NALUnitType::H264_NON_IDR);
    
    // H.264 SPS (type 7)
    std::vector<uint8_t> h264_sps = {0x27, 0x64, 0x00, 0x1F};
    type = parser_->detectNALUnitType(h264_sps);
    EXPECT_EQ(type, NALUnitType::H264_SPS);
    
    // H.264 PPS (type 8)
    std::vector<uint8_t> h264_pps = {0x28, 0xEE, 0x3C, 0x80};
    type = parser_->detectNALUnitType(h264_pps);
    EXPECT_EQ(type, NALUnitType::H264_PPS);
}

// Test H.265 NAL unit type detection
TEST_F(NALParserTest, H265NALUnitTypeDetection) {
    // H.265 IDR_W_RADL (type 19)
    std::vector<uint8_t> h265_idr = {0x26, 0x01, 0xAF, 0x06}; // 2-byte NAL header + data
    NALUnitType type = parser_->detectNALUnitType(h265_idr);
    EXPECT_EQ(type, NALUnitType::H265_IDR_W_RADL);
    
    // H.265 VPS (type 32)
    std::vector<uint8_t> h265_vps = {0x40, 0x01, 0x0C, 0x01};
    type = parser_->detectNALUnitType(h265_vps);
    EXPECT_EQ(type, NALUnitType::H265_VPS);
    
    // H.265 SPS (type 33)
    std::vector<uint8_t> h265_sps = {0x42, 0x01, 0x01, 0x01};
    type = parser_->detectNALUnitType(h265_sps);
    EXPECT_EQ(type, NALUnitType::H265_SPS);
    
    // H.265 PPS (type 34)
    std::vector<uint8_t> h265_pps = {0x44, 0x01, 0xC1, 0x73};
    type = parser_->detectNALUnitType(h265_pps);
    EXPECT_EQ(type, NALUnitType::H265_PPS);
}

// Test keyframe detection
TEST_F(NALParserTest, KeyframeDetection) {
    NALUnit nal_unit;
    
    // H.264 IDR should be keyframe
    nal_unit.type = NALUnitType::H264_IDR;
    EXPECT_TRUE(parser_->isKeyframe(nal_unit));
    
    // H.264 Non-IDR should not be keyframe
    nal_unit.type = NALUnitType::H264_NON_IDR;
    EXPECT_FALSE(parser_->isKeyframe(nal_unit));
    
    // H.265 IDR should be keyframe
    nal_unit.type = NALUnitType::H265_IDR_W_RADL;
    EXPECT_TRUE(parser_->isKeyframe(nal_unit));
    
    nal_unit.type = NALUnitType::H265_IDR_N_LP;
    EXPECT_TRUE(parser_->isKeyframe(nal_unit));
    
    // Parameter sets should not be keyframes
    nal_unit.type = NALUnitType::H264_SPS;
    EXPECT_FALSE(parser_->isKeyframe(nal_unit));
    
    nal_unit.type = NALUnitType::H265_VPS;
    EXPECT_FALSE(parser_->isKeyframe(nal_unit));
}

// Test NAL unit parsing
TEST_F(NALParserTest, NALUnitParsing) {
    std::vector<uint8_t> h264_idr_data = {0x25, 0x88, 0x84, 0x00, 0x01, 0x02, 0x03};
    NALUnit nal_unit;
    
    bool result = parser_->parseNALUnit(h264_idr_data, nal_unit);
    EXPECT_TRUE(result);
    EXPECT_EQ(nal_unit.type, NALUnitType::H264_IDR);
    EXPECT_TRUE(nal_unit.is_keyframe);
    EXPECT_EQ(nal_unit.data, h264_idr_data);
}

// Test RTP payload parsing with multiple NAL units
TEST_F(NALParserTest, RTPPayloadParsing) {
    // Create RTP payload with start codes and NAL units
    std::vector<uint8_t> payload;
    
    // First NAL unit: H.264 SPS
    payload.insert(payload.end(), {0x00, 0x00, 0x00, 0x01}); // Start code
    payload.insert(payload.end(), {0x27, 0x64, 0x00, 0x1F}); // SPS data
    
    // Second NAL unit: H.264 PPS
    payload.insert(payload.end(), {0x00, 0x00, 0x00, 0x01}); // Start code
    payload.insert(payload.end(), {0x28, 0xEE, 0x3C, 0x80}); // PPS data
    
    // Third NAL unit: H.264 IDR
    payload.insert(payload.end(), {0x00, 0x00, 0x00, 0x01}); // Start code
    payload.insert(payload.end(), {0x25, 0x88, 0x84, 0x00}); // IDR data
    
    StreamId stream_id = "test_stream";
    Timestamp timestamp = std::chrono::steady_clock::now();
    
    std::vector<NALUnit> nal_units = parser_->parseRTPPayload(payload, stream_id, timestamp);
    
    EXPECT_EQ(nal_units.size(), 3);
    
    // Check first NAL unit (SPS)
    EXPECT_EQ(nal_units[0].type, NALUnitType::H264_SPS);
    EXPECT_FALSE(nal_units[0].is_keyframe);
    EXPECT_EQ(nal_units[0].stream_id, stream_id);
    
    // Check second NAL unit (PPS)
    EXPECT_EQ(nal_units[1].type, NALUnitType::H264_PPS);
    EXPECT_FALSE(nal_units[1].is_keyframe);
    
    // Check third NAL unit (IDR)
    EXPECT_EQ(nal_units[2].type, NALUnitType::H264_IDR);
    EXPECT_TRUE(nal_units[2].is_keyframe);
}

// Test invalid NAL units
TEST_F(NALParserTest, InvalidNALUnits) {
    // Empty data
    std::vector<uint8_t> empty_data;
    NALUnit nal_unit;
    EXPECT_FALSE(parser_->parseNALUnit(empty_data, nal_unit));
    
    // Invalid forbidden bit (should be 0)
    std::vector<uint8_t> invalid_forbidden = {0x85, 0x88, 0x84, 0x00}; // Forbidden bit set
    EXPECT_FALSE(parser_->parseNALUnit(invalid_forbidden, nal_unit));
    
    // Unknown NAL unit type
    std::vector<uint8_t> unknown_type = {0x3F, 0x88, 0x84, 0x00}; // Type 31 (reserved)
    bool result = parser_->parseNALUnit(unknown_type, nal_unit);
    if (result) {
        EXPECT_EQ(nal_unit.type, NALUnitType::UNKNOWN);
    }
}

// Test statistics collection
TEST_F(NALParserTest, StatisticsCollection) {
    // Parse some NAL units
    std::vector<uint8_t> h264_sps = {0x27, 0x64, 0x00, 0x1F};
    std::vector<uint8_t> h264_pps = {0x28, 0xEE, 0x3C, 0x80};
    std::vector<uint8_t> h264_idr = {0x25, 0x88, 0x84, 0x00};
    
    NALUnit nal_unit;
    parser_->parseNALUnit(h264_sps, nal_unit);
    parser_->parseNALUnit(h264_pps, nal_unit);
    parser_->parseNALUnit(h264_idr, nal_unit);
    
    auto stats = parser_->getStatistics();
    EXPECT_GE(stats.nal_units_parsed, 3);
    EXPECT_GE(stats.sps_units_found, 1);
    EXPECT_GE(stats.pps_units_found, 1);
    EXPECT_GE(stats.idr_frames_found, 1);
}

// Test hardware acceleration enable/disable
TEST_F(NALParserTest, HardwareAcceleration) {
    // Initially should be disabled (set in config)
    EXPECT_FALSE(parser_->isHardwareAccelerationEnabled());
    
    // Try to enable (may fail if hardware not available)
    bool enabled = parser_->enableHardwareAcceleration(true);
    // Don't assert on result since hardware may not be available in test environment
    
    // Disable should always work
    parser_->enableHardwareAcceleration(false);
    EXPECT_FALSE(parser_->isHardwareAccelerationEnabled());
}

// Test memory usage reporting
TEST_F(NALParserTest, MemoryUsage) {
    size_t initial_memory = parser_->getMemoryUsage();
    EXPECT_GT(initial_memory, 0);
    
    // Parse some data to potentially increase memory usage
    std::vector<uint8_t> payload = {0x00, 0x00, 0x00, 0x01, 0x27, 0x64, 0x00, 0x1F};
    StreamId stream_id = "test_stream";
    Timestamp timestamp = std::chrono::steady_clock::now();
    
    parser_->parseRTPPayload(payload, stream_id, timestamp);
    
    size_t after_memory = parser_->getMemoryUsage();
    EXPECT_GE(after_memory, initial_memory);
}

// Test error handling
TEST_F(NALParserTest, ErrorHandling) {
    bool error_called = false;
    std::string error_message;
    
    parser_->setErrorCallback([&](const StreamId& stream_id, ErrorCategory category, const std::string& message) {
        error_called = true;
        error_message = message;
    });
    
    // Try to parse invalid data that should trigger an error
    std::vector<uint8_t> invalid_data = {0xFF, 0xFF, 0xFF, 0xFF}; // All bits set
    NALUnit nal_unit;
    parser_->parseNALUnit(invalid_data, nal_unit);
    
    // Error callback might be called depending on validation settings
    // Don't assert since behavior may vary based on configuration
}

// Test H.264 fragmentation unit parsing
TEST_F(NALParserTest, H264FragmentationUnit) {
    // Create FU-A start packet for H.264 IDR slice
    std::vector<uint8_t> fu_start = {
        0x7C,  // FU indicator (type 28, no ref_idc)
        0x85,  // FU header (start=1, end=0, type=5 for IDR)
        0x88, 0x84, 0x00, 0x01  // Payload data
    };

    NALUnit nal_unit;
    bool result = parser_->parseNALUnit(fu_start, nal_unit);
    EXPECT_TRUE(result);
    EXPECT_EQ(nal_unit.type, NALUnitType::H264_IDR);
    EXPECT_TRUE(nal_unit.is_keyframe);

    // Check that the reconstructed NAL header is correct
    EXPECT_EQ(nal_unit.data[0], 0x25); // Original NAL header for IDR
}

// Test H.265 fragmentation unit parsing
TEST_F(NALParserTest, H265FragmentationUnit) {
    // Create H.265 FU start packet for IDR slice
    std::vector<uint8_t> fu_start = {
        0x62, 0x01,  // PayloadHdr (type 49 for FU)
        0x93,        // FU header (start=1, end=0, type=19 for IDR_W_RADL)
        0xAF, 0x06, 0x01, 0x02  // Payload data
    };

    NALUnit nal_unit;
    bool result = parser_->parseNALUnit(fu_start, nal_unit);
    EXPECT_TRUE(result);
    EXPECT_EQ(nal_unit.type, NALUnitType::H265_IDR_W_RADL);
    EXPECT_TRUE(nal_unit.is_keyframe);

    // Check that the reconstructed NAL header is correct (2 bytes for H.265)
    EXPECT_EQ(nal_unit.data.size(), 8); // 2 bytes header + 6 bytes payload
}

// Test SPS parsing for resolution extraction
TEST_F(NALParserTest, SPSParsing) {
    // Create a simple H.264 SPS with known resolution
    // This is a simplified SPS that should parse correctly
    std::vector<uint8_t> sps_data = {
        0x27,  // NAL header (SPS)
        0x64, 0x00, 0x1F,  // profile_idc, constraints, level_idc
        0xE1,  // seq_parameter_set_id (ue(v) = 0)
        0x00,  // log2_max_frame_num_minus4 (ue(v) = 0)
        0x00,  // pic_order_cnt_type (ue(v) = 0)
        0x00,  // log2_max_pic_order_cnt_lsb_minus4 (ue(v) = 0)
        0x00,  // max_num_ref_frames (ue(v) = 0)
        0x00,  // gaps_in_frame_num_value_allowed_flag
        0x2F,  // pic_width_in_mbs_minus1 (ue(v) = 119 -> 1920 pixels)
        0x21,  // pic_height_in_map_units_minus1 (ue(v) = 67 -> 1088 pixels)
        0x80   // frame_mbs_only_flag = 1
    };

    VideoStreamInfo info;
    bool result = parser_->parseSPS(sps_data, info);

    // Note: The actual parsing may fail due to the simplified SPS structure
    // In a real implementation, we would use properly formatted SPS data
    if (result) {
        EXPECT_GT(info.resolution.width, 0);
        EXPECT_GT(info.resolution.height, 0);
    }
}

// Test stream information extraction
TEST_F(NALParserTest, StreamInfoExtraction) {
    std::vector<NALUnit> nal_units;

    // Create SPS NAL unit
    NALUnit sps_nal;
    sps_nal.type = NALUnitType::H264_SPS;
    sps_nal.data = {0x27, 0x64, 0x00, 0x1F, 0xE1, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2F, 0x21, 0x80};
    nal_units.push_back(sps_nal);

    // Create PPS NAL unit
    NALUnit pps_nal;
    pps_nal.type = NALUnitType::H264_PPS;
    pps_nal.data = {0x28, 0xEE, 0x3C, 0x80};
    nal_units.push_back(pps_nal);

    VideoStreamInfo info;
    bool result = parser_->extractStreamInfo(nal_units, info);

    if (result) {
        EXPECT_EQ(info.codec, VideoCodec::H264);
        EXPECT_TRUE(info.has_sps);
        EXPECT_TRUE(info.has_pps);
    }
}

// Test callback functionality
TEST_F(NALParserTest, CallbackFunctionality) {
    bool nal_callback_called = false;
    bool stream_info_callback_called = false;

    parser_->setNALUnitCallback([&](const NALUnit& nal) {
        nal_callback_called = true;
    });

    parser_->setStreamInfoCallback([&](const StreamId& stream_id, const VideoStreamInfo& info) {
        stream_info_callback_called = true;
    });

    // Parse a NAL unit that should trigger callbacks
    std::vector<uint8_t> payload = {0x00, 0x00, 0x00, 0x01, 0x27, 0x64, 0x00, 0x1F};
    StreamId stream_id = "test_stream";
    Timestamp timestamp = std::chrono::steady_clock::now();

    parser_->parseRTPPayload(payload, stream_id, timestamp);

    EXPECT_TRUE(nal_callback_called);
    // Stream info callback may or may not be called depending on SPS parsing success
}

// Test thermal throttling
TEST_F(NALParserTest, ThermalThrottling) {
    // Enable hardware acceleration first (if available)
    parser_->enableHardwareAcceleration(true);

    // Simulate high temperature
    parser_->handleThermalThrottling(85);

    // Should switch to software fallback
    auto status = parser_->getHardwareStatus();
    EXPECT_NE(status, HardwareAccelStatus::HARDWARE_ACTIVE);

    // Simulate temperature drop
    parser_->handleThermalThrottling(70);

    // May re-enable hardware acceleration
    // Don't assert since behavior depends on hardware availability
}

// Test performance mode switching
TEST_F(NALParserTest, PerformanceMode) {
    // Test high performance mode
    parser_->setPerformanceMode(true);

    // Test low performance mode
    parser_->setPerformanceMode(false);

    // Should not crash and should handle mode switching gracefully
    auto status = parser_->getHardwareStatus();
    // Don't assert specific status since it depends on hardware availability
}

// Test concurrent access (basic thread safety)
TEST_F(NALParserTest, ConcurrentAccess) {
    std::vector<uint8_t> test_data = {0x25, 0x88, 0x84, 0x00};

    // Test that multiple calls don't crash
    for (int i = 0; i < 10; ++i) {
        NALUnit nal_unit;
        parser_->parseNALUnit(test_data, nal_unit);

        auto stats = parser_->getStatistics();
        auto memory = parser_->getMemoryUsage();
        auto stream_count = parser_->getActiveStreamCount();
    }

    // Should complete without crashing
    EXPECT_TRUE(true);
}

// Test edge cases
TEST_F(NALParserTest, EdgeCases) {
    NALUnit nal_unit;

    // Very small data
    std::vector<uint8_t> tiny_data = {0x25};
    bool result = parser_->parseNALUnit(tiny_data, nal_unit);
    EXPECT_TRUE(result); // Should handle gracefully

    // Very large data (within limits)
    std::vector<uint8_t> large_data(1024, 0x25);
    large_data[0] = 0x25; // Valid NAL header
    result = parser_->parseNALUnit(large_data, nal_unit);
    EXPECT_TRUE(result);

    // Data at maximum size limit
    auto config = parser_->getConfig();
    if (config.max_nal_size_bytes > 0) {
        std::vector<uint8_t> max_size_data(config.max_nal_size_bytes, 0x00);
        max_size_data[0] = 0x25; // Valid NAL header
        result = parser_->parseNALUnit(max_size_data, nal_unit);
        // Should handle according to configuration
    }
}
