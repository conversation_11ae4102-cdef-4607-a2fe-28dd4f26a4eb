#!/bin/bash

# Orange Pi GLIBC Compatible Cross-Compilation Build Script
# Builds binaries compatible with Orange Pi GLIBC 2.36

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
BUILD_TYPE="Release"
BUILD_DIR="build-orangepi-compat"
CLEAN_BUILD=false
VERBOSE=false
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo "4")
ORANGE_PI_RAM="8GB"
PACKAGE_BUILD=false
SYSROOT=""
CHECK_GLIBC=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --release)
            BUILD_TYPE="Release"
            shift
            ;;
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        --ram)
            ORANGE_PI_RAM="$2"
            shift 2
            ;;
        --sysroot)
            SYSROOT="$2"
            shift 2
            ;;
        --package)
            PACKAGE_BUILD=true
            shift
            ;;
        --no-glibc-check)
            CHECK_GLIBC=false
            shift
            ;;
        --help)
            cat << EOF
Orange Pi GLIBC Compatible Cross-Compilation Build Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --release           Build in Release mode (default)
    --debug             Build in Debug mode
    --clean             Clean build directory before building
    --verbose           Enable verbose output
    --jobs N            Number of parallel jobs (default: auto-detect)
    --ram SIZE          Orange Pi RAM size: 4GB, 8GB, 16GB (default: 8GB)
    --sysroot PATH      Path to Orange Pi sysroot
    --package           Create deployment package
    --no-glibc-check    Skip GLIBC compatibility check
    --help              Show this help

DESCRIPTION:
    Cross-compiles c-aibox for Orange Pi with GLIBC 2.36 compatibility.
    This ensures binaries work on Orange Pi devices with older GLIBC versions.

EXAMPLES:
    $0                                    # Basic build
    $0 --clean --verbose                  # Clean verbose build
    $0 --ram 4GB --package               # Build for 4GB variant with package
    $0 --sysroot /opt/orangepi-sysroot   # Build with custom sysroot

EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            log_info "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Print build configuration
print_build_config() {
    log_step "Orange Pi GLIBC Compatible Build Configuration"
    echo "Build Type:        $BUILD_TYPE"
    echo "Build Directory:   $BUILD_DIR"
    echo "Clean Build:       $CLEAN_BUILD"
    echo "Verbose:           $VERBOSE"
    echo "Parallel Jobs:     $JOBS"
    echo "Orange Pi RAM:     $ORANGE_PI_RAM"
    echo "Package Build:     $PACKAGE_BUILD"
    echo "Check GLIBC:       $CHECK_GLIBC"
    if [[ -n "$SYSROOT" ]]; then
        echo "Sysroot:           $SYSROOT"
    fi
    echo ""
}

# Check cross-compilation requirements
check_cross_compile_requirements() {
    log_step "Checking Cross-Compilation Requirements"
    
    # Check CMake
    if ! command -v cmake >/dev/null 2>&1; then
        log_error "CMake is not installed"
        exit 1
    fi
    
    # Check cross-compiler
    local cross_compiler="aarch64-linux-gnu-gcc"
    if ! command -v "$cross_compiler" >/dev/null 2>&1; then
        log_error "Cross-compiler not found: $cross_compiler"
        log_info "Install with: sudo apt install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu"
        exit 1
    fi
    
    log_success "Cross-compiler found: $cross_compiler"
    
    # Check toolchain file
    local toolchain_file="$PROJECT_ROOT/cmake/toolchains/aarch64-linux-gnu.cmake"
    if [[ ! -f "$toolchain_file" ]]; then
        log_error "Toolchain file not found: $toolchain_file"
        exit 1
    fi
    
    # Check version script
    local version_script="$PROJECT_ROOT/cmake/version-scripts/glibc-2.36.map"
    if [[ ! -f "$version_script" ]]; then
        log_error "GLIBC version script not found: $version_script"
        exit 1
    fi
    
    # Set sysroot
    if [[ -n "$SYSROOT" ]]; then
        export ORANGE_PI_SYSROOT="$SYSROOT"
    elif [[ -n "${ORANGE_PI_SYSROOT:-}" ]]; then
        log_info "Using sysroot: $ORANGE_PI_SYSROOT"
    else
        log_warning "No sysroot specified. Dependencies may not be found."
        log_info "Set ORANGE_PI_SYSROOT environment variable or use --sysroot option"
    fi
}

# Configure CMake for GLIBC compatible cross-compilation
configure_cmake() {
    log_step "Configuring CMake for GLIBC Compatible Cross-Compilation"
    
    cd "$PROJECT_ROOT"
    
    # Clean build directory if requested
    if [[ "$CLEAN_BUILD" == "true" && -d "$BUILD_DIR" ]]; then
        log_info "Cleaning build directory..."
        rm -rf "$BUILD_DIR"
    fi
    
    # Create build directory
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # Prepare CMake arguments
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=$BUILD_TYPE"
        "-DCMAKE_TOOLCHAIN_FILE=../cmake/toolchains/aarch64-linux-gnu.cmake"
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
        "-DORANGE_PI_TARGET=ON"
        "-DGLIBC_COMPAT_MODE=ON"
        "-DENABLE_HARDWARE_ACCELERATION=ON"
        "-DENABLE_ARM64_OPTIMIZATIONS=ON"
        "-DBUILD_TESTING=ON"
        "-DBUILD_EXAMPLES=ON"
    )
    
    # Add RAM configuration
    case "$ORANGE_PI_RAM" in
        4GB|4gb)
            cmake_args+=("-DORANGE_PI_RAM_4GB=ON")
            ;;
        8GB|8gb)
            cmake_args+=("-DORANGE_PI_RAM_8GB=ON")
            ;;
        16GB|16gb)
            cmake_args+=("-DORANGE_PI_RAM_16GB=ON")
            ;;
        *)
            log_warning "Unknown RAM size: $ORANGE_PI_RAM, defaulting to 8GB"
            cmake_args+=("-DORANGE_PI_RAM_8GB=ON")
            ;;
    esac
    
    # Add verbose flag if requested
    if [[ "$VERBOSE" == "true" ]]; then
        cmake_args+=("-DCMAKE_VERBOSE_MAKEFILE=ON")
    fi
    
    log_info "Running CMake configuration..."
    if [[ "$VERBOSE" == "true" ]]; then
        echo "CMake command: cmake ${cmake_args[*]} .."
    fi
    
    cmake "${cmake_args[@]}" ..
    
    log_success "CMake configuration completed"
}

# Build the project
build_project() {
    log_step "Building Project with GLIBC Compatibility"
    
    cd "$PROJECT_ROOT/$BUILD_DIR"
    
    local make_args=("-j$JOBS")
    if [[ "$VERBOSE" == "true" ]]; then
        make_args+=("VERBOSE=1")
    fi
    
    log_info "Building with $JOBS parallel jobs..."
    make "${make_args[@]}"
    
    log_success "Build completed successfully"
}

# Check GLIBC compatibility of built binaries
check_glibc_compatibility() {
    if [[ "$CHECK_GLIBC" != "true" ]]; then
        return 0
    fi
    
    log_step "Checking GLIBC Compatibility"
    
    cd "$PROJECT_ROOT/$BUILD_DIR"
    
    # Find all executables
    local executables
    mapfile -t executables < <(find . -type f -executable -exec file {} \; | grep "ELF.*ARM aarch64" | cut -d: -f1)
    
    if [[ ${#executables[@]} -eq 0 ]]; then
        log_warning "No ARM64 executables found to check"
        return 0
    fi
    
    local all_compatible=true
    
    for exe in "${executables[@]}"; do
        log_info "Checking GLIBC compatibility for: $exe"
        
        # Check GLIBC version requirements
        local glibc_versions
        mapfile -t glibc_versions < <(aarch64-linux-gnu-objdump -T "$exe" 2>/dev/null | grep "GLIBC_" | sed 's/.*GLIBC_/GLIBC_/' | sort -V | uniq)
        
        local max_version=""
        for version in "${glibc_versions[@]}"; do
            if [[ "$version" > "$max_version" ]]; then
                max_version="$version"
            fi
        done
        
        if [[ -n "$max_version" ]]; then
            echo "  Required GLIBC: $max_version"
            
            # Check if version is compatible with Orange Pi (GLIBC 2.36)
            if [[ "$max_version" > "GLIBC_2.36" ]]; then
                log_error "  INCOMPATIBLE: Requires $max_version, Orange Pi has GLIBC_2.36"
                all_compatible=false
            else
                log_success "  COMPATIBLE: $max_version <= GLIBC_2.36"
            fi
        else
            log_info "  No GLIBC version requirements found"
        fi
    done
    
    if [[ "$all_compatible" == "true" ]]; then
        log_success "All binaries are compatible with Orange Pi GLIBC 2.36"
    else
        log_error "Some binaries are NOT compatible with Orange Pi GLIBC 2.36"
        log_info "Consider using static linking or older toolchain"
        return 1
    fi
}

# Main execution
main() {
    print_build_config
    check_cross_compile_requirements
    configure_cmake
    build_project
    check_glibc_compatibility
    
    log_success "Orange Pi GLIBC compatible build completed successfully!"
    log_info "Build artifacts are in: $PROJECT_ROOT/$BUILD_DIR"
    
    if [[ "$PACKAGE_BUILD" == "true" ]]; then
        log_info "Creating deployment package..."
        # Package creation logic would go here
        log_success "Deployment package created"
    fi
}

# Run main function
main "$@"
