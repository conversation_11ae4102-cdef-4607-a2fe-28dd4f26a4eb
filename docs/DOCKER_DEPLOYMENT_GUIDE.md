# Docker Deployment Guide for Orange Pi

This guide covers deploying C-AIBOX as Docker containers to Orange Pi 5 Plus devices.

## Overview

We provide two Docker deployment scripts:

1. **`deploy-docker-simple.sh`** - Recommended for most users
2. **`deploy-docker-to-orangepi.sh`** - Advanced deployment with multiple transfer methods

## Prerequisites

### On Development Machine
- Docker installed and running
- SSH access to Orange Pi
- Cross-compiled ARM64 binaries in `build-container/` directory

### On Orange Pi Device
- Docker installed and running
- SSH server enabled
- Network connectivity

## Quick Start

### 1. Build ARM64 Binaries
```bash
# Build ARM64 binaries using Docker
./scripts/cross-build/build-orangepi-docker.sh
```

### 2. Deploy to Orange Pi
```bash
# Simple deployment (recommended)
./scripts/deploy/deploy-docker-simple.sh --ip ***************

# Custom port
./scripts/deploy/deploy-docker-simple.sh --ip *************** --host-port 9000

# Custom container name
./scripts/deploy/deploy-docker-simple.sh --ip *************** --container-name my-aibox
```

## Deployment Scripts

### Simple Deployment Script

**File:** `scripts/deploy/deploy-docker-simple.sh`

**Features:**
- ✅ Transfers pre-built ARM64 binaries
- ✅ Builds Docker image directly on Orange Pi
- ✅ Automatic container management
- ✅ Health check verification
- ✅ Cleanup of build artifacts

**Usage:**
```bash
./scripts/deploy/deploy-docker-simple.sh --ip IP_ADDRESS [OPTIONS]

OPTIONS:
  --ip IP                 Orange Pi IP address (required)
  --user USER             SSH username (default: orangepi)
  --port PORT             SSH port (default: 22)
  --ssh-key PATH          SSH private key file
  --image-name NAME       Docker image name (default: c-aibox-arm64)
  --container-name NAME   Container name (default: c-aibox)
  --host-port PORT        Host port mapping (default: 8090)
  --build-dir DIR         Build directory (default: build-container)
  --help                  Show help
```

### Advanced Deployment Script

**File:** `scripts/deploy/deploy-docker-to-orangepi.sh`

**Features:**
- ✅ Multiple transfer methods (save, build-remote, registry)
- ✅ Advanced configuration options
- ✅ Multi-platform build support
- ✅ Container lifecycle management
- ✅ Health monitoring

**Transfer Methods:**
- **save**: Build locally, save to tar.gz, transfer via SCP
- **build-remote**: Transfer source, build on device
- **registry**: Push to registry, pull on device (not implemented)

## Docker Image Details

### Base Image
- **ARM64**: `arm64v8/debian:bookworm-slim`
- **Size**: ~116MB
- **Architecture**: Native ARM64/AArch64

### Runtime Dependencies
- libstdc++6
- libc6
- libgcc-s1
- ca-certificates
- curl

### Container Configuration
- **Working Directory**: `/app`
- **Exposed Port**: 8080
- **Health Check**: Every 30s via `/health` endpoint
- **Restart Policy**: `unless-stopped`
- **User**: Non-root (in advanced Dockerfile)

## Examples

### Basic Deployment
```bash
# Deploy to Orange Pi with default settings
./scripts/deploy/deploy-docker-simple.sh --ip ***************
```

### Production Deployment
```bash
# Deploy with custom settings for production
./scripts/deploy/deploy-docker-simple.sh \
  --ip *************** \
  --host-port 8080 \
  --container-name c-aibox-production
```

### Development Deployment
```bash
# Deploy for development with custom port
./scripts/deploy/deploy-docker-simple.sh \
  --ip *************** \
  --host-port 9090 \
  --container-name c-aibox-dev
```

### SSH Key Authentication
```bash
# Deploy using SSH key
./scripts/deploy/deploy-docker-simple.sh \
  --ip *************** \
  --ssh-key ~/.ssh/orangepi_key
```

## Container Management

### View Container Status
```bash
ssh orangepi@*************** 'docker ps'
```

### View Container Logs
```bash
ssh orangepi@*************** 'docker logs c-aibox'
```

### Stop Container
```bash
ssh orangepi@*************** 'docker stop c-aibox'
```

### Start Container
```bash
ssh orangepi@*************** 'docker start c-aibox'
```

### Remove Container
```bash
ssh orangepi@*************** 'docker rm -f c-aibox'
```

### View Resource Usage
```bash
ssh orangepi@*************** 'docker stats c-aibox --no-stream'
```

## API Testing

### Health Check
```bash
curl http://***************:8090/health
```

### API Information
```bash
curl http://***************:8090/api/v1/info
```

### System Status
```bash
curl http://***************:8090/api/v1/status
```

### YOLO Detection (requires valid image data)
```bash
curl -X POST http://***************:8090/api/v1/yolo/detect \
  -H "Content-Type: application/json" \
  -d '{"image": "base64_encoded_image", "confidence": 0.5}'
```

## Troubleshooting

### Container Won't Start
1. Check container logs: `docker logs c-aibox`
2. Verify binary permissions: `docker exec c-aibox ls -la /app/server`
3. Check port conflicts: `netstat -tlnp | grep 8090`

### Health Check Fails
1. Wait 30 seconds for service initialization
2. Check if port is accessible: `curl http://localhost:8090/health`
3. Verify container is running: `docker ps`

### SSH Connection Issues
1. Verify SSH service: `systemctl status ssh`
2. Check firewall: `ufw status`
3. Test SSH manually: `ssh orangepi@***************`

### Docker Build Fails
1. Check disk space: `df -h`
2. Verify Docker service: `systemctl status docker`
3. Check Docker permissions: `groups $USER`

## Performance Optimization

### Resource Limits
```bash
# Run with memory limit
docker run -d --name c-aibox --memory=1g -p 8090:8080 c-aibox-arm64:latest
```

### CPU Limits
```bash
# Run with CPU limit
docker run -d --name c-aibox --cpus=2 -p 8090:8080 c-aibox-arm64:latest
```

## Security Considerations

1. **Non-root User**: Advanced Dockerfile runs as non-root user
2. **Minimal Base Image**: Uses slim Debian image
3. **No SSH Keys in Image**: SSH keys are not embedded in images
4. **Health Checks**: Built-in health monitoring
5. **Restart Policy**: Automatic restart on failure

## Monitoring

### Container Health
```bash
# Check health status
docker inspect c-aibox | grep -A 10 Health
```

### Resource Usage
```bash
# Monitor resource usage
docker stats c-aibox
```

### Application Logs
```bash
# Follow application logs
docker logs -f c-aibox
```

## Next Steps

1. **Set up monitoring**: Use Prometheus/Grafana for monitoring
2. **Configure reverse proxy**: Use Nginx for SSL termination
3. **Add model files**: Mount model directory for AI inference
4. **Set up CI/CD**: Automate deployment pipeline
5. **Scale horizontally**: Deploy multiple instances with load balancer
