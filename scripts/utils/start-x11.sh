#!/bin/bash

# Script to start X11 server for GUI applications in development environment
# Supports both Xvfb (virtual framebuffer) and real X11 server

set -e

# Configuration
DISPLAY_NUM=${DISPLAY_NUM:-:99}
SCREEN_RESOLUTION=${SCREEN_RESOLUTION:-1920x1080x24}
X11_LOG_FILE="/tmp/x11-server.log"
XVFB_PID_FILE="/tmp/xvfb.pid"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if X11 server is running
is_x11_running() {
    if xset q >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to check if Xvfb is running
is_xvfb_running() {
    if [ -f "$XVFB_PID_FILE" ]; then
        local pid=$(cat "$XVFB_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$XVFB_PID_FILE"
            return 1
        fi
    fi
    return 1
}

# Function to start Xvfb (Virtual Framebuffer X server)
start_xvfb() {
    print_status "Starting Xvfb virtual X11 server..."
    
    # Kill any existing Xvfb processes
    pkill -f "Xvfb.*$DISPLAY_NUM" || true
    
    # Start Xvfb
    Xvfb "$DISPLAY_NUM" -screen 0 "$SCREEN_RESOLUTION" \
        -ac -noreset -nolisten tcp \
        > "$X11_LOG_FILE" 2>&1 &
    
    local xvfb_pid=$!
    echo "$xvfb_pid" > "$XVFB_PID_FILE"
    
    # Wait a moment for Xvfb to start
    sleep 2
    
    # Check if Xvfb started successfully
    if ps -p "$xvfb_pid" > /dev/null 2>&1; then
        export DISPLAY="$DISPLAY_NUM"
        print_success "Xvfb started successfully on display $DISPLAY_NUM (PID: $xvfb_pid)"
        print_status "Resolution: $SCREEN_RESOLUTION"
        return 0
    else
        print_error "Failed to start Xvfb"
        return 1
    fi
}

# Function to start window manager (optional)
start_window_manager() {
    print_status "Starting window manager..."
    
    # Check if a window manager is available and start it
    if command -v fluxbox >/dev/null 2>&1; then
        fluxbox > /dev/null 2>&1 &
        print_success "Fluxbox window manager started"
    elif command -v openbox >/dev/null 2>&1; then
        openbox > /dev/null 2>&1 &
        print_success "Openbox window manager started"
    elif command -v xfwm4 >/dev/null 2>&1; then
        xfwm4 > /dev/null 2>&1 &
        print_success "Xfwm4 window manager started"
    else
        print_warning "No window manager found. GUI applications may not have window decorations."
    fi
}

# Function to stop X11 server
stop_x11() {
    print_status "Stopping X11 server..."
    
    if is_xvfb_running; then
        local pid=$(cat "$XVFB_PID_FILE")
        kill "$pid" 2>/dev/null || true
        rm -f "$XVFB_PID_FILE"
        print_success "Xvfb stopped"
    fi
    
    # Kill any remaining X processes
    pkill -f "Xvfb.*$DISPLAY_NUM" || true
    pkill -f "fluxbox\|openbox\|xfwm4" || true
}

# Function to show status
show_status() {
    echo "=== X11 Server Status ==="
    echo "Display: ${DISPLAY:-Not set}"
    echo "Resolution: $SCREEN_RESOLUTION"
    
    if is_xvfb_running; then
        local pid=$(cat "$XVFB_PID_FILE")
        echo "Xvfb: Running (PID: $pid)"
    else
        echo "Xvfb: Not running"
    fi
    
    if is_x11_running; then
        echo "X11 Connection: Available"
    else
        echo "X11 Connection: Not available"
    fi
    
    echo "Log file: $X11_LOG_FILE"
}

# Function to test X11
test_x11() {
    print_status "Testing X11 server..."
    
    if is_x11_running; then
        print_success "X11 server is responding"
        
        # Test with xeyes if available
        if command -v xeyes >/dev/null 2>&1; then
            print_status "Starting xeyes test application (close it to continue)..."
            xeyes &
            local xeyes_pid=$!
            sleep 2
            if ps -p "$xeyes_pid" > /dev/null 2>&1; then
                print_success "GUI test successful - xeyes is running"
                print_status "Kill xeyes with: kill $xeyes_pid"
            fi
        else
            print_warning "xeyes not available for GUI testing"
        fi
    else
        print_error "X11 server is not responding"
        return 1
    fi
}

# Main function
main() {
    case "${1:-start}" in
        start)
            if is_x11_running; then
                print_warning "X11 server is already running on $DISPLAY"
                show_status
            else
                start_xvfb
                if [ $? -eq 0 ]; then
                    start_window_manager
                    print_success "X11 server setup complete!"
                    print_status "You can now run GUI applications"
                    print_status "Use 'export DISPLAY=$DISPLAY_NUM' in other terminals"
                fi
            fi
            ;;
        stop)
            stop_x11
            ;;
        restart)
            stop_x11
            sleep 1
            start_xvfb
            if [ $? -eq 0 ]; then
                start_window_manager
            fi
            ;;
        status)
            show_status
            ;;
        test)
            test_x11
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|test}"
            echo ""
            echo "Commands:"
            echo "  start   - Start X11 server (Xvfb)"
            echo "  stop    - Stop X11 server"
            echo "  restart - Restart X11 server"
            echo "  status  - Show X11 server status"
            echo "  test    - Test X11 server functionality"
            echo ""
            echo "Environment variables:"
            echo "  DISPLAY_NUM       - Display number (default: :99)"
            echo "  SCREEN_RESOLUTION - Screen resolution (default: 1920x1080x24)"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
