# Face Detection NPU Module - Detailed Implementation Plan

## AI Context & System Requirements

### Platform Context (RK3588 NPU Optimized)

```
Platform: Orange Pi 5 Plus/Ultra with RK3588 SoC
- CPU: 4x Cortex-A76 @ 2.4GHz + 4x Cortex-A55 @ 1.8GHz  
- Memory: 4GB/8GB LPDDR4X-4224 (CRITICAL: Memory-constrained environment)
- Storage: 240GB SSD
- Hardware Accelerators: 
  * NPU (Neural Processing Unit) - PRIMARY for face detection (6 TOPS, 3 cores)
  * RGA (2D Raster Graphics Accelerator) - Upstream integration (frame preprocessing)
  * VPU (Video Processing Unit) - Upstream pipeline coordination
- OS: Ubuntu 22.04 LTS ARM64
- Cross-compilation: aarch64-linux-gnu toolchain required
- Thermal Management: 85°C max operating, throttle at 80°C
- NPU Memory Limit: 4GB addressable memory per context
```

### Technology Stack Context (NPU-First Approach)

```
Face Detection Technology Choices (Mandatory for RK3588):
- AI Inference: RockChip NPU (Neural Processing Unit) - MANDATORY
- Model Format: RKNN (.rknn) optimized models with INT8 quantization
- Memory Management: DMABUF zero-copy operations - CRITICAL
- Hardware Integration: NPU + RGA coordination for preprocessing
- Build System: CMake with RK3588-specific optimizations
- AI Framework: RKNN Runtime API (librknnrt.so)
- Model Architecture: RetinaFace-MobileNet optimized for NPU
- Performance: NPU utilization >80% target across 3 cores

FORBIDDEN Technologies:
- Software-only inference (CPU/GPU usage too high)
- Non-quantized models (memory and performance impact)
- Generic inference frameworks (TensorRT, TensorFlow Lite)
- Memory copying operations (bandwidth limited)
```

### Resource Allocation Context (Critical Constraints)

```
Face Detection NPU Resource Assignment:
- CPU Cores: Coordinate with existing allocations
  * Core 0-1 (A55): UI Client + System Services (AVOID)
  * Core 2-3 (A55): RTSP + MPP coordination
  * Core 4-5 (A76): Available for NPU worker threads and post-processing
  * Core 6-7 (A76): Reserved for face recognition module coordination
  
- Memory Allocation (STRICT LIMITS):
  * 4GB Config: NPU Module ≤1.5GB (includes model weights, buffers)
  * 8GB Config: NPU Module ≤2.5GB (includes model weights, buffers)
  * NPU Context Limit: 4GB addressable memory per RKNN context
  
- Hardware Resource Coordination:
  * NPU: Primary user (3 cores for face detection inference)
  * RGA: Upstream integration (receive preprocessed frames)
  * Memory: DMABUF pools shared with RGA preprocessing
  
- Performance Targets (Platform Limits):
  * 4GB: 8 concurrent streams with face detection
  * 8GB: 16 concurrent streams with face detection
  * Latency: <50ms inference time per batch
  * NPU Utilization: 80-90% across 3 cores optimal range
  * Throughput: 30+ FPS per stream on 640x640 input
```

### Integration Context (AI Box Pipeline)

```
Module Dependencies & Interfaces:
- INPUT: RGA Preprocessing Module (DMABUF BGR/RGB frames)
- OUTPUT: Face Recognition Module (face bounding boxes, crop regions)
- COORDINATION: Other AI modules (NPU resource sharing, thermal control)

Data Flow Pipeline:
RGA Preprocess → BGR DMABUF → NPU Face Detection → Bounding Boxes → Face Recognition

AI Model Pipeline:
Preprocessed Frame → RetinaFace Inference → NMS Post-processing → Face Detections

Resource Sharing Strategy:
- Hardware: NPU primary (3 cores), coordinate with face recognition
- Memory: DMABUF pools shared with RGA preprocessing
- AI Models: Model sharing between detection and recognition contexts
- Timing: Frame synchronization across AI pipeline stages
- Error Handling: Cascade error recovery to dependent AI modules

Critical Integration Points:
1. DMABUF import from RGA preprocessing module
2. NPU hardware operation coordination (3 cores)
3. Face detection result export to face recognition module
4. Model management and hot-swapping capabilities
5. Real-time performance monitoring and adaptive quality control
```

### Development Constraints Context

```
Mandatory Requirements:
- Hardware-in-the-Loop: All testing on actual Orange Pi 5 Plus/Ultra
- RK3588-First Development: All decisions prioritize NPU capabilities
- Zero-Copy Architecture: DMABUF mandatory, no frame copying
- AI Model Optimization: RKNN format with INT8 quantization mandatory
- NPU Core Coordination: Utilize all 3 NPU cores optimally
- Hardware Validation: NPU, model accuracy, performance testing required

Performance Constraints:
- Memory Bandwidth: Limited, zero-copy operations critical
- Thermal Budget: Shared across all modules, adaptive scaling required
- Power Efficiency: Embedded platform, optimize for sustained AI operation
- AI Pipeline Latency: Total detection <50ms for real-time processing
- NPU Coordination: Must coordinate with face recognition module

Quality Gates:
- Orange Pi hardware validation for every major component
- AI model accuracy validation (mAP >0.85 on validation set)
- Thermal cycling tests (idle → max AI load → thermal throttling)
- Memory leak detection under 72-hour stress testing
- Integration testing with full AI pipeline (RGA → NPU → Face Recognition)
```

### Current Project Status Context

```
Face Detection NPU Module Status:
- Phase: Implementation Planning (Phase 1 pending)
- Dependencies: RGA Preprocessing Module (DMABUF BGR frame input)
- Critical Path: Foundation → Model Loading → NPU Engine → Integration
- Hardware Access: Orange Pi 5 Plus/Ultra required for validation

Key Decisions Made:
1. RKNN Runtime as primary inference engine (NPU acceleration)
2. RetinaFace-MobileNet as base detection model architecture
3. DMABUF zero-copy memory architecture with RGA integration
4. Multi-core NPU utilization strategy (3 cores)
5. Thermal-aware adaptive performance scaling

Integration Requirements:
- RGA Module: DMABUF BGR frame input interface (640x640, 320x320)
- Face Recognition Module: Face detection result output protocol
- AI Models: RKNN format with INT8 quantization (.rknn files)
- Model Management: Hot-swapping and version control system
- System Monitor: NPU utilization and thermal telemetry integration
```

### Risk Context (Critical for RK3588)

```
Critical Risks (Address Immediately):
- R2: NPU model accuracy degradation after INT8 quantization
- R1: RKNN Runtime compatibility and DMABUF integration complexity
- R3: NPU thermal throttling affecting inference performance

High Priority Risks:
- R4: Memory limitations constraining model size and batch processing
- R5: Multi-core NPU coordination complexity and load balancing
- R6: Real-time performance requirements vs. detection accuracy trade-offs

Mitigation Strategies (Mandatory):
- Early prototype testing on Orange Pi with real face detection models
- RKNN model quantization validation with accuracy benchmarks
- NPU thermal monitoring with adaptive inference rate control
- Model accuracy validation with diverse face datasets
- Hardware resource scheduling with performance monitoring

Testing Requirements:
- All components tested on target Orange Pi hardware
- Face detection accuracy validation (mAP, precision, recall metrics)
- Thermal cycling validation (cold boot → sustained AI processing)
- Memory pressure testing at platform limits
- NPU performance profiling under multi-stream load
```

## Task Overview Summary

| Task ID                            | Task Name                       | Priority | Status    | Estimated Hours | Dependencies | Assignee     | Platform Focus          |
| ---------------------------------- | ------------------------------- | -------- | --------- | --------------- | ------------ | ------------ | ----------------------- |
| **Phase 1: Foundation Setup**      |                                 |          |           | **4-7 hours**   |              |              |                         |
| 1.1                                | Project Structure Creation      | Critical | ⏳ Pending | 2-3h            | RGA Module   | Lead Dev     | RK3588 NPU System       |
| 1.2                                | CMake & RKNN Dependencies Setup | Critical | ⏳ Pending | 2-4h            | 1.1          | DevOps       | RockChip + DMABUF       |
| **Phase 2: Core Implementation**   |                                 |          |           | **42-58 hours** |              |              |                         |
| 2.1                                | RKNN Model Loader               | Critical | ⏳ Pending | 10-14h          | 1.2          | AI Engineer  | NPU Model Management    |
| 2.2                                | NPU Inference Engine            | Critical | ⏳ Pending | 12-16h          | 2.1          | NPU Dev      | 3-Core Optimization     |
| 2.3                                | Detection Post-Processor        | Critical | ⏳ Pending | 10-14h          | 2.2          | AI Engineer  | NMS + Result Parsing    |
| 2.4                                | Detection Tracker               | High     | ⏳ Pending | 10-14h          | 2.3          | CV Engineer  | Multi-Frame Tracking    |
| **Phase 3: Integration & Testing** |                                 |          |           | **18-26 hours** |              |              |                         |
| 3.1                                | RGA Integration                 | Critical | ⏳ Pending | 6-8h            | Phase 2      | Integration  | Pipeline Connection     |
| 3.2                                | Model Accuracy Validation       | Critical | ⏳ Pending | 8-12h           | 3.1          | AI Engineer  | mAP + Benchmark Tests   |
| 3.3                                | Unit Testing Suite              | High     | ⏳ Pending | 4-6h            | Phase 2      | QA + Devs    | NPU Hardware Validation |
| **Phase 4: AI Pipeline Features**  |                                 |          |           | **16-24 hours** |              |              |                         |
| 4.1                                | Multi-Stream Coordination       | Medium   | ⏳ Pending | 8-12h           | Phase 3      | AI Architect | NPU Resource Scheduling |
| 4.2                                | Performance Optimization        | Medium   | ⏳ Pending | 8-12h           | 4.1          | Performance  | Thermal + Batch Tuning  |
| **Phase 5: Production Readiness**  |                                 |          |           | **12-18 hours** |              |              |                         |
| 5.1                                | AI Accuracy Stress Testing      | High     | ⏳ Pending | 8-12h           | Phase 4      | AI Engineer  | Model Performance       |
| 5.2                                | Documentation & Examples        | Medium   | ⏳ Pending | 4-6h            | Phase 4      | Tech Writer  | NPU Integration         |

### Status Legend

- ⏳ **Pending**: Not started
- 🔄 **In Progress**: Currently being worked on
- ✅ **Completed**: Task finished and tested
- ⚠️ **Blocked**: Waiting for dependencies or resources
- ❌ **Failed**: Task failed and needs rework

### Resource Allocation Summary

- **Total Estimated Time**: 92-133 hours (7-10 weeks)
- **Critical Path**: Tasks 1.1 → 1.2 → 2.1 → 2.2 → 2.3 → 3.1 → 3.2 → 5.1
- **RK3588 NPU Focus**: 90% of tasks include hardware-specific optimizations
- **Hardware Testing Required**: Tasks 2.1, 2.2, 2.3, 3.2, 4.1, 4.2, 5.1

## Milestone Tracking

| Milestone                        | Target Date | Status    | Completion % | Key Deliverables                                          | Risk Level |
| -------------------------------- | ----------- | --------- | ------------ | --------------------------------------------------------- | ---------- |
| **M1: Foundation Complete**      | Week 1      | ⏳ Pending | 0%           | Build system, RKNN integration, basic structure           | 🟢 Low      |
| **M2: Core AI Engine Ready**     | Week 4      | ⏳ Pending | 0%           | Model loading, NPU inference, basic face detection        | 🔴 Critical |
| **M3: Pipeline Integration**     | Week 6      | ⏳ Pending | 0%           | RGA→NPU pipeline, multi-stream support                    | 🟡 Medium   |
| **M4: AI Performance Optimized** | Week 8      | ⏳ Pending | 0%           | Multi-core NPU, performance optimization, accuracy tuning | 🟠 High     |
| **M5: Production Ready**         | Week 10     | ⏳ Pending | 0%           | Stress testing, validation, documentation                 | 🟢 Low      |

### Milestone Success Criteria

#### M1: Foundation Complete

- [ ] CMake builds successfully with RKNN Runtime dependencies
- [ ] RockChip NPU library linked and functional
- [ ] DMABUF support detected and configured
- [ ] Basic project structure created following patterns

#### M2: Core AI Engine Ready

- [ ] Single RKNN model loads and initializes on NPU
- [ ] Basic face detection inference working on test images
- [ ] NPU utilization >60% for test workloads
- [ ] Memory usage within platform limits (4GB addressable)

#### M3: Pipeline Integration

- [ ] RGA BGR frames processed into face detections
- [ ] Multiple concurrent streams (8 for 4GB, 16 for 8GB)
- [ ] Integration with downstream face recognition module
- [ ] Frame timing and synchronization working

#### M4: AI Performance Optimized

- [ ] Multi-core NPU utilization (3 cores) working optimally
- [ ] Performance optimization targets met (30+ FPS per stream)
- [ ] Model accuracy validation passed (mAP >0.85)
- [ ] Thermal management and adaptive scaling working

#### M5: Production Ready

- [ ] 72-hour stress test with full AI pipeline
- [ ] Face detection accuracy validation on diverse datasets
- [ ] Complete documentation and integration guides
- [ ] RK3588 NPU compliance validation 100%

## Risk Assessment & Mitigation

| Risk ID | Risk Description                              | Probability | Impact | Risk Level | Mitigation Strategy                                       | Owner       |
| ------- | --------------------------------------------- | ----------- | ------ | ---------- | --------------------------------------------------------- | ----------- |
| **R1**  | RKNN Runtime compatibility and DMABUF issues  | Medium      | High   | 🟠 High     | Early prototype testing, software fallback implementation | NPU Dev     |
| **R2**  | NPU model accuracy degradation (quantization) | High        | High   | 🔴 Critical | Careful quantization process, accuracy benchmarking       | AI Engineer |
| **R3**  | NPU thermal throttling affecting performance  | High        | Medium | 🟡 Medium   | Thermal monitoring, adaptive inference rate control       | Performance |
| **R4**  | Memory limitations constraining model/batch   | Medium      | High   | 🟠 High     | Memory optimization, dynamic batch sizing                 | System Dev  |
| **R5**  | Multi-core NPU coordination complexity        | Medium      | Medium | 🟡 Medium   | Incremental implementation, core assignment testing       | NPU Dev     |
| **R6**  | Real-time performance vs. accuracy trade-offs | Low         | High   | 🟡 Medium   | Performance profiling, adaptive quality algorithms        | AI Engineer |
| **R7**  | NPU hardware failure and recovery             | Low         | High   | 🟡 Medium   | Error detection, graceful degradation mechanisms          | System Dev  |
| **R8**  | Integration complexity with AI pipeline       | Medium      | Medium | 🟡 Medium   | Clear interface design, incremental integration testing   | Integration |

### Risk Mitigation Actions

#### Critical Priority (Address Immediately)

- **R2**: Set up model quantization validation framework with accuracy benchmarks
- **R1**: Establish RKNN Runtime testing environment on Orange Pi hardware
- **R4**: Design memory optimization strategy with dynamic allocation

#### High Priority (Address in Phase 1-2)

- **R3**: Implement NPU thermal monitoring and adaptive performance scaling
- **R5**: Create multi-core NPU coordination and testing framework
- **R6**: Establish performance vs. accuracy profiling and optimization system

#### Medium Priority (Monitor and Address as Needed)

- **R7**: Design robust NPU error detection and recovery mechanisms
- **R8**: Establish clear AI pipeline interfaces and integration protocols

## Quick Reference

### Current Status Dashboard

```
📊 Overall Progress: 0% (0/12 tasks completed)
🎯 Current Phase: Phase 1 - Foundation Setup
⏰ Next Milestone: M1 - Foundation Complete (Week 1)
🔥 Critical Path: Task 1.1 (Project Structure Creation)
⚠️ Top Risk: NPU model accuracy degradation after quantization
🏗️ Platform Focus: RK3588 NPU and AI pipeline optimization
```

### Key Contacts

- **Technical Lead**: [Name] - Overall architecture and RK3588 optimization
- **AI Engineer**: [Name] - Model optimization, quantization, accuracy validation
- **NPU Developer**: [Name] - RKNN Runtime integration and multi-core coordination
- **Computer Vision Engineer**: [Name] - Detection algorithms and tracking
- **Performance Engineer**: [Name] - NPU optimization and thermal management

### Quick Commands

```bash
# Check task status
grep -E "⏳|🔄|✅|⚠️|❌" implementation-plan.md

# Update task status (example)
sed -i 's/| 2.1 | .* | ⏳ Pending |/| 2.1 | ... | 🔄 In Progress |/' implementation-plan.md

# View critical path
grep -A1 -B1 "Critical\|High" implementation-plan.md
```

## Executive Summary

This document provides a comprehensive implementation plan for the Face Detection NPU Module optimized for Orange Pi 5 Plus/Ultra with RK3588 SoC. The module will leverage the Neural Processing Unit (NPU) capabilities to perform real-time face detection using optimized RKNN models with zero-copy DMABUF integration.

## Project Structure Analysis

### Current Architecture (RK3588 NPU Optimized)

- **Platform**: Orange Pi 5 Plus/Ultra with RK3588 SoC and 3-core NPU
- **Integration Point**: `libraries/face-detection` (new module)
- **Hardware Acceleration**: RK3588 NPU (6 TOPS, 3 cores), DMABUF, RKNN Runtime
- **Primary Dependencies**: RKNN Runtime library, DRM/DRI, Linux DMABUF
- **Memory Architecture**: Zero-copy pipeline with DMABUF sharing
- **AI Model Format**: RKNN (.rknn) with INT8 quantization

### Integration Points

- Input from RGA preprocessing's DMABUF BGR/RGB frames
- Output to Face Recognition module via detection results and crop regions
- Hardware resource coordination with NPU cores
- Integration with existing error handling and logging systems
- AI model management and hot-swapping capabilities

## Detailed Task Breakdown

### Phase 1: Project Structure Setup

#### Task 1.1: Create Library Structure

**Priority**: Critical  
**Estimated Time**: 2-3 hours  
**Dependencies**: RGA Module DMABUF output

**Files to Create:**

```
libraries/face-detection/
├── CMakeLists.txt
├── include/
│   └── face_detection/
│       ├── rknn_model_loader.hpp
│       ├── npu_inference_engine.hpp
│       ├── detection_post_processor.hpp
│       ├── detection_tracker.hpp
│       ├── face_detection.hpp
│       └── detection_types.hpp
├── src/
│   ├── rknn_model_loader.cpp
│   ├── npu_inference_engine.cpp
│   ├── detection_post_processor.cpp
│   ├── detection_tracker.cpp
│   └── face_detection.cpp
├── models/
│   ├── retinaface_mobile320.rknn
│   ├── retinaface_mobile640.rknn
│   └── model_metadata.json
└── tests/
    ├── CMakeLists.txt
    ├── test_model_loader.cpp
    ├── test_npu_engine.cpp
    ├── test_detection.cpp
    └── test_integration.cpp
```

**Configuration Requirements:**

- CMake integration with RKNN Runtime library detection
- DMABUF and DRM library linking
- ARM64 cross-compilation support optimized for RK3588
- NPU hardware feature detection and configuration
- Memory management optimized for embedded platform constraints
- Integration with existing shared utilities and logging

#### Task 1.2: Update Root CMakeLists.txt and Dependencies

**Priority**: Critical  
**Estimated Time**: 2-4 hours  
**Dependencies**: Task 1.1

**Changes Required:**

- Add `add_subdirectory(libraries/face-detection)` to root CMakeLists.txt
- Configure RKNN Runtime library detection and linking
- Set up DMABUF and DRM dependencies
- Configure NPU hardware feature detection
- Ensure proper linking with RGA preprocessing module
- Add RK3588-specific compiler optimizations
- Configure memory management for 4GB/8GB RAM variants

### Phase 2: Core Components Implementation

#### Task 2.1: RKNN Model Loader Implementation

**Priority**: Critical  
**Estimated Time**: 10-14 hours  
**Dependencies**: Task 1.2

**Implementation Details:**

```cpp
// rknn_model_loader.hpp
struct ModelConfig {
    std::string model_path;
    std::string model_name;
    uint32_t input_width;
    uint32_t input_height;
    uint32_t input_channels;
    rknn_tensor_format input_format;
    float confidence_threshold;
    float nms_threshold;
};

class RKNNModelLoader {
public:
    struct ModelInfo {
        rknn_context context;
        rknn_input_output_num io_num;
        std::vector<rknn_tensor_attr> input_attrs;
        std::vector<rknn_tensor_attr> output_attrs;
        size_t model_size;
        std::chrono::steady_clock::time_point load_time;
    };
    
    bool loadModel(const ModelConfig& config, ModelInfo& model_info);
    void unloadModel(ModelInfo& model_info);
    bool validateModel(const ModelInfo& model_info);
    
    // Model management
    std::vector<std::string> getAvailableModels();
    bool hotSwapModel(const std::string& current_model, const std::string& new_model);
    
    // NPU configuration
    bool setNPUCoreAssignment(rknn_context context, rknn_core_mask core_mask);
    rknn_core_mask getOptimalCoreAssignment(int concurrent_streams);
    
private:
    std::unordered_map<std::string, ModelInfo> loaded_models_;
    std::mutex models_mutex_;
    
    bool readModelFile(const std::string& path, std::vector<uint8_t>& model_data);
    bool initializeRKNNContext(const std::vector<uint8_t>& model_data, 
                              rknn_context& context);
    bool queryModelAttributes(rknn_context context, ModelInfo& model_info);
};
```

**Features:**

- RKNN model file loading and validation
- NPU context initialization with optimal flags
- Model metadata extraction and management
- Multi-model support and hot-swapping
- NPU core assignment and optimization
- Model versioning and compatibility checking

#### Task 2.2: NPU Inference Engine Implementation

**Priority**: Critical  
**Estimated Time**: 12-16 hours  
**Dependencies**: Task 2.1

**Core Functionality:**

```cpp
// npu_inference_engine.hpp
struct InferenceTask {
    std::shared_ptr<RGABuffer> input_buffer;  // From RGA preprocessing
    std::string camera_id;
    uint64_t timestamp;
    uint32_t sequence_number;
    std::string model_name;
};

struct InferenceResult {
    std::string camera_id;
    uint64_t timestamp;
    uint32_t sequence_number;
    std::vector<rknn_output> raw_outputs;
    std::chrono::steady_clock::time_point inference_time;
    float inference_duration_ms;
};

class NPUInferenceEngine {
public:
    void start(size_t worker_count);
    void stop();
    
    void submitInferenceTask(const InferenceTask& task);
    bool getInferenceResult(InferenceResult& result, std::chrono::milliseconds timeout);
    
    // Batch processing
    void submitBatchTasks(const std::vector<InferenceTask>& tasks);
    std::vector<InferenceResult> getBatchResults(std::chrono::milliseconds timeout);
    
    // NPU management
    void setNPUCoreAssignment(rknn_core_mask core_mask);
    NPUUtilizationStats getNPUUtilization();
    void enableThermalThrottling(bool enable);
    
private:
    void workerThreadFunction(size_t worker_id);
    bool processInferenceTask(const InferenceTask& task, InferenceResult& result);
    
    std::vector<std::thread> worker_threads_;
    ThreadSafeQueue<InferenceTask> input_queue_;
    ThreadSafeQueue<InferenceResult> output_queue_;
    std::atomic<bool> should_stop_;
    
    std::shared_ptr<RKNNModelLoader> model_loader_;
    
    // NPU resource management
    struct NPUWorkerContext {
        rknn_context context;
        rknn_core_mask assigned_cores;
        std::vector<rknn_tensor_mem*> input_mems;
        std::vector<rknn_tensor_mem*> output_mems;
        size_t worker_id;
    };
    std::vector<NPUWorkerContext> worker_contexts_;
    
    // Performance monitoring
    struct NPUUtilizationStats {
        float core0_utilization;
        float core1_utilization;
        float core2_utilization;
        float average_inference_time_ms;
        uint64_t total_inferences;
        float thermal_throttle_factor;
    };
    NPUUtilizationStats current_stats_;
    std::mutex stats_mutex_;
    
    bool setupZeroCopyInput(const InferenceTask& task, NPUWorkerContext& ctx);
    bool executeInference(NPUWorkerContext& ctx);
    void updateUtilizationStats(float inference_time_ms);
};
```

**Features:**

- Multi-threaded NPU inference with worker pool
- Zero-copy DMABUF input from RGA preprocessing
- Multi-core NPU utilization (3 cores)
- Asynchronous inference processing
- Batch processing for optimal throughput
- Performance monitoring and thermal throttling
- NPU resource management and optimization

#### Task 2.3: Detection Post-Processor Implementation

**Priority**: Critical  
**Estimated Time**: 10-14 hours  
**Dependencies**: Task 2.2

**Core Functionality:**

```cpp
// detection_post_processor.hpp
struct FaceDetection {
    float x, y, width, height;  // Bounding box
    float confidence;
    std::vector<cv::Point2f> landmarks;  // 5-point facial landmarks
    uint64_t timestamp;
    std::string camera_id;
};

struct PostProcessingConfig {
    float confidence_threshold = 0.7f;
    float nms_threshold = 0.4f;
    float min_face_size = 20.0f;
    float max_face_size = 500.0f;
    bool enable_landmark_detection = true;
    int max_detections_per_frame = 50;
};

class DetectionPostProcessor {
public:
    struct PostProcessingTask {
        InferenceResult inference_result;
        PostProcessingConfig config;
        uint32_t frame_width;
        uint32_t frame_height;
    };
    
    std::vector<FaceDetection> processDetections(const PostProcessingTask& task);
    
    // RetinaFace-specific processing
    std::vector<FaceDetection> processRetinaFaceOutputs(
        const std::vector<rknn_output>& outputs,
        uint32_t frame_width, uint32_t frame_height,
        const PostProcessingConfig& config);
    
    // NMS and filtering
    std::vector<FaceDetection> applyNonMaximumSuppression(
        const std::vector<FaceDetection>& detections,
        float nms_threshold);
    
    std::vector<FaceDetection> filterDetectionsBySize(
        const std::vector<FaceDetection>& detections,
        float min_size, float max_size);
    
private:
    // Anchor generation and decoding
    std::vector<cv::Rect2f> generateAnchors(uint32_t feature_width, 
                                           uint32_t feature_height,
                                           const std::vector<float>& anchor_sizes);
    
    std::vector<FaceDetection> decodeDetections(
        const float* bbox_data, const float* confidence_data, 
        const float* landmark_data,
        const std::vector<cv::Rect2f>& anchors,
        uint32_t frame_width, uint32_t frame_height,
        float confidence_threshold);
    
    float calculateIoU(const cv::Rect2f& box1, const cv::Rect2f& box2);
    
    // Model-specific configurations
    struct RetinaFaceConfig {
        std::vector<std::vector<float>> anchor_sizes = {{16, 32}, {64, 128}, {256, 512}};
        std::vector<int> feature_strides = {8, 16, 32};
        std::vector<std::pair<int, int>> feature_maps;
    };
    RetinaFaceConfig retinaface_config_;
};
```

**Features:**

- RetinaFace model output decoding
- Anchor generation and bounding box regression
- Non-Maximum Suppression (NMS) implementation
- Facial landmark extraction (5-point)
- Confidence and size-based filtering
- Optimized post-processing for real-time performance
- Support for multiple detection model architectures

#### Task 2.4: Detection Tracker Implementation

**Priority**: High  
**Estimated Time**: 10-14 hours  
**Dependencies**: Task 2.3

**Core Functionality:**

```cpp
// detection_tracker.hpp
struct TrackedFace {
    uint32_t track_id;
    FaceDetection detection;
    std::vector<FaceDetection> history;
    uint32_t consecutive_misses;
    uint32_t total_detections;
    std::chrono::steady_clock::time_point first_seen;
    std::chrono::steady_clock::time_point last_seen;
    cv::Rect2f predicted_bbox;
    bool is_stable;
};

struct TrackingConfig {
    float max_distance_threshold = 50.0f;
    uint32_t max_missing_frames = 5;
    uint32_t min_track_length = 3;
    float stability_threshold = 0.8f;
    bool enable_prediction = true;
    float prediction_alpha = 0.7f;  // Exponential smoothing
};

class DetectionTracker {
public:
    std::vector<TrackedFace> updateTracks(
        const std::vector<FaceDetection>& detections,
        const std::string& camera_id,
        uint64_t timestamp);
    
    void configureLiveTracking(const TrackingConfig& config);
    std::vector<TrackedFace> getActiveTracks(const std::string& camera_id);
    void clearTracks(const std::string& camera_id);
    
    // Track management
    void removeStaleToTracks(uint64_t current_timestamp, 
                           uint64_t max_age_ms = 5000);
    TrackedFace* getTrackById(uint32_t track_id, const std::string& camera_id);
    
private:
    struct CameraTrackingState {
        std::vector<TrackedFace> active_tracks;
        uint32_t next_track_id;
        std::chrono::steady_clock::time_point last_update;
    };
    
    std::unordered_map<std::string, CameraTrackingState> camera_states_;
    std::shared_mutex tracking_mutex_;
    TrackingConfig config_;
    
    // Tracking algorithms
    std::vector<std::pair<int, int>> associateDetectionsToTracks(
        const std::vector<FaceDetection>& detections,
        std::vector<TrackedFace>& tracks);
    
    float calculateDistance(const FaceDetection& detection, 
                          const TrackedFace& track);
    
    cv::Rect2f predictNextPosition(const TrackedFace& track);
    void updateTrackHistory(TrackedFace& track, const FaceDetection& detection);
    bool isTrackStable(const TrackedFace& track);
    
    // Hungarian algorithm for optimal assignment
    std::vector<std::pair<int, int>> hungarianAssignment(
        const std::vector<std::vector<float>>& cost_matrix,
        float max_cost);
};
```

**Features:**

- Multi-object tracking for faces across frames
- Hungarian algorithm for optimal detection-to-track association
- Track prediction and smoothing
- Track lifecycle management
- Per-camera tracking state management
- Configurable tracking parameters
- Track stability assessment

### Phase 3: Integration and Testing

#### Task 3.1: RGA Integration

**Priority**: Critical  
**Estimated Time**: 6-8 hours  
**Dependencies**: Phase 2 completion

**Integration Points:**

- Connect to RGA preprocessing's DMABUF BGR output queue
- Implement frame metadata preservation through pipeline
- Handle dynamic model switching and reconfiguration
- Coordinate timing and synchronization between modules
- Implement backpressure handling from NPU to RGA

#### Task 3.2: Model Accuracy Validation

**Priority**: Critical  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 3.1

**Validation Requirements:**

- Test face detection accuracy with WIDER FACE dataset
- Validate model performance with real camera streams
- Benchmark detection mAP (mean Average Precision) >0.85
- Test model quantization impact on accuracy
- Validate detection performance across diverse face types

#### Task 3.3: Unit Testing Suite

**Priority**: High  
**Estimated Time**: 4-6 hours  
**Dependencies**: Phase 2 completion

**Test Coverage:**

- RKNN model loading and validation
- NPU inference engine functionality
- Detection post-processing accuracy
- Tracking algorithm correctness
- Integration with RGA preprocessing module

### Phase 4: AI Pipeline Features

#### Task 4.1: Multi-Stream Coordination

**Priority**: Medium  
**Estimated Time**: 8-12 hours  
**Dependencies**: Phase 3 completion

**Features:**

- NPU resource scheduling across multiple camera streams
- Dynamic load balancing between NPU cores
- Stream prioritization and quality adaptation
- Performance monitoring and optimization

#### Task 4.2: Performance Optimization

**Priority**: Medium  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 4.1

**Implementation:**

- NPU thermal throttling and adaptive performance
- Batch size optimization for different load conditions
- Memory usage optimization and garbage collection
- Real-time performance tuning and profiling

### Phase 5: Production Readiness

#### Task 5.1: AI Accuracy Stress Testing

**Priority**: High  
**Estimated Time**: 8-12 hours  
**Dependencies**: Phase 4 completion

**Testing Requirements:**

- 72-hour continuous face detection stress test
- Accuracy validation under various lighting conditions
- Performance validation under thermal stress
- Model robustness testing with edge cases

#### Task 5.2: Documentation & Examples

**Priority**: Medium  
**Estimated Time**: 4-6 hours  
**Dependencies**: Phase 4 completion

**Documentation Requirements:**

- RKNN model integration and optimization guide
- NPU performance tuning documentation
- Face detection accuracy benchmarking guide
- Troubleshooting and debugging guide

## Configuration Requirements

### Build Dependencies

```cmake
# RKNN Runtime library
find_library(RKNN_RUNTIME_LIBRARY
    NAMES rknnrt
    PATHS /usr/lib/aarch64-linux-gnu
    REQUIRED)

# DMABUF and DRM libraries
find_package(PkgConfig REQUIRED)
pkg_check_modules(DRM REQUIRED libdrm)

# OpenCV for post-processing
find_package(OpenCV REQUIRED)

# Threading support
find_package(Threads REQUIRED)

# Hardware detection
check_include_file("rknn_api.h" HAVE_RKNN)
check_include_file("linux/dma-buf.h" HAVE_DMABUF)
```

### Runtime Configuration

```json
{
  "face_detection": {
    "models": {
      "default": {
        "path": "models/retinaface_mobile640.rknn",
        "input_size": {"width": 640, "height": 640},
        "confidence_threshold": 0.7,
        "nms_threshold": 0.4
      },
      "fast": {
        "path": "models/retinaface_mobile320.rknn",
        "input_size": {"width": 320, "height": 320},
        "confidence_threshold": 0.6,
        "nms_threshold": 0.5
      }
    },
    "npu_config": {
      "core_assignment": "auto",
      "max_concurrent_contexts": 4,
      "worker_thread_count": 4,
      "enable_thermal_throttling": true,
      "batch_processing": {
        "enable": true,
        "max_batch_size": 4,
        "timeout_ms": 10
      }
    },
    "tracking": {
      "enable": true,
      "max_distance_threshold": 50.0,
      "max_missing_frames": 5,
      "min_track_length": 3
    },
    "performance": {
      "target_fps": 30,
      "max_detections_per_frame": 20,
      "enable_landmarks": true
    }
  }
}
```

## Success Criteria

### Functional Requirements

- [ ] Hardware-accelerated face detection using RK3588 NPU
- [ ] Zero-copy DMABUF pipeline from RGA to NPU
- [ ] Support for 16+ concurrent streams on 8GB platform
- [ ] Multi-model support with hot-swapping capabilities
- [ ] Real-time face tracking across frames

### Performance Requirements

- [ ] NPU utilization >80% across 3 cores for optimal throughput
- [ ] Detection latency <50ms per batch
- [ ] Memory usage <2.5GB for 16 concurrent streams (8GB config)
- [ ] Detection accuracy mAP >0.85 on WIDER FACE dataset
- [ ] Support 30+ FPS face detection per stream

### Quality Requirements

- [ ] Comprehensive unit and integration test coverage (>90%)
- [ ] Hardware validation on Orange Pi 5 Plus/Ultra
- [ ] Face detection accuracy validation with diverse datasets
- [ ] Documentation completeness for NPU integration
- [ ] Code review and RK3588 compliance validation

## Timeline Estimation

| Phase                          | Duration  | Dependencies |
| ------------------------------ | --------- | ------------ |
| Phase 1: Foundation Setup      | 1 week    | RGA Module   |
| Phase 2: Core Implementation   | 4-5 weeks | Phase 1      |
| Phase 3: Integration & Testing | 2-3 weeks | Phase 2      |
| Phase 4: AI Pipeline Features  | 2 weeks   | Phase 3      |
| Phase 5: Production Readiness  | 1-2 weeks | Phase 4      |

**Total Estimated Duration: 10-13 weeks**

## Next Steps

1. **Immediate Actions (Week 1)**
   - Set up project structure and RKNN dependencies (Tasks 1.1, 1.2)
   - Begin RKNN model loader implementation (Task 2.1)
   - Set up hardware testing environment with NPU models

2. **Short-term Goals (Weeks 2-6)**
   - Complete core AI engine implementation
   - Implement NPU inference and post-processing
   - Begin RGA integration testing

3. **Medium-term Goals (Weeks 7-10)**
   - Complete integration with RGA preprocessing
   - Implement AI pipeline features and optimization
   - Comprehensive accuracy and performance testing

4. **Long-term Goals (Weeks 11-13)**
   - AI accuracy validation and stress testing
   - Documentation completion
   - Production deployment validation

This implementation plan provides a comprehensive roadmap for developing the Face Detection NPU Module with hardware acceleration, zero-copy DMABUF integration, and AI model optimization tailored for RK3588 NPU capabilities. 