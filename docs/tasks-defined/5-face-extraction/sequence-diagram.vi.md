# Sơ Đồ <PERSON>ần Tự Module Trích <PERSON>t và Tiền Xử Lý <PERSON>ôn Mặt

Tài liệu này cung cấp các sơ đồ tuần tự chi tiết cho Module Trích <PERSON> Mặt, minh họa luồng logic và tương tác giữa các thành phần để trích xuất vùng khuôn mặt từ khung hình và chuẩn bị chúng cho nhận diện.

## Tuần Tự Khởi Tạo Module

```mermaid
sequenceDiagram
    participant Main as Main Application
    participant Extractor as FaceExtractionModule
    participant R<PERSON> as RGAManager
    participant BufferMgr as BufferManager
    participant ThreadPool as WorkerThreadPool

    Main->>Extractor: initialize(config)
    activate Extractor

    Extractor->>RGA: initializeRGA()
    activate RGA
    RGA->>RGA: c_RkRgaInit()
    RGA->>RGA: queryHardwareCapabilities()
    RGA-->>Extractor: initialization status
    deactivate RGA

    Extractor->>BufferMgr: initialize(buffer_config)
    activate BufferMgr
    BufferMgr->>BufferMgr: setupDMABUFSupport()
    BufferMgr->>BufferMgr: createBufferPools()
    BufferMgr-->>Extractor: buffer manager ready
    deactivate BufferMgr

    Extractor->>ThreadPool: createThreadPool(worker_count)
    activate ThreadPool
    ThreadPool-->>Extractor: thread pool ready
    deactivate ThreadPool

    Extractor-->>Main: initialization complete
    deactivate Extractor
```

## Tuần Tự Trích Xuất Khuôn Mặt

```mermaid
sequenceDiagram
    participant InQueue as DetectionQueue
    participant Worker as ExtractionWorker
    participant BufferMgr as BufferManager
    participant RGA as RGAManager
    participant OutQueue as RecognitionQueue

    activate Worker
    Worker->>InQueue: dequeueDetectionResult()
    activate InQueue
    InQueue-->>Worker: detection result with frame reference
    deactivate InQueue

    loop For Each Face Detection
        Worker->>Worker: calculateFaceRegion(detection, margin)
        Worker->>Worker: validateRegionBounds(region, frame_dimensions)

        Worker->>BufferMgr: allocateOutputBuffer(face_width, face_height, format)
        activate BufferMgr
        BufferMgr-->>Worker: face_buffer
        deactivate BufferMgr

        Worker->>RGA: setupCropOperation(frame_buffer, face_buffer, region)
        activate RGA

        RGA->>RGA: imcrop(frame_buffer, face_buffer, rect)
        RGA->>RGA: imresize(face_buffer, face_buffer, target_size)

        alt Format Conversion Needed
            RGA->>RGA: imcvtcolor(face_buffer, face_buffer, color_format)
        end

        RGA->>RGA: imsync()
        RGA-->>Worker: operation complete
        deactivate RGA

        Worker->>Worker: associateMetadata(face_buffer, detection_info)

        Worker->>OutQueue: enqueueFaceForRecognition(face_buffer, metadata)
        activate OutQueue
        OutQueue-->>Worker: enqueue status
        deactivate OutQueue
    end

    Worker->>Worker: processingComplete()
    deactivate Worker
```

## Tuần Tự Trích Xuất Khuôn Mặt Batch

```mermaid
sequenceDiagram
    participant Scheduler as BatchScheduler
    participant Worker as ExtractionWorker
    participant BufferMgr as BufferManager
    participant RGA as RGAManager
    participant OutQueue as RecognitionQueue

    activate Scheduler
    Scheduler->>Scheduler: collectDetectionBatch(max_batch_size, timeout)

    alt Batch Available
        Scheduler->>Worker: processBatch(detections_batch)
        activate Worker

        Worker->>BufferMgr: prepareBatchBuffers(batch_size)
        activate BufferMgr
        BufferMgr-->>Worker: batch_buffers
        deactivate BufferMgr

        loop For Each Face in Parallel
            Worker->>RGA: processFaceAsync(frame, detection, output_buffer)
            activate RGA
            RGA-->>Worker: job_handle
            deactivate RGA

            Worker->>Worker: trackJobHandle(job_handle)
        end

        Worker->>Worker: waitForAllJobsCompletion()

        loop For Each Processed Face
            Worker->>Worker: getFaceResult(job_handle)
            Worker->>Worker: validateFaceQuality(face_buffer)

            alt Face Quality Acceptable
                Worker->>OutQueue: enqueueFaceForRecognition(face_buffer, metadata)
                activate OutQueue
                OutQueue-->>Worker: enqueue status
                deactivate OutQueue
            else Low Quality Face
                Worker->>Worker: logLowQualityFace(metadata)
                Worker->>BufferMgr: releaseBuffer(face_buffer)
            end
        end

        Worker-->>Scheduler: batch processing complete
        deactivate Worker

    else No Batch Available
        Scheduler->>Scheduler: wait(polling_interval)
    end

    deactivate Scheduler
```

## Tuần Tự Alignment Khuôn Mặt (Khi Sử Dụng Landmark)

```mermaid
sequenceDiagram
    participant Worker as ExtractionWorker
    participant Aligner as FaceAligner
    participant RGA as RGAManager

    activate Worker
    Worker->>Worker: extractLandmarksFromDetection(detection)

    alt Landmarks Available
        Worker->>Aligner: calculateAlignmentTransform(landmarks)
        activate Aligner

        Aligner->>Aligner: computeTransformationMatrix(landmarks, reference_points)
        Aligner-->>Worker: transformation_matrix
        deactivate Aligner

        Worker->>RGA: setupAffineTransform(frame_buffer, face_buffer, transformation_matrix)
        activate RGA

        RGA->>RGA: improcess(frame_buffer, face_buffer, transform)
        RGA-->>Worker: transform complete
        deactivate RGA

    else No Landmarks Available
        Worker->>Worker: fallbackToSimpleCrop(detection)
    end

    Worker->>Worker: continueProcessing()
    deactivate Worker
```

## Tuần Tự Đánh Giá Chất Lượng Khuôn Mặt

```mermaid
sequenceDiagram
    participant Worker as ExtractionWorker
    participant QA as QualityAssessor
    participant BufferMgr as BufferManager

    activate Worker
    Worker->>Worker: processFaceExtraction()

    Worker->>QA: assessFaceQuality(face_buffer)
    activate QA

    QA->>QA: mapBufferForCPUAccess(face_buffer)
    QA->>QA: computeBrightness()
    QA->>QA: detectBlur()
    QA->>QA: checkFaceSize()
    QA->>QA: assessPose()

    QA-->>Worker: quality_metrics
    deactivate QA

    alt Quality Above Threshold
        Worker->>Worker: proceedWithRecognition(face_buffer)
    else Quality Below Threshold
        Worker->>Worker: logLowQualityFace(metrics)
        Worker->>BufferMgr: releaseBuffer(face_buffer)
        activate BufferMgr
        BufferMgr-->>Worker: buffer released
        deactivate BufferMgr
    end

    Worker->>Worker: processingComplete()
    deactivate Worker
```

## Tuần Tự Xử Lý Lỗi

```mermaid
sequenceDiagram
    participant Worker as ExtractionWorker
    participant ErrorHandler as ErrorHandler
    participant RGA as RGAManager
    participant BufferMgr as BufferManager
    participant App as Application

    activate Worker
    Worker->>Worker: detectError(error_type)
    Worker->>ErrorHandler: handleExtractionError(error_type, details)
    activate ErrorHandler

    alt RGA Operation Error
        ErrorHandler->>RGA: resetRGAState()
        activate RGA
        RGA-->>ErrorHandler: reset status
        deactivate RGA
        ErrorHandler-->>Worker: RETRY_OPERATION

    else Invalid Region Error
        ErrorHandler->>ErrorHandler: logInvalidRegion(details)
        ErrorHandler-->>Worker: SKIP_FACE

    else Buffer Allocation Error
        ErrorHandler->>BufferMgr: diagnoseBufferIssue()
        activate BufferMgr
        BufferMgr-->>ErrorHandler: diagnosis
        deactivate BufferMgr
        ErrorHandler-->>Worker: RETRY_WITH_DIFFERENT_SIZE

    else Fatal Error
        ErrorHandler->>App: notifyExtractionFailure(details)
        ErrorHandler-->>Worker: ABORT_PROCESSING
    end

    deactivate ErrorHandler

    alt Action is RETRY_OPERATION
        Worker->>Worker: retryRGAOperation()
    else Action is SKIP_FACE
        Worker->>Worker: skipCurrentFace()
        Worker->>Worker: processNextFace()
    else Action is RETRY_WITH_DIFFERENT_SIZE
        Worker->>Worker: adjustTargetSize(smaller_size)
        Worker->>Worker: retryExtraction()
    else Action is ABORT_PROCESSING
        Worker->>Worker: cleanupResources()
        Worker->>Worker: skipCurrentBatch()
    end
    deactivate Worker
```

## Tuần Tự Tắt Máy Module

```mermaid
sequenceDiagram
    participant App as Application
    participant Extractor as FaceExtractionModule
    participant ThreadPool as WorkerThreadPool
    participant RGA as RGAManager
    participant BufferMgr as BufferManager

    App->>Extractor: shutdown()
    activate Extractor

    Extractor->>ThreadPool: shutdownWorkers(timeout)
    activate ThreadPool
    ThreadPool-->>Extractor: workers terminated
    deactivate ThreadPool

    Extractor->>RGA: finalizeRGA()
    activate RGA
    RGA->>RGA: c_RkRgaDeInit()
    RGA-->>Extractor: RGA finalized
    deactivate RGA

    Extractor->>BufferMgr: releaseAllBuffers()
    activate BufferMgr

    loop For Each Buffer
        BufferMgr->>BufferMgr: releaseBuffer(handle)
    end

    BufferMgr-->>Extractor: all buffers released
    deactivate BufferMgr

    Extractor-->>App: shutdown complete
    deactivate Extractor
```

## Tuần Tự Xử Lý Đa Độ Phân Giải

```mermaid
sequenceDiagram
    participant Worker as ExtractionWorker
    participant Strategy as ResolutionStrategy
    participant RGA as RGAManager

    activate Worker
    Worker->>Worker: processFaceDetection(detection)

    Worker->>Strategy: determineOptimalResolution(face_size)
    activate Strategy

    alt Large Face
        Strategy-->>Worker: HIGH_RESOLUTION
    else Medium Face
        Strategy-->>Worker: MEDIUM_RESOLUTION
    else Small Face
        Strategy-->>Worker: LOW_RESOLUTION
    end

    deactivate Strategy

    Worker->>RGA: configureRGAForResolution(resolution_parameters)
    activate RGA
    RGA-->>Worker: configuration status

    Worker->>RGA: performExtraction(frame_buffer, face_buffer, region, params)
    RGA-->>Worker: extraction complete
    deactivate RGA

    Worker->>Worker: continueProcessing()
    deactivate Worker
```