#pragma once
#include <string>
#include <vector>
#include <optional>
#include <fstream>
#include <filesystem>
#include <chrono>
#include <functional>

namespace utils {
    /**
     * @brief File utilities for file and directory operations
     */
    class FileUtils {
    public:
        /**
         * @brief Read entire file content as string
         * @param filename Path to file
         * @return Optional string content (nullopt if failed)
         */
        static std::optional<std::string> readFile(const std::string& filename);

        /**
         * @brief Read file content as vector of lines
         * @param filename Path to file
         * @return Optional vector of lines (nullopt if failed)
         */
        static std::optional<std::vector<std::string>> readLines(const std::string& filename);

        /**
         * @brief Read file content as binary data
         * @param filename Path to file
         * @return Optional vector of bytes (nullopt if failed)
         */
        static std::optional<std::vector<uint8_t>> readBinary(const std::string& filename);

        /**
         * @brief Write string content to file
         * @param filename Path to file
         * @param content Content to write
         * @param append Whether to append (default: false)
         * @return true if successful
         */
        static bool writeFile(const std::string& filename, const std::string& content, bool append = false);

        /**
         * @brief Write vector of lines to file
         * @param filename Path to file
         * @param lines Lines to write
         * @param append Whether to append (default: false)
         * @return true if successful
         */
        static bool writeLines(const std::string& filename, const std::vector<std::string>& lines, bool append = false);

        /**
         * @brief Write binary data to file
         * @param filename Path to file
         * @param data Binary data to write
         * @param append Whether to append (default: false)
         * @return true if successful
         */
        static bool writeBinary(const std::string& filename, const std::vector<uint8_t>& data, bool append = false);

        /**
         * @brief Check if file exists
         * @param filename Path to file
         * @return true if file exists
         */
        static bool exists(const std::string& filename);

        /**
         * @brief Check if path is a file
         * @param path Path to check
         * @return true if path is a file
         */
        static bool isFile(const std::string& path);

        /**
         * @brief Check if path is a directory
         * @param path Path to check
         * @return true if path is a directory
         */
        static bool isDirectory(const std::string& path);

        /**
         * @brief Get file size in bytes
         * @param filename Path to file
         * @return Optional file size (nullopt if failed)
         */
        static std::optional<size_t> getFileSize(const std::string& filename);

        /**
         * @brief Get file modification time
         * @param filename Path to file
         * @return Optional modification time (nullopt if failed)
         */
        static std::optional<std::chrono::system_clock::time_point> getModificationTime(const std::string& filename);

        /**
         * @brief Copy file
         * @param source Source file path
         * @param destination Destination file path
         * @param overwrite Whether to overwrite existing file (default: false)
         * @return true if successful
         */
        static bool copyFile(const std::string& source, const std::string& destination, bool overwrite = false);

        /**
         * @brief Move/rename file
         * @param source Source file path
         * @param destination Destination file path
         * @return true if successful
         */
        static bool moveFile(const std::string& source, const std::string& destination);

        /**
         * @brief Delete file
         * @param filename Path to file
         * @return true if successful
         */
        static bool deleteFile(const std::string& filename);

        /**
         * @brief Create directory (including parent directories)
         * @param path Directory path
         * @return true if successful
         */
        static bool createDirectory(const std::string& path);

        /**
         * @brief Delete directory (recursively)
         * @param path Directory path
         * @return true if successful
         */
        static bool deleteDirectory(const std::string& path);

        /**
         * @brief List files in directory
         * @param path Directory path
         * @param recursive Whether to list recursively (default: false)
         * @return Optional vector of file paths (nullopt if failed)
         */
        static std::optional<std::vector<std::string>> listFiles(const std::string& path, bool recursive = false);

        /**
         * @brief List directories in directory
         * @param path Directory path
         * @param recursive Whether to list recursively (default: false)
         * @return Optional vector of directory paths (nullopt if failed)
         */
        static std::optional<std::vector<std::string>> listDirectories(const std::string& path, bool recursive = false);

        /**
         * @brief List all entries (files and directories) in directory
         * @param path Directory path
         * @param recursive Whether to list recursively (default: false)
         * @return Optional vector of paths (nullopt if failed)
         */
        static std::optional<std::vector<std::string>> listAll(const std::string& path, bool recursive = false);

        /**
         * @brief Get file extension
         * @param filename File path
         * @return File extension (without dot)
         */
        static std::string getExtension(const std::string& filename);

        /**
         * @brief Get filename without extension
         * @param filename File path
         * @return Filename without extension
         */
        static std::string getBasename(const std::string& filename);

        /**
         * @brief Get directory path
         * @param filename File path
         * @return Directory path
         */
        static std::string getDirectory(const std::string& filename);

        /**
         * @brief Get filename from path
         * @param path File path
         * @return Filename
         */
        static std::string getFilename(const std::string& path);

        /**
         * @brief Join path components
         * @param components Path components
         * @return Joined path
         */
        static std::string joinPath(const std::vector<std::string>& components);

        /**
         * @brief Join two path components
         * @param path1 First path component
         * @param path2 Second path component
         * @return Joined path
         */
        static std::string joinPath(const std::string& path1, const std::string& path2);

        /**
         * @brief Normalize path (resolve . and .. components)
         * @param path Path to normalize
         * @return Normalized path
         */
        static std::string normalizePath(const std::string& path);

        /**
         * @brief Get absolute path
         * @param path Relative or absolute path
         * @return Absolute path
         */
        static std::string getAbsolutePath(const std::string& path);

        /**
         * @brief Get relative path from base to target
         * @param base Base path
         * @param target Target path
         * @return Relative path
         */
        static std::string getRelativePath(const std::string& base, const std::string& target);

        /**
         * @brief Get current working directory
         * @return Current working directory path
         */
        static std::string getCurrentDirectory();

        /**
         * @brief Change current working directory
         * @param path New working directory
         * @return true if successful
         */
        static bool setCurrentDirectory(const std::string& path);

        /**
         * @brief Get temporary directory path
         * @return Temporary directory path
         */
        static std::string getTempDirectory();

        /**
         * @brief Create temporary file
         * @param prefix File prefix (default: "tmp")
         * @param suffix File suffix (default: ".tmp")
         * @return Optional temporary file path (nullopt if failed)
         */
        static std::optional<std::string> createTempFile(const std::string& prefix = "tmp", const std::string& suffix = ".tmp");

        /**
         * @brief Create temporary directory
         * @param prefix Directory prefix (default: "tmp")
         * @return Optional temporary directory path (nullopt if failed)
         */
        static std::optional<std::string> createTempDirectory(const std::string& prefix = "tmp");

        /**
         * @brief Find files matching pattern
         * @param directory Directory to search
         * @param pattern Glob pattern (e.g., "*.txt")
         * @param recursive Whether to search recursively (default: false)
         * @return Optional vector of matching file paths (nullopt if failed)
         */
        static std::optional<std::vector<std::string>> findFiles(const std::string& directory, const std::string& pattern, bool recursive = false);

        /**
         * @brief Calculate directory size (total size of all files)
         * @param path Directory path
         * @return Optional total size in bytes (nullopt if failed)
         */
        static std::optional<size_t> getDirectorySize(const std::string& path);

        /**
         * @brief Check if file is readable
         * @param filename File path
         * @return true if readable
         */
        static bool isReadable(const std::string& filename);

        /**
         * @brief Check if file is writable
         * @param filename File path
         * @return true if writable
         */
        static bool isWritable(const std::string& filename);

        /**
         * @brief Check if file is executable
         * @param filename File path
         * @return true if executable
         */
        static bool isExecutable(const std::string& filename);

        /**
         * @brief Get file permissions as string (e.g., "rwxr-xr-x")
         * @param filename File path
         * @return Optional permissions string (nullopt if failed)
         */
        static std::optional<std::string> getPermissions(const std::string& filename);

        /**
         * @brief Set file permissions
         * @param filename File path
         * @param permissions Permissions (e.g., 0644)
         * @return true if successful
         */
        static bool setPermissions(const std::string& filename, int permissions);

        /**
         * @brief Compare two files for equality
         * @param file1 First file path
         * @param file2 Second file path
         * @return Optional bool (nullopt if failed, true if equal)
         */
        static std::optional<bool> compareFiles(const std::string& file1, const std::string& file2);

        /**
         * @brief Get file hash (SHA-256)
         * @param filename File path
         * @return Optional hash string (nullopt if failed)
         */
        static std::optional<std::string> getFileHash(const std::string& filename);

        /**
         * @brief Watch file for changes (simplified implementation)
         * @param filename File path
         * @param callback Callback function called when file changes
         * @return true if watching started successfully
         */
        static bool watchFile(const std::string& filename, std::function<void()> callback);

    private:
        /**
         * @brief Helper function to match glob pattern
         * @param pattern Glob pattern
         * @param text Text to match
         * @return true if matches
         */
        static bool matchGlob(const std::string& pattern, const std::string& text);

        /**
         * @brief Helper function to convert filesystem permissions to string
         * @param perms Filesystem permissions
         * @return Permissions string
         */
        static std::string permissionsToString(std::filesystem::perms perms);
    };

    // Convenience functions for ease of use

    /**
     * @brief Read file content (convenience function)
     * @param filename File path
     * @return Optional file content
     */
    std::optional<std::string> readFile(const std::string& filename);

    /**
     * @brief Write file content (convenience function)
     * @param filename File path
     * @param content Content to write
     * @return true if successful
     */
    bool writeFile(const std::string& filename, const std::string& content);

    /**
     * @brief Check if file exists (convenience function)
     * @param filename File path
     * @return true if exists
     */
    bool fileExists(const std::string& filename);

    /**
     * @brief Create directory (convenience function)
     * @param path Directory path
     * @return true if successful
     */
    bool createDir(const std::string& path);

    /**
     * @brief Join paths (convenience function)
     * @param path1 First path
     * @param path2 Second path
     * @return Joined path
     */
    std::string joinPath(const std::string& path1, const std::string& path2);
}
