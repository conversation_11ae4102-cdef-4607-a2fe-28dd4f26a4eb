#!/bin/bash

# Complete GUI Environment Setup Script
# This script sets up everything needed for GUI applications in the development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE} GUI Environment Setup${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if running in container
is_container() {
    if [ -f /.dockerenv ] || grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Check if X11 is available
check_x11() {
    print_status "Checking X11 availability..."
    
    if [ -n "$DISPLAY" ]; then
        print_success "DISPLAY is set to: $DISPLAY"
        
        if xset q >/dev/null 2>&1; then
            print_success "X11 server is accessible"
            return 0
        else
            print_warning "DISPLAY is set but X11 server is not accessible"
            return 1
        fi
    else
        print_warning "DISPLAY environment variable is not set"
        return 1
    fi
}

# Check GPU/DRI availability
check_gpu() {
    print_status "Checking GPU/DRI availability..."
    
    if [ -d "/dev/dri" ]; then
        print_success "DRI directory exists"
        ls -la /dev/dri/
        
        if [ -c "/dev/dri/renderD128" ]; then
            print_success "Render node available: /dev/dri/renderD128"
        else
            print_warning "Render node missing: /dev/dri/renderD128"
            if [ -f "./create-render-node.sh" ]; then
                print_status "Attempting to create render node..."
                ./create-render-node.sh
            fi
        fi
    else
        print_warning "DRI directory not found - GPU acceleration may not be available"
    fi
}

# Setup X11 server if needed
setup_x11() {
    if ! check_x11; then
        print_status "Setting up virtual X11 server..."
        
        if [ -f "./start-x11.sh" ]; then
            ./start-x11.sh start
            
            # Set DISPLAY for current session
            export DISPLAY=:99
            print_success "DISPLAY set to: $DISPLAY"
            
            # Add to bashrc for future sessions
            if ! grep -q "export DISPLAY=:99" ~/.bashrc 2>/dev/null; then
                echo "export DISPLAY=:99" >> ~/.bashrc
                print_status "Added DISPLAY=:99 to ~/.bashrc"
            fi
        else
            print_error "start-x11.sh script not found"
            return 1
        fi
    fi
}

# Test GUI functionality
test_gui() {
    print_status "Testing GUI functionality..."
    
    # Test basic X11 connection
    if xset q >/dev/null 2>&1; then
        print_success "X11 connection test passed"
    else
        print_error "X11 connection test failed"
        return 1
    fi
    
    # Test OpenGL if available
    if command -v glxinfo >/dev/null 2>&1; then
        print_status "Testing OpenGL..."
        local renderer=$(glxinfo | grep "OpenGL renderer" | cut -d: -f2 | xargs)
        if [ -n "$renderer" ]; then
            print_success "OpenGL renderer: $renderer"
        else
            print_warning "OpenGL information not available"
        fi
    fi
    
    # Test with a simple GUI app if available
    if command -v xeyes >/dev/null 2>&1; then
        print_status "Starting xeyes test (will close automatically in 3 seconds)..."
        timeout 3 xeyes >/dev/null 2>&1 || true
        print_success "GUI application test completed"
    fi
}

# Show environment summary
show_summary() {
    echo ""
    print_header
    echo "Environment Summary:"
    echo "==================="
    echo "Container: $(is_container && echo "Yes" || echo "No")"
    echo "DISPLAY: ${DISPLAY:-Not set}"
    echo "X11 Available: $(xset q >/dev/null 2>&1 && echo "Yes" || echo "No")"
    echo "DRI Available: $([ -d "/dev/dri" ] && echo "Yes" || echo "No")"
    echo "Render Node: $([ -c "/dev/dri/renderD128" ] && echo "Yes" || echo "No")"
    
    if command -v glxinfo >/dev/null 2>&1; then
        local renderer=$(glxinfo 2>/dev/null | grep "OpenGL renderer" | cut -d: -f2 | xargs || echo "Unknown")
        echo "OpenGL Renderer: $renderer"
    fi
    
    echo ""
    echo "Available Scripts:"
    echo "=================="
    [ -f "./start-x11.sh" ] && echo "✓ start-x11.sh - X11 server management"
    [ -f "./create-render-node.sh" ] && echo "✓ create-render-node.sh - GPU render node creation"
    [ -f "./install-x11-deps.sh" ] && echo "✓ install-x11-deps.sh - X11 dependencies installer"
    
    echo ""
    echo "Next Steps:"
    echo "==========="
    if xset q >/dev/null 2>&1; then
        echo "✓ GUI environment is ready!"
        echo "  You can now run GUI applications like:"
        echo "  - Your Qt applications"
        echo "  - xeyes, xclock (for testing)"
        echo "  - Any other GUI software"
    else
        echo "⚠ GUI environment needs setup"
        echo "  Run: ./setup-gui-env.sh --setup"
    fi
}

# Main setup function
main_setup() {
    print_header
    print_status "Starting GUI environment setup..."
    
    # Check current environment
    check_x11
    check_gpu
    
    # Setup X11 if needed
    setup_x11
    
    # Test functionality
    test_gui
    
    print_success "GUI environment setup completed!"
    show_summary
}

# Handle command line arguments
case "${1:-status}" in
    --setup|-s|setup)
        main_setup
        ;;
    --status|status|"")
        show_summary
        ;;
    --test|test)
        test_gui
        ;;
    --help|-h|help)
        echo "GUI Environment Setup Script"
        echo "============================"
        echo ""
        echo "This script helps set up and verify the GUI environment for development."
        echo ""
        echo "Usage:"
        echo "  $0 [command]"
        echo ""
        echo "Commands:"
        echo "  status  - Show current environment status (default)"
        echo "  setup   - Set up GUI environment"
        echo "  test    - Test GUI functionality"
        echo "  help    - Show this help"
        echo ""
        echo "The script will:"
        echo "  1. Check X11 availability"
        echo "  2. Verify GPU/DRI access"
        echo "  3. Set up virtual X11 server if needed"
        echo "  4. Test GUI functionality"
        echo ""
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 --help' for usage information"
        exit 1
        ;;
esac
