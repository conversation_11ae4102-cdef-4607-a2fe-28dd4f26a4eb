# GUI Setup Guide for C-AIBOX Client

This guide explains how to run the Qt5 GUI client application both inside the dev container and on your host machine.

## Overview

The C-AIBOX client is a Qt5-based desktop application that can run in two modes:
- **GUI Mode**: Full graphical interface with QtWebEngine
- **Headless Mode**: Background execution for testing/CI

## Quick Start

### 1. Automated Setup (Recommended)

```bash
# Setup GUI environment (run once)
./scripts/setup.sh gui

# Fix X11 authorization if needed
./scripts/gui/fix-x11-auth.sh

# Build and run with automatic mode detection
./scripts/run.sh client
```

### 2. Manual Setup

```bash
# Source environment variables
source /tmp/qt_env.sh

# Build the project
cmake -S . -B build -DCMAKE_BUILD_TYPE=Debug
cmake --build build --target client_app --parallel

# Run the application
./build/apps/client/client_app
```

## Platform-Specific Instructions

### Linux Host with X11

**Requirements:**
- X11 server running
- DISPLAY environment variable set
- X11 forwarding enabled in container

**Setup:**
```bash
# Allow container to access X11 (on host)
xhost +local:docker

# In container
export DISPLAY=:0
./scripts/run.sh client
```

### macOS Host with XQuartz

**Requirements:**
- XQuartz installed and running
- X11 forwarding enabled

**Setup:**
```bash
# Install XQuartz (on host)
brew install --cask xquartz

# Start XQuartz and enable network connections
# XQuartz > Preferences > Security > "Allow connections from network clients"

# In container
export DISPLAY=host.docker.internal:0
./scripts/run.sh client
```

### Windows Host with WSL2

**Option 1: X11 Server (VcXsrv/Xming)**
```bash
# Install VcXsrv or Xming on Windows
# Configure to allow connections

# In container
export DISPLAY=host.docker.internal:0
./scripts/run.sh client
```

**Option 2: Headless Mode**
```bash
# Run without GUI
./scripts/run.sh client --headless
```

## Container Configuration

The devcontainer is pre-configured with:

```json
{
  "runArgs": [
    "--net=host",
    "-e", "DISPLAY=${localEnv:DISPLAY}",
    "-v", "/tmp/.X11-unix:/tmp/.X11-unix:rw",
    "--device=/dev/dri:/dev/dri"
  ],
  "containerEnv": {
    "QT_X11_NO_MITSHM": "1",
    "QT_QPA_PLATFORM": "xcb",
    "QTWEBENGINE_DISABLE_SANDBOX": "1",
    "LIBGL_ALWAYS_INDIRECT": "1"
  }
}
```

## Troubleshooting

### Common Issues

**1. "Authorization required" Error**
```bash
# Fix X11 authorization
./scripts/gui/fix-x11-auth.sh

# Or manually allow access (on host)
xhost +local:docker
```

**2. "Could not load Qt platform plugin 'xcb'"**
```bash
# Install missing Qt dependencies
./scripts/setup.sh gui

# Or manually install
apt-get update && apt-get install -y \
    libxcb-icccm4 libxcb-image0 libxcb-keysyms1 \
    libxcb-randr0 libxcb-render-util0 libxcb-xinerama0
```

**3. "Can't open display"**
```bash
# Check DISPLAY variable
echo $DISPLAY

# Test X11 connection
xdpyinfo

# Try different DISPLAY formats
export DISPLAY=:0
export DISPLAY=localhost:0
export DISPLAY=host.docker.internal:0
```

**4. WebEngine Sandbox Errors**
```bash
# These are normal in container environments
# The application disables sandbox automatically
export QTWEBENGINE_DISABLE_SANDBOX=1
```

### Debug Commands

```bash
# Test X11 connection
xset q
xdpyinfo

# Test with simple GUI app
xclock

# Check Qt platform plugins
ls /usr/lib/x86_64-linux-gnu/qt5/plugins/platforms/

# Run with Qt debug output
export QT_DEBUG_PLUGINS=1
export QT_LOGGING_RULES="*=true"
./build/apps/client/client_app
```

## Application Features

### GUI Mode Features
- Full Qt5 desktop interface
- QtWebEngine for web content
- Responsive window management
- System integration

### Headless Mode Features
- Background execution
- Automated testing support
- CI/CD compatibility
- Resource efficient

## Performance Considerations

### Container GUI Performance
- X11 forwarding adds latency
- Hardware acceleration may be limited
- Consider headless mode for automated tasks

### Optimization Tips
```bash
# Disable unnecessary Qt features
export QT_AUTO_SCREEN_SCALE_FACTOR=0
export QT_SCALE_FACTOR=1

# Reduce logging
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false"

# Use software rendering if needed
export LIBGL_ALWAYS_SOFTWARE=1
```

## Development Workflow

### Testing GUI Changes
```bash
# Quick rebuild and test
./scripts/run.sh client --rebuild

# Test both modes
./scripts/run.sh client
./scripts/run.sh client --headless
```

### CI/CD Integration
```bash
# Always use headless mode in CI
./scripts/run.sh client --headless

# Or set environment
export QT_QPA_PLATFORM=offscreen
./build/apps/client/client_app
```

## Security Notes

- X11 forwarding can expose the host display
- Only use `xhost +` in development environments
- Consider using Wayland for better security
- Container isolation provides some protection

## Next Steps

1. **Test the setup**: Run `./scripts/run.sh client`
2. **Verify GUI**: Check if the application window appears
3. **Fallback to headless**: Use `--headless` if GUI doesn't work
4. **Report issues**: Check logs and environment variables

For more help, see the main README.md or create an issue.
