{
    "name": "C++ <PERSON><PERSON><PERSON> (Simple)",
    "build": {
        "dockerfile": "Dockerfile",
        "context": ".."
    },
    "customizations": {
        "vscode": {
            "settings": {
                "terminal.integrated.copyOnSelection": true,
                "editor.formatOnSave": true
            },
            "extensions": [
                // Core C++ Development (using clangd instead of cpptools)
                "llvm-vs-code-extensions.vscode-clangd",
                "vadimcn.vscode-lldb",
                "ms-vscode.cpptools",
                // Build Systems
                "ms-vscode.cmake-tools",
                "twxs.cmake",
                // Code Quality & Formatting
                "xaver.clang-format",
                "notskm.clang-tidy",
                "cschlosser.doxdocgen",
                // Memory & Performance Analysis
                "ms-vscode.vscode-memory-inspector",
                "ms-vscode.hexeditor",
                // Git & Version Control
                "eamodio.gitlens",
                "mhutchie.git-graph",
                // Qt Development
                "ms-vscode.qt-tools",
                "tonka3000.qtvsctools",
                // Container & Docker
                "ms-azuretools.vscode-docker",
                "ms-vscode-remote.remote-containers",
                // Productivity Tools
                "formulahendry.code-runner",
                // File & Data Viewers
                "ms-vscode.vscode-json",
                "redhat.vscode-yaml",
                // Code Intelligence
                "VisualStudioExptTeam.vscodeintellicode",
                // Theme & UI
                "pkief.material-icon-theme",
                // Configuration
                "EditorConfig.EditorConfig",
                // Augment Integration
                "Augment.vscode-augment"
            ]
        }
    },
    "postCreateCommand": "echo 'DevContainer started successfully'",
    "remoteUser": "root",
    "forwardPorts": [
        8080,
        8081,
        5000
    ],
    "mounts": [
        "source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"
    ],
    "runArgs": [
        "--cap-add=SYS_PTRACE"
    ],
    "containerEnv": {}
}