# RTSP Input Module - Configuration Specification

## Overview

This document defines the complete configuration specification for the RTSP Input Module, including build-time dependencies, runtime configuration, and deployment settings.

## Build-Time Configuration

### CMake Dependencies (Optimized for RK3588)

```cmake
# libraries/rtsp/CMakeLists.txt
cmake_minimum_required(VERSION 3.18)

# Find required packages
find_package(PkgConfig REQUIRED)
find_package(Threads REQUIRED)

# GStreamer libraries (primary choice for RK3588 hardware acceleration)
pkg_check_modules(GSTREAMER REQUIRED
    gstreamer-1.0>=1.18.0
    gstreamer-rtsp-1.0>=1.18.0
    gstreamer-app-1.0>=1.18.0
    gstreamer-video-1.0>=1.18.0
    gstreamer-rtp-1.0>=1.18.0
    gstreamer-rtsp-server-1.0>=1.18.0
)

# RockChip specific GStreamer plugins
pkg_check_modules(GSTREAMER_ROCKCHIP
    gstreamer-rockchip-1.0
    gstreamer-mpp-1.0
)

# FFmpeg libraries (fallback option)
pkg_check_modules(FFMPEG
    libavformat>=58.0.0
    libavcodec>=58.0.0
    libavutil>=56.0.0
    libswscale>=5.0.0
    libavfilter>=7.0.0
)

# OpenSSL for secure connections
find_package(OpenSSL REQUIRED)

# JSON library for configuration
find_package(nlohmann_json REQUIRED)

# Create RTSP library
add_library(rtsp
    src/connection_manager.cpp
    src/packet_receiver.cpp
    src/nal_parser.cpp
    src/stream_multiplexer.cpp
    src/gstreamer_rtsp_client.cpp
    src/rtsp_config.cpp
)

target_include_directories(rtsp
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
    PRIVATE
        ${GSTREAMER_INCLUDE_DIRS}
        ${GSTREAMER_ROCKCHIP_INCLUDE_DIRS}
        ${FFMPEG_INCLUDE_DIRS}
)

target_link_libraries(rtsp
    PUBLIC
        shared  # Existing shared library
        Threads::Threads
        OpenSSL::SSL
        OpenSSL::Crypto
        nlohmann_json::nlohmann_json
    PRIVATE
        ${GSTREAMER_LIBRARIES}
        ${GSTREAMER_ROCKCHIP_LIBRARIES}
        ${FFMPEG_LIBRARIES}
)

target_compile_definitions(rtsp
    PRIVATE
        ${GSTREAMER_CFLAGS_OTHER}
        ${GSTREAMER_ROCKCHIP_CFLAGS_OTHER}
        ${FFMPEG_CFLAGS_OTHER}
        RTSP_USE_GSTREAMER=1
        RTSP_PLATFORM_RK3588=1
)

# RK3588 specific optimizations
target_compile_definitions(rtsp PRIVATE
    RTSP_HARDWARE_ACCELERATION=1
    RTSP_USE_MPP_DECODER=1
    RTSP_USE_RGA_SCALER=1
)

# Conditional compilation for fallback
if(FFMPEG_FOUND)
    target_compile_definitions(rtsp PRIVATE RTSP_FFMPEG_FALLBACK=1)
endif()

# Enable testing
if(BUILD_TESTING)
    add_subdirectory(tests)
endif()
```

### Compiler Flags (RK3588 Optimized)

```cmake
# RK3588 specific compiler flags
target_compile_options(rtsp PRIVATE
    -Wall
    -Wextra
    -Wpedantic
    -Wno-unused-parameter
    -O3
    -march=armv8-a+crc+crypto
    -mtune=cortex-a76.cortex-a55
    -mcpu=cortex-a76.cortex-a55
    -mfpu=neon-fp-armv8
    -ftree-vectorize
    -ffast-math
)

# Memory configuration for Orange Pi 5 Plus/Ultra
if(ORANGE_PI_RAM_4GB)
    target_compile_definitions(rtsp PRIVATE RTSP_MAX_MEMORY_MB=3072)
elseif(ORANGE_PI_RAM_8GB)
    target_compile_definitions(rtsp PRIVATE RTSP_MAX_MEMORY_MB=6144)
endif()

# Debug flags (lighter for ARM platform)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(rtsp PRIVATE
        -g
        -O1  # O1 instead of O0 for ARM performance
        -DDEBUG
        -fno-omit-frame-pointer
    )
    # Skip heavy sanitizers on ARM for performance
endif()

# Release optimizations
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_options(rtsp PRIVATE
        -DNDEBUG
        -flto
        -fuse-linker-plugin
    )
    target_link_options(rtsp PRIVATE
        -flto
        -Wl,--gc-sections
    )
endif()
```

## Runtime Configuration

### Main Configuration File (rtsp_config.json)

```json
{
  "rtsp_module": {
    "version": "1.0.0",
    "enabled": true,
    "log_level": "INFO",
    "log_file": "/var/log/aibox/rtsp.log",
    "max_log_size_mb": 100,
    "log_rotation_count": 5,
    
    "performance": {
      "max_concurrent_streams": 6,
      "thread_pool_size": 4,
      "io_thread_count": 2,
      "worker_thread_count": 2,
      "queue_size_per_stream": 60,
      "buffer_pool_size": 32,
      "max_memory_usage_mb": 1200,
      "cpu_affinity": {
        "network_threads": [2],
        "processing_threads": [3],
        "main_thread": [2, 3]
      },
      "system_integration": {
        "share_resources_with_face_ai": true,
        "priority_level": "high",
        "cpu_quota_percent": 25,
        "memory_quota_percent": 30
      },
      "rk3588_optimizations": {
        "use_mpp_decoder": true,
        "use_rga_scaler": true,
        "use_npu_preprocessing": false,
        "dma_buffer_count": 6,
        "mpp_decoder_instances": 6
      }
    },
    
    "network": {
      "default_timeout_ms": 5000,
      "connection_timeout_ms": 10000,
      "read_timeout_ms": 5000,
      "keep_alive_interval_ms": 30000,
      "max_retry_count": 3,
      "retry_delay_base_ms": 1000,
      "retry_delay_max_ms": 60000,
      "retry_jitter_ms": 500
    },
    
    "transport": {
      "prefer_tcp": true,
      "tcp_nodelay": true,
      "udp_buffer_size": 65536,
      "tcp_buffer_size": 65536,
      "rtp_port_range_start": 10000,
      "rtp_port_range_end": 20000
    },
    
    "codec": {
      "supported_codecs": ["H264", "H265"],
      "max_nal_unit_size": 1048576,
      "parameter_set_cache_size": 16,
      "validate_bitstream": true,
      "strict_compliance": false
    },
    
    "jitter_buffer": {
      "initial_size_ms": 200,
      "max_size_ms": 1000,
      "min_size_ms": 50,
      "adaptive_sizing": true,
      "target_latency_ms": 100,
      "packet_loss_threshold": 0.05
    },
    
    "statistics": {
      "enabled": true,
      "collection_interval_ms": 1000,
      "history_size": 3600,
      "export_prometheus": false,
      "prometheus_port": 9090
    },
    
    "security": {
      "ssl_verify_peer": true,
      "ssl_verify_hostname": true,
      "ssl_ca_file": "/etc/ssl/certs/ca-certificates.crt",
      "credential_storage": "encrypted",
      "max_auth_retries": 3
    }
  },
  
  "streams": [
    {
      "id": "camera_01",
      "name": "Front Door Camera",
      "enabled": true,
      "priority": "high",
      "rtsp_url": "rtsp://*************:554/stream1",
      "backup_url": "rtsp://*************:554/stream1",
      "username": "admin",
      "password": "encrypted:AES256:base64encodedpassword",
      "transport": "tcp",
      "profile": "main",
      "resolution": "1920x1080",
      "framerate": 30,
      "bitrate_kbps": 4000,
      "codec": "H264",
      "audio_enabled": false,
      "metadata": {
        "location": "entrance",
        "zone": "security",
        "camera_type": "dome",
        "manufacturer": "Hikvision",
        "model": "DS-2CD2185FWD-I"
      },
      "advanced": {
        "connection_timeout_ms": 8000,
        "read_timeout_ms": 3000,
        "retry_count": 5,
        "jitter_buffer_size_ms": 150,
        "queue_size": 150,
        "enable_statistics": true
      }
    },
    {
      "id": "camera_02",
      "name": "Parking Lot Camera",
      "enabled": true,
      "priority": "medium",
      "rtsp_url": "rtsp://*************:554/stream1",
      "username": "viewer",
      "password": "encrypted:AES256:anotherbas64password",
      "transport": "udp",
      "profile": "baseline",
      "resolution": "1280x720",
      "framerate": 15,
      "bitrate_kbps": 2000,
      "codec": "H264",
      "audio_enabled": false,
      "metadata": {
        "location": "parking",
        "zone": "monitoring",
        "camera_type": "bullet",
        "manufacturer": "Dahua",
        "model": "IPC-HFW4431S-I2"
      }
    }
  ],
  
  "profiles": {
    "high_quality": {
      "jitter_buffer_size_ms": 300,
      "queue_size": 200,
      "retry_count": 5,
      "connection_timeout_ms": 10000,
      "priority_weight": 1.0
    },
    "low_latency": {
      "jitter_buffer_size_ms": 50,
      "queue_size": 50,
      "retry_count": 2,
      "connection_timeout_ms": 3000,
      "priority_weight": 0.8
    },
    "bandwidth_optimized": {
      "jitter_buffer_size_ms": 500,
      "queue_size": 100,
      "retry_count": 3,
      "connection_timeout_ms": 5000,
      "priority_weight": 0.6,
      "prefer_tcp": false
    }
  }
}
```

### Environment Variables

```bash
# RTSP module configuration
export RTSP_CONFIG_FILE="/etc/aibox/rtsp_config.json"
export RTSP_LOG_LEVEL="INFO"
export RTSP_MAX_STREAMS="16"
export RTSP_THREAD_POOL_SIZE="8"

# FFmpeg configuration
export FFMPEG_LOG_LEVEL="warning"
export FFMPEG_THREAD_COUNT="4"

# Network configuration
export RTSP_NETWORK_TIMEOUT="5000"
export RTSP_RETRY_COUNT="3"

# Security configuration
export RTSP_SSL_VERIFY="true"
export RTSP_CREDENTIAL_KEY_FILE="/etc/aibox/keys/rtsp.key"

# Performance tuning
export RTSP_BUFFER_SIZE="65536"
export RTSP_QUEUE_SIZE="100"
export RTSP_MEMORY_LIMIT="2048"

# Debug configuration
export RTSP_DEBUG_ENABLED="false"
export RTSP_TRACE_PACKETS="false"
export RTSP_DUMP_NAL_UNITS="false"
```

### System Configuration

#### systemd Service Configuration

```ini
# /etc/systemd/system/aibox-rtsp.service
[Unit]
Description=AI Box RTSP Input Module
After=network.target
Requires=network.target

[Service]
Type=notify
User=aibox
Group=aibox
WorkingDirectory=/opt/aibox
ExecStart=/opt/aibox/bin/aibox-server
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
TimeoutStartSec=30
TimeoutStopSec=30

# Environment
Environment=RTSP_CONFIG_FILE=/etc/aibox/rtsp_config.json
Environment=RTSP_LOG_LEVEL=INFO

# Security
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/aibox /var/lib/aibox
PrivateTmp=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=4G
CPUQuota=200%

[Install]
WantedBy=multi-user.target
```

#### Network Configuration

```bash
# /etc/sysctl.d/99-aibox-rtsp.conf
# Network buffer sizes
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216

# UDP buffer sizes
net.core.netdev_max_backlog = 5000
net.ipv4.udp_rmem_min = 8192
net.ipv4.udp_wmem_min = 8192

# TCP settings
net.ipv4.tcp_rmem = 4096 87380 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr

# Connection tracking
net.netfilter.nf_conntrack_max = 65536
net.netfilter.nf_conntrack_tcp_timeout_established = 7200
```

## Configuration Validation

### JSON Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "RTSP Module Configuration",
  "type": "object",
  "required": ["rtsp_module"],
  "properties": {
    "rtsp_module": {
      "type": "object",
      "required": ["version", "performance", "network"],
      "properties": {
        "version": {
          "type": "string",
          "pattern": "^\\d+\\.\\d+\\.\\d+$"
        },
        "enabled": {
          "type": "boolean",
          "default": true
        },
        "log_level": {
          "type": "string",
          "enum": ["TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"],
          "default": "INFO"
        },
        "performance": {
          "type": "object",
          "properties": {
            "max_concurrent_streams": {
              "type": "integer",
              "minimum": 1,
              "maximum": 64,
              "default": 16
            },
            "thread_pool_size": {
              "type": "integer",
              "minimum": 1,
              "maximum": 32,
              "default": 8
            },
            "queue_size_per_stream": {
              "type": "integer",
              "minimum": 10,
              "maximum": 1000,
              "default": 100
            }
          }
        }
      }
    },
    "streams": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["id", "rtsp_url"],
        "properties": {
          "id": {
            "type": "string",
            "pattern": "^[a-zA-Z0-9_-]+$",
            "minLength": 1,
            "maxLength": 64
          },
          "rtsp_url": {
            "type": "string",
            "pattern": "^rtsp://.*"
          },
          "priority": {
            "type": "string",
            "enum": ["low", "medium", "high", "critical"],
            "default": "medium"
          }
        }
      }
    }
  }
}
```

## 16GB Configuration Support

### Enhanced Performance Configuration for 16GB Orange Pi

```json
{
  "rtsp_module": {
    "version": "1.0.0",
    "enabled": true,
    "log_level": "INFO",

    "performance": {
      "max_concurrent_streams": 24,
      "thread_pool_size": 8,
      "io_thread_count": 4,
      "worker_thread_count": 4,
      "queue_size_per_stream": 150,
      "buffer_pool_size": 128,
      "max_memory_usage_mb": 6000,
      "cpu_affinity": {
        "io_threads": [2],
        "worker_threads": [3],
        "main_thread": [2, 3]
      },
      "rk3588_optimizations": {
        "use_mpp_decoder": true,
        "use_rga_scaler": true,
        "use_npu_preprocessing": false,
        "dma_buffer_count": 16,
        "mpp_decoder_instances": 24
      }
    },

    "jitter_buffer": {
      "initial_size_ms": 150,
      "max_size_ms": 800,
      "min_size_ms": 30,
      "adaptive_sizing": true,
      "target_latency_ms": 80,
      "packet_loss_threshold": 0.03
    }
  },

  "streams": [
    {
      "id": "camera_01",
      "name": "High Priority Camera",
      "enabled": true,
      "priority": "critical",
      "rtsp_url": "rtsp://*************:554/stream1",
      "username": "admin",
      "password": "encrypted:AES256:base64encodedpassword",
      "transport": "tcp",
      "resolution": "1920x1080",
      "framerate": 30,
      "bitrate_kbps": 6000,
      "codec": "H265",
      "advanced": {
        "connection_timeout_ms": 5000,
        "read_timeout_ms": 2000,
        "retry_count": 3,
        "jitter_buffer_size_ms": 100,
        "queue_size": 200,
        "enable_statistics": true
      }
    }
  ]
}
```

### Build Configuration for 16GB

```cmake
# Configure for 16GB Orange Pi
cmake -DCMAKE_BUILD_TYPE=Release \
      -DRK3588_PLATFORM=ON \
      -DORANGE_PI_RAM_16GB=ON \
      -DRTSP_MAX_STREAMS=24 \
      -DRTSP_HIGH_PERFORMANCE=ON \
      ..
```

### Environment Variables for 16GB

```bash
# 16GB specific configuration
export RTSP_MAX_STREAMS="24"
export RTSP_MEMORY_LIMIT="6000"
export RTSP_THREAD_POOL_SIZE="8"
export RTSP_BUFFER_POOL_SIZE="128"
export RTSP_HIGH_PERFORMANCE_MODE="true"

# Enhanced hardware acceleration
export RTSP_MPP_INSTANCES="24"
export RTSP_RGA_CONCURRENT_OPS="8"
export RTSP_DMABUF_POOL_SIZE="16"
```

This configuration specification provides a comprehensive foundation for implementing and deploying the RTSP Input Module with proper build-time dependencies, runtime configuration, and system integration across 4GB, 8GB, and 16GB Orange Pi 5 Plus/Ultra variants.
