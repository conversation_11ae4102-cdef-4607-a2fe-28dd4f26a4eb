# Context Synchronization Workflow

## Overview

Since Augment AI memories are session-based and cannot be directly exported, we use a Git-based workflow to maintain consistent context across team members and development sessions.

## Repository Structure

```
docs/
├── TEAM_KNOWLEDGE_BASE.md          # Central knowledge repository
├── context/
│   ├── augment-context-template.md # Template for AI sessions
│   ├── current-decisions.md        # Latest technical decisions
│   ├── platform-constraints.md     # RK3588 specific constraints
│   └── module-contexts/
│       ├── rtsp-module-context.md  # RTSP module specific context
│       ├── face-detection-context.md
│       └── face-recognition-context.md
├── tasks-defined/
│   └── [existing task documentation]
└── meeting-notes/
    ├── technical-decisions/
    └── context-updates/
```

## Workflow Process

### 1. **Before Starting Work (Context Loading)**

```bash
# 1. Pull latest context
git pull origin main

# 2. Review updated knowledge base
cat docs/TEAM_KNOWLEDGE_BASE.md

# 3. Load relevant context into Augment AI
# Copy from docs/context/augment-context-template.md
# Add module-specific context from docs/context/module-contexts/
```

### 2. **During Development (Context Updates)**

```bash
# Create branch for context updates
git checkout -b context-update-$(date +%Y%m%d)

# Update relevant context files as you learn new information
# Example: Update RTSP module context with new findings
```

### 3. **After Significant Discoveries (Context Commit)**

```bash
# 1. Update knowledge base with new learnings
vim docs/TEAM_KNOWLEDGE_BASE.md

# 2. Update module-specific context
vim docs/context/module-contexts/rtsp-module-context.md

# 3. Document decisions made
vim docs/context/current-decisions.md

# 4. Commit changes
git add docs/
git commit -m "Context update: [brief description of new learnings]

- Updated RK3588 performance findings
- Added GStreamer pipeline optimization notes
- Documented thermal management requirements"

# 5. Push and create PR for team review
git push origin context-update-$(date +%Y%m%d)
```

### 4. **Team Context Sync (Daily/Weekly)**

```bash
# Team lead merges context updates
git checkout main
git merge context-update-branch
git push origin main

# Team members sync latest context
git pull origin main
```

## Context File Templates

### Module-Specific Context Template

```markdown
# [Module Name] Context

## Last Updated
- Date: [YYYY-MM-DD]
- Updated by: [Team Member]
- Augment Session: [Brief description]

## Current Status
- Implementation Phase: [Planning/Development/Testing/Complete]
- Key Challenges: [List current blockers/challenges]
- Recent Discoveries: [New learnings from latest work]

## Technical Context
- Technology Stack: [Specific technologies for this module]
- Dependencies: [Other modules/libraries this depends on]
- Resource Allocation: [CPU cores, memory limits, etc.]
- Performance Targets: [Specific metrics for this module]

## Integration Points
- Interfaces: [How this module connects to others]
- Shared Resources: [What resources are shared with other modules]
- Communication Protocols: [How modules communicate]

## Testing Context
- Hardware Requirements: [Specific Orange Pi testing needs]
- Test Scenarios: [Key test cases for this module]
- Performance Benchmarks: [Expected performance on RK3588]

## Known Issues & Workarounds
- Issue 1: [Description and current workaround]
- Issue 2: [Description and current workaround]

## Next Steps
- Immediate: [What needs to be done next]
- Short-term: [Goals for next week]
- Dependencies: [What this module is waiting for]
```

## Augment AI Session Workflow

### Starting a New Session

1. **Load Base Context**:
```
Platform: Orange Pi 5 Plus/Ultra with RK3588, 4GB/8GB RAM, ARM64 Ubuntu 22.04
Technology: GStreamer (not FFmpeg), MPP decoder, RGA scaler, NPU
Resource Allocation: [Copy from knowledge base]
Current Status: [Copy from latest context files]
```

2. **Add Module-Specific Context**:
```
Working on: [Module name]
Current task: [Specific task description]
Constraints: [Module-specific constraints]
Integration needs: [Other modules this affects]
```

3. **Include Recent Decisions**:
```
Recent decisions: [Copy from current-decisions.md]
Known issues: [Copy from module context]
Performance targets: [Copy from knowledge base]
```

### Ending a Session

1. **Document New Learnings**:
   - Update relevant context files
   - Add new decisions to current-decisions.md
   - Update module status and challenges

2. **Commit Context Updates**:
   - Create descriptive commit message
   - Include what was learned/decided
   - Push for team review

## Best Practices

### 1. **Context Granularity**
- **High-level**: Platform constraints, resource allocation
- **Module-level**: Specific implementation details, interfaces
- **Task-level**: Current work, immediate challenges

### 2. **Update Frequency**
- **Daily**: Update current status and immediate findings
- **Weekly**: Review and consolidate learnings
- **After major decisions**: Immediate context update

### 3. **Context Quality**
- **Specific**: Include exact numbers, configurations, commands
- **Actionable**: Focus on information that affects decisions
- **Current**: Remove outdated information regularly

### 4. **Team Coordination**
- **Review PRs**: All context updates should be reviewed
- **Sync meetings**: Weekly context sync discussions
- **Conflict resolution**: Process for handling conflicting information

## Tools and Scripts

### Context Sync Script

```bash
#!/bin/bash
# scripts/sync-context.sh

echo "🔄 Syncing AI Box context..."

# Pull latest changes
git pull origin main

# Show recent context updates
echo "📋 Recent context updates:"
git log --oneline --grep="Context update" -10

# Show current knowledge base summary
echo "📚 Current knowledge base status:"
head -20 docs/TEAM_KNOWLEDGE_BASE.md

echo "✅ Context sync complete!"
```

### Context Template Generator

```bash
#!/bin/bash
# scripts/generate-context.sh

MODULE_NAME=$1
if [ -z "$MODULE_NAME" ]; then
    echo "Usage: $0 <module-name>"
    exit 1
fi

# Generate module-specific context file
cp docs/context/module-context-template.md \
   docs/context/module-contexts/${MODULE_NAME}-context.md

echo "📝 Created context file for $MODULE_NAME"
echo "📍 Location: docs/context/module-contexts/${MODULE_NAME}-context.md"
```

## Integration with Development Workflow

### Pre-commit Hook

```bash
#!/bin/bash
# .git/hooks/pre-commit

# Check if context files are updated when code changes
if git diff --cached --name-only | grep -E "(src/|include/)" > /dev/null; then
    if ! git diff --cached --name-only | grep -E "docs/context/" > /dev/null; then
        echo "⚠️  Code changes detected but no context updates"
        echo "💡 Consider updating relevant context files in docs/context/"
        echo "🔄 Run: git add docs/context/ if context was updated"
    fi
fi
```

This workflow ensures that team knowledge and context are preserved, shared, and kept current across all development activities.
