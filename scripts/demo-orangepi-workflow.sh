#!/bin/bash

# Orange Pi Docker Build and Deploy Demo Script
# Demonstrates the complete workflow from Docker build to device deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Default configuration
ORANGE_PI_IP="***************"
DEMO_MODE=false
SKIP_DEPLOY=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --demo)
            DEMO_MODE=true
            shift
            ;;
        --skip-deploy)
            SKIP_DEPLOY=true
            shift
            ;;
        --help)
            cat << EOF
Orange Pi Docker Build and Deploy Demo Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --ip IP         Orange Pi IP address (default: ***************)
    --demo          Run in demo mode with pauses between steps
    --skip-deploy   Skip deployment step (build only)
    --help          Show this help

DESCRIPTION:
    This script demonstrates the complete workflow for building c-aibox
    using Docker and deploying it to an Orange Pi device.

EXAMPLES:
    $0                                    # Full workflow with default IP
    $0 --ip *************                # Use custom IP address
    $0 --demo                             # Run with pauses for demonstration
    $0 --skip-deploy                      # Build only, no deployment

EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            log_info "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Demo pause function
demo_pause() {
    if [[ "$DEMO_MODE" == "true" ]]; then
        echo ""
        read -p "Press Enter to continue..."
        echo ""
    fi
}

# Print banner
print_banner() {
    log_step "Orange Pi Docker Build and Deploy Demo"
    echo "Target Device: Orange Pi at $ORANGE_PI_IP"
    echo "Project Root:  $PROJECT_ROOT"
    echo "Demo Mode:     $DEMO_MODE"
    echo "Skip Deploy:   $SKIP_DEPLOY"
    echo ""
}

# Step 1: Build Docker Image
build_docker_image() {
    log_step "Step 1: Building Docker Image for Orange Pi Cross-Compilation"
    
    log_info "This step creates a Docker container with:"
    log_info "  - Ubuntu 22.04 base (GLIBC 2.35)"
    log_info "  - ARM64 cross-compilation toolchain"
    log_info "  - All project source code"
    log_info "  - Build dependencies"
    
    demo_pause
    
    cd "$PROJECT_ROOT"
    if ./scripts/cross-build/build-orangepi-docker.sh --build-image --no-build; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
    
    demo_pause
}

# Step 2: Cross-Compile Project
cross_compile_project() {
    log_step "Step 2: Cross-Compiling c-aibox for Orange Pi"
    
    log_info "This step:"
    log_info "  - Runs CMake with Orange Pi toolchain"
    log_info "  - Compiles all libraries and applications"
    log_info "  - Creates ARM64 binaries with static linking"
    log_info "  - Ensures GLIBC 2.36 compatibility"
    
    demo_pause
    
    cd "$PROJECT_ROOT"
    if ./scripts/cross-build/build-orangepi-docker.sh; then
        log_success "Cross-compilation completed successfully"
        
        # Show build artifacts
        if [[ -d "build-container" ]]; then
            log_info "Build artifacts created:"
            echo "  📁 build-container/"
            echo "  📱 build-container/bin/server"
            echo "  📚 build-container/bin/examples/"
            echo "  🔧 build-container/lib/"
        fi
    else
        log_error "Cross-compilation failed"
        exit 1
    fi
    
    demo_pause
}

# Step 3: Deploy to Orange Pi
deploy_to_orangepi() {
    if [[ "$SKIP_DEPLOY" == "true" ]]; then
        log_info "Skipping deployment step as requested"
        return
    fi

    log_step "Step 3: Deploying to Orange Pi Device"

    # Load SSH configuration
    if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
        source "$PROJECT_ROOT/.orangepi_ssh"
        log_info "Using SSH key authentication"
        SCP_CMD="scp -i $ORANGE_PI_SSH_KEY -P $ORANGE_PI_PORT"
        SSH_CMD="ssh -i $ORANGE_PI_SSH_KEY -p $ORANGE_PI_PORT"
    else
        log_warning "No SSH key found, will use password authentication"
        SCP_CMD="scp -P 22"
        SSH_CMD="ssh -p 22"
        ORANGE_PI_IP="***************"
        ORANGE_PI_USER="orangepi"
    fi
    
    log_info "This step:"
    log_info "  - Tests SSH connection to Orange Pi"
    log_info "  - Transfers ARM64 binaries to device"
    log_info "  - Sets up runtime environment"
    log_info "  - Installs dependencies if needed"
    
    demo_pause
    
    cd "$PROJECT_ROOT"
    if ./scripts/deploy/deploy-to-orangepi.sh --ip "$ORANGE_PI_IP" --build-dir build-container; then
        log_success "Deployment completed successfully"
    else
        log_error "Deployment failed"
        log_info "Check SSH connectivity and Orange Pi status"
        exit 1
    fi
    
    demo_pause
}

# Step 4: Test Deployment
test_deployment() {
    if [[ "$SKIP_DEPLOY" == "true" ]]; then
        log_info "Skipping testing step (no deployment)"
        return
    fi

    log_step "Step 4: Testing Deployment on Orange Pi"
    
    log_info "This step:"
    log_info "  - Verifies binary execution on Orange Pi"
    log_info "  - Checks system resources"
    log_info "  - Tests basic functionality"
    
    demo_pause
    
    cd "$PROJECT_ROOT"
    if ./scripts/deploy/test-on-orangepi.sh --ip "$ORANGE_PI_IP"; then
        log_success "Testing completed successfully"
    else
        log_warning "Some tests failed - check output above"
    fi
    
    demo_pause
}

# Show summary
show_summary() {
    log_step "Workflow Summary"
    
    echo "✅ Docker image built for Orange Pi cross-compilation"
    echo "✅ c-aibox cross-compiled for ARM64 architecture"
    echo "✅ GLIBC compatibility ensured (2.34-2.36)"
    
    if [[ "$SKIP_DEPLOY" != "true" ]]; then
        echo "✅ Deployed to Orange Pi at $ORANGE_PI_IP"
        echo "✅ Basic functionality tested"
    else
        echo "⏭️  Deployment skipped (build artifacts ready)"
    fi
    
    echo ""
    log_info "Next steps:"
    
    if [[ "$SKIP_DEPLOY" == "true" ]]; then
        log_info "  Deploy: ./scripts/deploy/deploy-to-orangepi.sh --ip $ORANGE_PI_IP --build-dir build-container"
        log_info "  Test:   ./scripts/deploy/test-on-orangepi.sh --ip $ORANGE_PI_IP"
    else
        log_info "  SSH to Orange Pi: ssh orangepi@$ORANGE_PI_IP"
        log_info "  Run server: cd /home/<USER>/c-aibox && ./server"
        log_info "  View logs: journalctl -f"
    fi
    
    echo ""
    log_info "For automated workflow:"
    log_info "  ./scripts/cross-build/build-and-deploy-orangepi.sh --ip $ORANGE_PI_IP"
    
    echo ""
    log_success "Orange Pi Docker workflow demonstration completed!"
}

# Main execution
main() {
    print_banner
    build_docker_image
    cross_compile_project
    deploy_to_orangepi
    test_deployment
    show_summary
}

# Run main function
main "$@"
