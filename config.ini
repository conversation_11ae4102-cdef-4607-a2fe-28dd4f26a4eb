# C-AIBOX Configuration File
# This file contains configuration settings for the C-AIBOX application
# Format: INI file with sections and key=value pairs

[database]
# Database connection settings
host=localhost
port=5432
name=c_aibox_db
user=postgres
password=your_password_here
ssl_mode=prefer
connection_timeout=30

[api]
# API server settings
host=0.0.0.0
port=8080
debug=false
api_key=your_api_key_here
rate_limit=1000
cors_enabled=true

[logging]
# Logging configuration
level=info
file_path=logs/c-aibox.log
max_file_size=10MB
max_files=5
console_output=true

[ai_models]
# AI model settings
face_recognition_model=arcface
object_detection_model=yolo
model_cache_dir=models/cache
gpu_enabled=true
batch_size=32

[security]
# Security settings
jwt_secret=your_jwt_secret_here
session_timeout=3600
max_login_attempts=5
password_min_length=8

[performance]
# Performance tuning
worker_threads=4
max_concurrent_requests=100
cache_size=1000
enable_compression=true

[features]
# Feature flags
face_recognition=true
object_detection=true
video_processing=true
real_time_analysis=false
