# Docker-in-DevContainer Configuration

This document explains how <PERSON><PERSON> is configured to work inside the development container and provides troubleshooting steps.

## Configuration Overview

The devcontainer is configured to use **Docker-outside-of-Docker** (DooD) pattern, which allows running Docker commands inside the container while using the host's Docker daemon.

### Key Configuration Elements

1. **Docker Feature**: Uses `ghcr.io/devcontainers/features/docker-outside-of-docker:1`
2. **Socket Access**: Mounts host Docker socket into container
3. **Network Configuration**: Removed `--net=host` to avoid conflicts
4. **No Manual Docker Host**: Removed conflicting `DOCKER_HOST` environment variable

## What Was Fixed

### Issues Identified and Resolved:

1. **Conflicting Docker Configuration**
   - **Problem**: Both `docker-outside-of-docker` feature AND manual `DOCKER_HOST` configuration
   - **Solution**: Removed manual `DOCKER_HOST` setting, let the feature handle it

2. **Incorrect Docker Host Setting**
   - **Problem**: `DOCKER_HOST=host.docker.internal:2376` pointing to TLS port
   - **Solution**: Removed this setting, use default Docker socket

3. **Network Conflicts**
   - **Problem**: `--net=host` can interfere with Docker-in-Docker networking
   - **Solution**: Removed `--net=host`, kept `--ipc=host` for GUI apps

4. **Missing Host Gateway**
   - **Problem**: `--add-host=host.docker.internal:host-gateway` not needed with DooD
   - **Solution**: Removed this argument

## How It Works

```mermaid
graph TD
    A[Host Docker Daemon] --> B[Docker Socket /var/run/docker.sock]
    B --> C[DevContainer]
    C --> D[Docker CLI in Container]
    D --> B
    D --> E[Docker Commands]
    E --> A
```

The container:
1. Mounts the host's Docker socket (`/var/run/docker.sock`)
2. Installs Docker CLI inside the container
3. Docker commands in container communicate with host Docker daemon
4. Containers are created on the host, not inside the devcontainer

## Testing the Configuration

Run the test script to verify Docker functionality:

```bash
# Inside the devcontainer
./scripts/test-docker-in-container.sh
```

This script tests:
- Docker daemon connectivity
- Basic Docker operations (ps, pull, run)
- Docker Compose availability
- Environment configuration

## Usage Examples

Once configured, you can use Docker normally inside the devcontainer:

```bash
# Basic Docker commands
docker --version
docker info
docker ps

# Build project images
docker build -f apps/server/Dockerfile -t myapp-server .
docker build -f apps/client/Dockerfile -t myapp-client .

# Run containers
docker run --rm myapp-server
docker run -p 8080:8080 myapp-server

# Use Docker Compose
docker-compose up -d
docker-compose logs
docker-compose down

# Cross-compilation for Orange Pi
./scripts/cross-build/build-orangepi-docker.sh
```

## Troubleshooting

### Common Issues and Solutions

#### 1. "Cannot connect to Docker daemon"

**Symptoms:**
```
Cannot connect to the Docker daemon at unix:///var/run/docker.sock
```

**Solutions:**
- Ensure Docker is running on the host
- Check if user is in docker group on host: `groups $USER`
- Restart the devcontainer: "Dev Containers: Rebuild Container"

#### 2. "Permission denied" accessing Docker socket

**Symptoms:**
```
permission denied while trying to connect to the Docker daemon socket
```

**Solutions:**
- On host, add user to docker group: `sudo usermod -aG docker $USER`
- Log out and back in on host
- Rebuild the devcontainer

#### 3. Docker commands hang

**Symptoms:**
- Docker commands start but never complete
- No error messages, just hangs

**Solutions:**
- Check if `DOCKER_HOST` is set incorrectly: `echo $DOCKER_HOST`
- Unset if present: `unset DOCKER_HOST`
- Restart devcontainer

#### 4. Network connectivity issues

**Symptoms:**
- Cannot pull images
- Cannot connect to registries

**Solutions:**
- Check host network connectivity
- Verify DNS resolution: `nslookup docker.io`
- Check proxy settings if behind corporate firewall

### Debug Commands

```bash
# Check Docker socket
ls -la /var/run/docker.sock

# Check Docker daemon status
docker info

# Check environment variables
env | grep -i docker

# Test basic connectivity
docker run --rm hello-world

# Check container networking
docker run --rm alpine ping -c 3 google.com
```

## Best Practices

1. **Use the test script** regularly to verify Docker functionality
2. **Keep Docker updated** on the host system
3. **Monitor disk space** as containers are created on host
4. **Use .dockerignore** files to optimize build contexts
5. **Clean up regularly** with `docker system prune`

## Integration with Project Scripts

The project includes several Docker-related scripts that work with this configuration:

- `scripts/start-dev-flow.sh` - Development environment management
- `scripts/cross-build/build-orangepi-docker.sh` - Cross-compilation
- `scripts/test-docker-in-container.sh` - Docker functionality testing

All these scripts are designed to work with the Docker-outside-of-Docker configuration.
