#include <gtest/gtest.h>
#include "rtsp/rtsp_config.hpp"
#include <fstream>
#include <filesystem>
#include <thread>
#include <chrono>

using namespace aibox::rtsp;

class RTSPConfigTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create temporary directory for test files
        test_dir_ = std::filesystem::temp_directory_path() / "rtsp_config_test";
        std::filesystem::create_directories(test_dir_);
    }

    void TearDown() override {
        // Clean up test files
        if (std::filesystem::exists(test_dir_)) {
            std::filesystem::remove_all(test_dir_);
        }
    }

    std::filesystem::path test_dir_;
};

// Test default configuration creation
TEST_F(RTSPConfigTest, CreateDefaultConfiguration) {
    auto config = RTSPConfigManager::createDefault();
    
    EXPECT_TRUE(config.enabled);
    EXPECT_EQ(config.version, "1.0.0");
    EXPECT_TRUE(config.auto_detect_platform);
    EXPECT_TRUE(config.isValid());
    
    // Check performance defaults
    EXPECT_GT(config.performance.max_concurrent_streams, 0);
    EXPECT_GT(config.performance.thread_pool_size, 0);
    EXPECT_GT(config.performance.max_memory_usage_mb, 0);
    
    // Check network defaults
    EXPECT_TRUE(config.network.prefer_tcp);
    EXPECT_GT(config.network.keep_alive_interval_ms, 0);
    
    // Check monitoring defaults
    EXPECT_TRUE(config.monitoring.enable_statistics);
    EXPECT_FALSE(config.monitoring.log_level.empty());
}

// Test platform-specific configurations
TEST_F(RTSPConfigTest, PlatformSpecificConfigurations) {
    auto config_4gb = RTSPConfigManager::createFor4GB();
    auto config_8gb = RTSPConfigManager::createFor8GB();
    auto config_16gb = RTSPConfigManager::createFor16GB();
    
    // Verify 4GB configuration
    EXPECT_EQ(config_4gb.platform_override, "4gb");
    EXPECT_FALSE(config_4gb.auto_detect_platform);
    EXPECT_EQ(config_4gb.performance.max_concurrent_streams, 6);
    EXPECT_EQ(config_4gb.performance.max_memory_usage_mb, platform::MEMORY_LIMIT_4GB_MB);
    
    // Verify 8GB configuration
    EXPECT_EQ(config_8gb.platform_override, "8gb");
    EXPECT_FALSE(config_8gb.auto_detect_platform);
    EXPECT_EQ(config_8gb.performance.max_concurrent_streams, 12);
    EXPECT_EQ(config_8gb.performance.max_memory_usage_mb, platform::MEMORY_LIMIT_8GB_MB);
    
    // Verify 16GB configuration
    EXPECT_EQ(config_16gb.platform_override, "16gb");
    EXPECT_FALSE(config_16gb.auto_detect_platform);
    EXPECT_EQ(config_16gb.performance.max_concurrent_streams, 24);
    EXPECT_EQ(config_16gb.performance.max_memory_usage_mb, platform::MEMORY_LIMIT_16GB_MB);
    
    // Verify scaling relationships
    EXPECT_LT(config_4gb.performance.max_concurrent_streams, config_8gb.performance.max_concurrent_streams);
    EXPECT_LT(config_8gb.performance.max_concurrent_streams, config_16gb.performance.max_concurrent_streams);
    
    EXPECT_LT(config_4gb.performance.max_memory_usage_mb, config_8gb.performance.max_memory_usage_mb);
    EXPECT_LT(config_8gb.performance.max_memory_usage_mb, config_16gb.performance.max_memory_usage_mb);
}

// Test configuration validation
TEST_F(RTSPConfigTest, ConfigurationValidation) {
    auto config = RTSPConfigManager::createDefault();
    
    // Valid configuration should pass
    auto result = RTSPConfigManager::validate(config);
    EXPECT_TRUE(result);
    EXPECT_TRUE(result.success);
    
    // Test invalid stream configuration
    RTSPConnectionConfig invalid_stream;
    invalid_stream.rtsp_url = "invalid_url";  // Not a valid RTSP URL
    config.streams.push_back(invalid_stream);
    
    result = RTSPConfigManager::validate(config);
    EXPECT_FALSE(result);
    EXPECT_FALSE(result.success);
    EXPECT_EQ(result.error_category, ErrorCategory::CONFIGURATION_ERROR);
}

// Test stream configuration validation
TEST_F(RTSPConfigTest, StreamConfigValidation) {
    RTSPConnectionConfig stream;
    
    // Valid stream configuration
    stream.rtsp_url = "rtsp://*************:554/stream1";
    stream.username = "admin";
    stream.password = "password";
    stream.timeout_ms = 5000;
    stream.retry_count = 3;
    stream.buffer_size_bytes = 1024 * 1024;
    stream.queue_size = 100;
    
    EXPECT_TRUE(stream.isValid());
    
    // Test invalid URL
    stream.rtsp_url = "http://invalid.com";
    EXPECT_FALSE(stream.isValid());
    
    // Reset to valid and test other invalid cases
    stream.rtsp_url = "rtsp://*************:554/stream1";
    EXPECT_TRUE(stream.isValid());
    
    // Test invalid timeout
    stream.timeout_ms = -1;
    EXPECT_FALSE(stream.isValid());
    
    stream.timeout_ms = 5000;
    EXPECT_TRUE(stream.isValid());
    
    // Test invalid buffer size
    stream.buffer_size_bytes = 0;
    EXPECT_FALSE(stream.isValid());
}

// Test memory usage estimation
TEST_F(RTSPConfigTest, MemoryUsageEstimation) {
    RTSPConnectionConfig stream;
    stream.rtsp_url = "rtsp://*************:554/stream1";
    stream.buffer_size_bytes = 1024 * 1024;  // 1MB
    stream.queue_size = 100;
    
    size_t estimated = stream.getEstimatedMemoryUsage();
    EXPECT_GT(estimated, 0);
    
    // Larger buffer should result in higher memory usage
    stream.buffer_size_bytes = 2 * 1024 * 1024;  // 2MB
    size_t estimated_larger = stream.getEstimatedMemoryUsage();
    EXPECT_GT(estimated_larger, estimated);
    
    // Test module-level memory estimation
    auto config = RTSPConfigManager::createFor4GB();
    config.streams.push_back(stream);
    
    size_t total_estimated = config.getTotalEstimatedMemoryUsage();
    EXPECT_GT(total_estimated, 0);
    EXPECT_LE(total_estimated, config.performance.max_memory_usage_mb * 1024 * 1024);
}

// Test configuration merging
TEST_F(RTSPConfigTest, ConfigurationMerging) {
    auto base = RTSPConfigManager::createFor4GB();
    auto override = RTSPConfigManager::createFor8GB();
    
    // Modify override to test merging
    override.version = "2.0.0";
    override.monitoring.log_level = "DEBUG";
    override.network.prefer_tcp = false;
    
    auto merged = RTSPConfigManager::merge(base, override);
    
    // Check that override values are used
    EXPECT_EQ(merged.version, "2.0.0");
    EXPECT_EQ(merged.monitoring.log_level, "DEBUG");
    EXPECT_FALSE(merged.network.prefer_tcp);
    
    // Check that 8GB performance settings are used
    EXPECT_EQ(merged.performance.max_concurrent_streams, 12);
    EXPECT_EQ(merged.performance.max_memory_usage_mb, platform::MEMORY_LIMIT_8GB_MB);
    
    // Check that platform override is preserved
    EXPECT_EQ(merged.platform_override, "8gb");
    EXPECT_FALSE(merged.auto_detect_platform);
}

// Test active stream counting
TEST_F(RTSPConfigTest, ActiveStreamCounting) {
    auto config = RTSPConfigManager::createDefault();
    
    // Add some streams
    RTSPConnectionConfig stream1, stream2, stream3;
    stream1.rtsp_url = "rtsp://*************:554/stream1";
    stream1.enabled = true;
    
    stream2.rtsp_url = "rtsp://192.168.1.101:554/stream1";
    stream2.enabled = false;  // Disabled
    
    stream3.rtsp_url = "rtsp://192.168.1.102:554/stream1";
    stream3.enabled = true;
    
    config.streams = {stream1, stream2, stream3};
    
    EXPECT_EQ(config.getActiveStreamCount(), 2);  // Only enabled streams
    EXPECT_EQ(config.streams.size(), 3);  // Total streams
}

// Test JSON serialization and deserialization
TEST_F(RTSPConfigTest, JSONSerialization) {
    auto original_config = RTSPConfigManager::createFor8GB();

    // Add a test stream
    RTSPConnectionConfig stream;
    stream.rtsp_url = "rtsp://*************:554/stream1";
    stream.username = "admin";
    stream.password = "password";
    stream.enabled = true;
    stream.priority = StreamPriority::HIGH;
    stream.transport = TransportProtocol::TCP;
    stream.metadata["location"] = "entrance";
    stream.metadata["zone"] = "security";

    original_config.streams.push_back(stream);

    // Convert to JSON
    auto json_result = RTSPConfigManager::toJson(original_config);
    if (json_result) {
        EXPECT_FALSE((*json_result).empty());

        // Parse back from JSON
        auto parsed_result = RTSPConfigManager::loadFromJson(*json_result);
        if (parsed_result) {
            auto& parsed_config = *parsed_result;

            // Verify basic settings
            EXPECT_EQ(parsed_config.version, original_config.version);
            EXPECT_EQ(parsed_config.enabled, original_config.enabled);
            EXPECT_EQ(parsed_config.platform_override, original_config.platform_override);

            // Verify performance settings
            EXPECT_EQ(parsed_config.performance.max_concurrent_streams,
                     original_config.performance.max_concurrent_streams);
            EXPECT_EQ(parsed_config.performance.max_memory_usage_mb,
                     original_config.performance.max_memory_usage_mb);

            // Verify stream settings
            ASSERT_EQ(parsed_config.streams.size(), 1);
            const auto& parsed_stream = parsed_config.streams[0];

            EXPECT_EQ(parsed_stream.rtsp_url, stream.rtsp_url);
            EXPECT_EQ(parsed_stream.username, stream.username);
            EXPECT_EQ(parsed_stream.password, stream.password);
            EXPECT_EQ(parsed_stream.enabled, stream.enabled);
            EXPECT_EQ(parsed_stream.priority, stream.priority);
            EXPECT_EQ(parsed_stream.transport, stream.transport);

            // Verify metadata
            EXPECT_EQ(parsed_stream.metadata.at("location"), "entrance");
            EXPECT_EQ(parsed_stream.metadata.at("zone"), "security");
        } else {
            // If JSON parsing is not available, that's also acceptable
            EXPECT_EQ(parsed_result.error_category, ErrorCategory::CONFIGURATION_ERROR);
            EXPECT_TRUE(parsed_result.error_message.find("JSON support not available") != std::string::npos);
        }
    } else {
        // If JSON serialization is not available, that's also acceptable
        EXPECT_EQ(json_result.error_category, ErrorCategory::CONFIGURATION_ERROR);
        EXPECT_TRUE(json_result.error_message.find("JSON support not available") != std::string::npos);
    }
}

// Test file I/O operations
TEST_F(RTSPConfigTest, FileOperations) {
    auto config = RTSPConfigManager::createFor4GB();

    // Add a test stream
    RTSPConnectionConfig stream;
    stream.rtsp_url = "rtsp://*************:554/stream1";
    stream.username = "admin";
    stream.password = "password";
    config.streams.push_back(stream);

    auto config_file = test_dir_ / "test_config.json";

    // Save to file
    auto save_result = RTSPConfigManager::saveToFile(config, config_file.string());
    if (save_result && *save_result) {
        // File should exist
        EXPECT_TRUE(std::filesystem::exists(config_file));

        // Load from file
        auto load_result = RTSPConfigManager::loadFromFile(config_file.string());
        if (load_result) {
            auto& loaded_config = *load_result;

            // Verify loaded configuration matches original
            EXPECT_EQ(loaded_config.version, config.version);
            EXPECT_EQ(loaded_config.platform_override, config.platform_override);
            EXPECT_EQ(loaded_config.performance.max_concurrent_streams,
                     config.performance.max_concurrent_streams);

            ASSERT_EQ(loaded_config.streams.size(), 1);
            EXPECT_EQ(loaded_config.streams[0].rtsp_url, stream.rtsp_url);
        } else {
            // If JSON support is not available, loading will fail
            EXPECT_EQ(load_result.error_category, ErrorCategory::CONFIGURATION_ERROR);
        }
    } else {
        // If JSON support is not available, saving will fail
        EXPECT_EQ(save_result.error_category, ErrorCategory::CONFIGURATION_ERROR);
    }
}

// Test error handling for invalid files
TEST_F(RTSPConfigTest, InvalidFileHandling) {
    auto non_existent_file = test_dir_ / "non_existent.json";

    // Try to load non-existent file
    auto result = RTSPConfigManager::loadFromFile(non_existent_file.string());
    EXPECT_FALSE(result);
    EXPECT_EQ(result.error_category, ErrorCategory::CONFIGURATION_ERROR);
    EXPECT_TRUE(result.error_message.find("Failed to open config file") != std::string::npos);

    // Try to load invalid JSON
    auto invalid_json_file = test_dir_ / "invalid.json";
    std::ofstream file(invalid_json_file);
    file << "{ invalid json content }";
    file.close();

    result = RTSPConfigManager::loadFromFile(invalid_json_file.string());
    EXPECT_FALSE(result);
    EXPECT_EQ(result.error_category, ErrorCategory::CONFIGURATION_ERROR);
}

// Test backward compatibility with existing shared library
TEST_F(RTSPConfigTest, BackwardCompatibility) {
    // Test that Result<T> type works correctly
    auto config = RTSPConfigManager::createDefault();
    auto validation_result = RTSPConfigManager::validate(config);

    // Test Result<T> interface
    EXPECT_TRUE(validation_result || !validation_result);

    if (validation_result) {
        EXPECT_TRUE(validation_result.success);
        // validation_result.value is bool, not a config object
        EXPECT_TRUE(*validation_result);
    } else {
        EXPECT_FALSE(validation_result.success);
        EXPECT_FALSE(validation_result.error_message.empty());
        // Test that error category is one of the valid ones
        EXPECT_TRUE(validation_result.error_category == ErrorCategory::CONFIGURATION_ERROR ||
                   validation_result.error_category == ErrorCategory::NETWORK_ERROR ||
                   validation_result.error_category == ErrorCategory::HARDWARE_ERROR);
    }
}

// Test thread safety of configuration operations
TEST_F(RTSPConfigTest, ThreadSafety) {
    const int num_threads = 4;
    const int operations_per_thread = 100;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    std::atomic<int> error_count{0};

    // Test concurrent configuration creation and validation
    for (int i = 0; i < num_threads; ++i) {
        threads.emplace_back([&, i]() {
            for (int j = 0; j < operations_per_thread; ++j) {
                try {
                    // Create different configurations based on thread ID
                    RTSPModuleConfig config;
                    switch (i % 3) {
                        case 0: config = RTSPConfigManager::createFor4GB(); break;
                        case 1: config = RTSPConfigManager::createFor8GB(); break;
                        case 2: config = RTSPConfigManager::createFor16GB(); break;
                    }

                    // Add a test stream
                    RTSPConnectionConfig stream;
                    stream.rtsp_url = "rtsp://192.168.1." + std::to_string(100 + i) + ":554/stream" + std::to_string(j);
                    stream.username = "admin";
                    stream.password = "password";
                    stream.enabled = (j % 2 == 0);
                    config.streams.push_back(stream);

                    // Validate configuration
                    auto result = RTSPConfigManager::validate(config);
                    if (result) {
                        success_count++;
                    } else {
                        error_count++;
                    }

                    // Test merging
                    auto base = RTSPConfigManager::createDefault();
                    auto merged = RTSPConfigManager::merge(base, config);

                    // Small delay to increase chance of race conditions
                    std::this_thread::sleep_for(std::chrono::microseconds(1));

                } catch (const std::exception& e) {
                    error_count++;
                }
            }
        });
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    // Verify that most operations succeeded (some may fail due to invalid configurations)
    int total_operations = num_threads * operations_per_thread;
    EXPECT_GT(success_count.load(), total_operations / 2);  // At least 50% should succeed
    EXPECT_LT(error_count.load(), total_operations / 2);    // Less than 50% should fail

    std::cout << "Thread safety test: " << success_count.load() << " successes, "
              << error_count.load() << " errors out of " << total_operations << " operations" << std::endl;
}

// Test platform detection and auto-configuration
TEST_F(RTSPConfigTest, PlatformDetection) {
    auto config = RTSPConfigManager::createDefault();

    // Test auto-configuration
    config.autoConfigurePlatform();

    // Verify that platform was detected and configured
    EXPECT_GT(config.performance.max_concurrent_streams, 0);
    EXPECT_GT(config.performance.max_memory_usage_mb, 0);
    EXPECT_GT(config.performance.thread_pool_size, 0);

    // Test manual platform override
    config.auto_detect_platform = false;
    config.platform_override = "8gb";
    config.autoConfigurePlatform();

    // Should use override settings
    EXPECT_EQ(config.platform_override, "8gb");
    EXPECT_FALSE(config.auto_detect_platform);
}

// Test edge cases and boundary conditions
TEST_F(RTSPConfigTest, EdgeCases) {
    // Test empty configuration
    RTSPModuleConfig empty_config;
    auto result = RTSPConfigManager::validate(empty_config);
    EXPECT_FALSE(result);  // Should fail validation

    // Test configuration with maximum values
    auto max_config = RTSPConfigManager::createFor16GB();
    max_config.performance.max_concurrent_streams = 32;  // Maximum allowed
    max_config.performance.max_memory_usage_mb = 16384;  // 16GB
    max_config.performance.cpu_usage_limit_percent = 100.0f;

    result = RTSPConfigManager::validate(max_config);
    EXPECT_TRUE(result);  // Should pass validation

    // Test configuration with invalid values
    auto invalid_config = RTSPConfigManager::createDefault();
    invalid_config.performance.max_concurrent_streams = -1;  // Invalid
    invalid_config.performance.max_memory_usage_mb = 0;      // Invalid
    invalid_config.performance.cpu_usage_limit_percent = 150.0f;  // Invalid

    result = RTSPConfigManager::validate(invalid_config);
    EXPECT_FALSE(result);  // Should fail validation

    // Test stream with boundary values
    RTSPConnectionConfig boundary_stream;
    boundary_stream.rtsp_url = "rtsp://*************:554/stream1";
    boundary_stream.timeout_ms = 300000;  // Maximum timeout (5 minutes)
    boundary_stream.retry_count = 10;     // Maximum retries
    boundary_stream.buffer_size_bytes = 16 * 1024 * 1024;  // 16MB buffer
    boundary_stream.queue_size = 1000;    // Maximum queue size

    EXPECT_TRUE(boundary_stream.isValid());

    // Test stream with invalid boundary values
    RTSPConnectionConfig invalid_stream;
    invalid_stream.rtsp_url = "rtsp://*************:554/stream1";
    invalid_stream.timeout_ms = 400000;   // Too large
    invalid_stream.retry_count = 15;      // Too many
    invalid_stream.buffer_size_bytes = 32 * 1024 * 1024;  // Too large
    invalid_stream.queue_size = 2000;     // Too large

    EXPECT_FALSE(invalid_stream.isValid());
}

// Test performance and memory usage
TEST_F(RTSPConfigTest, PerformanceAndMemory) {
    auto config = RTSPConfigManager::createFor8GB();

    // Add multiple streams to test memory calculation
    for (int i = 0; i < 10; ++i) {
        RTSPConnectionConfig stream;
        stream.rtsp_url = "rtsp://192.168.1." + std::to_string(100 + i) + ":554/stream1";
        stream.username = "admin";
        stream.password = "password";
        stream.enabled = true;
        stream.buffer_size_bytes = 1024 * 1024;  // 1MB per stream
        stream.queue_size = 100;
        config.streams.push_back(stream);
    }

    // Test memory usage calculation
    size_t total_memory = config.getTotalEstimatedMemoryUsage();
    EXPECT_GT(total_memory, 0);

    // Should be roughly 10MB + overhead for 10 streams with 1MB buffers each
    EXPECT_GT(total_memory, 10 * 1024 * 1024);  // At least 10MB
    EXPECT_LT(total_memory, 50 * 1024 * 1024);  // Less than 50MB (reasonable overhead)

    // Test that memory usage is within platform limits
    size_t memory_limit = config.performance.max_memory_usage_mb * 1024 * 1024;
    if (total_memory > memory_limit) {
        // This is expected for this test case, but validate the detection works
        auto validation_result = RTSPConfigManager::validate(config);
        EXPECT_FALSE(validation_result);
        EXPECT_TRUE(validation_result.error_message.find("memory") != std::string::npos ||
                   validation_result.error_message.find("Memory") != std::string::npos);
    }

    // Test active stream counting
    EXPECT_EQ(config.getActiveStreamCount(), 10);

    // Disable some streams and retest
    config.streams[0].enabled = false;
    config.streams[1].enabled = false;
    EXPECT_EQ(config.getActiveStreamCount(), 8);
}

// Test integration with existing error handling patterns
TEST_F(RTSPConfigTest, ErrorHandlingIntegration) {
    // Test all error categories are properly used
    std::vector<ErrorCategory> expected_categories = {
        ErrorCategory::CONFIGURATION_ERROR,
        ErrorCategory::NETWORK_ERROR,
        ErrorCategory::HARDWARE_ERROR,
        ErrorCategory::PROTOCOL_ERROR,
        ErrorCategory::CODEC_ERROR,
        ErrorCategory::RESOURCE_ERROR,
        ErrorCategory::THERMAL_ERROR
    };

    // Test that error categories are properly assigned
    auto invalid_config = RTSPConfigManager::createDefault();
    invalid_config.performance.max_concurrent_streams = -1;

    auto result = RTSPConfigManager::validate(invalid_config);
    EXPECT_FALSE(result);
    EXPECT_EQ(result.error_category, ErrorCategory::CONFIGURATION_ERROR);
    EXPECT_FALSE(result.error_message.empty());

    // Test file operation errors
    auto file_result = RTSPConfigManager::loadFromFile("/non/existent/path/config.json");
    EXPECT_FALSE(file_result);
    EXPECT_EQ(file_result.error_category, ErrorCategory::CONFIGURATION_ERROR);
    EXPECT_TRUE(file_result.error_message.find("Failed to open") != std::string::npos);
}
