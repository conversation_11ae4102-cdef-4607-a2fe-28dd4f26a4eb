#!/bin/bash

# MPP Hardware Acceleration Test for Orange Pi RK3588
# Tests the Media Processing Platform (MPP) functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Test MPP device access
test_mpp_device() {
    log_step "Testing MPP Device Access"
    
    if [ -c /dev/mpp_service ]; then
        log_success "MPP device found: /dev/mpp_service"
        
        # Check device permissions
        local perms=$(ls -l /dev/mpp_service | awk '{print $1}')
        log_info "Device permissions: $perms"
        
        # Check device major/minor numbers
        local major_minor=$(ls -l /dev/mpp_service | awk '{print $5 $6}' | tr -d ',')
        log_info "Device major/minor: $major_minor"
        
        # Test read access
        if [ -r /dev/mpp_service ]; then
            log_success "Read access: OK"
        else
            log_error "Read access: DENIED"
            return 1
        fi
        
        # Test write access
        if [ -w /dev/mpp_service ]; then
            log_success "Write access: OK"
        else
            log_error "Write access: DENIED"
            return 1
        fi
        
    else
        log_error "MPP device not found"
        return 1
    fi
}

# Test MPP library
test_mpp_library() {
    log_step "Testing MPP Library"
    
    # Check if pkg-config can find MPP
    if pkg-config --exists rockchip_mpp; then
        local version=$(pkg-config --modversion rockchip_mpp)
        log_success "MPP library version: $version"
        
        # Get library path
        local libpath=$(pkg-config --libs rockchip_mpp)
        log_info "Library flags: $libpath"
        
        # Check if library files exist
        if [ -f /usr/lib/aarch64-linux-gnu/librockchip_mpp.so ]; then
            log_success "MPP shared library found"
        else
            log_warning "MPP shared library not found in expected location"
        fi
        
    else
        log_error "MPP library not found via pkg-config"
        return 1
    fi
}

# Test MPP utilities
test_mpp_utilities() {
    log_step "Testing MPP Utilities"
    
    local utilities=("mpp_info_test" "mpi_dec_test" "mpi_enc_test")
    local found_count=0
    
    for util in "${utilities[@]}"; do
        if command -v "$util" >/dev/null 2>&1; then
            log_success "$util: Available"
            found_count=$((found_count + 1))
        else
            log_warning "$util: Not found"
        fi
    done
    
    if [ $found_count -gt 0 ]; then
        log_success "Found $found_count MPP utilities"
    else
        log_error "No MPP utilities found"
        return 1
    fi
}

# Test basic MPP functionality
test_mpp_functionality() {
    log_step "Testing Basic MPP Functionality"
    
    # Create a simple test program
    cat > /tmp/mpp_test.c << 'EOF'
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/ioctl.h>

int main() {
    int fd = open("/dev/mpp_service", O_RDWR);
    if (fd < 0) {
        printf("ERROR: Cannot open MPP device\n");
        return 1;
    }
    
    printf("SUCCESS: MPP device opened successfully\n");
    printf("File descriptor: %d\n", fd);
    
    close(fd);
    printf("SUCCESS: MPP device closed successfully\n");
    return 0;
}
EOF

    # Compile the test
    if gcc -o /tmp/mpp_test /tmp/mpp_test.c; then
        log_success "MPP test program compiled"
        
        # Run the test
        if /tmp/mpp_test; then
            log_success "MPP device access test passed"
        else
            log_error "MPP device access test failed"
            return 1
        fi
        
        # Cleanup
        rm -f /tmp/mpp_test /tmp/mpp_test.c
        
    else
        log_error "Failed to compile MPP test program"
        return 1
    fi
}

# Test GStreamer MPP plugin
test_gstreamer_mpp() {
    log_step "Testing GStreamer MPP Plugin"
    
    if command -v gst-inspect-1.0 >/dev/null 2>&1; then
        # Check for MPP plugins
        if gst-inspect-1.0 | grep -i mpp >/dev/null 2>&1; then
            log_success "GStreamer MPP plugins found"
            
            # List MPP elements
            log_info "Available MPP elements:"
            gst-inspect-1.0 | grep -i mpp | while read line; do
                log_info "  $line"
            done
            
        else
            log_warning "GStreamer MPP plugins not found"
        fi
    else
        log_warning "GStreamer not available for testing"
    fi
}

# Check system information
check_system_info() {
    log_step "System Information"
    
    log_info "Platform: $(cat /proc/device-tree/model 2>/dev/null | tr -d '\0' || echo 'Unknown')"
    log_info "Kernel: $(uname -r)"
    log_info "Architecture: $(uname -m)"
    
    # Check for video group membership
    if groups | grep -q video; then
        log_success "User is in video group"
    else
        log_warning "User is not in video group"
    fi
    
    # Check memory
    local mem_total=$(free -h | grep Mem | awk '{print $2}')
    log_info "Total memory: $mem_total"
}

# Generate test report
generate_report() {
    log_step "Generating MPP Test Report"
    
    local report_file="/tmp/mpp_test_report.txt"
    
    cat > "$report_file" << EOF
MPP Hardware Acceleration Test Report
=====================================
Date: $(date)
Platform: $(cat /proc/device-tree/model 2>/dev/null | tr -d '\0' || echo 'Unknown')
Kernel: $(uname -r)

Test Results:
- MPP Device: $([ -c /dev/mpp_service ] && echo "FOUND" || echo "NOT FOUND")
- MPP Library: $(pkg-config --modversion rockchip_mpp 2>/dev/null || echo "NOT FOUND")
- Device Access: $([ -r /dev/mpp_service ] && [ -w /dev/mpp_service ] && echo "OK" || echo "FAILED")
- User Groups: $(groups)

Recommendations:
- MPP hardware acceleration is $([ -c /dev/mpp_service ] && echo "AVAILABLE" || echo "NOT AVAILABLE")
- For video processing, use MPP-enabled applications
- Ensure user is in 'video' group for device access
EOF

    log_success "Report generated: $report_file"
    cat "$report_file"
}

# Main execution
main() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  MPP Hardware Acceleration Test"
    echo "  Orange Pi RK3588 Media Processing Platform"
    echo "=================================================="
    echo -e "${NC}"
    
    local test_passed=0
    local total_tests=5
    
    check_system_info
    
    if test_mpp_device; then
        test_passed=$((test_passed + 1))
    fi
    
    if test_mpp_library; then
        test_passed=$((test_passed + 1))
    fi
    
    if test_mpp_utilities; then
        test_passed=$((test_passed + 1))
    fi
    
    if test_mpp_functionality; then
        test_passed=$((test_passed + 1))
    fi
    
    if test_gstreamer_mpp; then
        test_passed=$((test_passed + 1))
    fi
    
    generate_report
    
    log_step "Test Summary"
    log_info "Tests passed: $test_passed/$total_tests"
    
    if [ $test_passed -eq $total_tests ]; then
        log_success "All MPP tests passed! Hardware acceleration is fully functional."
        exit 0
    elif [ $test_passed -gt 2 ]; then
        log_warning "Most MPP tests passed. Hardware acceleration is partially functional."
        exit 0
    else
        log_error "MPP tests failed. Hardware acceleration may not be available."
        exit 1
    fi
}

# Run main function
main "$@"
