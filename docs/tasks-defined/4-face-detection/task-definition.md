# Face Detection NPU Module Task Definition

## Overview
This module leverages the Neural Processing Unit (NPU) of the RK3588 to run face detection models (such as RetinaFace) on pre-processed video frames. It detects faces in the video streams and provides bounding box coordinates for further processing.

## Requirements
- Run optimized face detection models on the RK3588 NPU
- Process frames from multiple camera streams efficiently
- Maximize NPU utilization through proper core management and batch processing
- Maintain zero-copy operations for input/output data
- Support asynchronous inference for optimal performance

## Technical Approach
1. **RKNN Model Preparation and Initialization**:
   - Load optimized, quantized face detection model (.rknn format)
   - Initialize RKNN context with appropriate flags:
     - `RKNN_FLAG_MEM_ALLOC_OUTSIDE` for zero-copy
     - `RKNN_FLAG_ASYNC_MODE` for asynchronous inference
     - `RKNN_FLAG_BATCH_MODE` for batch processing if applicable
   - Configure NPU core assignment using `rknn_set_core_mask`

2. **Zero-Copy Input Processing**:
   - Create RKNN tensor memory from DMABUF file descriptors
   - Configure tensor input attributes properly
   - Implement batch processing for multiple frames when possible

3. **Model Inference**:
   - Execute model inference on the NPU
   - Manage asynchronous execution and result synchronization
   - Implement efficient tensor memory management

4. **Result Processing**:
   - Extract and decode detection results (bounding boxes, confidence scores)
   - Apply post-processing like Non-Maximum Suppression (NMS)
   - Prepare detection results for the face extraction module

5. **NPU Resource Management**:
   - Implement proper NPU core allocation strategies
   - Balance load across available NPU cores
   - Monitor and optimize NPU utilization

## Interfaces
- **Input**: DMABUF file descriptors pointing to pre-processed frames
- **Output**: Face detection results (bounding boxes, confidence scores)

## Dependencies
- RKNN Runtime library (librknnrt.so)
- RKNN API headers (rknn_api.h)
- Optimized face detection models (e.g., RetinaFace_mobile320.rknn)
- C++ standard library

## Performance Considerations
- Efficient NPU core utilization (using all 3 cores when possible)
- Batch processing to maximize throughput
- Asynchronous inference to avoid blocking
- Proper memory management to stay within NPU's 4GB addressable memory limit
- Balance between detection accuracy and performance
