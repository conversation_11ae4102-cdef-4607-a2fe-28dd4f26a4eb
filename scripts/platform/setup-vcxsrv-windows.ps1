# PowerShell script to install and configure VcXsrv on Windows
# Run this script as Administrator on Windows host

param(
    [switch]$AutoStart = $false,
    [switch]$CreateShortcut = $true,
    [switch]$ConfigureFirewall = $true
)

# Colors for output
function Write-Info { param($Message) Write-Host "[INFO] $Message" -ForegroundColor Blue }
function Write-Success { param($Message) Write-Host "[SUCCESS] $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "[WARNING] $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "[ERROR] $Message" -ForegroundColor Red }

Write-Info "VcXsrv Setup Script for C-AIBOX GUI"
Write-Info "===================================="

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script requires Administrator privileges"
    Write-Info "Please run PowerShell as Administrator and try again"
    exit 1
}

# Function to download file
function Download-File {
    param($Url, $OutputPath)
    try {
        Write-Info "Downloading from: $Url"
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath -UseBasicParsing
        Write-Success "Downloaded: $OutputPath"
        return $true
    } catch {
        Write-Error "Failed to download: $($_.Exception.Message)"
        return $false
    }
}

# Function to check if VcXsrv is installed
function Test-VcXsrvInstalled {
    $vcxsrvPath = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*" | 
                  Where-Object { $_.DisplayName -like "*VcXsrv*" }
    return $null -ne $vcxsrvPath
}

# Function to install VcXsrv
function Install-VcXsrv {
    Write-Info "Installing VcXsrv X Server..."
    
    # Check if already installed
    if (Test-VcXsrvInstalled) {
        Write-Success "VcXsrv is already installed"
        return $true
    }
    
    # Download VcXsrv
    $downloadUrl = "https://sourceforge.net/projects/vcxsrv/files/latest/download"
    $installerPath = "$env:TEMP\vcxsrv-installer.exe"
    
    Write-Info "Downloading VcXsrv installer..."
    if (-not (Download-File -Url $downloadUrl -OutputPath $installerPath)) {
        Write-Error "Failed to download VcXsrv installer"
        Write-Info "Please download manually from: https://sourceforge.net/projects/vcxsrv/"
        return $false
    }
    
    # Install VcXsrv silently
    Write-Info "Installing VcXsrv (this may take a few minutes)..."
    try {
        Start-Process -FilePath $installerPath -ArgumentList "/S" -Wait
        Write-Success "VcXsrv installed successfully"
        
        # Clean up installer
        Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
        return $true
    } catch {
        Write-Error "Failed to install VcXsrv: $($_.Exception.Message)"
        return $false
    }
}

# Function to create VcXsrv configuration
function Create-VcXsrvConfig {
    Write-Info "Creating VcXsrv configuration..."
    
    $configDir = "$env:APPDATA\Xming"
    $configFile = "$configDir\config.xlaunch"
    
    # Create directory if it doesn't exist
    if (-not (Test-Path $configDir)) {
        New-Item -ItemType Directory -Path $configDir -Force | Out-Null
    }
    
    # Create configuration file
    $configContent = @"
<?xml version="1.0" encoding="UTF-8"?>
<XLaunch WindowMode="MultiWindow" ClientMode="NoClient" LocalClient="False" Display="0" LocalProgram="xcalc" RemoteProgram="xterm" RemotePassword="" PrivateKey="" RemoteHost="" RemoteUser="" XDMCPHost="" XDMCPBroadcast="False" XDMCPIndirect="False" Clipboard="True" ClipboardPrimary="True" ExtraParams="" Wgl="True" DisableAC="True" XDMCPTerminate="False"/>
"@
    
    try {
        $configContent | Out-File -FilePath $configFile -Encoding UTF8
        Write-Success "Configuration created: $configFile"
        return $configFile
    } catch {
        Write-Error "Failed to create configuration: $($_.Exception.Message)"
        return $null
    }
}

# Function to configure Windows Firewall
function Configure-Firewall {
    if (-not $ConfigureFirewall) {
        return
    }
    
    Write-Info "Configuring Windows Firewall for VcXsrv..."
    
    try {
        # Allow VcXsrv through firewall
        New-NetFirewallRule -DisplayName "VcXsrv X Server" -Direction Inbound -Program "C:\Program Files\VcXsrv\vcxsrv.exe" -Action Allow -ErrorAction SilentlyContinue
        New-NetFirewallRule -DisplayName "VcXsrv X Server" -Direction Outbound -Program "C:\Program Files\VcXsrv\vcxsrv.exe" -Action Allow -ErrorAction SilentlyContinue
        
        # Allow X11 port (6000)
        New-NetFirewallRule -DisplayName "X11 Display :0" -Direction Inbound -Protocol TCP -LocalPort 6000 -Action Allow -ErrorAction SilentlyContinue
        
        Write-Success "Firewall rules configured"
    } catch {
        Write-Warning "Failed to configure firewall automatically"
        Write-Info "Please manually allow VcXsrv through Windows Firewall"
    }
}

# Function to create desktop shortcut
function Create-Shortcut {
    if (-not $CreateShortcut) {
        return
    }
    
    Write-Info "Creating desktop shortcut..."
    
    $vcxsrvPath = "C:\Program Files\VcXsrv\xlaunch.exe"
    $configFile = Create-VcXsrvConfig
    
    if ($null -eq $configFile) {
        Write-Warning "Cannot create shortcut without configuration file"
        return
    }
    
    try {
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\VcXsrv for C-AIBOX.lnk")
        $Shortcut.TargetPath = $vcxsrvPath
        $Shortcut.Arguments = "-run `"$configFile`""
        $Shortcut.Description = "VcXsrv X Server for C-AIBOX GUI"
        $Shortcut.Save()
        
        Write-Success "Desktop shortcut created"
    } catch {
        Write-Warning "Failed to create desktop shortcut: $($_.Exception.Message)"
    }
}

# Function to start VcXsrv
function Start-VcXsrv {
    Write-Info "Starting VcXsrv X Server..."
    
    $vcxsrvPath = "C:\Program Files\VcXsrv\vcxsrv.exe"
    
    # Check if VcXsrv is already running
    $vcxsrvProcess = Get-Process -Name "vcxsrv" -ErrorAction SilentlyContinue
    if ($vcxsrvProcess) {
        Write-Success "VcXsrv is already running"
        return $true
    }
    
    # Start VcXsrv with optimal settings for containers
    try {
        $arguments = @(
            ":0",
            "-multiwindow",
            "-clipboard",
            "-wgl",
            "-ac"
        )
        
        Start-Process -FilePath $vcxsrvPath -ArgumentList $arguments -WindowStyle Hidden
        Start-Sleep -Seconds 3
        
        # Verify it started
        $vcxsrvProcess = Get-Process -Name "vcxsrv" -ErrorAction SilentlyContinue
        if ($vcxsrvProcess) {
            Write-Success "VcXsrv started successfully"
            return $true
        } else {
            Write-Error "Failed to start VcXsrv"
            return $false
        }
    } catch {
        Write-Error "Failed to start VcXsrv: $($_.Exception.Message)"
        return $false
    }
}

# Function to create startup script
function Create-StartupScript {
    Write-Info "Creating startup script..."
    
    $startupScript = @"
@echo off
REM VcXsrv startup script for C-AIBOX
echo Starting VcXsrv X Server for C-AIBOX...

REM Kill any existing VcXsrv processes
taskkill /f /im vcxsrv.exe >nul 2>&1

REM Start VcXsrv with container-friendly settings
start "" "C:\Program Files\VcXsrv\vcxsrv.exe" :0 -multiwindow -clipboard -wgl -ac

echo VcXsrv started. You can now run GUI applications in your containers.
echo.
echo To test the connection from WSL2/Container:
echo   export DISPLAY=host.docker.internal:0
echo   xdpyinfo
echo.
pause
"@
    
    try {
        $startupScript | Out-File -FilePath "$env:USERPROFILE\Desktop\Start VcXsrv for C-AIBOX.bat" -Encoding ASCII
        Write-Success "Startup script created on desktop"
    } catch {
        Write-Warning "Failed to create startup script: $($_.Exception.Message)"
    }
}

# Main execution
Write-Info "Starting VcXsrv installation and configuration..."

# Install VcXsrv
if (-not (Install-VcXsrv)) {
    Write-Error "Installation failed"
    exit 1
}

# Configure firewall
Configure-Firewall

# Create configuration and shortcuts
Create-VcXsrvConfig | Out-Null
Create-Shortcut
Create-StartupScript

# Start VcXsrv if requested
if ($AutoStart) {
    Start-VcXsrv | Out-Null
}

Write-Info ""
Write-Success "VcXsrv setup completed!"
Write-Info ""
Write-Info "Next steps:"
Write-Info "1. Start VcXsrv using the desktop shortcut or batch file"
Write-Info "2. In your WSL2/Container, set: export DISPLAY=host.docker.internal:0"
Write-Info "3. Test connection: xdpyinfo"
Write-Info "4. Run your GUI application: ./scripts/run_client.sh --gui"
Write-Info ""
Write-Info "Configuration details:"
Write-Info "- Display: :0 (port 6000)"
Write-Info "- Mode: Multi-window"
Write-Info "- Clipboard: Enabled"
Write-Info "- Access control: Disabled (for container access)"
Write-Info ""
Write-Warning "Security note: Access control is disabled for container compatibility"
Write-Warning "Only use this configuration in trusted development environments"

if (-not $AutoStart) {
    Write-Info ""
    Write-Info "To start VcXsrv now, run: Start-VcXsrv"
    Write-Info "Or use the desktop shortcut/batch file"
}
