# Định Nghĩa Tác Vụ Module Giải Mã Video MPP

## Tổng Quan
Module này chịu trách nhiệm giải mã các đơn vị NAL H.264/H.265 nhận được từ module đầu vào RTSP thành các khung hình video thô sử dụng khả năng giải mã video phần cứng của RK3588 thông qua Media Process Platform (MPP).

## Yêu Cầu
- Giải mã các luồng video H.264/H.265 sử dụng tăng tốc phần cứng
- Hỗ trợ nhiều quá trình giải mã đồng thời cho các luồng camera khác nhau
- Triển khai luồng dữ liệu zero-copy sử dụng DMABUF
- Quản lý context decoder cho mỗi luồng camera
- X<PERSON> lý các độ phân giải và định dạng video khác nhau

## Phương Pháp Kỹ Thuật
1. **Khởi Tạo và Cấu Hình MPP**:
   - Tạo và khởi tạo context MPP cho mỗi luồng camera
   - Cấu hình MPP cho giải mã phần cứng H.264/H.265
   - Thiết lập MPP để sử dụng DMABUF cho các thao tác zero-copy

2. **Pipeline Giải Mã**:
   - Triển khai thread/pool worker giải mã
   - Xử lý các đơn vị NAL từ hàng đợi đầu vào
   - Cấu hình decoder cho loại codec phù hợp (H.264/H.265)
   - Thiết lập quản lý buffer phù hợp cho việc sử dụng bộ nhớ hiệu quả

3. **Tích Hợp DMABUF**:
   - Cấu hình MPP để xuất các khung hình đã giải mã dưới dạng DMABUF
   - Triển khai xử lý DMABUF cho các thao tác zero-copy
   - Xuất file descriptor DMABUF cho các giai đoạn xử lý tiếp theo

4. **Tối Ưu Hiệu Suất**:
   - Triển khai giải mã bất đồng bộ để tối đa hóa thông lượng
   - Cân bằng tài nguyên giải mã trên nhiều luồng
   - Giám sát và xử lý lỗi giải mã

## Giao Diện
- **Đầu vào**: Hàng đợi các đơn vị NAL H.264/H.265 từ module RTSP
- **Đầu ra**: File descriptor DMABUF trỏ đến các khung hình YUV đã giải mã (thường là định dạng NV12)

## Phụ Thuộc
- Thư viện Rockchip MPP (libmpp.so)
- Thư viện chuẩn C++
- Thư viện DMABUF/DRM cho quản lý buffer

## Cân Nhắc Hiệu Suất
- Sử dụng hiệu quả phần cứng VPU để tối đa hóa số lượng luồng
- Xử lý lỗi phù hợp để ngăn khóa phần cứng
- Giám sát hiệu suất và độ trễ giải mã
- Cân bằng giữa thông lượng và sử dụng bộ nhớ
