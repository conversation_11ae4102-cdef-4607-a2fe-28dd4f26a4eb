#include "utils/datetime_utils.hpp"
#include <iomanip>
#include <sstream>
#include <thread>
#include <cstring>
#include <regex>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

namespace utils {

    DateTimeUtils::TimePoint DateTimeUtils::now() {
        return std::chrono::system_clock::now();
    }

    int64_t DateTimeUtils::nowUnix() {
        return toUnix(now());
    }

    int64_t DateTimeUtils::nowUnixMs() {
        return toUnixMs(now());
    }

    int64_t DateTimeUtils::toUnix(const TimePoint& tp) {
        return std::chrono::duration_cast<std::chrono::seconds>(tp.time_since_epoch()).count();
    }

    int64_t DateTimeUtils::toUnixMs(const TimePoint& tp) {
        return std::chrono::duration_cast<std::chrono::milliseconds>(tp.time_since_epoch()).count();
    }

    DateTimeUtils::TimePoint DateTimeUtils::fromUnix(int64_t timestamp) {
        return TimePoint(std::chrono::seconds(timestamp));
    }

    DateTimeUtils::TimePoint DateTimeUtils::fromUnixMs(int64_t timestamp) {
        return TimePoint(std::chrono::milliseconds(timestamp));
    }

    std::string DateTimeUtils::format(const TimePoint& tp, const std::string& format) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t);

        std::ostringstream oss;
        oss << std::put_time(&tm, format.c_str());
        return oss.str();
    }

    std::string DateTimeUtils::formatNow(const std::string& format) {
        return DateTimeUtils::format(now(), format);
    }

    std::optional<DateTimeUtils::TimePoint> DateTimeUtils::parse(const std::string& timeStr, const std::string& format) {
        std::tm tm = {};
        std::istringstream ss(timeStr);
        ss >> std::get_time(&tm, format.c_str());
        if (ss.fail()) {
            return std::nullopt;
        }

        return tmToTimePoint(tm);
    }

    std::optional<DateTimeUtils::TimePoint> DateTimeUtils::parseISO8601(const std::string& isoStr) {
        // Handle different ISO 8601 formats
        std::regex iso_regex(R"((\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?(?:Z|([+-]\d{2}):?(\d{2}))?)");
        std::smatch matches;

        if (!std::regex_match(isoStr, matches, iso_regex)) {
            return std::nullopt;
        }

        std::tm tm = {};
        tm.tm_year = std::stoi(matches[1]) - 1900;
        tm.tm_mon = std::stoi(matches[2]) - 1;
        tm.tm_mday = std::stoi(matches[3]);
        tm.tm_hour = std::stoi(matches[4]);
        tm.tm_min = std::stoi(matches[5]);
        tm.tm_sec = std::stoi(matches[6]);

        auto tp = tmToTimePoint(tm);

        // Add milliseconds if present
        if (matches[7].matched) {
            int ms = std::stoi(matches[7]);
            tp += std::chrono::milliseconds(ms);
        }

        // Handle timezone offset (simplified - assumes UTC if Z or offset specified)
        // For full timezone support, would need more complex implementation

        return tp;
    }

    std::string DateTimeUtils::toISO8601(const TimePoint& tp, bool includeMs) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::gmtime(&time_t);  // Use GMT for ISO 8601

        std::ostringstream oss;
        oss << std::put_time(&tm, "%Y-%m-%dT%H:%M:%S");

        if (includeMs) {
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(tp.time_since_epoch()) % 1000;
            oss << "." << std::setfill('0') << std::setw(3) << ms.count();
        }

        oss << "Z";
        return oss.str();
    }

    std::string DateTimeUtils::nowISO8601(bool includeMs) {
        return toISO8601(now(), includeMs);
    }

    DateTimeUtils::TimePoint DateTimeUtils::add(const TimePoint& tp, const Duration& duration) {
        return tp + duration;
    }

    DateTimeUtils::TimePoint DateTimeUtils::subtract(const TimePoint& tp, const Duration& duration) {
        return tp - duration;
    }

    DateTimeUtils::Duration DateTimeUtils::diff(const TimePoint& tp1, const TimePoint& tp2) {
        return tp1 - tp2;
    }

    int64_t DateTimeUtils::diffSeconds(const TimePoint& tp1, const TimePoint& tp2) {
        return std::chrono::duration_cast<std::chrono::seconds>(diff(tp1, tp2)).count();
    }

    int64_t DateTimeUtils::diffMs(const TimePoint& tp1, const TimePoint& tp2) {
        return std::chrono::duration_cast<std::chrono::milliseconds>(diff(tp1, tp2)).count();
    }

    bool DateTimeUtils::isBefore(const TimePoint& tp1, const TimePoint& tp2) {
        return tp1 < tp2;
    }

    bool DateTimeUtils::isAfter(const TimePoint& tp1, const TimePoint& tp2) {
        return tp1 > tp2;
    }

    void DateTimeUtils::sleep(const Duration& duration) {
        std::this_thread::sleep_for(duration);
    }

    void DateTimeUtils::sleepMs(int64_t ms) {
        sleep(std::chrono::milliseconds(ms));
    }

    void DateTimeUtils::sleepSeconds(int64_t seconds) {
        sleep(std::chrono::seconds(seconds));
    }

    DateTimeUtils::TimePoint DateTimeUtils::startOfDay(const TimePoint& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t);
        tm.tm_hour = 0;
        tm.tm_min = 0;
        tm.tm_sec = 0;

        return tmToTimePoint(tm);
    }

    DateTimeUtils::TimePoint DateTimeUtils::endOfDay(const TimePoint& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        std::tm tm = *std::localtime(&time_t);
        tm.tm_hour = 23;
        tm.tm_min = 59;
        tm.tm_sec = 59;

        auto result = tmToTimePoint(tm);
        result += std::chrono::milliseconds(999);
        return result;
    }

    bool DateTimeUtils::isSameDay(const TimePoint& tp1, const TimePoint& tp2) {
        auto time_t1 = std::chrono::system_clock::to_time_t(tp1);
        auto time_t2 = std::chrono::system_clock::to_time_t(tp2);

        std::tm tm1 = *std::localtime(&time_t1);
        std::tm tm2 = *std::localtime(&time_t2);

        return tm1.tm_year == tm2.tm_year &&
               tm1.tm_mon == tm2.tm_mon &&
               tm1.tm_mday == tm2.tm_mday;
    }

    std::string DateTimeUtils::getElapsedTime(const TimePoint& tp) {
        auto now_tp = now();
        auto duration = now_tp - tp;

        auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration).count();
        auto minutes = seconds / 60;
        auto hours = minutes / 60;
        auto days = hours / 24;

        if (days > 0) {
            return std::to_string(days) + (days == 1 ? " day ago" : " days ago");
        } else if (hours > 0) {
            return std::to_string(hours) + (hours == 1 ? " hour ago" : " hours ago");
        } else if (minutes > 0) {
            return std::to_string(minutes) + (minutes == 1 ? " minute ago" : " minutes ago");
        } else {
            return std::to_string(seconds) + (seconds == 1 ? " second ago" : " seconds ago");
        }
    }

    bool DateTimeUtils::isValidFormat(const std::string& timeStr, const std::string& format) {
        // Try to parse the string
        auto parsed = parse(timeStr, format);
        if (!parsed.has_value()) {
            return false;
        }

        // Format it back and compare with original
        std::string reformatted = DateTimeUtils::format(parsed.value(), format);
        return reformatted == timeStr;
    }

    DateTimeUtils::TimePoint DateTimeUtils::tmToTimePoint(const std::tm& tm) {
        std::tm tm_copy = tm;
        auto time_t = std::mktime(&tm_copy);
        return std::chrono::system_clock::from_time_t(time_t);
    }

    std::tm DateTimeUtils::timePointToTm(const TimePoint& tp) {
        auto time_t = std::chrono::system_clock::to_time_t(tp);
        return *std::localtime(&time_t);
    }

    // Convenience functions implementation
    DateTimeUtils::TimePoint now() {
        return DateTimeUtils::now();
    }

    int64_t nowUnix() {
        return DateTimeUtils::nowUnix();
    }

    std::string formatNow(const std::string& format) {
        return DateTimeUtils::formatNow(format);
    }

    std::optional<DateTimeUtils::TimePoint> parseTime(const std::string& timeStr, const std::string& format) {
        return DateTimeUtils::parse(timeStr, format);
    }

    void sleepMs(int64_t ms) {
        DateTimeUtils::sleepMs(ms);
    }
}
