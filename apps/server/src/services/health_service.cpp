#include "services/health_service.hpp"
#include "shared/config.hpp"
#include <iostream>

namespace server::services {

HealthService::HealthService(std::shared_ptr<repositories::IAIModelRepository> repository)
    : repository_(repository), server_start_time_(std::chrono::system_clock::now()) {
    std::cout << "[HealthService] Initialized" << std::endl;
}

models::HealthResponse HealthService::getHealthStatus() {
    models::HealthResponse response;
    
    try {
        // Check system health
        bool system_healthy = isSystemHealthy();
        bool models_available = checkModelAvailability();
        bool resources_ok = checkSystemResources();
        
        response.healthy = system_healthy && models_available && resources_ok;
        response.status = getSystemStatus();
        response.timestamp = std::chrono::system_clock::now();
        response.version = shared::AppConstants::APP_VERSION;
        
        std::cout << "[HealthService] Health check completed - Status: " 
                  << (response.healthy ? "Healthy" : "Unhealthy") << std::endl;
        
    } catch (const std::exception& e) {
        response.healthy = false;
        response.status = "Error during health check: " + std::string(e.what());
        response.timestamp = std::chrono::system_clock::now();
        response.version = shared::AppConstants::APP_VERSION;
        
        std::cout << "[HealthService] Health check failed: " << e.what() << std::endl;
    }
    
    return response;
}

models::ApiInfoResponse HealthService::getApiInfo() {
    models::ApiInfoResponse response;
    
    response.version = shared::AppConstants::APP_VERSION;
    response.description = "C-AIBOX HTTP API Server - AI Model Inference Service";
    response.server_start_time = server_start_time_;
    response.endpoints = getAvailableEndpoints();
    
    std::cout << "[HealthService] API info requested" << std::endl;
    
    return response;
}

bool HealthService::isSystemHealthy() {
    try {
        // Basic system health checks
        bool models_ok = checkModelAvailability();
        bool resources_ok = checkSystemResources();
        
        return models_ok && resources_ok;
        
    } catch (const std::exception& e) {
        std::cout << "[HealthService] System health check failed: " << e.what() << std::endl;
        return false;
    }
}

void HealthService::recordServerStart() {
    server_start_time_ = std::chrono::system_clock::now();
    std::cout << "[HealthService] Server start time recorded" << std::endl;
}

std::chrono::system_clock::time_point HealthService::getServerStartTime() const {
    return server_start_time_;
}

std::chrono::milliseconds HealthService::getUptime() const {
    auto now = std::chrono::system_clock::now();
    return std::chrono::duration_cast<std::chrono::milliseconds>(now - server_start_time_);
}

// Helper methods
bool HealthService::checkModelAvailability() const {
    try {
        // Check if core models are available
        bool arcface_available = repository_->isModelAvailable("arcface");
        bool yolo_available = repository_->isModelAvailable("yolo");
        
        std::cout << "[HealthService] Model availability - ArcFace: " 
                  << (arcface_available ? "OK" : "FAIL") 
                  << ", YOLO: " << (yolo_available ? "OK" : "FAIL") << std::endl;
        
        return arcface_available && yolo_available;
        
    } catch (const std::exception& e) {
        std::cout << "[HealthService] Model availability check failed: " << e.what() << std::endl;
        return false;
    }
}

bool HealthService::checkSystemResources() const {
    try {
        // Basic resource checks
        // In a real implementation, you would check:
        // - Available memory
        // - CPU usage
        // - Disk space
        // - GPU/NPU availability
        
        // For now, always return true
        return true;
        
    } catch (const std::exception& e) {
        std::cout << "[HealthService] System resource check failed: " << e.what() << std::endl;
        return false;
    }
}

std::string HealthService::getSystemStatus() const {
    try {
        bool models_ok = checkModelAvailability();
        bool resources_ok = checkSystemResources();
        
        if (models_ok && resources_ok) {
            return "All systems operational";
        } else if (!models_ok && resources_ok) {
            return "Model availability issues detected";
        } else if (models_ok && !resources_ok) {
            return "System resource issues detected";
        } else {
            return "Multiple system issues detected";
        }
        
    } catch (const std::exception& e) {
        return "Error checking system status: " + std::string(e.what());
    }
}

std::vector<std::string> HealthService::getAvailableEndpoints() const {
    return {
        "GET /health",
        "GET /api/v1/info",
        "GET /api/v1/status",
        "POST /api/v1/arcface/infer",
        "POST /api/v1/yolo/detect"
    };
}

} // namespace server::services
