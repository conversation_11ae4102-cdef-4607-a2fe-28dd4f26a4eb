#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check GLIBC version in container
log_info "Container GLIBC version:"
ldd --version | head -n1

log_info "Cross-compiler GLIBC version:"
aarch64-linux-gnu-gcc --version | head -n1

# Check available GLIBC symbols in cross-compiler
log_info "Available GLIBC symbols in cross-compiler:"
if [[ -f "/usr/aarch64-linux-gnu/lib/libc.so.6" ]]; then
    strings /usr/aarch64-linux-gnu/lib/libc.so.6 | grep "GLIBC_" | sort -V | tail -10
else
    log_info "Cross-compiler libc not found at expected location"
fi

# Debug: Show current directory and files
log_info "Current working directory: $(pwd)"
log_info "Contents of /workspace:"
ls -la /workspace/ || true

# Build project (source code is copied into container)
if [[ -f "/workspace/CMakeLists.txt" ]]; then
    log_info "Building c-aibox with Orange Pi compatibility..."

    mkdir -p /workspace/build-container
    cd /workspace/build-container

    cmake \
        -DCMAKE_BUILD_TYPE=Release \
        -DCMAKE_TOOLCHAIN_FILE=/workspace/cmake/toolchains/aarch64-linux-gnu.cmake \
        -DGLIBC_COMPAT_MODE=ON \
        -DORANGE_PI_TARGET=ON \
        -DENABLE_HARDWARE_ACCELERATION=ON \
        -DENABLE_ARM64_OPTIMIZATIONS=ON \
        ..

    make -j$(nproc)

    log_success "Build completed in container"

    # Check GLIBC compatibility if checker script exists
    if [[ -f "/workspace/scripts/cross-build/check-glibc-compat.sh" ]]; then
        log_info "Checking GLIBC compatibility of built binaries..."
        /workspace/scripts/cross-build/check-glibc-compat.sh --dir /workspace/build-container --target-glibc 2.36 || true
    fi

    # Show built binaries
    log_info "Built binaries:"
    find /workspace/build-container -name "server" -type f -executable 2>/dev/null || true
    find /workspace/build-container -name "*demo" -type f -executable 2>/dev/null | head -5 || true
else
    log_error "CMakeLists.txt not found in /workspace"
    log_info "Available files:"
    find /workspace -name "*.txt" -o -name "*.cmake" | head -10
fi
