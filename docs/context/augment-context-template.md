# Augment AI Context Template

## How to Use This Template

Copy and paste the relevant sections below when starting a new Augment AI session to maintain consistent context across team members and platforms.

## Platform Context (Always Include)

```
Platform: Orange Pi 5 Plus/Ultra with RK3588 chip
- CPU: 4x Cortex-A76 @ 2.4GHz + 4x Cortex-A55 @ 1.8GHz  
- Memory: 4GB/8GB LPDDR4X-4224
- Storage: 240GB SSD
- Hardware Accelerators: MPP decoder, RGA scaler, NPU (6 TOPS)
- OS: Ubuntu 22.04 LTS ARM64
- Cross-compilation: aarch64-linux-gnu toolchain required
```

## Technology Stack Context

```
Primary Technology Choices (RK3588 Optimized):
- Video Processing: GStreamer (NOT FFmpeg) with RockChip plugins
- Hardware Acceleration: MPP decoder (mandatory), RGA scaler, DMABUF zero-copy
- Build System: CMake with ARM64 cross-compilation
- UI Framework: Qt 5.15+ optimized for embedded display
- AI Processing: NPU for face detection/recognition
- Memory Management: DMABUF-based zero-copy operations
```

## Resource Allocation Context

```
System Resource Distribution:
- Core 0-1 (A55): UI Client + System Services
- Core 2-3 (A55): RTSP Input Module (25-30% system CPU)
- Core 4-5 (A76): Face Detection (NPU coordination)
- Core 6-7 (A76): Face Recognition & Identification

Memory Limits:
- 4GB Config: RTSP ≤1.2GB, Face Detection ≤800MB, Face Recognition ≤600MB
- 8GB Config: RTSP ≤2.5GB, Face Detection ≤1.5GB, Face Recognition ≤1.5GB

Performance Targets:
- 4GB: 6 streams, <200ms latency, <75% total CPU
- 8GB: 12 streams, <150ms latency, <80% total CPU
- Thermal: 85°C max, throttle at 80°C
```

## Development Constraints Context

```
Mandatory Requirements:
- Hardware-in-the-Loop testing on actual Orange Pi hardware
- RK3588-first development approach (all decisions prioritize RK3588)
- Cross-compilation support for ARM64
- Thermal management and power efficiency considerations
- Hardware accelerator utilization >80% for video processing
- Memory usage within embedded platform constraints
```

## Current Project Status Context

```
RTSP Input Module Status:
- Phase: Planning and early implementation
- Technology: GStreamer-based with MPP decoder integration
- Resource Allocation: Cores 2-3, limited memory budget
- Integration: Must coordinate with Face AI modules
- Testing: Orange Pi hardware validation required

Key Decisions Made:
1. GStreamer over FFmpeg for RK3588 hardware support
2. Resource sharing strategy between all AI Box modules
3. Thermal-aware performance scaling implementation
4. DMABUF zero-copy memory management
5. Hardware accelerator mandatory usage with software fallback
```

## Risk Context

```
Critical Risks to Consider:
1. GStreamer RockChip plugin compatibility issues
2. Hardware accelerator resource contention between modules
3. Thermal throttling under maximum load conditions
4. Memory constraints limiting concurrent stream count
5. Integration complexity between RTSP and Face AI modules

Mitigation Strategies:
- Early prototype testing on Orange Pi hardware
- Resource allocation framework design
- Thermal monitoring and adaptive performance scaling
- Software fallback mechanisms for hardware failures
```

## Usage Instructions

### For New Team Members:
1. Copy the "Platform Context" section into your first Augment prompt
2. Add relevant "Technology Stack Context" for your module
3. Include "Resource Allocation Context" for system-wide awareness
4. Reference "Development Constraints" for all technical decisions

### For Continuing Work:
1. Start with "Current Project Status Context"
2. Add specific context for your current task/module
3. Include relevant risk context for your work area
4. Update status as work progresses

### For Cross-Module Work:
1. Include full "Resource Allocation Context"
2. Add "Risk Context" for integration awareness
3. Reference other modules' constraints and requirements
4. Coordinate resource usage and performance targets

## Context Update Process

1. **Weekly Updates**: Update project status context
2. **Decision Points**: Document new decisions in knowledge base
3. **Risk Changes**: Update risk context when new risks identified
4. **Performance Data**: Update targets based on actual Orange Pi testing
5. **Integration Points**: Update when module interfaces change

## Example Usage

```
Hi Augment, I'm working on the RTSP Input Module for AI Box system.

Platform Context: Orange Pi 5 Plus/Ultra with RK3588 chip, 4GB/8GB RAM, ARM64 Ubuntu 22.04

Technology Context: Using GStreamer (not FFmpeg) with RockChip plugins, MPP decoder for hardware acceleration, targeting cores 2-3 with 1.2GB memory limit (4GB config)

Current Task: Implementing GStreamer pipeline for RTSP stream processing with MPP decoder integration

Constraints: Must coordinate with Face Detection (cores 4-5) and Face Recognition (cores 6-7) modules, thermal management required, hardware-in-the-loop testing on Orange Pi mandatory

Please help me design the GStreamer pipeline configuration for optimal RK3588 performance.
```

This template ensures consistent context across all team members and Augment AI sessions.
