#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <utils/json_utils.hpp>
#include "shared/config.hpp"

namespace server::handlers {

/**
 * @brief Base handler class for API logic processing
 * Handles business logic and data transformation
 */
class BaseHandler {
public:
    explicit BaseHandler(std::shared_ptr<shared::ServerConfig> config);
    virtual ~BaseHandler() = default;

protected:
    std::shared_ptr<shared::ServerConfig> config_;

    // JSON response builders using shared utilities
    utils::JsonValue createSuccessResponse(const std::string& message, const utils::JsonValue& data = utils::JsonValue());
    utils::JsonValue createErrorResponse(const std::string& error, const std::string& error_code = "", int http_code = 400);
    
    // Data transformation helpers
    utils::JsonValue createTimestamp(const std::chrono::system_clock::time_point& time);
    utils::JsonValue createDuration(const std::chrono::milliseconds& duration);
    
    // Validation helpers
    bool validateJsonRequest(const utils::JsonValue& json, const std::vector<std::string>& required_fields);
    std::string extractStringField(const utils::JsonValue& json, const std::string& field, const std::string& default_value = "");
    std::optional<float> extractFloatField(const utils::JsonValue& json, const std::string& field);
    std::optional<int> extractIntField(const utils::JsonValue& json, const std::string& field);
    std::optional<bool> extractBoolField(const utils::JsonValue& json, const std::string& field);
    
    // Logging helpers
    void logInfo(const std::string& message, const std::string& handler_name = "BaseHandler");
    void logError(const std::string& error, const std::string& handler_name = "BaseHandler");
    void logDebug(const std::string& message, const std::string& handler_name = "BaseHandler");

private:
    std::string getCurrentTimestamp() const;
};

} // namespace server::handlers
