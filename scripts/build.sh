#!/bin/bash

# Build script for c-aibox-v1 project
# Usage: ./scripts/build.sh [Debug|Release] [clean]

set -e  # Exit on any error

# Default values
BUILD_TYPE="Debug"
CLEAN_BUILD=false
BUILD_DIR="build"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [BUILD_TYPE] [OPTIONS]"
    echo ""
    echo "BUILD_TYPE:"
    echo "  Debug      Build in debug mode (default)"
    echo "  Release    Build in release mode"
    echo ""
    echo "OPTIONS:"
    echo "  clean      Clean build directory before building"
    echo "  help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Build in Debug mode"
    echo "  $0 Release            # Build in Release mode"
    echo "  $0 Debug clean        # Clean and build in Debug mode"
}

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        Debug|Release)
            BUILD_TYPE="$arg"
            ;;
        clean)
            CLEAN_BUILD=true
            ;;
        help|--help|-h)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown argument: $arg"
            show_usage
            exit 1
            ;;
    esac
done

# Change to project root directory
cd "$PROJECT_ROOT"

print_info "Building c-aibox-v1 project"
print_info "Build type: $BUILD_TYPE"
print_info "Project root: $PROJECT_ROOT"

# Clean build directory if requested
if [ "$CLEAN_BUILD" = true ]; then
    print_info "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
fi

# Create build directory
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Configure with CMake
print_info "Configuring with CMake..."
cmake -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
      -DCMAKE_EXPORT_COMPILE_COMMANDS=ON \
      -DUSE_OPENSSL=ON \
      ..

# Build the project
print_info "Building project..."
cmake --build . --config "$BUILD_TYPE" --parallel $(nproc 2>/dev/null || echo 4)

print_success "Build completed successfully!"
print_info "Executables are in: $BUILD_DIR/bin/"
print_info "Libraries are in: $BUILD_DIR/lib/"

# Show build artifacts
if [ -d "bin" ]; then
    print_info "Built executables:"
    ls -la bin/
fi

if [ -d "lib" ]; then
    print_info "Built libraries:"
    ls -la lib/
fi
