# Sơ Đồ Tuần Tự Module Nhận Diện <PERSON>huôn Mặt NPU

Tài liệu này cung cấp các sơ đồ tuần tự chi tiết cho Module Nhận Diệ<PERSON> Mặt NPU, minh họa luồng logic và tương tác giữa các thành phần cho tạo embedding khuôn mặt và nhận dạng.

## Tuần Tự Khởi Tạo Module

```mermaid
sequenceDiagram
    participant Main as Main Application
    participant Recognizer as FaceRecognitionModule
    participant ModelMgr as ModelManager
    participant NPU as NPUManager
    participant ThreadPool as InferenceThreadPool

    Main->>Recognizer: initialize(config)
    activate Recognizer

    Recognizer->>ModelMgr: loadRecognitionModel(model_path)
    activate ModelMgr

    ModelMgr->>ModelMgr: readModelFile(model_path)

    ModelMgr->>NPU: createRKNNContext()
    activate NPU

    NPU->>NPU: prepareInitExtend(flags)
    Note over NPU: Set RKNN_FLAG_MEM_ALLOC_OUTSIDE,<br/>RKNN_FLAG_ASYNC_MODE flags

    NPU->>NPU: rknn_init2(&ctx, model_data, model_size, flags, &init_extend)
    NPU-->>ModelMgr: context handle

    ModelMgr->>NPU: rknn_set_core_mask(ctx, RKNN_NPU_CORE_0_1)
    Note over ModelMgr,NPU: Assign cores differently from<br/>detection if possible
    NPU-->>ModelMgr: core mask set status

    ModelMgr->>NPU: rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num)
    NPU-->>ModelMgr: input/output counts

    loop For each input/output
        ModelMgr->>NPU: rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &attr)
        NPU-->>ModelMgr: input attributes

        ModelMgr->>NPU: rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &attr)
        NPU-->>ModelMgr: output attributes
    end

    ModelMgr-->>Recognizer: model loaded
    deactivate ModelMgr

    Recognizer->>ThreadPool: createThreadPool(worker_count)
    activate ThreadPool
    ThreadPool-->>Recognizer: thread pool ready
    deactivate ThreadPool

    Recognizer-->>Main: initialization complete
    deactivate Recognizer
    deactivate NPU
```

## Tuần Tự Nhận Diện Batch

```mermaid
sequenceDiagram
    participant InQueue as FaceQueue
    participant Scheduler as BatchScheduler
    participant Worker as InferenceWorker
    participant NPU as NPUManager
    participant OutQueue as EmbeddingQueue

    activate Scheduler
    Scheduler->>InQueue: collectFaceBatch(max_batch_size, timeout)
    activate InQueue
    InQueue-->>Scheduler: batch of face images
    deactivate InQueue

    alt Batch Available
        Scheduler->>Worker: processBatch(face_batch)
        activate Worker

        loop For Each Face in Batch
            Worker->>NPU: setupInputTensor(face, batch_idx)
            activate NPU
            NPU-->>Worker: input tensor ready
            deactivate NPU
        end

        Worker->>NPU: rknn_inputs_set(ctx, io_num.n_input, inputs)
        activate NPU
        NPU-->>Worker: inputs set

        Worker->>NPU: rknn_run(ctx, NULL)
        NPU-->>Worker: batch inference complete

        Worker->>NPU: rknn_outputs_get(ctx, outputs)
        NPU-->>Worker: raw embeddings

        loop For Each Face in Batch
            Worker->>Worker: extractEmbedding(outputs, batch_idx)
            Worker->>Worker: normalizeEmbedding(embedding)

            Worker->>OutQueue: enqueueEmbedding(embedding, face_metadata)
            activate OutQueue
            OutQueue-->>Worker: enqueue status
            deactivate OutQueue
        end

        Worker->>NPU: rknn_outputs_release(ctx, outputs)
        NPU-->>Worker: outputs released
        deactivate NPU

        Worker-->>Scheduler: batch processing complete
        deactivate Worker

    else No Batch Available
        Scheduler->>Scheduler: wait(polling_interval)
    end

    deactivate Scheduler
```

## Tuần Tự Xử Lý Vector Embedding

```mermaid
sequenceDiagram
    participant Worker as InferenceWorker
    participant Embedding as EmbeddingProcessor
    participant Normalizer as VectorNormalizer
    participant OutQueue as ResultQueue

    activate Worker
    Worker->>Worker: getInferenceResults()

    Worker->>Embedding: extractEmbeddings(output_tensor)
    activate Embedding

    loop For Each Face in Batch
        Embedding->>Embedding: extractSingleEmbedding(output_tensor, batch_idx)

        Embedding->>Normalizer: normalizeVector(embedding)
        activate Normalizer

        Normalizer->>Normalizer: calculateL2Norm(vector)
        Normalizer->>Normalizer: divideByNorm(vector)

        Normalizer-->>Embedding: normalized embedding
        deactivate Normalizer

        Embedding->>Embedding: associateWithMetadata(embedding, metadata)
    end

    Embedding-->>Worker: processed embeddings
    deactivate Embedding

    Worker->>OutQueue: enqueueResults(embeddings)
    activate OutQueue
    OutQueue-->>Worker: enqueue status
    deactivate OutQueue

    deactivate Worker
```

## Tuần Tự So Sánh Tương Tự

```mermaid
sequenceDiagram
    participant RecModule as RecognitionModule
    participant Matcher as EmbeddingMatcher
    participant Database as FaceDatabase
    participant Result as MatchResult

    activate RecModule
    RecModule->>RecModule: receiveEmbedding(new_embedding, metadata)

    RecModule->>Database: findMatches(new_embedding, threshold)
    activate Database

    Database->>Database: prepareSearchQuery(embedding)

    alt Using Vector Index
        Database->>Database: performANNSearch(embedding, k_nearest)
    else Linear Search
        Database->>Database: performLinearSearch(embedding)
    end

    Database-->>RecModule: candidate_matches
    deactivate Database

    RecModule->>Matcher: refineCandidates(new_embedding, candidates)
    activate Matcher

    loop For Each Candidate
        Matcher->>Matcher: computeCosineSimilarity(new_embedding, candidate)

        alt Similarity > Threshold
            Matcher->>Matcher: addToMatchResults(candidate, similarity)
        end
    end

    Matcher-->>RecModule: refined_matches
    deactivate Matcher

    RecModule->>Result: createMatchResult(refined_matches, metadata)
    activate Result
    Result-->>RecModule: match_result
    deactivate Result

    RecModule->>RecModule: processMatchResult(match_result)
    deactivate RecModule
```

## Tuần Tự Xử Lý Lỗi

```mermaid
sequenceDiagram
    participant Worker as InferenceWorker
    participant ErrorHandler as ErrorHandler
    participant NPU as NPUManager
    participant App as Application

    activate Worker
    Worker->>Worker: detectError(error_type)
    Worker->>ErrorHandler: handleRecognitionError(error_type, details)
    activate ErrorHandler

    alt Memory Allocation Error
        ErrorHandler->>ErrorHandler: logMemoryError(details)
        ErrorHandler->>ErrorHandler: attemptMemoryRecovery()
        ErrorHandler-->>Worker: RETRY_WITH_REDUCED_BATCH

    else Inference Timeout
        ErrorHandler->>NPU: rknn_destroy(problematic_ctx)
        activate NPU
        NPU-->>ErrorHandler: context destroyed

        ErrorHandler->>NPU: recreateContext(model_path)
        NPU-->>ErrorHandler: new context
        deactivate NPU

        ErrorHandler-->>Worker: RETRY_WITH_NEW_CONTEXT

    else Model Error
        ErrorHandler->>ErrorHandler: logModelError(details)
        ErrorHandler->>App: notifyModelError(details)
        ErrorHandler-->>Worker: SKIP_BATCH

    else Fatal NPU Error
        ErrorHandler->>App: notifyNPUFailure(details)
        ErrorHandler-->>Worker: TERMINATE_WORKER
    end

    deactivate ErrorHandler

    alt Action is RETRY_WITH_REDUCED_BATCH
        Worker->>Worker: reduceBatchSize()
        Worker->>Worker: retryInference()
    else Action is RETRY_WITH_NEW_CONTEXT
        Worker->>Worker: updateContextReference()
        Worker->>Worker: retryInference()
    else Action is SKIP_BATCH
        Worker->>Worker: discardCurrentBatch()
        Worker->>Worker: processNextBatch()
    else Action is TERMINATE_WORKER
        Worker->>Worker: cleanupResources()
        Worker->>Worker: signalTermination()
        deactivate Worker
    end
```

## Tuần Tự Tắt Máy Module

```mermaid
sequenceDiagram
    participant App as Application
    participant Recognizer as FaceRecognitionModule
    participant ThreadPool as InferenceThreadPool
    participant ModelMgr as ModelManager
    participant NPU as NPUManager

    App->>Recognizer: shutdown()
    activate Recognizer

    Recognizer->>ThreadPool: shutdownWorkers(timeout)
    activate ThreadPool
    ThreadPool-->>Recognizer: workers terminated
    deactivate ThreadPool

    Recognizer->>ModelMgr: unloadModels()
    activate ModelMgr

    loop For Each Model Context
        ModelMgr->>NPU: rknn_destroy(ctx)
        activate NPU
        NPU-->>ModelMgr: context destroyed
        deactivate NPU
    end

    ModelMgr-->>Recognizer: models unloaded
    deactivate ModelMgr

    Recognizer->>Recognizer: releaseResources()
    Recognizer-->>App: shutdown complete
    deactivate Recognizer
```
