#include "controllers/health_controller.hpp"
#include "shared/config.hpp"
#include <chrono>

namespace server::controllers {

HealthController::HealthController(std::shared_ptr<shared::ServerConfig> config,
                                 std::shared_ptr<handlers::HealthHandler> health_handler)
    : BaseC<PERSON>roller(config), health_handler_(health_handler) {
}

void HealthController::handleHealthCheck(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "HealthController::Health");

    try {
        // Delegate to handler
        auto response_json = health_handler_->handleHealthCheck();

        // Send JSON response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("Health check exception: " + std::string(e.what()), "HealthController");
        sendErrorResponse(res, "Health check failed",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "HealthController::Health", duration);
}

void HealthController::handleApiInfo(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "HealthController::ApiInfo");

    try {
        // Delegate to handler
        auto response_json = health_handler_->handleApiInfo();

        // Send JSON response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("API info exception: " + std::string(e.what()), "HealthController");
        sendErrorResponse(res, "Failed to get API information",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "HealthController::ApiInfo", duration);
}

void HealthController::handleSystemStatus(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "HealthController::SystemStatus");

    try {
        // Delegate to handler
        auto response_json = health_handler_->handleSystemStatus();

        // Send JSON response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("System status exception: " + std::string(e.what()), "HealthController");
        sendErrorResponse(res, "Failed to get system status",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "HealthController::SystemStatus", duration);
}

void HealthController::registerRoutes(httplib::Server& server) {
    // Health check route
    server.Get("/health", [this](const httplib::Request& req, httplib::Response& res) {
        handleHealthCheck(req, res);
    });

    // API info route
    server.Get("/api/v1/info", [this](const httplib::Request& req, httplib::Response& res) {
        handleApiInfo(req, res);
    });

    // System status route
    server.Get("/api/v1/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleSystemStatus(req, res);
    });
}

// HTTP processing helpers
void HealthController::sendJsonResponse(httplib::Response& res, const utils::JsonValue& json_response) {
    // Determine status code from response
    int status_code = shared::AppConstants::HTTP_OK;

    if (json_response.hasKey("success") && !json_response["success"].asBool()) {
        if (json_response.hasKey("code")) {
            status_code = json_response["code"].asInt();
        } else {
            status_code = shared::AppConstants::HTTP_BAD_REQUEST;
        }
    }

    // Special handling for health responses
    if (json_response.hasKey("healthy") && !json_response["healthy"].asBool()) {
        status_code = 503; // Service Unavailable
    }

    // Convert to string and send using BaseController method
    std::string json_str = utils::JsonUtils::stringify(json_response);
    BaseController::sendJsonResponse(res, json_str, status_code);
}

} // namespace server::controllers
