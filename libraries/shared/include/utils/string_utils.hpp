#pragma once
#include <string>
#include <vector>
#include <optional>
#include <algorithm>
#include <cctype>

namespace utils {
    /**
     * @brief String utilities for common string operations
     */
    class StringUtils {
    public:
        /**
         * @brief Split string by delimiter character
         * @param str String to split
         * @param delimiter Delimiter character
         * @return Vector of split strings
         */
        static std::vector<std::string> split(const std::string& str, char delimiter);

        /**
         * @brief Split string by delimiter string
         * @param str String to split
         * @param delimiter Delimiter string
         * @return Vector of split strings
         */
        static std::vector<std::string> split(const std::string& str, const std::string& delimiter);

        /**
         * @brief Join vector of strings with delimiter
         * @param strings Vector of strings to join
         * @param delimiter Delimiter to use
         * @return Joined string
         */
        static std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

        /**
         * @brief Trim whitespace from both ends of string
         * @param str String to trim
         * @return Trimmed string
         */
        static std::string trim(const std::string& str);

        /**
         * @brief Trim whitespace from left end of string
         * @param str String to trim
         * @return Left-trimmed string
         */
        static std::string ltrim(const std::string& str);

        /**
         * @brief Trim whitespace from right end of string
         * @param str String to trim
         * @return Right-trimmed string
         */
        static std::string rtrim(const std::string& str);

        /**
         * @brief Trim specific characters from both ends of string
         * @param str String to trim
         * @param chars Characters to trim
         * @return Trimmed string
         */
        static std::string trim(const std::string& str, const std::string& chars);

        /**
         * @brief Convert string to lowercase
         * @param str String to convert
         * @return Lowercase string
         */
        static std::string toLower(const std::string& str);

        /**
         * @brief Convert string to uppercase
         * @param str String to convert
         * @return Uppercase string
         */
        static std::string toUpper(const std::string& str);

        /**
         * @brief Capitalize first letter of string
         * @param str String to capitalize
         * @return Capitalized string
         */
        static std::string capitalize(const std::string& str);

        /**
         * @brief Convert string to title case (capitalize each word)
         * @param str String to convert
         * @return Title case string
         */
        static std::string toTitleCase(const std::string& str);

        /**
         * @brief Check if string starts with prefix
         * @param str String to check
         * @param prefix Prefix to check for
         * @param ignoreCase Whether to ignore case (default: false)
         * @return true if string starts with prefix
         */
        static bool startsWith(const std::string& str, const std::string& prefix, bool ignoreCase = false);

        /**
         * @brief Check if string ends with suffix
         * @param str String to check
         * @param suffix Suffix to check for
         * @param ignoreCase Whether to ignore case (default: false)
         * @return true if string ends with suffix
         */
        static bool endsWith(const std::string& str, const std::string& suffix, bool ignoreCase = false);

        /**
         * @brief Check if string contains substring
         * @param str String to search in
         * @param substring Substring to search for
         * @param ignoreCase Whether to ignore case (default: false)
         * @return true if string contains substring
         */
        static bool contains(const std::string& str, const std::string& substring, bool ignoreCase = false);

        /**
         * @brief Replace all occurrences of substring
         * @param str String to modify
         * @param from Substring to replace
         * @param to Replacement string
         * @return String with replacements
         */
        static std::string replace(const std::string& str, const std::string& from, const std::string& to);

        /**
         * @brief Replace first occurrence of substring
         * @param str String to modify
         * @param from Substring to replace
         * @param to Replacement string
         * @return String with replacement
         */
        static std::string replaceFirst(const std::string& str, const std::string& from, const std::string& to);

        /**
         * @brief Replace last occurrence of substring
         * @param str String to modify
         * @param from Substring to replace
         * @param to Replacement string
         * @return String with replacement
         */
        static std::string replaceLast(const std::string& str, const std::string& from, const std::string& to);

        /**
         * @brief Pad string to specified length with character
         * @param str String to pad
         * @param length Target length
         * @param padChar Character to pad with (default: space)
         * @return Padded string
         */
        static std::string padLeft(const std::string& str, size_t length, char padChar = ' ');

        /**
         * @brief Pad string to specified length with character on right
         * @param str String to pad
         * @param length Target length
         * @param padChar Character to pad with (default: space)
         * @return Padded string
         */
        static std::string padRight(const std::string& str, size_t length, char padChar = ' ');

        /**
         * @brief Pad string to specified length with character on both sides
         * @param str String to pad
         * @param length Target length
         * @param padChar Character to pad with (default: space)
         * @return Padded string
         */
        static std::string padCenter(const std::string& str, size_t length, char padChar = ' ');

        /**
         * @brief Reverse string
         * @param str String to reverse
         * @return Reversed string
         */
        static std::string reverse(const std::string& str);

        /**
         * @brief Repeat string n times
         * @param str String to repeat
         * @param count Number of times to repeat
         * @return Repeated string
         */
        static std::string repeat(const std::string& str, size_t count);

        /**
         * @brief Check if string is empty or contains only whitespace
         * @param str String to check
         * @return true if string is blank
         */
        static bool isBlank(const std::string& str);

        /**
         * @brief Check if string is numeric (integer or float)
         * @param str String to check
         * @return true if string is numeric
         */
        static bool isNumeric(const std::string& str);

        /**
         * @brief Check if string is integer
         * @param str String to check
         * @return true if string is integer
         */
        static bool isInteger(const std::string& str);

        /**
         * @brief Check if string is alphabetic
         * @param str String to check
         * @return true if string contains only letters
         */
        static bool isAlpha(const std::string& str);

        /**
         * @brief Check if string is alphanumeric
         * @param str String to check
         * @return true if string contains only letters and digits
         */
        static bool isAlphaNumeric(const std::string& str);

        /**
         * @brief Count occurrences of substring
         * @param str String to search in
         * @param substring Substring to count
         * @param ignoreCase Whether to ignore case (default: false)
         * @return Number of occurrences
         */
        static size_t count(const std::string& str, const std::string& substring, bool ignoreCase = false);

        /**
         * @brief Find all positions of substring
         * @param str String to search in
         * @param substring Substring to find
         * @param ignoreCase Whether to ignore case (default: false)
         * @return Vector of positions
         */
        static std::vector<size_t> findAll(const std::string& str, const std::string& substring, bool ignoreCase = false);

        /**
         * @brief Extract substring between two delimiters
         * @param str String to extract from
         * @param start Start delimiter
         * @param end End delimiter
         * @return Optional substring (nullopt if not found)
         */
        static std::optional<std::string> extractBetween(const std::string& str, const std::string& start, const std::string& end);

        /**
         * @brief Remove all occurrences of substring
         * @param str String to modify
         * @param substring Substring to remove
         * @param ignoreCase Whether to ignore case (default: false)
         * @return String with substring removed
         */
        static std::string remove(const std::string& str, const std::string& substring, bool ignoreCase = false);

        /**
         * @brief Truncate string to specified length with optional suffix
         * @param str String to truncate
         * @param length Maximum length
         * @param suffix Suffix to add if truncated (default: "...")
         * @return Truncated string
         */
        static std::string truncate(const std::string& str, size_t length, const std::string& suffix = "...");

        /**
         * @brief Compare strings ignoring case
         * @param str1 First string
         * @param str2 Second string
         * @return true if strings are equal (case-insensitive)
         */
        static bool equalsIgnoreCase(const std::string& str1, const std::string& str2);

        /**
         * @brief Generate random string of specified length
         * @param length Length of random string
         * @param charset Character set to use (default: alphanumeric)
         * @return Random string
         */
        static std::string random(size_t length, const std::string& charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");

    private:
        /**
         * @brief Helper function to convert string to lowercase for comparison
         * @param str String to convert
         * @return Lowercase string
         */
        static std::string toLowerHelper(const std::string& str);
    };

    // Convenience functions for backward compatibility and ease of use

    /**
     * @brief Split string by delimiter (convenience function)
     * @param str String to split
     * @param delimiter Delimiter character
     * @return Vector of split strings
     */
    std::vector<std::string> split(const std::string& str, char delimiter);

    /**
     * @brief Join strings with delimiter (convenience function)
     * @param strings Vector of strings to join
     * @param delimiter Delimiter to use
     * @return Joined string
     */
    std::string join(const std::vector<std::string>& strings, const std::string& delimiter);

    /**
     * @brief Trim whitespace (convenience function)
     * @param str String to trim
     * @return Trimmed string
     */
    std::string trim(const std::string& str);

    /**
     * @brief Convert to lowercase (convenience function)
     * @param str String to convert
     * @return Lowercase string
     */
    std::string toLower(const std::string& str);

    /**
     * @brief Convert to uppercase (convenience function)
     * @param str String to convert
     * @return Uppercase string
     */
    std::string toUpper(const std::string& str);
}
