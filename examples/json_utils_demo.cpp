#include <iostream>
#include <utils/json_utils.hpp>

int main() {
    std::cout << "=== C-AIBOX JSON Utils Demo ===" << std::endl;

    // 1. Creating JSON Values
    std::cout << "\n1. Creating JSON Values:" << std::endl;
    
    utils::JsonValue nullVal;
    utils::JsonValue boolVal(true);
    utils::JsonValue numberVal(42.5);
    utils::JsonValue stringVal("Hello, JSON!");
    
    std::cout << "Null: " << nullVal.toString() << std::endl;
    std::cout << "Boolean: " << boolVal.toString() << std::endl;
    std::cout << "Number: " << numberVal.toString() << std::endl;
    std::cout << "String: " << stringVal.toString() << std::endl;

    // 2. Creating JSON Arrays
    std::cout << "\n2. Creating JSON Arrays:" << std::endl;
    
    utils::JsonValue arrayVal = utils::createArray();
    arrayVal.asArray().push_back(utils::JsonValue(1));
    arrayVal.asArray().push_back(utils::JsonValue("two"));
    arrayVal.asArray().push_back(utils::JsonValue(true));
    arrayVal.asArray().push_back(utils::JsonValue());
    
    std::cout << "Array: " << arrayVal.toString() << std::endl;
    std::cout << "Array (pretty): " << arrayVal.toString(true) << std::endl;
    std::cout << "Array size: " << arrayVal.size() << std::endl;
    std::cout << "First element: " << arrayVal[0].toString() << std::endl;

    // 3. Creating JSON Objects
    std::cout << "\n3. Creating JSON Objects:" << std::endl;
    
    utils::JsonValue objectVal = utils::createObject();
    objectVal["name"] = utils::JsonValue("John Doe");
    objectVal["age"] = utils::JsonValue(30);
    objectVal["active"] = utils::JsonValue(true);
    objectVal["salary"] = utils::JsonValue(50000.75);
    objectVal["address"] = utils::JsonValue();
    
    std::cout << "Object: " << objectVal.toString() << std::endl;
    std::cout << "Object (pretty): " << objectVal.toString(true) << std::endl;
    std::cout << "Object size: " << objectVal.size() << std::endl;
    std::cout << "Has 'name' key: " << (objectVal.hasKey("name") ? "Yes" : "No") << std::endl;
    std::cout << "Has 'email' key: " << (objectVal.hasKey("email") ? "Yes" : "No") << std::endl;

    // 4. Nested JSON Structures
    std::cout << "\n4. Nested JSON Structures:" << std::endl;
    
    utils::JsonValue nestedObj = utils::createObject();
    nestedObj["user"] = utils::createObject();
    nestedObj["user"]["name"] = utils::JsonValue("Alice");
    nestedObj["user"]["details"] = utils::createObject();
    nestedObj["user"]["details"]["age"] = utils::JsonValue(25);
    nestedObj["user"]["details"]["email"] = utils::JsonValue("<EMAIL>");
    
    nestedObj["items"] = utils::createArray();
    utils::JsonValue item1 = utils::createObject();
    item1["id"] = utils::JsonValue(1);
    item1["name"] = utils::JsonValue("Item 1");
    nestedObj["items"].asArray().push_back(item1);
    
    utils::JsonValue item2 = utils::createObject();
    item2["id"] = utils::JsonValue(2);
    item2["name"] = utils::JsonValue("Item 2");
    nestedObj["items"].asArray().push_back(item2);
    
    std::cout << "Nested structure (pretty):" << std::endl;
    std::cout << nestedObj.toString(true) << std::endl;

    // 5. JSON Parsing
    std::cout << "\n5. JSON Parsing:" << std::endl;
    
    std::string jsonString = R"({
        "name": "Bob",
        "age": 35,
        "hobbies": ["reading", "coding", "gaming"],
        "address": {
            "street": "123 Main St",
            "city": "Anytown",
            "zipcode": "12345"
        },
        "married": true,
        "children": null
    })";
    
    auto parsed = utils::JsonUtils::parse(jsonString);
    if (parsed.has_value()) {
        std::cout << "Parsed JSON successfully!" << std::endl;
        std::cout << "Name: " << parsed->operator[]("name").asString() << std::endl;
        std::cout << "Age: " << parsed->operator[]("age").asInt() << std::endl;
        std::cout << "First hobby: " << parsed->operator[]("hobbies")[0].asString() << std::endl;
        std::cout << "City: " << parsed->operator[]("address")["city"].asString() << std::endl;
        std::cout << "Married: " << (parsed->operator[]("married").asBool() ? "Yes" : "No") << std::endl;
        std::cout << "Children is null: " << (parsed->operator[]("children").isNull() ? "Yes" : "No") << std::endl;
    } else {
        std::cout << "Failed to parse JSON!" << std::endl;
    }

    // Test convenience function
    auto convParsed = utils::parseJson(R"({"test": 123})");
    if (convParsed.has_value()) {
        std::cout << "Convenience parse: " << convParsed->operator[]("test").asInt() << std::endl;
    }

    // 6. JSON Stringification
    std::cout << "\n6. JSON Stringification:" << std::endl;
    
    utils::JsonValue dataObj = utils::createObject();
    dataObj["timestamp"] = utils::JsonValue(1640995200);
    dataObj["message"] = utils::JsonValue("Hello World");
    dataObj["tags"] = utils::createArray();
    dataObj["tags"].asArray().push_back(utils::JsonValue("important"));
    dataObj["tags"].asArray().push_back(utils::JsonValue("urgent"));
    
    std::string compact = utils::JsonUtils::stringify(dataObj);
    std::string pretty = utils::JsonUtils::stringify(dataObj, true);
    
    std::cout << "Compact: " << compact << std::endl;
    std::cout << "Pretty:" << std::endl << pretty << std::endl;
    
    // Test convenience function
    std::cout << "Convenience stringify: " << utils::toJson(dataObj) << std::endl;

    // 7. JSON Validation
    std::cout << "\n7. JSON Validation:" << std::endl;
    
    std::vector<std::string> testStrings = {
        R"({"valid": true})",
        R"([1, 2, 3])",
        R"("simple string")",
        R"(null)",
        R"(42)",
        R"({invalid json})",
        R"([1, 2, 3,])",
        R"({"key": })"
    };
    
    for (const auto& testStr : testStrings) {
        bool isValid = utils::JsonUtils::isValid(testStr);
        std::cout << "'" << testStr << "' is " << (isValid ? "valid" : "invalid") << std::endl;
    }

    // 8. JSON Merging
    std::cout << "\n8. JSON Merging:" << std::endl;
    
    utils::JsonValue base = utils::createObject();
    base["name"] = utils::JsonValue("Original");
    base["version"] = utils::JsonValue(1);
    base["config"] = utils::createObject();
    base["config"]["debug"] = utils::JsonValue(false);
    base["config"]["timeout"] = utils::JsonValue(30);
    
    utils::JsonValue overlay = utils::createObject();
    overlay["version"] = utils::JsonValue(2); // Override
    overlay["author"] = utils::JsonValue("Developer"); // New field
    overlay["config"] = utils::createObject();
    overlay["config"]["debug"] = utils::JsonValue(true); // Override nested
    overlay["config"]["retries"] = utils::JsonValue(3); // New nested field
    
    auto merged = utils::JsonUtils::merge(base, overlay);
    
    std::cout << "Base:" << std::endl << base.toString(true) << std::endl;
    std::cout << "Overlay:" << std::endl << overlay.toString(true) << std::endl;
    std::cout << "Merged:" << std::endl << merged.toString(true) << std::endl;

    // 9. Type Checking and Conversion
    std::cout << "\n9. Type Checking and Conversion:" << std::endl;
    
    utils::JsonValue mixedArray = utils::createArray();
    mixedArray.asArray().push_back(utils::JsonValue(42));
    mixedArray.asArray().push_back(utils::JsonValue("text"));
    mixedArray.asArray().push_back(utils::JsonValue(true));
    mixedArray.asArray().push_back(utils::JsonValue());
    mixedArray.asArray().push_back(utils::createObject());
    
    for (size_t i = 0; i < mixedArray.size(); ++i) {
        const auto& element = mixedArray[i];
        std::cout << "Element " << i << ": ";
        
        if (element.isNull()) {
            std::cout << "null";
        } else if (element.isBool()) {
            std::cout << "boolean (" << (element.asBool() ? "true" : "false") << ")";
        } else if (element.isNumber()) {
            std::cout << "number (" << element.asNumber() << ")";
        } else if (element.isString()) {
            std::cout << "string (\"" << element.asString() << "\")";
        } else if (element.isArray()) {
            std::cout << "array (size: " << element.size() << ")";
        } else if (element.isObject()) {
            std::cout << "object (size: " << element.size() << ")";
        }
        std::cout << std::endl;
    }

    // 10. Error Handling
    std::cout << "\n10. Error Handling:" << std::endl;
    
    // Test invalid JSON parsing
    auto invalidResult = utils::JsonUtils::parse("{invalid json}");
    std::cout << "Invalid JSON parse result: " << (invalidResult.has_value() ? "Success" : "Failed") << std::endl;
    
    // Test type mismatches
    utils::JsonValue stringValue("not a number");
    try {
        int num = stringValue.asInt();
        std::cout << "Unexpected success: " << num << std::endl;
    } catch (const std::exception& e) {
        std::cout << "Expected error: " << e.what() << std::endl;
    }
    
    // Test array bounds
    utils::JsonValue smallArray = utils::createArray();
    smallArray.asArray().push_back(utils::JsonValue("only element"));
    
    try {
        auto element = smallArray[5]; // Out of bounds
        std::cout << "Unexpected success: " << element.toString() << std::endl;
    } catch (const std::exception& e) {
        std::cout << "Expected error: " << e.what() << std::endl;
    }

    // 11. Working with Object Keys
    std::cout << "\n11. Working with Object Keys:" << std::endl;
    
    utils::JsonValue configObj = utils::createObject();
    configObj["database_host"] = utils::JsonValue("localhost");
    configObj["database_port"] = utils::JsonValue(5432);
    configObj["database_name"] = utils::JsonValue("myapp");
    configObj["debug_mode"] = utils::JsonValue(true);
    configObj["max_connections"] = utils::JsonValue(100);
    
    auto keys = configObj.getKeys();
    std::cout << "Configuration keys (" << keys.size() << " total):" << std::endl;
    for (const auto& key : keys) {
        std::cout << "  - " << key << ": " << configObj[key].toString() << std::endl;
    }

    // 12. String Escaping
    std::cout << "\n12. String Escaping:" << std::endl;
    
    utils::JsonValue escapedString("Line 1\nLine 2\tTabbed\"Quoted\"\\Backslash");
    std::cout << "Original string: " << escapedString.asString() << std::endl;
    std::cout << "JSON representation: " << escapedString.toString() << std::endl;
    
    // Parse it back
    auto reparsed = utils::JsonUtils::parse(escapedString.toString());
    if (reparsed.has_value()) {
        std::cout << "Reparsed string: " << reparsed->asString() << std::endl;
        std::cout << "Strings match: " << (reparsed->asString() == escapedString.asString() ? "Yes" : "No") << std::endl;
    }

    std::cout << "\n=== Demo completed ===" << std::endl;
    return 0;
}
