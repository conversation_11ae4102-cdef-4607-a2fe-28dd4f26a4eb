#include "utils/env_utils.hpp"
#include <fstream>
#include <sstream>
#include <iostream>
#include <cstdlib>
#include <algorithm>

#ifdef _WIN32
#include <cstdlib>  // for _putenv
#else
#include <cstdlib>  // for setenv, unsetenv
#endif

namespace utils {
    // Static member definition
    std::unordered_map<std::string, std::string> EnvUtils::loadedEnvVars;

    bool EnvUtils::loadConfigFromFile(const std::string& filepath) {
        std::ifstream file(filepath);
        if (!file.is_open()) {
            std::cerr << "Warning: Could not open config file: " << filepath << std::endl;
            return false;
        }

        std::string line;
        int lineNumber = 0;
        bool success = true;
        std::string currentSection = "";

        while (std::getline(file, line)) {
            lineNumber++;

            // Skip empty lines and comments
            std::string trimmedLine = trim(line);
            if (trimmedLine.empty() || trimmedLine[0] == '#' || trimmedLine[0] == ';') {
                continue;
            }

            std::string key, value;
            bool isValidLine = parseIniLine(trimmedLine, currentSection, key, value);
            if (isValidLine && !key.empty()) {
                // Store in our internal map
                loadedEnvVars[key] = value;

                // Also set in system environment
                if (!setEnv(key, value, true)) {
                    std::cerr << "Warning: Failed to set environment variable: " << key << std::endl;
                    success = false;
                }
            } else if (!isValidLine) {
                std::cerr << "Warning: Invalid line " << lineNumber << " in " << filepath << ": " << line << std::endl;
                success = false;
            }
            // Note: Section headers are valid but don't produce key-value pairs, so we don't treat them as errors
        }

        file.close();
        return success;
    }



    bool EnvUtils::loadConfigFromString(const std::string& content, bool silent) {
        std::istringstream stream(content);
        std::string line;
        int lineNumber = 0;
        bool success = true;
        std::string currentSection = "";

        while (std::getline(stream, line)) {
            lineNumber++;

            // Skip empty lines and comments
            std::string trimmedLine = trim(line);
            if (trimmedLine.empty() || trimmedLine[0] == '#' || trimmedLine[0] == ';') {
                continue;
            }

            std::string key, value;
            bool isValidLine = parseIniLine(trimmedLine, currentSection, key, value);
            if (isValidLine && !key.empty()) {
                // Store in our internal map
                loadedEnvVars[key] = value;

                // Also set in system environment
                if (!setEnv(key, value, true)) {
                    if (!silent) {
                        std::cerr << "Warning: Failed to set environment variable: " << key << std::endl;
                    }
                    success = false;
                }
            } else if (!isValidLine) {
                if (!silent) {
                    std::cerr << "Warning: Invalid line " << lineNumber << " in content: " << line << std::endl;
                }
                success = false;
            }
            // Note: Section headers are valid but don't produce key-value pairs, so we don't treat them as errors
        }

        return success;
    }



    std::string EnvUtils::getEnv(const std::string& key, const std::string& defaultValue) {
        // First check our loaded variables
        auto it = loadedEnvVars.find(key);
        if (it != loadedEnvVars.end()) {
            return it->second;
        }

        // Then check system environment
        const char* value = std::getenv(key.c_str());
        if (value != nullptr) {
            return std::string(value);
        }

        return defaultValue;
    }

    std::optional<std::string> EnvUtils::getEnvOptional(const std::string& key) {
        // First check our loaded variables
        auto it = loadedEnvVars.find(key);
        if (it != loadedEnvVars.end()) {
            return it->second;
        }

        // Then check system environment
        const char* value = std::getenv(key.c_str());
        if (value != nullptr) {
            return std::string(value);
        }

        return std::nullopt;
    }

    bool EnvUtils::setEnv(const std::string& key, const std::string& value, bool overwrite) {
        // Update our internal map
        if (overwrite || loadedEnvVars.find(key) == loadedEnvVars.end()) {
            loadedEnvVars[key] = value;
        }

        // Set in system environment
#ifdef _WIN32
        std::string envString = key + "=" + value;
        return _putenv(envString.c_str()) == 0;
#else
        return setenv(key.c_str(), value.c_str(), overwrite ? 1 : 0) == 0;
#endif
    }

    bool EnvUtils::hasEnv(const std::string& key) {
        // Check our loaded variables first
        if (loadedEnvVars.find(key) != loadedEnvVars.end()) {
            return true;
        }

        // Then check system environment
        return std::getenv(key.c_str()) != nullptr;
    }

    bool EnvUtils::unsetEnv(const std::string& key) {
        // Remove from our internal map
        loadedEnvVars.erase(key);

        // Remove from system environment
#ifdef _WIN32
        std::string envString = key + "=";
        return _putenv(envString.c_str()) == 0;
#else
        return unsetenv(key.c_str()) == 0;
#endif
    }

    std::unordered_map<std::string, std::string> EnvUtils::getAllEnv() {
        return loadedEnvVars;
    }

    void EnvUtils::clearLoadedEnv() {
        loadedEnvVars.clear();
    }

    bool EnvUtils::parseIniLine(const std::string& line, std::string& currentSection, std::string& key, std::string& value) {
        // Check if this is a section header [section_name]
        if (line.front() == '[' && line.back() == ']') {
            currentSection = line.substr(1, line.length() - 2);
            currentSection = trim(currentSection);
            key = ""; // Clear key to indicate this is a section header
            value = "";
            return true; // Section headers are valid lines
        }

        // Parse key=value pairs
        size_t equalPos = line.find('=');
        if (equalPos == std::string::npos) {
            return false;
        }

        std::string rawKey = trim(line.substr(0, equalPos));
        if (rawKey.empty()) {
            return false;
        }

        // Check if key contains spaces or invalid characters
        if (rawKey.find(' ') != std::string::npos ||
            rawKey.find('\t') != std::string::npos) {
            return false;
        }

        // Create full key with section prefix if section exists
        if (!currentSection.empty()) {
            key = currentSection + "." + rawKey;
        } else {
            key = rawKey;
        }

        value = trim(line.substr(equalPos + 1));
        value = unquote(value);

        return true;
    }



    std::string EnvUtils::trim(const std::string& str) {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos) {
            return "";
        }

        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }

    std::string EnvUtils::unquote(const std::string& str) {
        if (str.length() < 2) {
            return str;
        }

        char first = str.front();
        char last = str.back();

        if ((first == '"' && last == '"') || (first == '\'' && last == '\'')) {
            return str.substr(1, str.length() - 2);
        }

        return str;
    }

    // Convenience functions implementation
    bool loadConfig(const std::string& filepath) {
        return EnvUtils::loadConfigFromFile(filepath);
    }

    std::string getEnv(const std::string& key, const std::string& defaultValue) {
        return EnvUtils::getEnv(key, defaultValue);
    }

    bool setEnv(const std::string& key, const std::string& value) {
        return EnvUtils::setEnv(key, value);
    }

    bool hasEnv(const std::string& key) {
        return EnvUtils::hasEnv(key);
    }
}
