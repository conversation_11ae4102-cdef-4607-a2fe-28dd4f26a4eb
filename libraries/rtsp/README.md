# RTSP Input Module

## Overview

The RTSP Input Module is a high-performance library specifically optimized for Orange Pi 5 Plus/Ultra with RK3588 chip. It provides robust RTSP stream handling capabilities with hardware acceleration support.

## Platform Optimization

This library is designed and optimized specifically for:
- **Hardware**: Orange Pi 5 Plus/Ultra with RK3588 SoC
- **Architecture**: ARM64 with big.LITTLE (4x Cortex-A76 + 4x Cortex-A55)
- **Memory**: 4GB/8GB/16GB LPDDR4X configurations
- **Hardware Accelerators**: MPP decoder, RGA scaler, DMABUF zero-copy

## Technology Stack

### Primary (RK3588 Optimized)
- **GStreamer 1.18+** with RockChip plugins
- **MPP Decoder** for hardware H.264/H.265 decoding
- **RGA Scaler** for hardware image processing
- **DMABUF** for zero-copy memory operations

### Fallback
- **FFmpeg** libraries (software decoding when hardware unavailable)

## Features

- **Multi-stream Support**: Handle multiple concurrent RTSP streams
- **Hardware Acceleration**: Utilize RK3588 MPP decoder and RGA scaler
- **Thermal Management**: Adaptive performance based on SoC temperature
- **Memory Efficiency**: DMABUF zero-copy operations
- **Error Recovery**: Robust error handling with automatic retry
- **Resource Sharing**: Coordinate with other AI Box modules

## Performance Targets

### Orange Pi 5 Plus (4GB RAM)
- **Max Streams**: 6 concurrent 1080p@30fps
- **Memory Usage**: ≤ 1.2GB
- **CPU Usage**: ≤ 25% (cores 2-3)
- **Latency**: < 200ms end-to-end

### Orange Pi 5 Ultra (8GB RAM)
- **Max Streams**: 12 concurrent 1080p@30fps
- **Memory Usage**: ≤ 2.5GB
- **CPU Usage**: ≤ 30% (cores 2-3)
- **Latency**: < 150ms end-to-end

### Orange Pi 5 Ultra (16GB RAM)
- **Max Streams**: 24 concurrent 1080p@30fps
- **Memory Usage**: ≤ 6GB
- **CPU Usage**: ≤ 35% (cores 2-3)
- **Latency**: < 120ms end-to-end

## Directory Structure

```
libraries/rtsp/
├── CMakeLists.txt              # Build configuration
├── README.md                   # This file
├── include/
│   └── rtsp/
│       ├── connection_manager.hpp
│       ├── packet_receiver.hpp
│       ├── nal_parser.hpp
│       ├── stream_multiplexer.hpp
│       ├── gstreamer_rtsp_client.hpp
│       ├── rtsp_config.hpp
│       └── rtsp_types.hpp
├── src/
│   ├── connection_manager.cpp
│   ├── packet_receiver.cpp
│   ├── nal_parser.cpp
│   ├── stream_multiplexer.cpp
│   ├── gstreamer_rtsp_client.cpp
│   └── rtsp_config.cpp
└── test/
    ├── test_connection_manager.cpp
    ├── test_packet_receiver.cpp
    ├── test_nal_parser.cpp
    └── test_stream_multiplexer.cpp
```

## Dependencies

### Required (RK3588 Platform)
- GStreamer 1.18+ with RockChip plugins
- librockchip_mpp (MPP decoder)
- librga (RGA scaler)
- libdrm_rockchip (DMABUF support)
- OpenSSL (secure connections)
- nlohmann/json (configuration)

### Optional
- FFmpeg libraries (fallback)
- GoogleTest (testing)

## Build Instructions

### Prerequisites
```bash
# Install GStreamer with RockChip plugins
sudo apt install \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    gstreamer1.0-rockchip \
    librockchip-mpp-dev \
    librga-dev \
    libdrm-rockchip-dev

# Install other dependencies
sudo apt install \
    libssl-dev \
    nlohmann-json3-dev
```

### Cross-Compilation (Recommended)
```bash
# Set up ARM64 toolchain
export CC=aarch64-linux-gnu-gcc
export CXX=aarch64-linux-gnu-g++

# Configure with CMake
mkdir build && cd build
cmake -DCMAKE_TOOLCHAIN_FILE=../cmake/rk3588-toolchain.cmake \
      -DCMAKE_BUILD_TYPE=Release \
      -DRK3588_PLATFORM=ON \
      -DORANGE_PI_RAM_8GB=ON \
      ..

# Build
make -j$(nproc)
```

### Native Build (On Orange Pi)
```bash
mkdir build && cd build
cmake -DCMAKE_BUILD_TYPE=Release \
      -DRK3588_PLATFORM=ON \
      -DORANGE_PI_RAM_16GB=ON \
      ..
make -j$(nproc)
```

## Testing

### Unit Tests
```bash
# Build with tests enabled
cmake -DBUILD_RTSP_TESTS=ON ..
make -j$(nproc)

# Run tests
ctest --verbose
```

### Hardware-in-the-Loop Testing
```bash
# Test on actual Orange Pi hardware
./rtsp_test_connection_manager
./rtsp_test_packet_receiver
```

## Configuration

### Basic Configuration
```json
{
  "rtsp_module": {
    "max_streams": 6,
    "memory_limit_mb": 1200,
    "cpu_cores": [2, 3],
    "hardware_acceleration": {
      "mpp_decoder": true,
      "rga_scaler": true,
      "dmabuf_zerocopy": true
    }
  }
}
```

### Performance Tuning
See `docs/tasks-defined/1-rtsp-input/rk3588-optimization-guide.md` for detailed optimization guidelines.

## Integration

### With Other AI Box Modules
- **Face Detection**: Shares video frames via DMABUF queues
- **Face Recognition**: Receives processed frames for analysis
- **UI Client**: Provides stream status and control interface

### Resource Coordination
- **CPU Cores**: Uses cores 2-3 (Cortex-A55)
- **Memory**: Limited to platform-specific quotas
- **Hardware Accelerators**: Coordinates MPP/RGA usage

## Troubleshooting

### Common Issues
1. **GStreamer plugins not found**: Install gstreamer1.0-rockchip
2. **MPP decoder fails**: Check hardware accelerator availability
3. **Memory pressure**: Reduce stream count or quality
4. **Thermal throttling**: Implement adaptive performance scaling

### Debug Build
```bash
cmake -DCMAKE_BUILD_TYPE=Debug ..
make -j$(nproc)
```

## Contributing

1. Follow RK3588-first development approach
2. Test on actual Orange Pi hardware
3. Respect resource allocation constraints
4. Update documentation for platform-specific changes

## License

[License information]

## Support

For RK3588-specific issues and optimization questions, refer to:
- `docs/tasks-defined/1-rtsp-input/rk3588-platform-guidelines.md`
- `docs/tasks-defined/1-rtsp-input/rk3588-compliance-checklist.md`
