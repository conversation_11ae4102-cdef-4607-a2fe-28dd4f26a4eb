# ----- Stage 1: Build -----
FROM ubuntu:24.04 as builder

# Cài đặt các gói build cần thiết
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy toàn bộ source code vào image
WORKDIR /build
COPY ../../ ./

# T<PERSON>o thư mục build riêng biệt
RUN cmake -Bbuild -H. -DCMAKE_BUILD_TYPE=Release
RUN cmake --build build --target client --config Release

# ----- Stage 2: Runtime -----
FROM ubuntu:24.04

# Cài đặt các thư viện runtime cần thiết (nếu app link động)
RUN apt-get update && apt-get install -y \
    libstdc++6 \
    && rm -rf /var/lib/apt/lists/*

# Copy binary từ stage build sang stage run
WORKDIR /app
COPY --from=builder /build/build/apps/client/client ./

# Command mặc định khi chạy container
ENTRYPOINT ["./client"]
