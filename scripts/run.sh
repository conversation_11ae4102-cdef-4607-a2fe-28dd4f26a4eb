#!/bin/bash

# Main run script for C-AIBOX project
# Usage: ./scripts/run.sh [target] [options]

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [TARGET] [OPTIONS]"
    echo ""
    echo "TARGETS:"
    echo "  client         Run the Qt5 GUI client application"
    echo "  server         Run the HTTP API server"
    echo "  examples       Run utility examples"
    echo "  gui            Run GUI application with X11 setup"
    echo ""
    echo "OPTIONS:"
    echo "  --build-type TYPE    Build type (Debug|Release) [default: Debug]"
    echo "  --headless           Run in headless mode (no GUI)"
    echo "  --rebuild            Clean and rebuild before running"
    echo "  --help, -h           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 client                    # Run client in GUI mode"
    echo "  $0 client --headless         # Run client in headless mode"
    echo "  $0 server --build-type Release"
    echo "  $0 gui                       # Run with full GUI setup"
}

# Function to run client
run_client() {
    local build_type="${1:-Debug}"
    local headless="${2:-false}"
    local rebuild="${3:-false}"
    
    print_info "Running C-AIBOX Client..."
    
    if [[ "$headless" == "true" ]]; then
        "$SCRIPT_DIR/dev/run-client.sh" -t "$build_type" --headless ${rebuild:+-r}
    else
        "$SCRIPT_DIR/dev/run-client.sh" -t "$build_type" ${rebuild:+-r}
    fi
}

# Function to run server
run_server() {
    local build_type="${1:-Debug}"
    local rebuild="${2:-false}"
    
    print_info "Running C-AIBOX Server..."
    
    # Build if needed
    if [[ "$rebuild" == "true" ]] || [[ ! -f "$PROJECT_ROOT/build/apps/server/server" ]]; then
        "$SCRIPT_DIR/build.sh" "$build_type" ${rebuild:+clean}
    fi
    
    cd "$PROJECT_ROOT/build"
    ./apps/server/server
}

# Function to run examples
run_examples() {
    local build_type="${1:-Debug}"
    
    print_info "Running C-AIBOX Examples..."
    
    # Build if needed
    if [[ ! -d "$PROJECT_ROOT/build/bin/examples" ]]; then
        "$SCRIPT_DIR/build.sh" "$build_type"
    fi
    
    cd "$PROJECT_ROOT/build/bin/examples"
    
    print_info "Available examples:"
    for example in *_demo; do
        if [[ -x "$example" ]]; then
            echo "  - $example"
        fi
    done
    
    echo ""
    read -p "Enter example name to run (or 'all' for all): " choice
    
    if [[ "$choice" == "all" ]]; then
        for example in *_demo; do
            if [[ -x "$example" ]]; then
                print_info "Running $example..."
                ./"$example"
                echo ""
            fi
        done
    elif [[ -x "$choice" ]]; then
        ./"$choice"
    else
        print_error "Example not found: $choice"
        exit 1
    fi
}

# Function to run GUI with full setup
run_gui() {
    local build_type="${1:-Debug}"
    local rebuild="${2:-false}"
    
    print_info "Running C-AIBOX GUI with full setup..."
    if [[ "$rebuild" == "true" ]]; then
        # For rebuild, we need to clean first then run
        "$SCRIPT_DIR/clean.sh" --build
        "$SCRIPT_DIR/dev/run-gui-app.sh" -t "$build_type"
    else
        "$SCRIPT_DIR/dev/run-gui-app.sh" -t "$build_type"
    fi
}

# Parse command line arguments
TARGET=""
BUILD_TYPE="Debug"
HEADLESS=false
REBUILD=false

while [[ $# -gt 0 ]]; do
    case $1 in
        client|server|examples|gui)
            TARGET="$1"
            shift
            ;;
        --build-type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        --headless)
            HEADLESS=true
            shift
            ;;
        --rebuild)
            REBUILD=true
            shift
            ;;
        --help|-h)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate build type
if [[ "$BUILD_TYPE" != "Debug" && "$BUILD_TYPE" != "Release" ]]; then
    print_error "Invalid build type: $BUILD_TYPE. Must be Debug or Release."
    exit 1
fi

# Default target if none specified
if [[ -z "$TARGET" ]]; then
    TARGET="client"
fi

# Change to project root
cd "$PROJECT_ROOT"

# Execute target
case $TARGET in
    client)
        run_client "$BUILD_TYPE" "$HEADLESS" "$REBUILD"
        ;;
    server)
        run_server "$BUILD_TYPE" "$REBUILD"
        ;;
    examples)
        run_examples "$BUILD_TYPE"
        ;;
    gui)
        run_gui "$BUILD_TYPE" "$REBUILD"
        ;;
    *)
        print_error "Unknown target: $TARGET"
        show_usage
        exit 1
        ;;
esac

print_success "Run completed!"
