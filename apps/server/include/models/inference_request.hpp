#pragma once

#include <string>
#include <optional>
#include <vector>
#include <chrono>

namespace server::models {

/**
 * @brief Base request model for AI inference
 */
struct InferenceRequest {
    std::string image_path;
    std::optional<std::string> model_version;
    std::optional<int> timeout_ms;
    
    // Validation
    bool isValid() const {
        return !image_path.empty();
    }
};

/**
 * @brief ArcFace inference request
 */
struct ArcFaceRequest : public InferenceRequest {
    std::optional<float> confidence_threshold;
    std::optional<bool> extract_features;
    
    bool isValid() const {
        return InferenceRequest::isValid() && 
               (!confidence_threshold.has_value() || 
                (confidence_threshold.value() >= 0.0f && confidence_threshold.value() <= 1.0f));
    }
};

/**
 * @brief YOLO detection request
 */
struct YoloRequest : public InferenceRequest {
    std::optional<float> confidence_threshold;
    std::optional<float> nms_threshold;
    std::optional<std::vector<std::string>> target_classes;
    
    bool isValid() const {
        return InferenceRequest::isValid() && 
               (!confidence_threshold.has_value() || 
                (confidence_threshold.value() >= 0.0f && confidence_threshold.value() <= 1.0f)) &&
               (!nms_threshold.has_value() || 
                (nms_threshold.value() >= 0.0f && nms_threshold.value() <= 1.0f));
    }
};

} // namespace server::models
