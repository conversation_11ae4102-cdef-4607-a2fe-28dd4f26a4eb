# RTSP Input Module - RK3588 Implementation Summary

## Executive Summary

Dự án RTSP Input Module đã được tối ưu hóa đặc biệt cho nền tảng Orange Pi 5 Plus/Ultra với chip RK3588, <PERSON><PERSON> tiên sử dụng GStreamer thay vì FFmpeg để tận dụng tối đa khả năng tăng tốc phần cứng của chip RockChip.

## Platform Specifications

### Hardware Platform: Orange Pi 5 Plus/Ultra
- **SoC**: RockChip RK3588 (8nm, 8-core ARM64)
  - 4x Cortex-A76 @ 2.4GHz (big cores)
  - 4x Cortex-A55 @ 1.8GHz (little cores)
- **GPU**: Mali-G610 MP4
- **VPU**: 8K@60fps H.265/VP9 decoder, 8K@30fps H.264 encoder
- **NPU**: 6 TOPS AI accelerator
- **Memory**: 4GB/8GB/16GB LPDDR4X-4224
- **Storage**: 240GB SSD
- **Network**: Gigabit Ethernet + WiFi 6

### Hardware Acceleration Units
1. **MPP (Media Process Platform)**: Hardware video decoder/encoder
2. **RGA (Raster Graphics Accelerator)**: 2D graphics processing
3. **NPU**: Neural network processing (future use)
4. **DMABUF**: Zero-copy memory management

## Technology Stack (Updated)

### Primary Dependencies
```
GStreamer 1.18+ (Primary choice)
├── gstreamer-rtsp-1.0      # RTSP protocol support
├── gstreamer-rtp-1.0       # RTP packet handling
├── gstreamer-app-1.0       # Application integration
├── gstreamer-video-1.0     # Video processing
├── gstreamer-rockchip-1.0  # RK3588 hardware plugins
└── gstreamer-mpp-1.0       # MPP decoder integration

RockChip Libraries
├── librockchip_mpp         # Media Process Platform
├── librga                  # Raster Graphics Accelerator
└── libdrm_rockchip        # DRM/DMABUF support

System Libraries
├── OpenSSL                 # Secure connections
├── nlohmann/json          # Configuration parsing
├── Qt 5.15+               # GUI framework
└── httplib                # HTTP server
```

### Fallback Dependencies
```
FFmpeg 4.4+ (Fallback only)
├── libavformat            # Container format support
├── libavcodec             # Codec support
├── libavutil              # Utility functions
└── libswscale             # Software scaling
```

## Performance Targets (Platform Optimized)

### Orange Pi 5 Plus (4GB RAM) - System-Wide Targets
- **Max Concurrent Streams**: 6 (RTSP module allocation)
- **Max Resolution**: 1920x1080 @ 30fps
- **Total System Memory**: < 3.8GB (200MB reserve)
- **RTSP Module Memory**: < 1.2GB
- **Face Processing**: 4-6 faces/second across all streams
- **System CPU Usage**: < 75% (all modules combined)
- **RTSP Module CPU**: < 25% (cores 2-3)
- **End-to-End Latency**: < 200ms (RTSP → Face Recognition)

### Orange Pi 5 Ultra (8GB RAM) - System-Wide Targets
- **Max Concurrent Streams**: 12 (RTSP module allocation)
- **Max Resolution**: 1920x1080 @ 30fps
- **Total System Memory**: < 7.5GB (500MB reserve)
- **RTSP Module Memory**: < 2.5GB
- **Face Processing**: 8-12 faces/second across all streams
- **System CPU Usage**: < 80% (all modules combined)
- **RTSP Module CPU**: < 30% (cores 2-3)
- **End-to-End Latency**: < 150ms (RTSP → Face Recognition)

## Architecture Optimizations

### System-Wide Thread Distribution Strategy
```cpp
// Optimal CPU core allocation for complete AI Box system
Core 0-1 (Cortex-A55): UI Client, Qt GUI, system services
Core 2-3 (Cortex-A55): RTSP Input Module, network I/O
Core 4-5 (Cortex-A76): Face Detection (NPU coordination)
Core 6-7 (Cortex-A76): Face Recognition & Identification

// Specialized Processing Units
NPU (6 TOPS): Face detection, face recognition inference
MPP Decoder: RTSP video decoding (hardware accelerated)
RGA: Image preprocessing, format conversion
GPU: UI rendering, optional image processing

// CPU Governor Settings
Little cores (0-3): ondemand (balanced performance/power)
Big cores (4-7): performance (maximum AI processing power)
```

### System-Wide Memory Management Strategy
```cpp
// Memory allocation for complete AI Box system
4GB Configuration (Conservative):
- System Reserve: 800MB
- UI Client: 400MB
- RTSP Module: 1.2GB (max 6 streams)
- Face Detection: 800MB (models + buffers)
- Face Recognition: 600MB (embeddings + processing)
- Shared Buffers: 200MB (inter-module communication)

8GB Configuration (Optimal):
- System Reserve: 1GB
- UI Client: 500MB
- RTSP Module: 2.5GB (max 12 streams)
- Face Detection: 1.5GB (larger models + buffers)
- Face Recognition: 1.5GB (larger embedding database)
- Shared Buffers: 1GB (high-throughput communication)
- Available: 500MB (dynamic allocation)

16GB Configuration (High Performance):
- System Reserve: 1.5GB
- UI Client: 500MB
- RTSP Module: 6GB (max 24 streams)
- Face Detection: 2.5GB (multiple models + large buffers)
- Face Recognition: 3GB (extensive embedding database)
- Shared Buffers: 2GB (maximum throughput communication)
- Available: 500MB (dynamic allocation)
```

### GStreamer Pipeline Optimization
```bash
# Optimized pipeline for RK3588
rtspsrc location=rtsp://camera/stream ! 
rtph264depay ! 
h264parse ! 
mppvideodec fast-mode=true ! 
rgaconvert output-io-mode=4 ! 
appsink max-buffers=3 drop=true
```

## Implementation Plan Updates

### Phase 1: Foundation (1-2 days)
- [x] **Platform Analysis**: RK3588 capabilities and constraints
- [x] **Technology Selection**: GStreamer over FFmpeg
- [ ] **Project Structure**: ARM64 optimized build system
- [ ] **Dependencies**: GStreamer + RockChip libraries

### Phase 2: Core Implementation (2-3 weeks)
- [ ] **GStreamer Integration**: RTSP pipeline with hardware acceleration
- [ ] **MPP Decoder**: Hardware H.264/H.265 decoding
- [ ] **RGA Scaler**: Hardware-accelerated format conversion
- [ ] **DMABUF Management**: Zero-copy memory operations
- [ ] **Thread Optimization**: CPU affinity and core allocation

### Phase 3: Platform Integration (1-2 weeks)
- [ ] **Memory Optimization**: Embedded platform constraints
- [ ] **Power Management**: Thermal throttling and frequency scaling
- [ ] **Performance Tuning**: ARM64 specific optimizations
- [ ] **Testing**: Hardware-in-the-loop validation

### Phase 4: System Integration (1-2 weeks)
- [ ] **Client GUI**: Qt interface optimized for embedded display
- [ ] **Server API**: Lightweight HTTP endpoints
- [ ] **Configuration**: Platform-specific settings
- [ ] **Monitoring**: Hardware metrics and thermal monitoring

## Key Technical Decisions

### 1. GStreamer vs FFmpeg
**Decision**: GStreamer as primary choice
**Rationale**: 
- Better RK3588 hardware acceleration support
- Native MPP decoder integration
- RGA scaler support
- DMABUF zero-copy capabilities
- Active RockChip community support

### 2. Memory Management
**Decision**: DMABUF-based zero-copy architecture
**Rationale**:
- Minimize memory bandwidth usage
- Reduce CPU overhead
- Better cache efficiency
- Hardware accelerator compatibility

### 3. Thread Architecture
**Decision**: Asymmetric multiprocessing (AMP) approach
**Rationale**:
- Leverage big.LITTLE architecture
- Optimize for power efficiency
- Maximize performance per watt
- Thermal management

### 4. Configuration Strategy
**Decision**: Adaptive configuration based on detected hardware
**Rationale**:
- Automatic 4GB vs 8GB RAM detection
- Dynamic stream count adjustment
- Thermal-aware performance scaling
- Storage space optimization

## Development Environment Setup

### Cross-Compilation Toolchain
```bash
# ARM64 cross-compilation setup
export CC=aarch64-linux-gnu-gcc
export CXX=aarch64-linux-gnu-g++
export AR=aarch64-linux-gnu-ar
export STRIP=aarch64-linux-gnu-strip

# CMake configuration
cmake -DCMAKE_TOOLCHAIN_FILE=cmake/aarch64-linux-gnu.cmake \
      -DCMAKE_BUILD_TYPE=Release \
      -DORANGE_PI_RAM_8GB=ON \
      -DRTSP_USE_GSTREAMER=ON \
      -DRTSP_PLATFORM_RK3588=ON \
      ..
```

### Required Packages (Orange Pi)
```bash
# GStreamer packages
sudo apt install \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    libgstreamer-plugins-good1.0-dev \
    libgstreamer-plugins-bad1.0-dev \
    gstreamer1.0-plugins-ugly \
    gstreamer1.0-rockchip

# RockChip specific libraries
sudo apt install \
    librockchip-mpp-dev \
    librga-dev \
    libdrm-rockchip-dev

# Development tools
sudo apt install \
    build-essential \
    cmake \
    pkg-config \
    git
```

## Testing and Validation

### Hardware-in-the-Loop Testing
1. **Thermal Testing**: Continuous operation under load
2. **Memory Stress**: Maximum stream count validation
3. **Network Resilience**: Connection failure recovery
4. **Power Efficiency**: Battery life optimization (if applicable)

### Performance Benchmarks
```cpp
// Expected benchmarks for Orange Pi 5 Plus/Ultra
struct RK3588Benchmarks {
    // 4GB model
    static constexpr int streams_4gb = 8;
    static constexpr float cpu_usage_4gb = 60.0f;
    static constexpr int memory_mb_4gb = 2500;
    
    // 8GB model  
    static constexpr int streams_8gb = 16;
    static constexpr float cpu_usage_8gb = 70.0f;
    static constexpr int memory_mb_8gb = 5000;
    
    // Common targets
    static constexpr int latency_ms = 120;
    static constexpr int max_temp_celsius = 85;
};
```

## Risk Mitigation (Platform Specific)

### High-Risk Items
1. **Thermal Throttling**: Implement dynamic performance scaling
2. **Memory Constraints**: Adaptive stream count based on available RAM
3. **Hardware Compatibility**: Fallback to software decoding if MPP fails
4. **Power Management**: Optimize for sustained operation

### Mitigation Strategies
1. **Thermal Management**: Monitor SoC temperature, reduce performance if needed
2. **Memory Management**: Implement memory pressure detection and response
3. **Graceful Degradation**: Automatic fallback from hardware to software processing
4. **Resource Monitoring**: Real-time system resource tracking

## Deployment Configuration

### Production Settings
```json
{
  "platform": "rk3588",
  "model": "orange_pi_5_plus",
  "ram_size_gb": 8,
  "performance_profile": "balanced",
  "thermal_management": "enabled",
  "hardware_acceleration": {
    "mpp_decoder": true,
    "rga_scaler": true,
    "dmabuf_zerocopy": true
  },
  "resource_limits": {
    "max_streams": 12,
    "max_memory_mb": 5000,
    "max_cpu_percent": 70,
    "max_temperature_celsius": 80
  }
}
```

## Success Criteria (Updated)

### Functional Requirements
- [ ] Successfully utilize RK3588 hardware acceleration (MPP, RGA)
- [ ] Handle 8-16 concurrent 1080p streams (depending on RAM)
- [ ] Maintain <120ms latency with hardware decoding
- [ ] Operate within thermal limits (<85°C)
- [ ] Graceful degradation when hardware limits reached

### Performance Requirements
- [ ] CPU usage <70% with hardware acceleration
- [ ] Memory usage within platform constraints
- [ ] Zero-copy operations for video data
- [ ] Sustained operation without thermal throttling
- [ ] >99% uptime under normal conditions

This implementation summary provides a comprehensive roadmap specifically optimized for the RK3588 platform, ensuring maximum utilization of hardware capabilities while respecting the constraints of the Orange Pi 5 Plus/Ultra embedded system.
