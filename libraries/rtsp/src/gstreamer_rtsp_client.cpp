#include "rtsp/gstreamer_rtsp_client.hpp"
#include <iostream>
#include <sstream>
#include <cstring>
#include <unistd.h>
#include <sys/syscall.h>
#include <pthread.h>
#include <sched.h>

// GStreamer includes
#ifdef HAVE_GSTREAMER
#include <gst/gst.h>
#include <gst/app/gstappsink.h>
#include <gst/rtsp/gstrtsp.h>
#include <gst/video/video.h>
#endif

namespace aibox {
namespace rtsp {

// GStreamerManager implementation
GStreamerManager& GStreamerManager::getInstance() {
    static GStreamerManager instance;
    return instance;
}

bool GStreamerManager::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        return true;
    }
    
#ifdef HAVE_GSTREAMER
    // Initialize GStreamer
    GError* error = nullptr;
    if (!gst_init_check(nullptr, nullptr, &error)) {
        std::cerr << "[GStreamerManager] Failed to initialize GStreamer: " 
                  << (error ? error->message : "Unknown error") << std::endl;
        if (error) g_error_free(error);
        return false;
    }
    
    std::cout << "[GStreamerManager] GStreamer initialized successfully" << std::endl;
    std::cout << "[GStreamerManager] Version: " << getVersion() << std::endl;
    
    // Cache capabilities
    cacheCapabilities();
    
    initialized_ = true;
    return true;
#else
    std::cerr << "[GStreamerManager] GStreamer support not compiled in" << std::endl;
    return false;
#endif
}

void GStreamerManager::cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!initialized_) {
        return;
    }
    
#ifdef HAVE_GSTREAMER
    gst_deinit();
    std::cout << "[GStreamerManager] GStreamer cleaned up" << std::endl;
#endif
    
    initialized_ = false;
}

bool GStreamerManager::isInitialized() const {
    return initialized_;
}

std::string GStreamerManager::getVersion() const {
#ifdef HAVE_GSTREAMER
    guint major, minor, micro, nano;
    gst_version(&major, &minor, &micro, &nano);
    
    std::ostringstream oss;
    oss << major << "." << minor << "." << micro;
    if (nano > 0) {
        oss << "." << nano;
    }
    return oss.str();
#else
    return "Not available";
#endif
}

std::vector<std::string> GStreamerManager::getAvailablePlugins() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!capabilities_cached_) {
        cacheCapabilities();
    }
    
    return available_plugins_;
}

bool GStreamerManager::isPluginAvailable(const std::string& plugin_name) const {
#ifdef HAVE_GSTREAMER
    GstPlugin* plugin = gst_registry_find_plugin(gst_registry_get(), plugin_name.c_str());
    if (plugin) {
        gst_object_unref(plugin);
        return true;
    }
#endif
    return false;
}

bool GStreamerManager::isMPPDecoderAvailable() const {
    return isPluginAvailable("mpp") || isPluginAvailable("rockchipmpp");
}

bool GStreamerManager::isRGAConverterAvailable() const {
    return isPluginAvailable("rga") || isPluginAvailable("rockchiprga");
}

bool GStreamerManager::isDMABufSupported() const {
    // Check for DMABUF support in the system
    return access("/dev/dri/card0", F_OK) == 0;
}

std::vector<std::string> GStreamerManager::getSupportedCodecs() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!capabilities_cached_) {
        cacheCapabilities();
    }
    
    return supported_codecs_;
}

void GStreamerManager::setDebugLevel(const std::string& level) {
#ifdef HAVE_GSTREAMER
    gst_debug_set_threshold_from_string(level.c_str(), TRUE);
    std::cout << "[GStreamerManager] Debug level set to: " << level << std::endl;
#endif
}

void GStreamerManager::enableDebugOutput(bool enable) {
#ifdef HAVE_GSTREAMER
    if (enable) {
        gst_debug_set_default_threshold(GST_LEVEL_DEBUG);
    } else {
        gst_debug_set_default_threshold(GST_LEVEL_WARNING);
    }
#endif
}

void GStreamerManager::cacheCapabilities() const {
#ifdef HAVE_GSTREAMER
    available_plugins_.clear();
    supported_codecs_.clear();
    
    // Get available plugins
    GstRegistry* registry = gst_registry_get();
    GList* plugins = gst_registry_get_plugin_list(registry);
    
    for (GList* l = plugins; l != nullptr; l = l->next) {
        GstPlugin* plugin = GST_PLUGIN(l->data);
        const gchar* name = gst_plugin_get_name(plugin);
        if (name) {
            available_plugins_.emplace_back(name);
        }
    }
    
    gst_plugin_list_free(plugins);
    
    // Check for common codecs
    if (isPluginAvailable("mpp") || isPluginAvailable("rockchipmpp")) {
        supported_codecs_.push_back("H.264");
        supported_codecs_.push_back("H.265");
    }
    
    capabilities_cached_ = true;
#endif
}

// GStreamerRTSPClient implementation
GStreamerRTSPClient::GStreamerRTSPClient(const RTSPConnectionConfig& config)
    : config_(config)
    , pipeline_(nullptr)
    , rtspsrc_(nullptr)
    , depayloader_(nullptr)
    , parser_(nullptr)
    , decoder_(nullptr)
    , converter_(nullptr)
    , appsink_(nullptr)
    , bus_(nullptr)
    , initialized_(false)
    , running_(false)
    , state_(ConnectionState::DISCONNECTED)
    , mpp_status_(HardwareAccelStatus::DISABLED)
    , rga_status_(HardwareAccelStatus::DISABLED)
    , dmabuf_enabled_(false)
    , should_stop_(false) {
    
    // Initialize GStreamer if not already done
    if (!GStreamerManager::getInstance().initialize()) {
        throw std::runtime_error("Failed to initialize GStreamer");
    }
    
    // Set default pipeline configuration
    pipeline_config_.use_mpp_decoder = config_.use_mpp_decoder;
    pipeline_config_.use_rga_scaler = config_.use_rga_scaler;
    pipeline_config_.use_dmabuf = config_.use_dmabuf_zerocopy;
    
    std::cout << "[GStreamerRTSPClient] Created for URL: " << config_.rtsp_url << std::endl;
}

GStreamerRTSPClient::~GStreamerRTSPClient() {
    cleanup();
    std::cout << "[GStreamerRTSPClient] Destroyed" << std::endl;
}

bool GStreamerRTSPClient::initialize() {
    std::lock_guard<std::mutex> lock(config_mutex_);
    
    if (initialized_) {
        return true;
    }
    
    std::cout << "[GStreamerRTSPClient] Initializing..." << std::endl;
    
    // Validate configuration
    if (!validateConfig()) {
        setLastError("Invalid configuration");
        return false;
    }
    
    // Check GStreamer version and capabilities
    if (!checkGStreamerVersion()) {
        setLastError("Incompatible GStreamer version");
        return false;
    }
    
    // Check hardware capabilities
    if (!checkHardwareCapabilities()) {
        std::cout << "[GStreamerRTSPClient] Hardware acceleration not available, using software fallback" << std::endl;
        fallbackToSoftware();
    }
    
    // Create pipeline
    if (!createPipeline()) {
        setLastError("Failed to create GStreamer pipeline");
        return false;
    }
    
    // Configure pipeline
    if (!configurePipeline()) {
        setLastError("Failed to configure GStreamer pipeline");
        destroyPipeline();
        return false;
    }
    
    // Link pipeline elements
    if (!linkPipelineElements()) {
        setLastError("Failed to link pipeline elements");
        destroyPipeline();
        return false;
    }
    
    // Setup callbacks
    setupCallbacks();
    
    // Apply RK3588 optimizations
    optimizeForRK3588();
    
    initialized_ = true;
    updateState(ConnectionState::DISCONNECTED);
    
    std::cout << "[GStreamerRTSPClient] Initialized successfully" << std::endl;
    return true;
}

bool GStreamerRTSPClient::start() {
    std::lock_guard<std::mutex> lock(config_mutex_);

    if (!initialized_) {
        setLastError("Client not initialized");
        return false;
    }

    if (running_) {
        return true;
    }

    std::cout << "[GStreamerRTSPClient] Starting pipeline..." << std::endl;

#ifdef HAVE_GSTREAMER
    // Set pipeline to PLAYING state
    GstStateChangeReturn ret = gst_element_set_state(GST_ELEMENT(pipeline_), GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        setLastError("Failed to start pipeline");
        return false;
    }

    // Start worker threads
    should_stop_ = false;
    bus_thread_ = std::make_unique<std::thread>(&GStreamerRTSPClient::busWorker, this);
    buffer_thread_ = std::make_unique<std::thread>(&GStreamerRTSPClient::bufferWorker, this);

    running_ = true;
    updateState(ConnectionState::CONNECTING);

    std::cout << "[GStreamerRTSPClient] Pipeline started" << std::endl;
    return true;
#else
    setLastError("GStreamer support not available");
    return false;
#endif
}

void GStreamerRTSPClient::stop() {
    std::lock_guard<std::mutex> lock(config_mutex_);

    if (!running_) {
        return;
    }

    std::cout << "[GStreamerRTSPClient] Stopping pipeline..." << std::endl;

    should_stop_ = true;
    running_ = false;
    updateState(ConnectionState::DISCONNECTED);

#ifdef HAVE_GSTREAMER
    // Stop pipeline
    if (pipeline_) {
        gst_element_set_state(GST_ELEMENT(pipeline_), GST_STATE_NULL);
    }
#endif

    // Stop worker threads
    if (bus_thread_ && bus_thread_->joinable()) {
        bus_thread_->join();
    }
    if (buffer_thread_ && buffer_thread_->joinable()) {
        buffer_thread_->join();
    }

    std::cout << "[GStreamerRTSPClient] Pipeline stopped" << std::endl;
}

void GStreamerRTSPClient::cleanup() {
    stop();
    destroyPipeline();
    initialized_ = false;
}

bool GStreamerRTSPClient::isInitialized() const {
    return initialized_;
}

bool GStreamerRTSPClient::isRunning() const {
    return running_;
}

bool GStreamerRTSPClient::connect() {
    if (!isInitialized()) {
        if (!initialize()) {
            return false;
        }
    }

    return start();
}

void GStreamerRTSPClient::disconnect() {
    stop();
}

bool GStreamerRTSPClient::isConnected() const {
    ConnectionState current_state = state_.load();
    return current_state == ConnectionState::CONNECTED || current_state == ConnectionState::STREAMING;
}

ConnectionState GStreamerRTSPClient::getState() const {
    return state_.load();
}

void GStreamerRTSPClient::updateConfig(const RTSPConnectionConfig& new_config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    config_ = new_config;

    // Update pipeline configuration
    pipeline_config_.use_mpp_decoder = config_.use_mpp_decoder;
    pipeline_config_.use_rga_scaler = config_.use_rga_scaler;
    pipeline_config_.use_dmabuf = config_.use_dmabuf_zerocopy;

    std::cout << "[GStreamerRTSPClient] Configuration updated" << std::endl;
}

void GStreamerRTSPClient::updatePipelineConfig(const GStreamerPipelineConfig& pipeline_config) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    pipeline_config_ = pipeline_config;
    std::cout << "[GStreamerRTSPClient] Pipeline configuration updated" << std::endl;
}

const RTSPConnectionConfig& GStreamerRTSPClient::getConfig() const {
    return config_;
}

const GStreamerPipelineConfig& GStreamerRTSPClient::getPipelineConfig() const {
    return pipeline_config_;
}

bool GStreamerRTSPClient::enableMPPDecoder(bool enable) {
    pipeline_config_.use_mpp_decoder = enable;

    if (enable && GStreamerManager::getInstance().isMPPDecoderAvailable()) {
        mpp_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
        std::cout << "[GStreamerRTSPClient] MPP decoder enabled" << std::endl;
        return true;
    } else {
        mpp_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
        std::cout << "[GStreamerRTSPClient] MPP decoder disabled or not available" << std::endl;
        return false;
    }
}

bool GStreamerRTSPClient::enableRGAScaler(bool enable) {
    pipeline_config_.use_rga_scaler = enable;

    if (enable && GStreamerManager::getInstance().isRGAConverterAvailable()) {
        rga_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
        std::cout << "[GStreamerRTSPClient] RGA scaler enabled" << std::endl;
        return true;
    } else {
        rga_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
        std::cout << "[GStreamerRTSPClient] RGA scaler disabled or not available" << std::endl;
        return false;
    }
}

bool GStreamerRTSPClient::enableDMABuf(bool enable) {
    pipeline_config_.use_dmabuf = enable;

    if (enable && GStreamerManager::getInstance().isDMABufSupported()) {
        dmabuf_enabled_ = true;
        std::cout << "[GStreamerRTSPClient] DMABUF enabled" << std::endl;
        return true;
    } else {
        dmabuf_enabled_ = false;
        std::cout << "[GStreamerRTSPClient] DMABUF disabled or not supported" << std::endl;
        return false;
    }
}

HardwareAccelStatus GStreamerRTSPClient::getMPPStatus() const {
    return mpp_status_;
}

HardwareAccelStatus GStreamerRTSPClient::getRGAStatus() const {
    return rga_status_;
}

bool GStreamerRTSPClient::isDMABufEnabled() const {
    return dmabuf_enabled_;
}

// Callback setters
void GStreamerRTSPClient::setBufferCallback(std::function<void(const std::vector<uint8_t>&, Timestamp)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    buffer_callback_ = callback;
}

void GStreamerRTSPClient::setNALUnitCallback(std::function<void(const NALUnit&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    nal_callback_ = callback;
}

void GStreamerRTSPClient::setErrorCallback(StreamErrorCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    error_callback_ = callback;
}

void GStreamerRTSPClient::setStateChangeCallback(std::function<void(ConnectionState)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    state_callback_ = callback;
}

// Statistics and monitoring
GStreamerStatistics GStreamerRTSPClient::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Create a copy of statistics (atomic values need to be loaded)
    GStreamerStatistics stats;
    stats.buffers_received = statistics_.buffers_received.load();
    stats.bytes_received = statistics_.bytes_received.load();
    stats.hardware_decode_count = statistics_.hardware_decode_count.load();
    stats.software_decode_count = statistics_.software_decode_count.load();
    stats.pipeline_errors = statistics_.pipeline_errors.load();
    stats.buffer_drops = statistics_.buffer_drops.load();
    stats.format_changes = statistics_.format_changes.load();
    stats.current_fps = statistics_.current_fps.load();
    stats.current_bitrate_kbps = statistics_.current_bitrate_kbps.load();

    return stats;
}

std::string GStreamerRTSPClient::getPipelineDescription() const {
#ifdef HAVE_GSTREAMER
    if (pipeline_) {
        gchar* desc = gst_element_get_name(GST_ELEMENT(pipeline_));
        std::string result(desc ? desc : "Unknown");
        g_free(desc);
        return result;
    }
#endif
    return "No pipeline";
}

size_t GStreamerRTSPClient::getMemoryUsage() const {
    // TODO: Implement memory usage calculation
    return 0;
}

// Quality control
void GStreamerRTSPClient::setLatency(uint32_t latency_ms) {
    pipeline_config_.latency_ms = latency_ms;

#ifdef HAVE_GSTREAMER
    if (rtspsrc_) {
        g_object_set(rtspsrc_, "latency", latency_ms, nullptr);
        std::cout << "[GStreamerRTSPClient] Latency set to " << latency_ms << "ms" << std::endl;
    }
#endif
}

uint32_t GStreamerRTSPClient::getLatency() const {
    return pipeline_config_.latency_ms;
}

void GStreamerRTSPClient::enableAdaptiveQuality(bool enable) {
    // TODO: Implement adaptive quality control
    std::cout << "[GStreamerRTSPClient] Adaptive quality " << (enable ? "enabled" : "disabled") << std::endl;
}

bool GStreamerRTSPClient::isAdaptiveQualityEnabled() const {
    // TODO: Return actual adaptive quality state
    return false;
}

// Thermal management
void GStreamerRTSPClient::handleThermalThrottling(int temperature) {
    if (temperature >= 80) {
        // Reduce hardware acceleration to lower heat
        enableMPPDecoder(false);
        enableRGAScaler(false);
        std::cout << "[GStreamerRTSPClient] Thermal throttling activated at " << temperature << "°C" << std::endl;
    } else if (temperature < 75) {
        // Restore hardware acceleration
        enableMPPDecoder(config_.use_mpp_decoder);
        enableRGAScaler(config_.use_rga_scaler);
        std::cout << "[GStreamerRTSPClient] Thermal throttling deactivated" << std::endl;
    }
}

void GStreamerRTSPClient::setPerformanceMode(bool high_performance) {
    if (high_performance) {
        pipeline_config_.latency_ms = 50;  // Lower latency
        pipeline_config_.max_size_buffers = 300;  // More buffers
    } else {
        pipeline_config_.latency_ms = 200;  // Higher latency for stability
        pipeline_config_.max_size_buffers = 100;  // Fewer buffers
    }

    std::cout << "[GStreamerRTSPClient] Performance mode: "
              << (high_performance ? "high" : "balanced") << std::endl;
}

// Debug and diagnostics
void GStreamerRTSPClient::dumpPipelineGraph(const std::string& filename) const {
#ifdef HAVE_GSTREAMER
    if (pipeline_) {
        GST_DEBUG_BIN_TO_DOT_FILE(GST_BIN(pipeline_), GST_DEBUG_GRAPH_SHOW_ALL, filename.c_str());
        std::cout << "[GStreamerRTSPClient] Pipeline graph dumped to " << filename << ".dot" << std::endl;
    }
#endif
}

std::string GStreamerRTSPClient::getLastError() const {
    std::lock_guard<std::mutex> lock(error_mutex_);
    return last_error_;
}

void GStreamerRTSPClient::enableDebugOutput(bool enable) {
    pipeline_config_.enable_debug = enable;

    if (enable) {
        GStreamerManager::getInstance().setDebugLevel("DEBUG");
    } else {
        GStreamerManager::getInstance().setDebugLevel("WARNING");
    }
}

// Private helper methods
void GStreamerRTSPClient::setLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(error_mutex_);
    last_error_ = error;
    std::cerr << "[GStreamerRTSPClient] Error: " << error << std::endl;
}

void GStreamerRTSPClient::updateState(ConnectionState new_state) {
    ConnectionState old_state = state_.exchange(new_state);

    if (old_state != new_state) {
        std::cout << "[GStreamerRTSPClient] State changed: "
                  << static_cast<int>(old_state) << " -> " << static_cast<int>(new_state) << std::endl;

        // Notify callback
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (state_callback_) {
            state_callback_(new_state);
        }
    }
}

bool GStreamerRTSPClient::validateConfig() const {
    return !config_.rtsp_url.empty() &&
           config_.timeout_ms > 0 &&
           config_.retry_count >= 0;
}

bool GStreamerRTSPClient::checkGStreamerVersion() const {
#ifdef HAVE_GSTREAMER
    guint major, minor, micro, nano;
    gst_version(&major, &minor, &micro, &nano);

    // Require GStreamer 1.18 or later
    if (major > 1 || (major == 1 && minor >= 18)) {
        return true;
    }

    std::cerr << "[GStreamerRTSPClient] GStreamer version " << major << "." << minor
              << " is too old, require 1.18+" << std::endl;
    return false;
#else
    return false;
#endif
}

bool GStreamerRTSPClient::checkHardwareCapabilities() const {
    bool mpp_available = GStreamerManager::getInstance().isMPPDecoderAvailable();
    bool rga_available = GStreamerManager::getInstance().isRGAConverterAvailable();
    bool dmabuf_available = GStreamerManager::getInstance().isDMABufSupported();

    std::cout << "[GStreamerRTSPClient] Hardware capabilities:" << std::endl;
    std::cout << "  MPP Decoder: " << (mpp_available ? "Available" : "Not available") << std::endl;
    std::cout << "  RGA Scaler: " << (rga_available ? "Available" : "Not available") << std::endl;
    std::cout << "  DMABUF: " << (dmabuf_available ? "Supported" : "Not supported") << std::endl;

    return mpp_available || rga_available;  // At least one hardware component should be available
}

bool GStreamerRTSPClient::checkCodecSupport(VideoCodec codec) const {
    // TODO: Implement codec support checking
    return codec == VideoCodec::H264 || codec == VideoCodec::H265;
}

void GStreamerRTSPClient::fallbackToSoftware() {
    pipeline_config_.use_mpp_decoder = false;
    pipeline_config_.use_rga_scaler = false;
    pipeline_config_.use_dmabuf = false;

    mpp_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
    rga_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
    dmabuf_enabled_ = false;

    std::cout << "[GStreamerRTSPClient] Falling back to software processing" << std::endl;
}

// Pipeline creation and management
bool GStreamerRTSPClient::createPipeline() {
#ifdef HAVE_GSTREAMER
    std::cout << "[GStreamerRTSPClient] Creating GStreamer pipeline..." << std::endl;

    // Create pipeline
    pipeline_ = GST_PIPELINE(gst_pipeline_new("rtsp-pipeline"));
    if (!pipeline_) {
        setLastError("Failed to create pipeline");
        return false;
    }

    // Create elements
    rtspsrc_ = createRTSPSource();
    if (!rtspsrc_) {
        setLastError("Failed to create RTSP source");
        return false;
    }

    // Create depayloader (will be determined dynamically)
    depayloader_ = nullptr;  // Created in pad-added callback

    // Create parser (will be determined dynamically)
    parser_ = nullptr;  // Created in pad-added callback

    // Create decoder
    decoder_ = createDecoder("h264");  // Default to H.264, will adapt
    if (!decoder_) {
        setLastError("Failed to create decoder");
        return false;
    }

    // Create converter
    converter_ = createConverter();
    if (!converter_) {
        setLastError("Failed to create converter");
        return false;
    }

    // Create app sink
    appsink_ = createAppSink();
    if (!appsink_) {
        setLastError("Failed to create app sink");
        return false;
    }

    // Add elements to pipeline
    gst_bin_add_many(GST_BIN(pipeline_),
                     rtspsrc_, decoder_, converter_, appsink_, nullptr);

    // Get bus for message handling
    bus_ = gst_element_get_bus(GST_ELEMENT(pipeline_));
    if (!bus_) {
        setLastError("Failed to get pipeline bus");
        return false;
    }

    std::cout << "[GStreamerRTSPClient] Pipeline created successfully" << std::endl;
    return true;
#else
    setLastError("GStreamer support not available");
    return false;
#endif
}

void GStreamerRTSPClient::destroyPipeline() {
#ifdef HAVE_GSTREAMER
    if (pipeline_) {
        gst_element_set_state(GST_ELEMENT(pipeline_), GST_STATE_NULL);
        gst_object_unref(pipeline_);
        pipeline_ = nullptr;
        std::cout << "[GStreamerRTSPClient] Pipeline destroyed" << std::endl;
    }

    if (bus_) {
        gst_object_unref(bus_);
        bus_ = nullptr;
    }

    // Reset element pointers (they're owned by the pipeline)
    rtspsrc_ = nullptr;
    depayloader_ = nullptr;
    parser_ = nullptr;
    decoder_ = nullptr;
    converter_ = nullptr;
    appsink_ = nullptr;
#endif
}

bool GStreamerRTSPClient::configurePipeline() {
#ifdef HAVE_GSTREAMER
    std::cout << "[GStreamerRTSPClient] Configuring pipeline..." << std::endl;

    // Configure RTSP source
    if (rtspsrc_) {
        g_object_set(rtspsrc_,
                     "location", config_.rtsp_url.c_str(),
                     "latency", pipeline_config_.latency_ms,
                     "retry", config_.retry_count,
                     "timeout", static_cast<guint64>(config_.timeout_ms * 1000000),  // Convert to nanoseconds
                     "do-retransmission", pipeline_config_.do_retransmission,
                     nullptr);

        // Set transport protocol
        if (config_.transport == TransportProtocol::TCP) {
            g_object_set(rtspsrc_, "protocols", 0x04, nullptr);  // TCP only
        } else if (config_.transport == TransportProtocol::UDP) {
            g_object_set(rtspsrc_, "protocols", 0x01, nullptr);  // UDP only
        }
        // For AUTO, let GStreamer decide (don't set protocols property)

        // Set authentication if provided
        if (!config_.username.empty()) {
            g_object_set(rtspsrc_,
                         "user-id", config_.username.c_str(),
                         "user-pw", config_.password.c_str(),
                         nullptr);
        }
    }

    // Configure app sink
    if (appsink_) {
        g_object_set(appsink_,
                     "max-buffers", pipeline_config_.max_size_buffers,
                     "drop", pipeline_config_.drop_on_latency,
                     "sync", FALSE,  // Don't sync to clock for lower latency
                     "emit-signals", TRUE,
                     nullptr);

        // Set caps for app sink
        GstCaps* caps = gst_caps_new_simple("video/x-raw",
                                            "format", G_TYPE_STRING, "NV12",
                                            nullptr);
        g_object_set(appsink_, "caps", caps, nullptr);
        gst_caps_unref(caps);
    }

    std::cout << "[GStreamerRTSPClient] Pipeline configured" << std::endl;
    return true;
#else
    return false;
#endif
}

bool GStreamerRTSPClient::linkPipelineElements() {
#ifdef HAVE_GSTREAMER
    std::cout << "[GStreamerRTSPClient] Linking pipeline elements..." << std::endl;

    // Connect pad-added signal for dynamic linking
    g_signal_connect(rtspsrc_, "pad-added", G_CALLBACK(onPadAdded), this);

    // Link static elements (decoder -> converter -> appsink)
    if (!gst_element_link_many(decoder_, converter_, appsink_, nullptr)) {
        setLastError("Failed to link decoder -> converter -> appsink");
        return false;
    }

    std::cout << "[GStreamerRTSPClient] Pipeline elements linked" << std::endl;
    return true;
#else
    return false;
#endif
}

void GStreamerRTSPClient::setupCallbacks() {
#ifdef HAVE_GSTREAMER
    if (appsink_) {
        // Set up new-sample callback
        g_signal_connect(appsink_, "new-sample", G_CALLBACK(onNewSample), this);
    }
#endif
}

// Static callback functions for GStreamer
#ifdef HAVE_GSTREAMER
void GStreamerRTSPClient::onPadAdded(GstElement* element, GstPad* pad, gpointer user_data) {
    GStreamerRTSPClient* client = static_cast<GStreamerRTSPClient*>(user_data);
    client->handlePadAdded(element, pad);
}

GstFlowReturn GStreamerRTSPClient::onNewSample(GstAppSink* appsink, gpointer user_data) {
    GStreamerRTSPClient* client = static_cast<GStreamerRTSPClient*>(user_data);
    return client->handleNewSample(appsink);
}
#endif

// Element creation methods
GstElement* GStreamerRTSPClient::createRTSPSource() {
#ifdef HAVE_GSTREAMER
    GstElement* element = gst_element_factory_make("rtspsrc", "rtsp-source");
    if (!element) {
        setLastError("Failed to create rtspsrc element");
        return nullptr;
    }

    std::cout << "[GStreamerRTSPClient] Created RTSP source" << std::endl;
    return element;
#else
    return nullptr;
#endif
}

GstElement* GStreamerRTSPClient::createDepayloader(const std::string& codec) {
#ifdef HAVE_GSTREAMER
    std::string element_name;

    if (codec == "h264") {
        element_name = "rtph264depay";
    } else if (codec == "h265") {
        element_name = "rtph265depay";
    } else {
        setLastError("Unsupported codec for depayloader: " + codec);
        return nullptr;
    }

    GstElement* element = gst_element_factory_make(element_name.c_str(), "depayloader");
    if (!element) {
        setLastError("Failed to create " + element_name + " element");
        return nullptr;
    }

    std::cout << "[GStreamerRTSPClient] Created depayloader: " << element_name << std::endl;
    return element;
#else
    return nullptr;
#endif
}

GstElement* GStreamerRTSPClient::createParser(const std::string& codec) {
#ifdef HAVE_GSTREAMER
    std::string element_name;

    if (codec == "h264") {
        element_name = "h264parse";
    } else if (codec == "h265") {
        element_name = "h265parse";
    } else {
        setLastError("Unsupported codec for parser: " + codec);
        return nullptr;
    }

    GstElement* element = gst_element_factory_make(element_name.c_str(), "parser");
    if (!element) {
        setLastError("Failed to create " + element_name + " element");
        return nullptr;
    }

    std::cout << "[GStreamerRTSPClient] Created parser: " << element_name << std::endl;
    return element;
#else
    return nullptr;
#endif
}

GstElement* GStreamerRTSPClient::createDecoder(const std::string& codec) {
#ifdef HAVE_GSTREAMER
    GstElement* element = nullptr;

    // Try hardware decoder first if enabled
    if (pipeline_config_.use_mpp_decoder && GStreamerManager::getInstance().isMPPDecoderAvailable()) {
        element = gst_element_factory_make(pipeline_config_.mpp_decoder_name.c_str(), "decoder");
        if (element) {
            mpp_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
            std::cout << "[GStreamerRTSPClient] Created MPP decoder: " << pipeline_config_.mpp_decoder_name << std::endl;
        }
    }

    // Fallback to software decoder
    if (!element) {
        std::string software_decoder;
        if (codec == "h264") {
            software_decoder = "avdec_h264";
        } else if (codec == "h265") {
            software_decoder = "avdec_h265";
        } else {
            setLastError("Unsupported codec: " + codec);
            return nullptr;
        }

        element = gst_element_factory_make(software_decoder.c_str(), "decoder");
        if (element) {
            mpp_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
            std::cout << "[GStreamerRTSPClient] Created software decoder: " << software_decoder << std::endl;
        }
    }

    if (!element) {
        setLastError("Failed to create decoder for codec: " + codec);
        return nullptr;
    }

    return element;
#else
    return nullptr;
#endif
}

GstElement* GStreamerRTSPClient::createConverter() {
#ifdef HAVE_GSTREAMER
    GstElement* element = nullptr;

    // Try hardware converter first if enabled
    if (pipeline_config_.use_rga_scaler && GStreamerManager::getInstance().isRGAConverterAvailable()) {
        element = gst_element_factory_make(pipeline_config_.rga_scaler_name.c_str(), "converter");
        if (element) {
            rga_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
            std::cout << "[GStreamerRTSPClient] Created RGA converter: " << pipeline_config_.rga_scaler_name << std::endl;
        }
    }

    // Fallback to software converter
    if (!element) {
        element = gst_element_factory_make("videoconvert", "converter");
        if (element) {
            rga_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
            std::cout << "[GStreamerRTSPClient] Created software converter: videoconvert" << std::endl;
        }
    }

    if (!element) {
        setLastError("Failed to create converter");
        return nullptr;
    }

    return element;
#else
    return nullptr;
#endif
}

GstElement* GStreamerRTSPClient::createAppSink() {
#ifdef HAVE_GSTREAMER
    GstElement* element = gst_element_factory_make("appsink", "app-sink");
    if (!element) {
        setLastError("Failed to create appsink element");
        return nullptr;
    }

    std::cout << "[GStreamerRTSPClient] Created app sink" << std::endl;
    return element;
#else
    return nullptr;
#endif
}

// Hardware acceleration setup
bool GStreamerRTSPClient::setupMPPDecoder() {
    // TODO: Implement MPP decoder specific setup
    return pipeline_config_.use_mpp_decoder && GStreamerManager::getInstance().isMPPDecoderAvailable();
}

bool GStreamerRTSPClient::setupRGAConverter() {
    // TODO: Implement RGA converter specific setup
    return pipeline_config_.use_rga_scaler && GStreamerManager::getInstance().isRGAConverterAvailable();
}

bool GStreamerRTSPClient::setupDMABuf() {
    // TODO: Implement DMABUF specific setup
    return pipeline_config_.use_dmabuf && GStreamerManager::getInstance().isDMABufSupported();
}

// Threading workers
void GStreamerRTSPClient::busWorker() {
    setCPUAffinity();

    std::cout << "[GStreamerRTSPClient] Bus worker started" << std::endl;

#ifdef HAVE_GSTREAMER
    while (!should_stop_) {
        if (!bus_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            continue;
        }

        GstMessage* message = gst_bus_timed_pop(bus_, 100 * GST_MSECOND);  // 100ms timeout
        if (message) {
            handleBusMessage(message);
            gst_message_unref(message);
        }
    }
#endif

    std::cout << "[GStreamerRTSPClient] Bus worker stopped" << std::endl;
}

void GStreamerRTSPClient::bufferWorker() {
    setCPUAffinity();

    std::cout << "[GStreamerRTSPClient] Buffer worker started" << std::endl;

    while (!should_stop_) {
        // Update statistics
        updateStatistics();

        // Update performance statistics
        updatePerformanceStatistics();

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    std::cout << "[GStreamerRTSPClient] Buffer worker stopped" << std::endl;
}

// Message handling
void GStreamerRTSPClient::handleBusMessage(GstMessage* message) {
#ifdef HAVE_GSTREAMER
    if (!message) return;

    switch (GST_MESSAGE_TYPE(message)) {
        case GST_MESSAGE_ERROR:
            handleErrorMessage(message);
            break;
        case GST_MESSAGE_WARNING:
            handleWarningMessage(message);
            break;
        case GST_MESSAGE_INFO:
            handleInfoMessage(message);
            break;
        case GST_MESSAGE_STATE_CHANGED:
            handleStateChangeMessage(message);
            break;
        case GST_MESSAGE_EOS:
            handleEOSMessage(message);
            break;
        default:
            // Ignore other message types
            break;
    }
#endif
}

void GStreamerRTSPClient::handleErrorMessage(GstMessage* message) {
#ifdef HAVE_GSTREAMER
    GError* error = nullptr;
    gchar* debug_info = nullptr;

    gst_message_parse_error(message, &error, &debug_info);

    std::string error_msg = error ? error->message : "Unknown error";
    std::string debug_msg = debug_info ? debug_info : "No debug info";

    std::cerr << "[GStreamerRTSPClient] Pipeline error: " << error_msg << std::endl;
    std::cerr << "[GStreamerRTSPClient] Debug info: " << debug_msg << std::endl;

    handlePipelineError(error_msg);

    // Update statistics
    statistics_.pipeline_errors++;

    // Update state
    updateState(ConnectionState::ERROR);

    // Notify error callback
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (error_callback_) {
        error_callback_(config_.stream_id, ErrorCategory::HARDWARE_ERROR, error_msg);
    }

    g_error_free(error);
    g_free(debug_info);
#endif
}

void GStreamerRTSPClient::handleWarningMessage(GstMessage* message) {
#ifdef HAVE_GSTREAMER
    GError* warning = nullptr;
    gchar* debug_info = nullptr;

    gst_message_parse_warning(message, &warning, &debug_info);

    std::string warning_msg = warning ? warning->message : "Unknown warning";
    std::string debug_msg = debug_info ? debug_info : "No debug info";

    std::cout << "[GStreamerRTSPClient] Pipeline warning: " << warning_msg << std::endl;
    if (pipeline_config_.enable_debug) {
        std::cout << "[GStreamerRTSPClient] Debug info: " << debug_msg << std::endl;
    }

    g_error_free(warning);
    g_free(debug_info);
#endif
}

void GStreamerRTSPClient::handleInfoMessage(GstMessage* message) {
#ifdef HAVE_GSTREAMER
    if (pipeline_config_.enable_debug) {
        GError* info = nullptr;
        gchar* debug_info = nullptr;

        gst_message_parse_info(message, &info, &debug_info);

        std::string info_msg = info ? info->message : "Unknown info";
        std::string debug_msg = debug_info ? debug_info : "No debug info";

        std::cout << "[GStreamerRTSPClient] Pipeline info: " << info_msg << std::endl;
        std::cout << "[GStreamerRTSPClient] Debug info: " << debug_msg << std::endl;

        g_error_free(info);
        g_free(debug_info);
    }
#endif
}

void GStreamerRTSPClient::handleStateChangeMessage(GstMessage* message) {
#ifdef HAVE_GSTREAMER
    GstState old_state, new_state, pending_state;
    gst_message_parse_state_changed(message, &old_state, &new_state, &pending_state);

    // Only handle pipeline state changes
    if (GST_MESSAGE_SRC(message) == GST_OBJECT(pipeline_)) {
        std::cout << "[GStreamerRTSPClient] Pipeline state changed: "
                  << gstStateToString(old_state) << " -> " << gstStateToString(new_state) << std::endl;

        // Update connection state based on GStreamer state
        switch (new_state) {
            case GST_STATE_PLAYING:
                updateState(ConnectionState::STREAMING);
                break;
            case GST_STATE_PAUSED:
                updateState(ConnectionState::CONNECTED);
                break;
            case GST_STATE_NULL:
                updateState(ConnectionState::DISCONNECTED);
                break;
            default:
                // Keep current state for other states
                break;
        }
    }
#endif
}

void GStreamerRTSPClient::handleEOSMessage(GstMessage* message) {
    std::cout << "[GStreamerRTSPClient] End of stream received" << std::endl;
    updateState(ConnectionState::DISCONNECTED);

    // Notify error callback about stream end
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (error_callback_) {
        error_callback_(config_.stream_id, ErrorCategory::NETWORK_ERROR, "Stream ended");
    }
}

// Buffer processing and pad handling
#ifdef HAVE_GSTREAMER
void GStreamerRTSPClient::handlePadAdded(GstElement* element, GstPad* pad) {
    gchar* pad_name = gst_pad_get_name(pad);
    std::cout << "[GStreamerRTSPClient] Pad added: " << (pad_name ? pad_name : "unknown") << std::endl;

    // Get pad capabilities to determine codec
    GstCaps* caps = gst_pad_get_current_caps(pad);
    if (!caps) {
        caps = gst_pad_query_caps(pad, nullptr);
    }

    if (caps) {
        VideoCodec codec = detectCodecFromCaps(caps);
        std::string codec_str = (codec == VideoCodec::H264) ? "h264" :
                               (codec == VideoCodec::H265) ? "h265" : "unknown";

        std::cout << "[GStreamerRTSPClient] Detected codec: " << codec_str << std::endl;

        // Create and link depayloader and parser dynamically
        if (codec != VideoCodec::UNKNOWN) {
            depayloader_ = createDepayloader(codec_str);
            parser_ = createParser(codec_str);

            if (depayloader_ && parser_) {
                // Add elements to pipeline
                gst_bin_add_many(GST_BIN(pipeline_), depayloader_, parser_, nullptr);

                // Link: pad -> depayloader -> parser -> decoder
                GstPad* depay_sink = gst_element_get_static_pad(depayloader_, "sink");
                if (depay_sink) {
                    if (gst_pad_link(pad, depay_sink) == GST_PAD_LINK_OK) {
                        std::cout << "[GStreamerRTSPClient] Linked RTSP source to depayloader" << std::endl;
                    }
                    gst_object_unref(depay_sink);
                }

                // Link depayloader -> parser -> decoder
                if (gst_element_link_many(depayloader_, parser_, decoder_, nullptr)) {
                    std::cout << "[GStreamerRTSPClient] Linked depayloader -> parser -> decoder" << std::endl;

                    // Sync element states
                    gst_element_sync_state_with_parent(depayloader_);
                    gst_element_sync_state_with_parent(parser_);
                } else {
                    std::cerr << "[GStreamerRTSPClient] Failed to link depayloader -> parser -> decoder" << std::endl;
                }
            }
        }

        gst_caps_unref(caps);
    }

    g_free(pad_name);
}
#else
void GStreamerRTSPClient::handlePadAdded(GstElement* element, GstPad* pad) {
    // No-op when GStreamer is not available
    (void)element;
    (void)pad;
}
#endif

#ifdef HAVE_GSTREAMER
GstFlowReturn GStreamerRTSPClient::handleNewSample(GstAppSink* appsink) {
    GstSample* sample = gst_app_sink_pull_sample(appsink);
    if (!sample) {
        return GST_FLOW_ERROR;
    }

    processBuffer(sample);

    gst_sample_unref(sample);
    return GST_FLOW_OK;
}
#else
GstFlowReturn GStreamerRTSPClient::handleNewSample(GstAppSink* appsink) {
    (void)appsink;
    return -1;  // GST_FLOW_ERROR equivalent
}
#endif

void GStreamerRTSPClient::processBuffer(GstSample* sample) {
#ifdef HAVE_GSTREAMER
    if (!sample) return;

    GstBuffer* buffer = gst_sample_get_buffer(sample);
    if (!buffer) return;

    // Update buffer statistics
    gsize buffer_size = gst_buffer_get_size(buffer);
    updateBufferStatistics(buffer_size);

    // Extract buffer data
    GstMapInfo map_info;
    if (gst_buffer_map(buffer, &map_info, GST_MAP_READ)) {
        std::vector<uint8_t> buffer_data(map_info.data, map_info.data + map_info.size);

        // Get timestamp
        Timestamp timestamp = std::chrono::steady_clock::now();

        // Extract NAL units from buffer
        extractNALUnits(buffer_data);

        // Notify buffer callback
        std::lock_guard<std::mutex> lock(callback_mutex_);
        if (buffer_callback_) {
            buffer_callback_(buffer_data, timestamp);
        }

        gst_buffer_unmap(buffer, &map_info);
    }
#endif
}

void GStreamerRTSPClient::extractNALUnits(const std::vector<uint8_t>& buffer) {
    // TODO: Implement NAL unit extraction
    // This should parse the buffer and extract individual NAL units
    // For now, create a dummy NAL unit

    NALUnit nal_unit;
    nal_unit.type = NALUnitType::H264_NON_IDR;  // Default type
    nal_unit.data = buffer;
    nal_unit.timestamp = std::chrono::steady_clock::now();
    nal_unit.stream_id = config_.stream_id;
    nal_unit.is_keyframe = false;  // TODO: Detect keyframes

    // Notify NAL unit callback
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (nal_callback_) {
        nal_callback_(nal_unit);
    }
}

// Error handling
void GStreamerRTSPClient::handlePipelineError(const std::string& error) {
    setLastError("Pipeline error: " + error);

    // Try to recover from certain errors
    if (error.find("timeout") != std::string::npos ||
        error.find("connection") != std::string::npos) {
        // Network-related error, might be recoverable
        std::cout << "[GStreamerRTSPClient] Network error detected, attempting recovery..." << std::endl;
        // TODO: Implement recovery logic
    } else if (error.find("decoder") != std::string::npos ||
               error.find("mpp") != std::string::npos) {
        // Hardware decoder error, fallback to software
        std::cout << "[GStreamerRTSPClient] Hardware decoder error, falling back to software..." << std::endl;
        handleHardwareError("decoder", error);
    }
}

void GStreamerRTSPClient::handleHardwareError(const std::string& component, const std::string& error) {
    std::cout << "[GStreamerRTSPClient] Hardware error in " << component << ": " << error << std::endl;

    if (component == "decoder") {
        mpp_status_ = HardwareAccelStatus::HARDWARE_ERROR;
        // TODO: Recreate pipeline with software decoder
    } else if (component == "converter") {
        rga_status_ = HardwareAccelStatus::HARDWARE_ERROR;
        // TODO: Recreate pipeline with software converter
    }
}

// Statistics helpers
void GStreamerRTSPClient::updateStatistics() {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Update basic statistics
    // TODO: Implement actual statistics collection from GStreamer

    // Update hardware acceleration statistics
    if (mpp_status_ == HardwareAccelStatus::HARDWARE_ACTIVE) {
        statistics_.hardware_decode_count++;
    } else {
        statistics_.software_decode_count++;
    }
}

void GStreamerRTSPClient::updateBufferStatistics(size_t buffer_size) {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    statistics_.buffers_received++;
    statistics_.bytes_received += buffer_size;

    // Calculate current FPS (simplified)
    static auto last_time = std::chrono::steady_clock::now();
    auto current_time = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - last_time);

    if (duration.count() >= 1000) {  // Update every second
        statistics_.current_fps = static_cast<float>(statistics_.buffers_received.load());
        statistics_.buffers_received = 0;  // Reset counter
        last_time = current_time;
    }
}

void GStreamerRTSPClient::updatePerformanceStatistics() {
    // TODO: Implement performance statistics collection
    // - CPU usage
    // - Memory usage
    // - Bitrate calculation
    // - Latency measurement
}

// RK3588 optimizations
void GStreamerRTSPClient::optimizeForRK3588() {
    setCPUAffinity();
    configureMemoryPools();

    std::cout << "[GStreamerRTSPClient] Applied RK3588 optimizations" << std::endl;
}

void GStreamerRTSPClient::setCPUAffinity() {
    // Set thread affinity to cores 2-3 (RK3588 little cores for RTSP)
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(2, &cpuset);  // Core 2
    CPU_SET(3, &cpuset);  // Core 3

    if (pthread_setaffinity_np(pthread_self(), sizeof(cpu_set_t), &cpuset) != 0) {
        std::cerr << "[GStreamerRTSPClient] Failed to set CPU affinity" << std::endl;
    } else {
        std::cout << "[GStreamerRTSPClient] Set CPU affinity to cores 2-3" << std::endl;
    }
}

void GStreamerRTSPClient::configureMemoryPools() {
    // TODO: Implement memory pool configuration for RK3588
    // - Configure buffer pools for different frame sizes
    // - Set up DMABUF memory allocators
    // - Optimize memory allocation patterns
    std::cout << "[GStreamerRTSPClient] Configured memory pools for RK3588" << std::endl;
}

// Utility methods
std::string GStreamerRTSPClient::gstStateToString(int state) const {
#ifdef HAVE_GSTREAMER
    switch (state) {
        case GST_STATE_VOID_PENDING: return "VOID_PENDING";
        case GST_STATE_NULL: return "NULL";
        case GST_STATE_READY: return "READY";
        case GST_STATE_PAUSED: return "PAUSED";
        case GST_STATE_PLAYING: return "PLAYING";
        default: return "UNKNOWN";
    }
#else
    return "UNKNOWN";
#endif
}

VideoCodec GStreamerRTSPClient::detectCodecFromCaps(GstCaps* caps) const {
#ifdef HAVE_GSTREAMER
    if (!caps) return VideoCodec::UNKNOWN;

    for (guint i = 0; i < gst_caps_get_size(caps); i++) {
        GstStructure* structure = gst_caps_get_structure(caps, i);
        const gchar* name = gst_structure_get_name(structure);

        if (name) {
            std::string structure_name(name);

            if (structure_name.find("h264") != std::string::npos ||
                structure_name.find("H264") != std::string::npos) {
                return VideoCodec::H264;
            } else if (structure_name.find("h265") != std::string::npos ||
                       structure_name.find("H265") != std::string::npos ||
                       structure_name.find("hevc") != std::string::npos ||
                       structure_name.find("HEVC") != std::string::npos) {
                return VideoCodec::H265;
            }
        }
    }
#endif
    return VideoCodec::UNKNOWN;
}

void GStreamerRTSPClient::logPipelineInfo() const {
#ifdef HAVE_GSTREAMER
    if (pipeline_) {
        std::cout << "[GStreamerRTSPClient] Pipeline information:" << std::endl;
        std::cout << "  State: " << gstStateToString(GST_STATE(pipeline_)) << std::endl;
        std::cout << "  MPP Status: " << static_cast<int>(mpp_status_.load()) << std::endl;
        std::cout << "  RGA Status: " << static_cast<int>(rga_status_.load()) << std::endl;
        std::cout << "  DMABUF Enabled: " << (dmabuf_enabled_ ? "Yes" : "No") << std::endl;
    }
#endif
}

// Connection state management helpers
void GStreamerRTSPClient::handleConnectionLost() {
    std::cout << "[GStreamerRTSPClient] Connection lost, attempting recovery..." << std::endl;
    updateState(ConnectionState::RECONNECTING);

    // TODO: Implement connection recovery logic
    // - Stop current pipeline
    // - Wait for retry delay
    // - Restart pipeline
    // - Update retry statistics
}

void GStreamerRTSPClient::handleConnectionRestored() {
    std::cout << "[GStreamerRTSPClient] Connection restored" << std::endl;
    updateState(ConnectionState::STREAMING);

    // Reset error counters
    statistics_.pipeline_errors = 0;
}

} // namespace rtsp
} // namespace aibox
