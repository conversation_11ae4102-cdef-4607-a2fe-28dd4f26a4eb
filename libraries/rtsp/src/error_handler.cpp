#include "rtsp/error_handler.hpp"
#include "rtsp/logger.hpp"
#include <iostream>
#include <sstream>
#include <fstream>
#include <algorithm>
#include <thread>
#include <random>
#include <iomanip>

namespace aibox {
namespace rtsp {

/**
 * @brief Internal implementation of ErrorHandler using PIMPL pattern
 */
class ErrorHandler::Impl {
public:
    explicit Impl(std::shared_ptr<Logger> logger, const RecoveryConfig& config)
        : logger_(logger), config_(config), statistics_() {
        
        // Initialize random number generator for jitter
        std::random_device rd;
        rng_.seed(rd());
        
        // Start monitoring thread if enabled
        if (config_.enable_resource_monitoring) {
            startMonitoring();
        }
    }
    
    ~Impl() {
        stopMonitoring();
    }
    
    RecoveryAction handleError(const ErrorInfo& error_info) {
        // Update statistics
        updateStatistics(error_info);
        
        // Log the error
        if (logger_) {
            logger_->logError(error_info);
        } else {
            logToConsole(error_info);
        }
        
        // Notify error callback
        if (error_callback_) {
            error_callback_(error_info);
        }
        
        // Determine recovery action
        RecoveryAction action = determineRecoveryAction(error_info);
        
        // Execute recovery if enabled
        if (config_.enable_automatic_recovery && action != RecoveryAction::NONE) {
            executeRecovery(error_info, action);
        }
        
        return action;
    }
    
    void reportRecoverySuccess(const std::string& stream_id,
                              RecoveryAction action,
                              const std::string& details) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        statistics_.successful_recoveries++;

        if (logger_) {
            std::unordered_map<std::string, std::string> context;
            context["recovery_action"] = recoveryActionToString(action);
            context["details"] = details;

            logger_->log(LogLevel::INFO, "Recovery successful", "ErrorHandler", stream_id, context);
        }
    }
    
    void reportRecoveryFailure(const std::string& stream_id,
                              RecoveryAction action,
                              const std::string& details) {
        if (logger_) {
            std::unordered_map<std::string, std::string> context;
            context["recovery_action"] = recoveryActionToString(action);
            context["details"] = details;

            logger_->log(LogLevel::ERROR, "Recovery failed", "ErrorHandler", stream_id, context);
        }
    }
    
    void updateConfig(const RecoveryConfig& config) {
        std::lock_guard<std::mutex> lock(config_mutex_);
        config_ = config;
    }
    
    RecoveryConfig getConfig() const {
        std::lock_guard<std::mutex> lock(config_mutex_);
        return config_;
    }
    
    void setLogger(std::shared_ptr<Logger> logger) {
        std::lock_guard<std::mutex> lock(logger_mutex_);
        logger_ = logger;
    }
    
    void setErrorCallback(ErrorCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        error_callback_ = callback;
    }
    
    void setRecoveryCallback(RecoveryCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        recovery_callback_ = callback;
    }
    
    void setMetricsCallback(MetricsCallback callback) {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        metrics_callback_ = callback;
    }
    
    ErrorStatistics getStatistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return statistics_;
    }

    void resetStatistics() {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        statistics_ = ErrorStatistics{};
    }
    
    double getErrorRate() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(
            now - statistics_.start_time).count();

        if (duration == 0) return 0.0;
        return static_cast<double>(statistics_.total_errors) / static_cast<double>(duration);
    }

    double getRecoverySuccessRate() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        uint64_t attempts = statistics_.recovery_attempts;
        if (attempts == 0) return 1.0;

        return static_cast<double>(statistics_.successful_recoveries) / static_cast<double>(attempts);
    }
    
    bool isSystemHealthy() const {
        // Check error rate
        double error_rate = getErrorRate();
        if (error_rate > 10.0) { // More than 10 errors per second
            return false;
        }
        
        // Check recovery success rate
        double recovery_rate = getRecoverySuccessRate();
        if (recovery_rate < 0.5) { // Less than 50% recovery success
            return false;
        }
        
        // Check thermal status
        if (getCurrentTemperature() > config_.critical_thermal_threshold_celsius) {
            return false;
        }
        
        return true;
    }
    
    std::string getThermalStatus() const {
        int temp = getCurrentTemperature();
        
        if (temp < config_.thermal_threshold_celsius) {
            return "Normal (" + std::to_string(temp) + "°C)";
        } else if (temp < config_.critical_thermal_threshold_celsius) {
            return "Warning (" + std::to_string(temp) + "°C)";
        } else {
            return "Critical (" + std::to_string(temp) + "°C)";
        }
    }
    
    std::string getMemoryPressureStatus() const {
        double pressure = getCurrentMemoryPressure();
        
        if (pressure < 0.7) {
            return "Normal (" + std::to_string(static_cast<int>(pressure * 100)) + "%)";
        } else if (pressure < config_.memory_pressure_threshold) {
            return "Elevated (" + std::to_string(static_cast<int>(pressure * 100)) + "%)";
        } else {
            return "High (" + std::to_string(static_cast<int>(pressure * 100)) + "%)";
        }
    }

private:
    // Configuration and state
    std::shared_ptr<Logger> logger_;
    RecoveryConfig config_;
    ErrorStatistics statistics_;
    
    // Thread safety
    mutable std::mutex config_mutex_;
    mutable std::mutex logger_mutex_;
    mutable std::mutex callback_mutex_;
    mutable std::mutex stats_mutex_;
    
    // Callbacks
    ErrorCallback error_callback_;
    RecoveryCallback recovery_callback_;
    MetricsCallback metrics_callback_;
    
    // Monitoring
    std::atomic<bool> monitoring_active_{false};
    std::thread monitoring_thread_;
    
    // Random number generator for jitter
    std::mt19937 rng_;
    
    void updateStatistics(const ErrorInfo& error_info) {
        statistics_.total_errors++;
        statistics_.last_error_time = error_info.timestamp;
        
        // Update category-specific counters
        switch (error_info.category) {
            case ErrorCategory::NETWORK_ERROR:
                statistics_.network_errors++;
                break;
            case ErrorCategory::PROTOCOL_ERROR:
                statistics_.protocol_errors++;
                break;
            case ErrorCategory::CODEC_ERROR:
                statistics_.codec_errors++;
                break;
            case ErrorCategory::HARDWARE_ERROR:
                statistics_.hardware_errors++;
                break;
            case ErrorCategory::RESOURCE_ERROR:
                statistics_.resource_errors++;
                break;
            case ErrorCategory::CONFIGURATION_ERROR:
                statistics_.configuration_errors++;
                break;
            case ErrorCategory::THERMAL_ERROR:
                statistics_.thermal_errors++;
                break;
        }
        
        // Update recovery counters
        if (error_info.is_recoverable) {
            statistics_.recoverable_errors++;
        } else {
            statistics_.fatal_errors++;
        }
    }
    
    void logToConsole(const ErrorInfo& error_info) {
        std::cout << "[" << getCurrentTimestamp() << "] "
                  << "[" << severityToString(error_info.severity) << "] "
                  << "[" << categoryToString(error_info.category) << "] "
                  << error_info.component;
        
        if (!error_info.stream_id.empty()) {
            std::cout << "[" << error_info.stream_id << "]";
        }
        
        std::cout << ": " << error_info.message << std::endl;
    }
    
    RecoveryAction determineRecoveryAction(const ErrorInfo& error_info) {
        // Use suggested action if available
        if (error_info.suggested_action != RecoveryAction::NONE) {
            return error_info.suggested_action;
        }
        
        // Determine action based on category and severity
        switch (error_info.category) {
            case ErrorCategory::NETWORK_ERROR:
                if (error_info.severity >= ErrorSeverity::ERROR) {
                    return RecoveryAction::RESET_CONNECTION;
                }
                return RecoveryAction::RETRY;
                
            case ErrorCategory::PROTOCOL_ERROR:
                return RecoveryAction::RESTART_STREAM;
                
            case ErrorCategory::CODEC_ERROR:
                return RecoveryAction::RESTART_STREAM;
                
            case ErrorCategory::HARDWARE_ERROR:
                return RecoveryAction::FALLBACK_SOFTWARE;
                
            case ErrorCategory::RESOURCE_ERROR:
                return RecoveryAction::REDUCE_QUALITY;
                
            case ErrorCategory::THERMAL_ERROR:
                return RecoveryAction::THERMAL_THROTTLE;
                
            case ErrorCategory::CONFIGURATION_ERROR:
                return RecoveryAction::NONE; // Requires manual intervention
        }
        
        return RecoveryAction::NONE;
    }
    
    void executeRecovery(const ErrorInfo& error_info, RecoveryAction action) {
        statistics_.recovery_attempts++;
        
        // Use custom recovery callback if available
        if (recovery_callback_) {
            std::lock_guard<std::mutex> lock(callback_mutex_);
            if (recovery_callback_(error_info, action)) {
                statistics_.successful_recoveries++;
                return;
            }
        }
        
        // Default recovery implementation
        switch (action) {
            case RecoveryAction::RETRY:
                executeRetryRecovery(error_info);
                break;
            case RecoveryAction::RESET_CONNECTION:
                executeResetConnectionRecovery(error_info);
                break;
            case RecoveryAction::RESTART_STREAM:
                executeRestartStreamRecovery(error_info);
                break;
            case RecoveryAction::FALLBACK_SOFTWARE:
                executeFallbackSoftwareRecovery(error_info);
                break;
            case RecoveryAction::REDUCE_QUALITY:
                executeReduceQualityRecovery(error_info);
                break;
            case RecoveryAction::THERMAL_THROTTLE:
                executeThermalThrottleRecovery(error_info);
                break;
            case RecoveryAction::SHUTDOWN_STREAM:
                executeShutdownStreamRecovery(error_info);
                break;
            case RecoveryAction::SYSTEM_RESTART:
                executeSystemRestartRecovery(error_info);
                break;
            default:
                break;
        }
    }

    void executeRetryRecovery(const ErrorInfo& error_info) {
        if (logger_) {
            logger_->info("Executing retry recovery", "ErrorHandler", error_info.stream_id);
        }
        // Implementation would depend on the specific component
        // This is a placeholder for the actual retry logic
    }

    void executeResetConnectionRecovery(const ErrorInfo& error_info) {
        if (logger_) {
            logger_->info("Executing connection reset recovery", "ErrorHandler", error_info.stream_id);
        }
        // Implementation would reset the RTSP connection
    }

    void executeRestartStreamRecovery(const ErrorInfo& error_info) {
        if (logger_) {
            logger_->info("Executing stream restart recovery", "ErrorHandler", error_info.stream_id);
        }
        // Implementation would restart the entire stream
    }

    void executeFallbackSoftwareRecovery(const ErrorInfo& error_info) {
        if (logger_) {
            logger_->info("Executing software fallback recovery", "ErrorHandler", error_info.stream_id);
        }
        // Implementation would switch from hardware to software processing
    }

    void executeReduceQualityRecovery(const ErrorInfo& error_info) {
        if (logger_) {
            logger_->info("Executing quality reduction recovery", "ErrorHandler", error_info.stream_id);
        }
        // Implementation would reduce stream quality to save resources
    }

    void executeThermalThrottleRecovery(const ErrorInfo& error_info) {
        if (logger_) {
            logger_->info("Executing thermal throttling recovery", "ErrorHandler", error_info.stream_id);
        }
        // Implementation would reduce processing load due to thermal constraints
    }

    void executeShutdownStreamRecovery(const ErrorInfo& error_info) {
        if (logger_) {
            logger_->warning("Executing stream shutdown recovery", "ErrorHandler", error_info.stream_id);
        }
        // Implementation would shutdown the problematic stream
    }

    void executeSystemRestartRecovery(const ErrorInfo& error_info) {
        if (logger_) {
            logger_->critical("Executing system restart recovery", "ErrorHandler", error_info.stream_id);
        }
        // Implementation would restart the entire system (last resort)
    }

    void startMonitoring() {
        monitoring_active_ = true;
        monitoring_thread_ = std::thread([this]() {
            while (monitoring_active_) {
                // Monitor system health
                monitorSystemHealth();

                // Sleep for monitoring interval
                std::this_thread::sleep_for(std::chrono::seconds(5));
            }
        });
    }

    void stopMonitoring() {
        monitoring_active_ = false;
        if (monitoring_thread_.joinable()) {
            monitoring_thread_.join();
        }
    }

    void monitorSystemHealth() {
        // Check thermal status
        if (config_.enable_thermal_protection) {
            int temp = getCurrentTemperature();
            if (temp > config_.critical_thermal_threshold_celsius) {
                ErrorInfo thermal_error;
                thermal_error.category = ErrorCategory::THERMAL_ERROR;
                thermal_error.severity = ErrorSeverity::CRITICAL;
                thermal_error.message = "Critical temperature reached: " + std::to_string(temp) + "°C";
                thermal_error.component = "ThermalMonitor";
                thermal_error.suggested_action = RecoveryAction::THERMAL_THROTTLE;

                handleError(thermal_error);
            }
        }

        // Check memory pressure
        if (config_.enable_resource_monitoring) {
            double memory_pressure = getCurrentMemoryPressure();
            if (memory_pressure > config_.memory_pressure_threshold) {
                ErrorInfo memory_error;
                memory_error.category = ErrorCategory::RESOURCE_ERROR;
                memory_error.severity = ErrorSeverity::WARNING;
                memory_error.message = "High memory pressure: " +
                    std::to_string(static_cast<int>(memory_pressure * 100)) + "%";
                memory_error.component = "MemoryMonitor";
                memory_error.suggested_action = RecoveryAction::REDUCE_QUALITY;

                handleError(memory_error);
            }
        }

        // Notify metrics callback
        if (metrics_callback_) {
            std::lock_guard<std::mutex> lock(callback_mutex_);
            metrics_callback_(statistics_);
        }
    }

    int getCurrentTemperature() const {
        // Read temperature from RK3588 thermal zone
        std::ifstream temp_file("/sys/class/thermal/thermal_zone0/temp");
        if (temp_file.is_open()) {
            int temp_millidegrees;
            temp_file >> temp_millidegrees;
            return temp_millidegrees / 1000; // Convert to degrees Celsius
        }
        return 25; // Default temperature if reading fails
    }

    double getCurrentMemoryPressure() const {
        // Read memory information from /proc/meminfo
        std::ifstream meminfo("/proc/meminfo");
        if (!meminfo.is_open()) {
            return 0.0;
        }

        long total_memory = 0;
        long available_memory = 0;
        std::string line;

        while (std::getline(meminfo, line)) {
            if (line.find("MemTotal:") == 0) {
                std::istringstream iss(line);
                std::string label, value, unit;
                iss >> label >> value >> unit;
                total_memory = std::stol(value);
            } else if (line.find("MemAvailable:") == 0) {
                std::istringstream iss(line);
                std::string label, value, unit;
                iss >> label >> value >> unit;
                available_memory = std::stol(value);
                break;
            }
        }

        if (total_memory > 0) {
            return 1.0 - (static_cast<double>(available_memory) / total_memory);
        }

        return 0.0;
    }

    std::string getCurrentTimestamp() const {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;

        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        ss << "." << std::setfill('0') << std::setw(3) << ms.count();
        return ss.str();
    }

    std::string severityToString(ErrorSeverity severity) const {
        switch (severity) {
            case ErrorSeverity::DEBUG: return "DEBUG";
            case ErrorSeverity::INFO: return "INFO";
            case ErrorSeverity::WARNING: return "WARNING";
            case ErrorSeverity::ERROR: return "ERROR";
            case ErrorSeverity::CRITICAL: return "CRITICAL";
            case ErrorSeverity::FATAL: return "FATAL";
            default: return "UNKNOWN";
        }
    }

    std::string categoryToString(ErrorCategory category) const {
        switch (category) {
            case ErrorCategory::NETWORK_ERROR: return "NETWORK";
            case ErrorCategory::PROTOCOL_ERROR: return "PROTOCOL";
            case ErrorCategory::CODEC_ERROR: return "CODEC";
            case ErrorCategory::HARDWARE_ERROR: return "HARDWARE";
            case ErrorCategory::RESOURCE_ERROR: return "RESOURCE";
            case ErrorCategory::CONFIGURATION_ERROR: return "CONFIG";
            case ErrorCategory::THERMAL_ERROR: return "THERMAL";
            default: return "UNKNOWN";
        }
    }

    std::string recoveryActionToString(RecoveryAction action) const {
        switch (action) {
            case RecoveryAction::NONE: return "NONE";
            case RecoveryAction::RETRY: return "RETRY";
            case RecoveryAction::RESET_CONNECTION: return "RESET_CONNECTION";
            case RecoveryAction::RESTART_STREAM: return "RESTART_STREAM";
            case RecoveryAction::FALLBACK_SOFTWARE: return "FALLBACK_SOFTWARE";
            case RecoveryAction::REDUCE_QUALITY: return "REDUCE_QUALITY";
            case RecoveryAction::THERMAL_THROTTLE: return "THERMAL_THROTTLE";
            case RecoveryAction::SHUTDOWN_STREAM: return "SHUTDOWN_STREAM";
            case RecoveryAction::SYSTEM_RESTART: return "SYSTEM_RESTART";
            default: return "UNKNOWN";
        }
    }
};

// ErrorHandler public interface implementation

ErrorHandler::ErrorHandler(std::shared_ptr<Logger> logger, const RecoveryConfig& config)
    : pImpl(std::make_unique<Impl>(logger, config)) {
}

ErrorHandler::~ErrorHandler() = default;

RecoveryAction ErrorHandler::handleError(ErrorCategory category,
                                        ErrorSeverity severity,
                                        const std::string& message,
                                        const std::string& component,
                                        const std::string& stream_id,
                                        const std::unordered_map<std::string, std::string>& context) {
    ErrorInfo error_info;
    error_info.category = category;
    error_info.severity = severity;
    error_info.message = message;
    error_info.component = component;
    error_info.stream_id = stream_id;
    error_info.context = context;
    error_info.timestamp = std::chrono::steady_clock::now();

    return pImpl->handleError(error_info);
}

RecoveryAction ErrorHandler::handleError(const ErrorInfo& error_info) {
    return pImpl->handleError(error_info);
}

void ErrorHandler::reportRecoverySuccess(const std::string& stream_id,
                                        RecoveryAction action,
                                        const std::string& details) {
    pImpl->reportRecoverySuccess(stream_id, action, details);
}

void ErrorHandler::reportRecoveryFailure(const std::string& stream_id,
                                        RecoveryAction action,
                                        const std::string& details) {
    pImpl->reportRecoveryFailure(stream_id, action, details);
}

void ErrorHandler::updateConfig(const RecoveryConfig& config) {
    pImpl->updateConfig(config);
}

RecoveryConfig ErrorHandler::getConfig() const {
    return pImpl->getConfig();
}

void ErrorHandler::setLogger(std::shared_ptr<Logger> logger) {
    pImpl->setLogger(logger);
}

void ErrorHandler::setErrorCallback(ErrorCallback callback) {
    pImpl->setErrorCallback(callback);
}

void ErrorHandler::setRecoveryCallback(RecoveryCallback callback) {
    pImpl->setRecoveryCallback(callback);
}

void ErrorHandler::setMetricsCallback(MetricsCallback callback) {
    pImpl->setMetricsCallback(callback);
}

ErrorStatistics ErrorHandler::getStatistics() const {
    return pImpl->getStatistics();
}

void ErrorHandler::resetStatistics() {
    pImpl->resetStatistics();
}

double ErrorHandler::getErrorRate() const {
    return pImpl->getErrorRate();
}

double ErrorHandler::getRecoverySuccessRate() const {
    return pImpl->getRecoverySuccessRate();
}

bool ErrorHandler::isSystemHealthy() const {
    return pImpl->isSystemHealthy();
}

std::string ErrorHandler::getThermalStatus() const {
    return pImpl->getThermalStatus();
}

std::string ErrorHandler::getMemoryPressureStatus() const {
    return pImpl->getMemoryPressureStatus();
}

} // namespace rtsp
} // namespace aibox
