# Định Nghĩa Tác Vụ Module Phát Hiện Khuôn Mặt NPU

## Tổng Quan
Module này tận dụng Neural Processing Unit (NPU) của RK3588 để chạy các mô hình phát hiện khuôn mặt (như RetinaFace) trên các khung hình video đã được tiền xử lý. Nó phát hiện khuôn mặt trong các luồng video và cung cấp tọa độ bounding box cho việc xử lý tiếp theo.

## Yêu Cầu
- Chạy các mô hình phát hiện khuôn mặt được tối ưu hóa trên NPU RK3588
- Xử lý khung hình từ nhiều luồng camera một cách hiệu quả
- Tối đa hóa việc sử dụng NPU thông qua quản lý lõi phù hợp và xử lý batch
- Du<PERSON> trì các thao tác zero-copy cho dữ liệu đầu vào/đầu ra
- Hỗ trợ suy luận bất đồng bộ để có hiệu suất tối ưu

## Phương Pháp Kỹ Thuật
1. **Chuẩn Bị và Khởi Tạo Mô Hình RKNN**:
   - Tải mô hình phát hiện khuôn mặt được tối ưu hóa, lượng tử hóa (định dạng .rknn)
   - Khởi tạo context RKNN với các flag phù hợp:
     - `RKNN_FLAG_MEM_ALLOC_OUTSIDE` cho zero-copy
     - `RKNN_FLAG_ASYNC_MODE` cho suy luận bất đồng bộ
     - `RKNN_FLAG_BATCH_MODE` cho xử lý batch nếu áp dụng được
   - Cấu hình phân bổ lõi NPU sử dụng `rknn_set_core_mask`

2. **Xử Lý Đầu Vào Zero-Copy**:
   - Tạo bộ nhớ tensor RKNN từ file descriptor DMABUF
   - Cấu hình thuộc tính đầu vào tensor đúng cách
   - Triển khai xử lý batch cho nhiều khung hình khi có thể

3. **Suy Luận Mô Hình**:
   - Thực thi suy luận mô hình trên NPU
   - Quản lý thực thi bất đồng bộ và đồng bộ hóa kết quả
   - Triển khai quản lý bộ nhớ tensor hiệu quả

4. **Xử Lý Kết Quả**:
   - Trích xuất và giải mã kết quả phát hiện (bounding box, điểm tin cậy)
   - Áp dụng hậu xử lý như Non-Maximum Suppression (NMS)
   - Chuẩn bị kết quả phát hiện cho module trích xuất khuôn mặt

5. **Quản Lý Tài Nguyên NPU**:
   - Triển khai chiến lược phân bổ lõi NPU phù hợp
   - Cân bằng tải trên các lõi NPU có sẵn
   - Giám sát và tối ưu hóa việc sử dụng NPU

## Giao Diện
- **Đầu vào**: File descriptor DMABUF trỏ đến các khung hình đã tiền xử lý
- **Đầu ra**: Kết quả phát hiện khuôn mặt (bounding box, điểm tin cậy)

## Phụ Thuộc
- Thư viện RKNN Runtime (librknnrt.so)
- Header RKNN API (rknn_api.h)
- Mô hình phát hiện khuôn mặt được tối ưu hóa (ví dụ: RetinaFace_mobile320.rknn)
- Thư viện chuẩn C++

## Cân Nhắc Hiệu Suất
- Sử dụng lõi NPU hiệu quả (sử dụng cả 3 lõi khi có thể)
- Xử lý batch để tối đa hóa thông lượng
- Suy luận bất đồng bộ để tránh chặn
- Quản lý bộ nhớ phù hợp để duy trì trong giới hạn bộ nhớ địa chỉ 4GB của NPU
- Cân bằng giữa độ chính xác phát hiện và hiệu suất
