#include <gtest/gtest.h>
#include "rtsp/connection_manager.hpp"
#include "rtsp/rtsp_config.hpp"
#include <thread>
#include <chrono>

namespace aibox {
namespace rtsp {
namespace test {

class ConnectionManagerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration
        config_.rtsp_url = "rtsp://test.example.com:554/stream";
        config_.username = "testuser";
        config_.password = "testpass";
        config_.timeout_ms = 5000;
        config_.retry_count = 3;
        config_.transport = TransportProtocol::TCP;
        config_.use_mpp_decoder = true;
        config_.use_rga_scaler = true;
        config_.use_dmabuf_zerocopy = true;
        config_.buffer_size_bytes = 1024 * 1024;  // 1MB
        config_.queue_size = 100;
        config_.priority = StreamPriority::HIGH;
        
        // Ensure configuration is valid
        ASSERT_TRUE(config_.isValid()) << "Test configuration should be valid";
    }
    
    void TearDown() override {
        // Cleanup any resources
    }
    
    RTSPConnectionConfig config_;
};

// Test basic construction and destruction
TEST_F(ConnectionManagerTest, ConstructionDestruction) {
    EXPECT_NO_THROW({
        ConnectionManager manager(config_);
    });
}

// Test configuration validation
TEST_F(ConnectionManagerTest, ConfigurationValidation) {
    // Valid configuration should work
    EXPECT_NO_THROW({
        ConnectionManager manager(config_);
        EXPECT_EQ(manager.getConfig().rtsp_url, config_.rtsp_url);
        EXPECT_EQ(manager.getConfig().username, config_.username);
    });
    
    // Invalid configuration should throw
    RTSPConnectionConfig invalid_config;
    invalid_config.rtsp_url = "";  // Empty URL is invalid
    
    EXPECT_THROW({
        ConnectionManager manager(invalid_config);
    }, std::invalid_argument);
}

// Test initial state
TEST_F(ConnectionManagerTest, InitialState) {
    ConnectionManager manager(config_);
    
    EXPECT_EQ(manager.getState(), ConnectionState::DISCONNECTED);
    EXPECT_FALSE(manager.isConnected());
    
    // Statistics should be initialized
    auto stats = manager.getStatistics();
    EXPECT_EQ(stats.packets_received, 0);
    EXPECT_EQ(stats.packets_lost, 0);
    EXPECT_EQ(stats.reconnect_count, 0);
}

// Test configuration updates
TEST_F(ConnectionManagerTest, ConfigurationUpdate) {
    ConnectionManager manager(config_);
    
    // Update configuration
    RTSPConnectionConfig new_config = config_;
    new_config.timeout_ms = 10000;
    new_config.retry_count = 5;
    
    manager.updateConfig(new_config);
    
    auto updated_config = manager.getConfig();
    EXPECT_EQ(updated_config.timeout_ms, 10000);
    EXPECT_EQ(updated_config.retry_count, 5);
}

// Test connection attempt (will fail without real RTSP server)
TEST_F(ConnectionManagerTest, ConnectionAttempt) {
    ConnectionManager manager(config_);
    
    // This will fail since we don't have a real RTSP server
    // but we can test that the method doesn't crash
    auto result = manager.connect();
    
    // The connection should fail gracefully
    EXPECT_FALSE(result.success);
    EXPECT_FALSE(result.error_message.empty());
    
    // State should be updated appropriately
    auto state = manager.getState();
    EXPECT_TRUE(state == ConnectionState::ERROR || state == ConnectionState::DISCONNECTED);
}

// Test disconnect when not connected
TEST_F(ConnectionManagerTest, DisconnectWhenNotConnected) {
    ConnectionManager manager(config_);
    
    // Should not crash when disconnecting while not connected
    EXPECT_NO_THROW({
        manager.disconnect();
    });
    
    EXPECT_EQ(manager.getState(), ConnectionState::DISCONNECTED);
    EXPECT_FALSE(manager.isConnected());
}

// Test health monitoring
TEST_F(ConnectionManagerTest, HealthMonitoring) {
    ConnectionManager manager(config_);
    
    auto health = manager.getHealth();
    EXPECT_EQ(health.state, ConnectionState::DISCONNECTED);
    EXPECT_EQ(health.reconnect_count, 0);
    EXPECT_FALSE(health.hardware_acceleration_active);
}

// Test statistics collection
TEST_F(ConnectionManagerTest, StatisticsCollection) {
    ConnectionManager manager(config_);
    
    auto stats = manager.getStatistics();
    
    // Initial statistics should be zero
    EXPECT_EQ(stats.packets_received, 0);
    EXPECT_EQ(stats.packets_lost, 0);
    EXPECT_EQ(stats.bytes_received, 0);
    EXPECT_EQ(stats.reconnect_count, 0);
    EXPECT_EQ(stats.current_fps, 0);
    EXPECT_EQ(stats.network_errors, 0);
    EXPECT_EQ(stats.decode_errors, 0);
    EXPECT_EQ(stats.hardware_errors, 0);
    
    // Test statistics methods
    EXPECT_EQ(stats.getPacketLossRate(), 0.0);
    EXPECT_EQ(stats.getHardwareAccelRate(), 0.0);
}

// Test hardware acceleration status
TEST_F(ConnectionManagerTest, HardwareAccelerationStatus) {
    ConnectionManager manager(config_);
    
    // Initial status should be disabled
    EXPECT_EQ(manager.getMPPStatus(), HardwareAccelStatus::DISABLED);
    EXPECT_EQ(manager.getRGAStatus(), HardwareAccelStatus::DISABLED);
}

// Test RK3588 platform optimizations
TEST_F(ConnectionManagerTest, RK3588Optimizations) {
    ConnectionManager manager(config_);
    
    // Test that RK3588-specific optimizations are applied
    auto config = manager.getConfig();
    EXPECT_TRUE(config.use_mpp_decoder);
    EXPECT_TRUE(config.use_rga_scaler);
    EXPECT_TRUE(config.use_dmabuf_zerocopy);
}

// Test memory usage estimation
TEST_F(ConnectionManagerTest, MemoryUsageEstimation) {
    // Test memory usage calculation
    size_t estimated_memory = config_.getEstimatedMemoryUsage();
    
    // Should be reasonable for the configuration
    EXPECT_GT(estimated_memory, 0);
    EXPECT_LT(estimated_memory, 100 * 1024 * 1024);  // Less than 100MB for single stream
    
    // Larger buffer should result in larger memory estimate
    RTSPConnectionConfig large_buffer_config = config_;
    large_buffer_config.buffer_size_bytes = 10 * 1024 * 1024;  // 10MB
    large_buffer_config.queue_size = 1000;
    
    size_t large_estimated_memory = large_buffer_config.getEstimatedMemoryUsage();
    EXPECT_GT(large_estimated_memory, estimated_memory);
}

// Test error handling
TEST_F(ConnectionManagerTest, ErrorHandling) {
    ConnectionManager manager(config_);
    
    // Test error callback mechanism
    bool error_received = false;
    std::string error_message;
    ErrorCategory error_category;
    
    manager.setErrorCallback([&](const StreamId& id, ErrorCategory category, const std::string& message) {
        error_received = true;
        error_message = message;
        error_category = category;
    });
    
    // Trigger an error (this is internal method, so we test indirectly)
    // The connection attempt should trigger error callbacks
    auto result = manager.connect();
    
    // Give some time for error processing
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // We expect some kind of error since we don't have a real RTSP server
    // The exact error depends on the system configuration
}

// Test thermal management
TEST_F(ConnectionManagerTest, ThermalManagement) {
    ConnectionManager manager(config_);
    
    // Test thermal throttling
    EXPECT_NO_THROW({
        manager.handleThermalThrottling(85);  // High temperature
    });
    
    // Test adaptive quality
    EXPECT_NO_THROW({
        manager.setAdaptiveQuality(true);
        manager.setAdaptiveQuality(false);
    });
}

// Test thread safety
TEST_F(ConnectionManagerTest, ThreadSafety) {
    ConnectionManager manager(config_);
    
    // Test concurrent access to statistics
    std::vector<std::thread> threads;
    std::atomic<bool> should_stop(false);
    
    // Start multiple threads accessing statistics
    for (int i = 0; i < 4; ++i) {
        threads.emplace_back([&manager, &should_stop]() {
            while (!should_stop) {
                auto stats = manager.getStatistics();
                auto health = manager.getHealth();
                auto state = manager.getState();
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        });
    }
    
    // Let threads run for a short time
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // Stop threads
    should_stop = true;
    for (auto& thread : threads) {
        thread.join();
    }
    
    // Should not crash
    SUCCEED();
}

// Performance test for RK3588
TEST_F(ConnectionManagerTest, RK3588Performance) {
    ConnectionManager manager(config_);
    
    // Measure construction time (should be fast)
    auto start = std::chrono::high_resolution_clock::now();
    
    // Perform some operations
    for (int i = 0; i < 1000; ++i) {
        auto stats = manager.getStatistics();
        auto health = manager.getHealth();
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    // Should complete quickly (less than 10ms for 1000 operations)
    EXPECT_LT(duration.count(), 10000);
    
    std::cout << "1000 statistics calls took: " << duration.count() << " microseconds" << std::endl;
}

} // namespace test
} // namespace rtsp
} // namespace aibox
