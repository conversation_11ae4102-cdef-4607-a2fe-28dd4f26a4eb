#include <gtest/gtest.h>
#include <utils/number_utils.hpp>
#include <limits>

class NumberUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up test fixtures if needed
    }

    void TearDown() override {
        // Clean up after tests
    }
};

TEST_F(NumberUtilsTest, IntegerParsing) {
    // Test parseInt
    auto result1 = utils::NumberUtils::parseInt("123");
    EXPECT_TRUE(result1.has_value());
    EXPECT_EQ(result1.value(), 123);

    auto result2 = utils::NumberUtils::parseInt("-456");
    EXPECT_TRUE(result2.has_value());
    EXPECT_EQ(result2.value(), -456);

    auto result3 = utils::NumberUtils::parseInt("abc");
    EXPECT_FALSE(result3.has_value());

    auto result4 = utils::NumberUtils::parseInt("123abc");
    EXPECT_FALSE(result4.has_value());

    auto result5 = utils::NumberUtils::parseInt("");
    EXPECT_FALSE(result5.has_value());

    // Test convenience function
    auto result6 = utils::parseInt("789");
    EXPECT_TRUE(result6.has_value());
    EXPECT_EQ(result6.value(), 789);

    // Test parseLong
    auto longResult = utils::NumberUtils::parseLong("1234567890");
    EXPECT_TRUE(longResult.has_value());
    EXPECT_EQ(longResult.value(), 1234567890L);

    // Test parseLongLong
    auto longLongResult = utils::NumberUtils::parseLongLong("1234567890123456789");
    EXPECT_TRUE(longLongResult.has_value());
}

TEST_F(NumberUtilsTest, UnsignedIntegerParsing) {
    // Test parseUInt
    auto result1 = utils::NumberUtils::parseUInt("123");
    EXPECT_TRUE(result1.has_value());
    EXPECT_EQ(result1.value(), 123u);

    auto result2 = utils::NumberUtils::parseUInt("-123");
    EXPECT_FALSE(result2.has_value()); // Negative not allowed

    // Test parseULong
    auto ulongResult = utils::NumberUtils::parseULong("4294967295");
    EXPECT_TRUE(ulongResult.has_value());

    // Test parseULongLong
    auto ulongLongResult = utils::NumberUtils::parseULongLong("18446744073709551615");
    EXPECT_TRUE(ulongLongResult.has_value());
}

TEST_F(NumberUtilsTest, FloatingPointParsing) {
    // Test parseFloat
    auto result1 = utils::NumberUtils::parseFloat("123.45");
    EXPECT_TRUE(result1.has_value());
    EXPECT_FLOAT_EQ(result1.value(), 123.45f);

    auto result2 = utils::NumberUtils::parseFloat("-67.89");
    EXPECT_TRUE(result2.has_value());
    EXPECT_FLOAT_EQ(result2.value(), -67.89f);

    auto result3 = utils::NumberUtils::parseFloat("1.23e10");
    EXPECT_TRUE(result3.has_value());

    auto result4 = utils::NumberUtils::parseFloat("abc");
    EXPECT_FALSE(result4.has_value());

    // Test parseDouble
    auto doubleResult1 = utils::NumberUtils::parseDouble("123.456789");
    EXPECT_TRUE(doubleResult1.has_value());
    EXPECT_DOUBLE_EQ(doubleResult1.value(), 123.456789);

    // Test convenience function
    auto doubleResult2 = utils::parseDouble("987.654");
    EXPECT_TRUE(doubleResult2.has_value());
    EXPECT_DOUBLE_EQ(doubleResult2.value(), 987.654);
}

TEST_F(NumberUtilsTest, NumberToString) {
    // Test integer toString with different bases
    EXPECT_EQ(utils::NumberUtils::toString(123LL), "123");
    EXPECT_EQ(utils::NumberUtils::toString(-456LL), "-456");
    EXPECT_EQ(utils::NumberUtils::toString(255LL, 16), "FF");
    EXPECT_EQ(utils::NumberUtils::toString(8LL, 2), "1000");
    EXPECT_EQ(utils::NumberUtils::toString(0LL), "0");

    // Test unsigned integer toString
    EXPECT_EQ(utils::NumberUtils::toString(123ULL), "123");
    EXPECT_EQ(utils::NumberUtils::toString(255ULL, 16), "FF");

    // Test double toString
    EXPECT_EQ(utils::NumberUtils::toString(123.456, 2, true), "123.46");
    EXPECT_EQ(utils::NumberUtils::toString(123.0, 0, true), "123");

    // Test convenience function
    std::string result = utils::toString(456.789);
    EXPECT_FALSE(result.empty());
}

TEST_F(NumberUtilsTest, NumberFormatting) {
    // Test formatWithSeparator for integers
    EXPECT_EQ(utils::NumberUtils::formatWithSeparator(1234567LL), "1,234,567");
    EXPECT_EQ(utils::NumberUtils::formatWithSeparator(-1234567LL), "-1,234,567");
    EXPECT_EQ(utils::NumberUtils::formatWithSeparator(123LL), "123");
    EXPECT_EQ(utils::NumberUtils::formatWithSeparator(1234567LL, "."), "1.234.567");

    // Test formatWithSeparator for doubles
    EXPECT_EQ(utils::NumberUtils::formatWithSeparator(1234567.89, ",", 2), "1,234,567.89");
    EXPECT_EQ(utils::NumberUtils::formatWithSeparator(-1234567.89, ",", 2), "-1,234,567.89");
    EXPECT_EQ(utils::NumberUtils::formatWithSeparator(123.44, ",", 1), "123.4");
}

TEST_F(NumberUtilsTest, FloatingPointComparison) {
    // Test isEqual
    EXPECT_TRUE(utils::NumberUtils::isEqual(1.0, 1.0));
    EXPECT_TRUE(utils::NumberUtils::isEqual(1.0, 1.0000000001, 1e-8));
    EXPECT_FALSE(utils::NumberUtils::isEqual(1.0, 1.1));

    // Test convenience function
    EXPECT_TRUE(utils::isEqual(2.0, 2.0));

    // Test isZero
    EXPECT_TRUE(utils::NumberUtils::isZero(0.0));
    EXPECT_TRUE(utils::NumberUtils::isZero(0.0000000001, 1e-8));
    EXPECT_FALSE(utils::NumberUtils::isZero(0.1));
}

TEST_F(NumberUtilsTest, NumberProperties) {
    // Test isPositive
    EXPECT_TRUE(utils::NumberUtils::isPositive(5));
    EXPECT_TRUE(utils::NumberUtils::isPositive(0.1));
    EXPECT_FALSE(utils::NumberUtils::isPositive(0));
    EXPECT_FALSE(utils::NumberUtils::isPositive(-5));

    // Test isNegative
    EXPECT_TRUE(utils::NumberUtils::isNegative(-5));
    EXPECT_TRUE(utils::NumberUtils::isNegative(-0.1));
    EXPECT_FALSE(utils::NumberUtils::isNegative(0));
    EXPECT_FALSE(utils::NumberUtils::isNegative(5));

    // Test isEven
    EXPECT_TRUE(utils::NumberUtils::isEven(4));
    EXPECT_TRUE(utils::NumberUtils::isEven(0));
    EXPECT_TRUE(utils::NumberUtils::isEven(-2));
    EXPECT_FALSE(utils::NumberUtils::isEven(3));
    EXPECT_FALSE(utils::NumberUtils::isEven(-1));

    // Test isOdd
    EXPECT_TRUE(utils::NumberUtils::isOdd(3));
    EXPECT_TRUE(utils::NumberUtils::isOdd(-1));
    EXPECT_FALSE(utils::NumberUtils::isOdd(4));
    EXPECT_FALSE(utils::NumberUtils::isOdd(0));

    // Test isPrime
    EXPECT_TRUE(utils::NumberUtils::isPrime(2));
    EXPECT_TRUE(utils::NumberUtils::isPrime(3));
    EXPECT_TRUE(utils::NumberUtils::isPrime(17));
    EXPECT_FALSE(utils::NumberUtils::isPrime(1));
    EXPECT_FALSE(utils::NumberUtils::isPrime(4));
    EXPECT_FALSE(utils::NumberUtils::isPrime(15));
    EXPECT_FALSE(utils::NumberUtils::isPrime(-5));

    // Test isPerfectSquare
    EXPECT_TRUE(utils::NumberUtils::isPerfectSquare(0));
    EXPECT_TRUE(utils::NumberUtils::isPerfectSquare(1));
    EXPECT_TRUE(utils::NumberUtils::isPerfectSquare(4));
    EXPECT_TRUE(utils::NumberUtils::isPerfectSquare(16));
    EXPECT_FALSE(utils::NumberUtils::isPerfectSquare(2));
    EXPECT_FALSE(utils::NumberUtils::isPerfectSquare(15));
    EXPECT_FALSE(utils::NumberUtils::isPerfectSquare(-4));
}

TEST_F(NumberUtilsTest, MathOperations) {
    // Test clamp
    EXPECT_EQ(utils::NumberUtils::clamp(5, 1, 10), 5);
    EXPECT_EQ(utils::NumberUtils::clamp(-5, 1, 10), 1);
    EXPECT_EQ(utils::NumberUtils::clamp(15, 1, 10), 10);

    // Test abs
    EXPECT_EQ(utils::NumberUtils::abs(-5), 5);
    EXPECT_EQ(utils::NumberUtils::abs(5), 5);
    EXPECT_EQ(utils::NumberUtils::abs(0), 0);

    // Test min/max
    EXPECT_EQ(utils::NumberUtils::min(3, 7), 3);
    EXPECT_EQ(utils::NumberUtils::max(3, 7), 7);

    // Test round
    EXPECT_DOUBLE_EQ(utils::NumberUtils::round(3.14159, 2), 3.14);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::round(3.16159, 2), 3.16);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::round(123.456), 123.0);

    // Test ceil
    EXPECT_DOUBLE_EQ(utils::NumberUtils::ceil(3.14159, 2), 3.15);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::ceil(3.1), 4.0);

    // Test floor
    EXPECT_DOUBLE_EQ(utils::NumberUtils::floor(3.14159, 2), 3.14);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::floor(3.9), 3.0);
}

TEST_F(NumberUtilsTest, PercentageOperations) {
    // Test percentage
    EXPECT_DOUBLE_EQ(utils::NumberUtils::percentage(25, 100), 25.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::percentage(1, 4), 25.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::percentage(0, 100), 0.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::percentage(100, 0), 0.0); // Division by zero

    // Test percentageOf
    EXPECT_DOUBLE_EQ(utils::NumberUtils::percentageOf(100, 25), 25.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::percentageOf(200, 50), 100.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::percentageOf(100, 0), 0.0);
}

TEST_F(NumberUtilsTest, RandomNumbers) {
    // Test randomInt
    for (int i = 0; i < 100; ++i) {
        int random = utils::NumberUtils::randomInt(1, 10);
        EXPECT_GE(random, 1);
        EXPECT_LE(random, 10);
    }

    // Test convenience function
    int random2 = utils::randomInt(5, 15);
    EXPECT_GE(random2, 5);
    EXPECT_LE(random2, 15);

    // Test randomDouble
    for (int i = 0; i < 100; ++i) {
        double random = utils::NumberUtils::randomDouble(0.0, 1.0);
        EXPECT_GE(random, 0.0);
        EXPECT_LT(random, 1.0);
    }

    // Test randomDouble with custom range
    for (int i = 0; i < 100; ++i) {
        double random = utils::NumberUtils::randomDouble(10.0, 20.0);
        EXPECT_GE(random, 10.0);
        EXPECT_LT(random, 20.0);
    }
}

TEST_F(NumberUtilsTest, MathematicalFunctions) {
    // Test gcd
    EXPECT_EQ(utils::NumberUtils::gcd(12, 8), 4);
    EXPECT_EQ(utils::NumberUtils::gcd(17, 13), 1);
    EXPECT_EQ(utils::NumberUtils::gcd(0, 5), 5);
    EXPECT_EQ(utils::NumberUtils::gcd(-12, 8), 4);

    // Test lcm
    EXPECT_EQ(utils::NumberUtils::lcm(4, 6), 12);
    EXPECT_EQ(utils::NumberUtils::lcm(17, 13), 221);
    EXPECT_EQ(utils::NumberUtils::lcm(0, 5), 0);

    // Test factorial
    EXPECT_EQ(utils::NumberUtils::factorial(0), 1);
    EXPECT_EQ(utils::NumberUtils::factorial(1), 1);
    EXPECT_EQ(utils::NumberUtils::factorial(5), 120);
    EXPECT_EQ(utils::NumberUtils::factorial(-1), 0);

    // Test power
    EXPECT_DOUBLE_EQ(utils::NumberUtils::power(2.0, 3.0), 8.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::power(5.0, 0.0), 1.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::power(4.0, 0.5), 2.0);

    // Test sqrt
    EXPECT_DOUBLE_EQ(utils::NumberUtils::sqrt(4.0), 2.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::sqrt(9.0), 3.0);

    // Test logarithms
    EXPECT_DOUBLE_EQ(utils::NumberUtils::log10(100.0), 2.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::log10(1000.0), 3.0);

    EXPECT_NEAR(utils::NumberUtils::ln(2.718281828), 1.0, 1e-6);
}

TEST_F(NumberUtilsTest, AngleConversions) {
    // Test toRadians
    EXPECT_NEAR(utils::NumberUtils::toRadians(180.0), M_PI, 1e-10);
    EXPECT_NEAR(utils::NumberUtils::toRadians(90.0), M_PI/2, 1e-10);
    EXPECT_NEAR(utils::NumberUtils::toRadians(0.0), 0.0, 1e-10);

    // Test toDegrees
    EXPECT_NEAR(utils::NumberUtils::toDegrees(M_PI), 180.0, 1e-10);
    EXPECT_NEAR(utils::NumberUtils::toDegrees(M_PI/2), 90.0, 1e-10);
    EXPECT_NEAR(utils::NumberUtils::toDegrees(0.0), 0.0, 1e-10);
}

TEST_F(NumberUtilsTest, RangeAndMapping) {
    // Test inRange
    EXPECT_TRUE(utils::NumberUtils::inRange(5, 1, 10));
    EXPECT_TRUE(utils::NumberUtils::inRange(1, 1, 10)); // boundary
    EXPECT_TRUE(utils::NumberUtils::inRange(10, 1, 10)); // boundary
    EXPECT_FALSE(utils::NumberUtils::inRange(0, 1, 10));
    EXPECT_FALSE(utils::NumberUtils::inRange(11, 1, 10));

    // Test lerp
    EXPECT_DOUBLE_EQ(utils::NumberUtils::lerp(0.0, 10.0, 0.5), 5.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::lerp(0.0, 10.0, 0.0), 0.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::lerp(0.0, 10.0, 1.0), 10.0);

    // Test map
    EXPECT_DOUBLE_EQ(utils::NumberUtils::map(5.0, 0.0, 10.0, 0.0, 100.0), 50.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::map(0.0, 0.0, 10.0, 0.0, 100.0), 0.0);
    EXPECT_DOUBLE_EQ(utils::NumberUtils::map(10.0, 0.0, 10.0, 0.0, 100.0), 100.0);

    // Test map with different ranges
    EXPECT_DOUBLE_EQ(utils::NumberUtils::map(2.0, 0.0, 4.0, 10.0, 20.0), 15.0);
}

TEST_F(NumberUtilsTest, EdgeCases) {
    // Test parsing edge cases
    EXPECT_FALSE(utils::NumberUtils::parseInt("").has_value());
    EXPECT_FALSE(utils::NumberUtils::parseDouble("").has_value());

    // Test very large numbers
    auto maxInt = utils::NumberUtils::parseInt(std::to_string(std::numeric_limits<int>::max()));
    EXPECT_TRUE(maxInt.has_value());

    // Test very small numbers
    auto minInt = utils::NumberUtils::parseInt(std::to_string(std::numeric_limits<int>::min()));
    EXPECT_TRUE(minInt.has_value());

    // Test special float values
    EXPECT_TRUE(std::isnan(utils::NumberUtils::sqrt(-1.0)));
    EXPECT_TRUE(std::isinf(utils::NumberUtils::log10(0.0)));

    // Test zero cases
    EXPECT_EQ(utils::NumberUtils::gcd(0, 0), 0);
    EXPECT_EQ(utils::NumberUtils::lcm(0, 0), 0);
    EXPECT_EQ(utils::NumberUtils::factorial(0), 1);
}
