#pragma once

#include <string>
#include <chrono>

namespace server::shared {

/**
 * @brief Server configuration
 */
struct ServerConfig {
    // Network settings
    std::string host = "0.0.0.0";
    int port = 8080;
    int timeout_seconds = 30;
    
    // CORS settings
    bool enable_cors = true;
    std::string cors_origin = "*";
    std::string cors_methods = "GET, POST, PUT, DELETE, OPTIONS";
    std::string cors_headers = "Content-Type, Authorization";
    
    // Logging settings
    bool enable_request_logging = true;
    bool enable_debug_logging = false;
    
    // AI Model settings
    std::string models_base_path = "./models";
    int max_concurrent_requests = 10;
    int inference_timeout_ms = 30000;
    
    // Performance settings
    bool enable_threading = true;
    int thread_pool_size = 4;
    
    // Security settings
    bool enable_rate_limiting = false;
    int rate_limit_requests_per_minute = 60;
    
    // API settings
    std::string api_version = "1.0.0";
    std::string api_description = "C-AIBOX HTTP API Server";
    
    // Validation
    bool isValid() const {
        return port > 0 && port <= 65535 && 
               timeout_seconds > 0 && 
               max_concurrent_requests > 0 &&
               thread_pool_size > 0;
    }
};

/**
 * @brief Application constants
 */
struct AppConstants {
    static constexpr const char* APP_NAME = "C-AIBOX Server";
    static constexpr const char* APP_VERSION = "1.0.0";
    static constexpr const char* API_PREFIX = "/api/v1";
    
    // HTTP status codes
    static constexpr int HTTP_OK = 200;
    static constexpr int HTTP_BAD_REQUEST = 400;
    static constexpr int HTTP_NOT_FOUND = 404;
    static constexpr int HTTP_INTERNAL_ERROR = 500;
    
    // Content types
    static constexpr const char* CONTENT_TYPE_JSON = "application/json";
    static constexpr const char* CONTENT_TYPE_TEXT = "text/plain";
    
    // Error codes
    static constexpr const char* ERROR_INVALID_REQUEST = "INVALID_REQUEST";
    static constexpr const char* ERROR_FILE_NOT_FOUND = "FILE_NOT_FOUND";
    static constexpr const char* ERROR_INFERENCE_FAILED = "INFERENCE_FAILED";
    static constexpr const char* ERROR_INTERNAL = "INTERNAL_ERROR";
};

} // namespace server::shared
