#include <gtest/gtest.h>
#include "utils/regex_utils.hpp"
#include <string>
#include <vector>
#include <regex>

using namespace utils;

class RegexUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        testText = "Hello World! Email: <EMAIL>, Phone: +1234567890";
        simplePattern = R"(\w+)";
        emailPattern = R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})";
    }

    std::string testText;
    std::string simplePattern;
    std::string emailPattern;
};

// =============================================================================
// RegexBuilder Tests
// =============================================================================

TEST_F(RegexUtilsTest, RegexBuilder_BasicConstruction) {
    RegexBuilder builder;
    std::string pattern = builder.literal("hello").build();
    EXPECT_EQ(pattern, "hello");
}

TEST_F(RegexUtilsTest, RegexBuilder_CopyConstructor) {
    RegexBuilder builder1;
    builder1.literal("test");

    RegexBuilder builder2(builder1);
    EXPECT_EQ(builder1.build(), builder2.build());
}

TEST_F(RegexUtilsTest, RegexBuilder_AssignmentOperator) {
    RegexBuilder builder1;
    builder1.literal("test");

    RegexBuilder builder2;
    builder2 = builder1;
    EXPECT_EQ(builder1.build(), builder2.build());
}

TEST_F(RegexUtilsTest, RegexBuilder_LiteralEscaping) {
    RegexBuilder builder;
    std::string pattern = builder.literal("hello.world").build();
    EXPECT_EQ(pattern, "hello\\.world");
}

TEST_F(RegexUtilsTest, RegexBuilder_RawPattern) {
    RegexBuilder builder;
    std::string pattern = builder.raw(R"(\d+)").build();
    EXPECT_EQ(pattern, R"(\d+)");
}

TEST_F(RegexUtilsTest, RegexBuilder_CharacterClasses) {
    RegexBuilder builder;
    std::string pattern = builder.anyChar().digit().word().whitespace().build();
    EXPECT_EQ(pattern, R"(.\d\w\s)");
}

TEST_F(RegexUtilsTest, RegexBuilder_CustomCharClass) {
    RegexBuilder builder;
    std::string pattern = builder.charClass("abc").build();
    EXPECT_EQ(pattern, "[abc]");
}

TEST_F(RegexUtilsTest, RegexBuilder_NegatedCharClass) {
    RegexBuilder builder;
    std::string pattern = builder.notCharClass("abc").build();
    EXPECT_EQ(pattern, "[^abc]");
}

TEST_F(RegexUtilsTest, RegexBuilder_CharRange) {
    RegexBuilder builder;
    std::string pattern = builder.range('a', 'z').build();
    EXPECT_EQ(pattern, "[a-z]");
}

TEST_F(RegexUtilsTest, RegexBuilder_Quantifiers) {
    RegexBuilder builder;
    std::string pattern = builder.digit().zeroOrMore().word().oneOrMore().anyChar().zeroOrOne().build();
    EXPECT_EQ(pattern, R"(\d*\w+.?)");
}

TEST_F(RegexUtilsTest, RegexBuilder_ExactQuantifiers) {
    RegexBuilder builder;
    std::string pattern = builder.digit().exactly(3).build();
    EXPECT_EQ(pattern, R"(\d{3})");
}

TEST_F(RegexUtilsTest, RegexBuilder_RangeQuantifiers) {
    RegexBuilder builder;
    std::string pattern = builder.digit().between(2, 4).build();
    EXPECT_EQ(pattern, R"(\d{2,4})");
}

TEST_F(RegexUtilsTest, RegexBuilder_MinQuantifiers) {
    RegexBuilder builder;
    std::string pattern = builder.digit().atLeast(2).build();
    EXPECT_EQ(pattern, R"(\d{2,})");
}

TEST_F(RegexUtilsTest, RegexBuilder_Anchors) {
    RegexBuilder builder;
    std::string pattern = builder.startOfString().word().oneOrMore().endOfString().build();
    EXPECT_EQ(pattern, R"(^\w+$)");
}

TEST_F(RegexUtilsTest, RegexBuilder_WordBoundaries) {
    RegexBuilder builder;
    std::string pattern = builder.wordBoundary().word().oneOrMore().wordBoundary().build();
    EXPECT_EQ(pattern, R"(\b\w+\b)");
}

TEST_F(RegexUtilsTest, RegexBuilder_NonWordBoundary) {
    RegexBuilder builder;
    std::string pattern = builder.notWordBoundary().word().oneOrMore().build();
    EXPECT_EQ(pattern, R"(\B\w+)");
}

TEST_F(RegexUtilsTest, RegexBuilder_Groups) {
    RegexBuilder builder;
    std::string pattern = builder.startGroup().word().oneOrMore().endGroup().build();
    EXPECT_EQ(pattern, R"((\w+))");
}

TEST_F(RegexUtilsTest, RegexBuilder_NonCapturingGroups) {
    RegexBuilder builder;
    std::string pattern = builder.startNonCapturingGroup().word().oneOrMore().endGroup().build();
    EXPECT_EQ(pattern, R"((?:\w+))");
}

TEST_F(RegexUtilsTest, RegexBuilder_Alternatives) {
    RegexBuilder builder;
    std::string pattern = builder.literal("cat").or_().literal("dog").build();
    EXPECT_EQ(pattern, "cat|dog");
}

TEST_F(RegexUtilsTest, RegexBuilder_CommonPatterns) {
    RegexBuilder builder;

    // Test email pattern
    builder.reset();
    std::string emailPat = builder.email().build();
    EXPECT_FALSE(emailPat.empty());

    // Test URL pattern
    builder.reset();
    std::string urlPat = builder.url().build();
    EXPECT_FALSE(urlPat.empty());

    // Test phone pattern
    builder.reset();
    std::string phonePat = builder.phoneNumber().build();
    EXPECT_FALSE(phonePat.empty());

    // Test IP pattern
    builder.reset();
    std::string ipPat = builder.ipAddress().build();
    EXPECT_FALSE(ipPat.empty());

    // Test date pattern
    builder.reset();
    std::string datePat = builder.date().build();
    EXPECT_FALSE(datePat.empty());

    // Test time pattern
    builder.reset();
    std::string timePat = builder.time().build();
    EXPECT_FALSE(timePat.empty());
}

TEST_F(RegexUtilsTest, RegexBuilder_BuildRegex) {
    RegexBuilder builder;
    builder.digit().oneOrMore();

    std::regex regex = builder.buildRegex();
    EXPECT_TRUE(std::regex_match("123", regex));
    EXPECT_FALSE(std::regex_match("abc", regex));
}

TEST_F(RegexUtilsTest, RegexBuilder_Reset) {
    RegexBuilder builder;
    builder.literal("test");
    EXPECT_FALSE(builder.build().empty());

    builder.reset();
    EXPECT_TRUE(builder.build().empty());
}

TEST_F(RegexUtilsTest, RegexBuilder_ComplexPattern) {
    RegexBuilder builder;
    std::string pattern = builder
        .startOfString()
        .startGroup()
        .digit().exactly(3)
        .endGroup()
        .literal("-")
        .startGroup()
        .digit().exactly(2)
        .endGroup()
        .literal("-")
        .startGroup()
        .digit().exactly(4)
        .endGroup()
        .endOfString()
        .build();

    EXPECT_EQ(pattern, R"(^(\d{3})-(\d{2})-(\d{4})$)");

    std::regex regex = builder.buildRegex();
    EXPECT_TRUE(std::regex_match("123-45-6789", regex));
    EXPECT_FALSE(std::regex_match("12-345-6789", regex));
}

// =============================================================================
// RegexUtils Matching Tests
// =============================================================================

TEST_F(RegexUtilsTest, RegexUtils_BasicMatching) {
    EXPECT_TRUE(RegexUtils::matches("hello", "hello"));
    EXPECT_FALSE(RegexUtils::matches("hello", "world"));
    EXPECT_TRUE(RegexUtils::matches("123", R"(\d+)"));
}

TEST_F(RegexUtilsTest, RegexUtils_MatchingWithCompiledRegex) {
    std::regex regex(R"(\d+)");
    EXPECT_TRUE(RegexUtils::matches("123", regex));
    EXPECT_FALSE(RegexUtils::matches("abc", regex));
}

TEST_F(RegexUtilsTest, RegexUtils_InvalidPattern) {
    // Invalid regex pattern should return false
    EXPECT_FALSE(RegexUtils::matches("test", "[invalid"));
}

TEST_F(RegexUtilsTest, RegexUtils_FindFirst) {
    auto result = RegexUtils::findFirst(testText, emailPattern);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->str(), "<EMAIL>");
}

TEST_F(RegexUtilsTest, RegexUtils_FindFirstNoMatch) {
    auto result = RegexUtils::findFirst("no email here", emailPattern);
    EXPECT_FALSE(result.has_value());
}

TEST_F(RegexUtilsTest, RegexUtils_FindAll) {
    std::string text = "Email1: <EMAIL>, Email2: <EMAIL>";
    auto results = RegexUtils::findAll(text, emailPattern);
    EXPECT_EQ(results.size(), 2);
    EXPECT_EQ(results[0].str(), "<EMAIL>");
    EXPECT_EQ(results[1].str(), "<EMAIL>");
}

TEST_F(RegexUtilsTest, RegexUtils_FindAllNoMatches) {
    auto results = RegexUtils::findAll("no emails here", emailPattern);
    EXPECT_TRUE(results.empty());
}

// =============================================================================
// RegexUtils Replacement Tests
// =============================================================================

TEST_F(RegexUtilsTest, RegexUtils_ReplaceFirst) {
    std::string text = "hello world hello universe";
    std::string result = RegexUtils::replaceFirst(text, "hello", "hi");
    EXPECT_EQ(result, "hi world hello universe");
}

TEST_F(RegexUtilsTest, RegexUtils_ReplaceAll) {
    std::string text = "hello world hello universe";
    std::string result = RegexUtils::replaceAll(text, "hello", "hi");
    EXPECT_EQ(result, "hi world hi universe");
}

TEST_F(RegexUtilsTest, RegexUtils_ReplaceWithGroups) {
    std::string text = "John Doe";
    std::string result = RegexUtils::replaceFirst(text, R"((\w+) (\w+))", "$2, $1");
    EXPECT_EQ(result, "Doe, John");
}

TEST_F(RegexUtilsTest, RegexUtils_ReplaceInvalidPattern) {
    std::string text = "hello world";
    std::string result = RegexUtils::replaceFirst(text, "[invalid", "hi");
    EXPECT_EQ(result, text); // Should return original text on error
}

// =============================================================================
// RegexUtils Splitting Tests
// =============================================================================

TEST_F(RegexUtilsTest, RegexUtils_Split) {
    std::string text = "apple,banana,cherry";
    auto results = RegexUtils::split(text, ",");
    EXPECT_EQ(results.size(), 3);
    EXPECT_EQ(results[0], "apple");
    EXPECT_EQ(results[1], "banana");
    EXPECT_EQ(results[2], "cherry");
}

TEST_F(RegexUtilsTest, RegexUtils_SplitWithWhitespace) {
    std::string text = "apple banana cherry";
    auto results = RegexUtils::split(text, R"(\s+)");
    EXPECT_EQ(results.size(), 3);
    EXPECT_EQ(results[0], "apple");
    EXPECT_EQ(results[1], "banana");
    EXPECT_EQ(results[2], "cherry");
}

TEST_F(RegexUtilsTest, RegexUtils_SplitNoMatches) {
    std::string text = "no separators here";
    auto results = RegexUtils::split(text, ",");
    EXPECT_EQ(results.size(), 1);
    EXPECT_EQ(results[0], text);
}

TEST_F(RegexUtilsTest, RegexUtils_SplitInvalidPattern) {
    std::string text = "apple,banana,cherry";
    auto results = RegexUtils::split(text, "[invalid");
    EXPECT_EQ(results.size(), 1);
    EXPECT_EQ(results[0], text); // Should return original text as single element
}

// =============================================================================
// RegexUtils Extraction Tests
// =============================================================================

TEST_F(RegexUtilsTest, RegexUtils_ExtractGroups) {
    std::string text = "John Doe, Age: 30";
    auto groups = RegexUtils::extractGroups(text, R"((\w+) (\w+), Age: (\d+))");
    EXPECT_EQ(groups.size(), 3);
    EXPECT_EQ(groups[0], "John");
    EXPECT_EQ(groups[1], "Doe");
    EXPECT_EQ(groups[2], "30");
}

TEST_F(RegexUtilsTest, RegexUtils_ExtractGroupsNoMatch) {
    auto groups = RegexUtils::extractGroups("no match here", R"((\d+) (\d+))");
    EXPECT_TRUE(groups.empty());
}

TEST_F(RegexUtilsTest, RegexUtils_ExtractNamedGroups) {
    std::string text = "John Doe, Age: 30";
    std::vector<std::string> groupNames = {"first", "last", "age"};
    auto namedGroups = RegexUtils::extractNamedGroups(text, R"((\w+) (\w+), Age: (\d+))", groupNames);

    EXPECT_EQ(namedGroups.size(), 3);
    EXPECT_EQ(namedGroups["first"], "John");
    EXPECT_EQ(namedGroups["last"], "Doe");
    EXPECT_EQ(namedGroups["age"], "30");
}

TEST_F(RegexUtilsTest, RegexUtils_ExtractNamedGroupsPartial) {
    std::string text = "John Doe";
    std::vector<std::string> groupNames = {"first", "last", "extra"};
    auto namedGroups = RegexUtils::extractNamedGroups(text, R"((\w+) (\w+))", groupNames);

    EXPECT_EQ(namedGroups.size(), 2); // Only 2 groups matched
    EXPECT_EQ(namedGroups["first"], "John");
    EXPECT_EQ(namedGroups["last"], "Doe");
    EXPECT_EQ(namedGroups.find("extra"), namedGroups.end());
}

// =============================================================================
// RegexUtils Validation Tests
// =============================================================================

TEST_F(RegexUtilsTest, RegexUtils_ValidEmail) {
    EXPECT_TRUE(RegexUtils::isValidEmail("<EMAIL>"));
    EXPECT_TRUE(RegexUtils::isValidEmail("<EMAIL>"));
    EXPECT_FALSE(RegexUtils::isValidEmail("invalid.email"));
    EXPECT_FALSE(RegexUtils::isValidEmail("@domain.com"));
    EXPECT_FALSE(RegexUtils::isValidEmail("user@"));
}

TEST_F(RegexUtilsTest, RegexUtils_ValidUrl) {
    EXPECT_TRUE(RegexUtils::isValidUrl("http://example.com"));
    EXPECT_TRUE(RegexUtils::isValidUrl("https://www.example.com/path?query=value"));
    EXPECT_FALSE(RegexUtils::isValidUrl("ftp://example.com"));
    EXPECT_FALSE(RegexUtils::isValidUrl("not-a-url"));
}

TEST_F(RegexUtilsTest, RegexUtils_ValidPhoneNumber) {
    EXPECT_TRUE(RegexUtils::isValidPhoneNumber("+1234567890"));
    EXPECT_TRUE(RegexUtils::isValidPhoneNumber("1234567890"));
    EXPECT_FALSE(RegexUtils::isValidPhoneNumber("+0123456789")); // Starts with 0
    EXPECT_FALSE(RegexUtils::isValidPhoneNumber("123")); // Too short
}

TEST_F(RegexUtilsTest, RegexUtils_ValidIpAddress) {
    EXPECT_TRUE(RegexUtils::isValidIpAddress("***********"));
    EXPECT_TRUE(RegexUtils::isValidIpAddress("0.0.0.0"));
    EXPECT_TRUE(RegexUtils::isValidIpAddress("***************"));
    EXPECT_FALSE(RegexUtils::isValidIpAddress("256.1.1.1"));
    EXPECT_FALSE(RegexUtils::isValidIpAddress("192.168.1"));
    EXPECT_FALSE(RegexUtils::isValidIpAddress("***********.1"));
}

TEST_F(RegexUtilsTest, RegexUtils_ValidDate) {
    EXPECT_TRUE(RegexUtils::isValidDate("2023-12-25"));
    EXPECT_TRUE(RegexUtils::isValidDate("2000-01-01"));
    EXPECT_FALSE(RegexUtils::isValidDate("2023-13-01")); // Invalid month
    EXPECT_FALSE(RegexUtils::isValidDate("2023-12-32")); // Invalid day
    EXPECT_FALSE(RegexUtils::isValidDate("2023-02-30")); // Invalid day for February
    EXPECT_FALSE(RegexUtils::isValidDate("23-12-25")); // Wrong format
}

TEST_F(RegexUtilsTest, RegexUtils_ValidTime) {
    EXPECT_TRUE(RegexUtils::isValidTime("12:30:45"));
    EXPECT_TRUE(RegexUtils::isValidTime("00:00:00"));
    EXPECT_TRUE(RegexUtils::isValidTime("23:59:59"));
    EXPECT_FALSE(RegexUtils::isValidTime("24:00:00")); // Invalid hour
    EXPECT_FALSE(RegexUtils::isValidTime("12:60:00")); // Invalid minute
    EXPECT_FALSE(RegexUtils::isValidTime("12:30:60")); // Invalid second
    EXPECT_FALSE(RegexUtils::isValidTime("12:30")); // Wrong format
}

// =============================================================================
// RegexUtils Utility Tests
// =============================================================================

TEST_F(RegexUtilsTest, RegexUtils_Escape) {
    std::string text = "hello.world*test+";
    std::string escaped = RegexUtils::escape(text);
    EXPECT_EQ(escaped, "hello\\.world\\*test\\+");
}

TEST_F(RegexUtilsTest, RegexUtils_EscapeSpecialChars) {
    std::string text = "^$\\.*+?()[]{}|";
    std::string escaped = RegexUtils::escape(text);
    EXPECT_EQ(escaped, "\\^\\$\\\\\\.\\*\\+\\?\\(\\)\\[\\]\\{\\}\\|");
}

TEST_F(RegexUtilsTest, RegexUtils_EscapeNormalText) {
    std::string text = "normal text 123";
    std::string escaped = RegexUtils::escape(text);
    EXPECT_EQ(escaped, text); // Should remain unchanged
}

TEST_F(RegexUtilsTest, RegexUtils_IsValidPattern) {
    EXPECT_TRUE(RegexUtils::isValidPattern(R"(\d+)"));
    EXPECT_TRUE(RegexUtils::isValidPattern("hello"));
    EXPECT_FALSE(RegexUtils::isValidPattern("[invalid"));
    EXPECT_FALSE(RegexUtils::isValidPattern("(unclosed"));
}

TEST_F(RegexUtilsTest, RegexUtils_GetPatternError) {
    std::string error = RegexUtils::getPatternError(R"(\d+)");
    EXPECT_EQ(error, "Pattern is valid");

    std::string invalidError = RegexUtils::getPatternError("[invalid");
    EXPECT_NE(invalidError, "Pattern is valid");
    EXPECT_TRUE(invalidError.find("Regex error:") != std::string::npos);
}

// =============================================================================
// Convenience Functions Tests
// =============================================================================

TEST_F(RegexUtilsTest, ConvenienceFunctions_RegexMatch) {
    EXPECT_TRUE(regexMatch("123", R"(\d+)"));
    EXPECT_FALSE(regexMatch("abc", R"(\d+)"));
}

TEST_F(RegexUtilsTest, ConvenienceFunctions_RegexReplace) {
    std::string result = regexReplace("hello world", "hello", "hi");
    EXPECT_EQ(result, "hi world");
}

TEST_F(RegexUtilsTest, ConvenienceFunctions_RegexSplit) {
    auto results = regexSplit("a,b,c", ",");
    EXPECT_EQ(results.size(), 3);
    EXPECT_EQ(results[0], "a");
    EXPECT_EQ(results[1], "b");
    EXPECT_EQ(results[2], "c");
}

TEST_F(RegexUtilsTest, ConvenienceFunctions_RegexBuilder) {
    RegexBuilder builder = regex();
    std::string pattern = builder.digit().oneOrMore().build();
    EXPECT_EQ(pattern, R"(\d+)");
}

// =============================================================================
// Edge Cases and Error Handling Tests
// =============================================================================

TEST_F(RegexUtilsTest, EdgeCases_EmptyStrings) {
    EXPECT_TRUE(RegexUtils::matches("", ""));
    EXPECT_FALSE(RegexUtils::matches("", "test"));
    EXPECT_TRUE(RegexUtils::matches("test", ".*"));

    auto result = RegexUtils::findFirst("", "test");
    EXPECT_FALSE(result.has_value());

    auto results = RegexUtils::findAll("", "test");
    EXPECT_TRUE(results.empty());

    std::string replaced = RegexUtils::replaceAll("", "test", "replacement");
    EXPECT_EQ(replaced, "");

    auto split = RegexUtils::split("", ",");
    EXPECT_EQ(split.size(), 1);
    EXPECT_EQ(split[0], "");
}

TEST_F(RegexUtilsTest, EdgeCases_LargeStrings) {
    std::string largeText(10000, 'a');
    EXPECT_TRUE(RegexUtils::matches(largeText, "a+"));

    auto result = RegexUtils::findFirst(largeText, "a+");
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->str(), largeText);
}

TEST_F(RegexUtilsTest, EdgeCases_UnicodeText) {
    std::string unicodeText = "Hello 世界 🌍";
    EXPECT_TRUE(RegexUtils::matches(unicodeText, ".*"));

    auto result = RegexUtils::findFirst(unicodeText, "世界");
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->str(), "世界");
}

TEST_F(RegexUtilsTest, EdgeCases_ComplexPatterns) {
    // Test complex email validation
    std::string complexEmail = "<EMAIL>";
    EXPECT_TRUE(RegexUtils::isValidEmail(complexEmail));

    // Test complex URL validation
    std::string complexUrl = "https://user:<EMAIL>:8080/path/to/resource?param1=value1&param2=value2#section";
    // Note: Our URL pattern might not handle all these cases, but it should handle basic ones

    // Test nested groups
    std::string nestedPattern = R"(((a+)(b+))(c+))";
    auto groups = RegexUtils::extractGroups("aaabbbc", nestedPattern);
    EXPECT_EQ(groups.size(), 4);
    EXPECT_EQ(groups[0], "aaabbb"); // Group 1: (a+)(b+)
    EXPECT_EQ(groups[1], "aaa");    // Group 2: a+
    EXPECT_EQ(groups[2], "bbb");    // Group 3: b+
    EXPECT_EQ(groups[3], "c");      // Group 4: c+
}

// =============================================================================
// Performance and Stress Tests
// =============================================================================

TEST_F(RegexUtilsTest, Performance_RepeatedMatching) {
    std::regex compiledRegex(R"(\d+)");

    // Test that using compiled regex is consistent
    for (int i = 0; i < 100; ++i) {
        EXPECT_TRUE(RegexUtils::matches("123", compiledRegex));
        EXPECT_FALSE(RegexUtils::matches("abc", compiledRegex));
    }
}

TEST_F(RegexUtilsTest, Performance_MultipleReplacements) {
    std::string text = "The quick brown fox jumps over the lazy dog";

    // Multiple replacements should work correctly (case sensitive)
    text = RegexUtils::replaceAll(text, "The", "A");  // Replace "The" with capital T
    text = RegexUtils::replaceAll(text, "the", "a");  // Replace "the" with lowercase t
    text = RegexUtils::replaceAll(text, "quick", "slow");
    text = RegexUtils::replaceAll(text, "brown", "red");

    EXPECT_EQ(text, "A slow red fox jumps over a lazy dog");
}

// =============================================================================
// Integration Tests
// =============================================================================

TEST_F(RegexUtilsTest, Integration_EmailExtraction) {
    std::string text = "Contact <NAME_EMAIL> or <EMAIL> for more information.";

    auto emails = RegexUtils::findAll(text, RegexUtils::escape("@") + R"([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    // This won't work as expected, let's use proper email pattern

    auto properEmails = RegexUtils::findAll(text, R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    EXPECT_EQ(properEmails.size(), 2);
    EXPECT_EQ(properEmails[0].str(), "<EMAIL>");
    EXPECT_EQ(properEmails[1].str(), "<EMAIL>");
}

TEST_F(RegexUtilsTest, Integration_DataParsing) {
    std::string logEntry = "2023-12-25 14:30:15 [INFO] User <EMAIL> logged in from ***********00";

    // Extract date
    auto dateMatch = RegexUtils::findFirst(logEntry, R"(\d{4}-\d{2}-\d{2})");
    ASSERT_TRUE(dateMatch.has_value());
    EXPECT_EQ(dateMatch->str(), "2023-12-25");

    // Extract time
    auto timeMatch = RegexUtils::findFirst(logEntry, R"(\d{2}:\d{2}:\d{2})");
    ASSERT_TRUE(timeMatch.has_value());
    EXPECT_EQ(timeMatch->str(), "14:30:15");

    // Extract email
    auto emailMatch = RegexUtils::findFirst(logEntry, R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    ASSERT_TRUE(emailMatch.has_value());
    EXPECT_EQ(emailMatch->str(), "<EMAIL>");

    // Extract IP
    auto ipMatch = RegexUtils::findFirst(logEntry, R"(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})");
    ASSERT_TRUE(ipMatch.has_value());
    EXPECT_EQ(ipMatch->str(), "***********00");
}

TEST_F(RegexUtilsTest, Integration_RegexBuilderWithValidation) {
    // Build a pattern for validating a specific format
    RegexBuilder builder;
    std::string pattern = builder
        .startOfString()
        .literal("USER-")
        .digit().exactly(4)
        .literal("-")
        .range('A', 'Z').exactly(2)
        .endOfString()
        .build();

    EXPECT_EQ(pattern, R"(^USER-\d{4}-[A-Z]{2}$)");

    // Test the pattern
    EXPECT_TRUE(RegexUtils::matches("USER-1234-AB", pattern));
    EXPECT_FALSE(RegexUtils::matches("USER-123-AB", pattern));
    EXPECT_FALSE(RegexUtils::matches("USER-1234-ab", pattern));
    EXPECT_FALSE(RegexUtils::matches("user-1234-AB", pattern));
}
