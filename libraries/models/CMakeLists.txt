# libraries/models/CMakeLists.txt

add_library(models
    src/arcface/arcface.cpp
    src/yolo/yolo.cpp
)

target_include_directories(models
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# ==== GoogleTest FetchContent (nên dùng cho CI/CD, không phụ thuộc hệ thống) ====
include(Fetch<PERSON>ontent)
FetchContent_Declare(
    googletest
    URL https://github.com/google/googletest/archive/refs/heads/main.zip
)
# Nếu build test mới tải googletest
option(BUILD_MODELS_TESTS "Build tests for models lib" ON)
if(BUILD_MODELS_TESTS)
    FetchContent_MakeAvailable(googletest)
    enable_testing()

    add_executable(models_arcface_test test/arcface_test.cpp)
    target_link_libraries(models_arcface_test PRIVATE models GTest::gtest_main GTest::gtest)
    add_test(NAME models_arcface_test COMMAND models_arcface_test)

    add_executable(models_yolo_test test/yolo_test.cpp)
    target_link_libraries(models_yolo_test PRIVATE models GTest::gtest_main GTest::gtest)
    add_test(NAME models_yolo_test COMMAND models_yolo_test)
endif()