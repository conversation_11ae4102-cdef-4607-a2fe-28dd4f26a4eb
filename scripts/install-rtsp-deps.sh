#!/bin/bash

# RTSP Dependencies Installation Script
# Installs GStreamer, FFmpeg, Qt5, and other dependencies for RTSP functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Detect OS
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    else
        log_error "Cannot detect OS"
        exit 1
    fi
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Install dependencies for Ubuntu/Debian
install_ubuntu_debian() {
    log_step "Installing RTSP Dependencies for Ubuntu/Debian"
    
    # Update package list
    log_info "Updating package list..."
    apt-get update
    
    # Install GStreamer
    log_info "Installing GStreamer..."
    apt-get install -y \
        libgstreamer1.0-dev \
        libgstreamer-plugins-base1.0-dev \
        libgstreamer-plugins-bad1.0-dev \
        libgstreamer-plugins-good1.0-dev \
        gstreamer1.0-plugins-base \
        gstreamer1.0-plugins-good \
        gstreamer1.0-plugins-bad \
        gstreamer1.0-plugins-ugly \
        gstreamer1.0-libav \
        gstreamer1.0-tools \
        gstreamer1.0-rtsp
    
    # Install FFmpeg
    log_info "Installing FFmpeg..."
    apt-get install -y \
        libavformat-dev \
        libavcodec-dev \
        libavutil-dev \
        libswscale-dev \
        libavfilter-dev \
        ffmpeg
    
    # Install Qt5
    log_info "Installing Qt5..."
    apt-get install -y \
        qtbase5-dev \
        qtwebengine5-dev \
        libqt5webkit5-dev \
        libqt5network5 \
        libqt5widgets5 \
        libqt5core5a \
        qt5-qmake \
        qttools5-dev-tools
    
    # Install OpenSSL
    log_info "Installing OpenSSL..."
    apt-get install -y \
        libssl-dev \
        openssl
    
    # Install JSON library
    log_info "Installing JSON library..."
    apt-get install -y \
        nlohmann-json3-dev
    
    # Install build tools
    log_info "Installing build tools..."
    apt-get install -y \
        build-essential \
        cmake \
        pkg-config \
        git
    
    log_success "Ubuntu/Debian dependencies installed successfully"
}

# Install dependencies for CentOS/RHEL/Fedora
install_redhat() {
    log_step "Installing RTSP Dependencies for Red Hat based systems"
    
    if command -v dnf >/dev/null 2>&1; then
        PKG_MGR="dnf"
    else
        PKG_MGR="yum"
    fi
    
    # Enable EPEL repository for CentOS/RHEL
    if [[ "$OS" == "centos" || "$OS" == "rhel" ]]; then
        $PKG_MGR install -y epel-release
    fi
    
    # Install GStreamer
    log_info "Installing GStreamer..."
    $PKG_MGR install -y \
        gstreamer1-devel \
        gstreamer1-plugins-base-devel \
        gstreamer1-plugins-good \
        gstreamer1-plugins-bad-free \
        gstreamer1-plugins-ugly-free \
        gstreamer1-libav
    
    # Install FFmpeg (may require RPM Fusion)
    log_info "Installing FFmpeg..."
    $PKG_MGR install -y \
        ffmpeg-devel \
        ffmpeg
    
    # Install Qt5
    log_info "Installing Qt5..."
    $PKG_MGR install -y \
        qt5-qtbase-devel \
        qt5-qtwebengine-devel \
        qt5-qttools-devel
    
    # Install OpenSSL
    log_info "Installing OpenSSL..."
    $PKG_MGR install -y \
        openssl-devel
    
    # Install JSON library
    log_info "Installing JSON library..."
    $PKG_MGR install -y \
        json-devel
    
    # Install build tools
    log_info "Installing build tools..."
    $PKG_MGR install -y \
        gcc-c++ \
        cmake \
        pkgconfig \
        git
    
    log_success "Red Hat based system dependencies installed successfully"
}

# Install dependencies for Arch Linux
install_arch() {
    log_step "Installing RTSP Dependencies for Arch Linux"
    
    # Update package database
    pacman -Sy
    
    # Install GStreamer
    log_info "Installing GStreamer..."
    pacman -S --noconfirm \
        gstreamer \
        gst-plugins-base \
        gst-plugins-good \
        gst-plugins-bad \
        gst-plugins-ugly \
        gst-libav
    
    # Install FFmpeg
    log_info "Installing FFmpeg..."
    pacman -S --noconfirm ffmpeg
    
    # Install Qt5
    log_info "Installing Qt5..."
    pacman -S --noconfirm \
        qt5-base \
        qt5-webengine \
        qt5-tools
    
    # Install OpenSSL
    log_info "Installing OpenSSL..."
    pacman -S --noconfirm openssl
    
    # Install JSON library
    log_info "Installing JSON library..."
    pacman -S --noconfirm nlohmann-json
    
    # Install build tools
    log_info "Installing build tools..."
    pacman -S --noconfirm \
        base-devel \
        cmake \
        pkgconf \
        git
    
    log_success "Arch Linux dependencies installed successfully"
}

# Install RockChip specific dependencies (for RK3588)
install_rockchip_deps() {
    log_step "Installing RockChip RK3588 Specific Dependencies"
    
    log_warning "RockChip specific GStreamer plugins require custom installation"
    log_info "Please refer to RockChip documentation for:"
    log_info "  - gstreamer-rockchip-1.0"
    log_info "  - gstreamer-mpp-1.0"
    log_info "  - MPP (Media Process Platform) libraries"
    log_info "  - RGA (Raster Graphic Acceleration) libraries"
    
    # These are typically provided by RockChip SDK or custom builds
    log_info "For Orange Pi 5 Plus/Ultra, check Orange Pi official repositories"
}

# Verify installation
verify_installation() {
    log_step "Verifying Installation"
    
    # Check GStreamer
    if pkg-config --exists gstreamer-1.0; then
        local gst_version=$(pkg-config --modversion gstreamer-1.0)
        log_success "GStreamer found: $gst_version"
    else
        log_error "GStreamer not found"
    fi
    
    # Check FFmpeg
    if pkg-config --exists libavformat; then
        local ffmpeg_version=$(pkg-config --modversion libavformat)
        log_success "FFmpeg found: $ffmpeg_version"
    else
        log_warning "FFmpeg not found"
    fi
    
    # Check Qt5
    if pkg-config --exists Qt5Core; then
        local qt_version=$(pkg-config --modversion Qt5Core)
        log_success "Qt5 found: $qt_version"
    else
        log_error "Qt5 not found"
    fi
    
    # Check OpenSSL
    if pkg-config --exists openssl; then
        local ssl_version=$(pkg-config --modversion openssl)
        log_success "OpenSSL found: $ssl_version"
    else
        log_warning "OpenSSL not found"
    fi
    
    # Check JSON
    if pkg-config --exists nlohmann_json; then
        log_success "nlohmann_json found"
    else
        log_warning "nlohmann_json not found (will use fallback)"
    fi
}

# Main installation function
main() {
    log_step "RTSP Dependencies Installer"
    
    check_root
    detect_os
    
    log_info "Detected OS: $OS $VERSION"
    
    case "$OS" in
        ubuntu|debian)
            install_ubuntu_debian
            ;;
        centos|rhel|fedora)
            install_redhat
            ;;
        arch|manjaro)
            install_arch
            ;;
        *)
            log_error "Unsupported OS: $OS"
            log_info "Please install dependencies manually:"
            echo "  - GStreamer 1.18+ with RTSP, app, video, and RTP plugins"
            echo "  - FFmpeg 4.0+ with libavformat, libavcodec, libavutil, libswscale"
            echo "  - Qt5 with Core, Widgets, Network, WebEngine components"
            echo "  - OpenSSL development libraries"
            echo "  - nlohmann_json library"
            echo "  - CMake 3.18+, pkg-config, build tools"
            exit 1
            ;;
    esac
    
    # Install RockChip specific dependencies if requested
    if [[ "${1:-}" == "--rockchip" ]]; then
        install_rockchip_deps
    fi
    
    verify_installation
    
    log_success "Installation complete!"
    log_info "You can now build the RTSP library with:"
    log_info "  mkdir build && cd build"
    log_info "  cmake .."
    log_info "  make -j\$(nproc)"
}

# Show help
show_help() {
    cat << EOF
RTSP Dependencies Installer
==========================

This script installs all dependencies required for the RTSP library.

USAGE:
    sudo $0 [OPTIONS]

OPTIONS:
    --rockchip      Also install RockChip RK3588 specific dependencies
    --help          Show this help

DEPENDENCIES INSTALLED:
    - GStreamer 1.18+ (core, RTSP, app, video, RTP plugins)
    - FFmpeg 4.0+ (libavformat, libavcodec, libavutil, libswscale)
    - Qt5 (Core, Widgets, Network, WebEngine, WebEngineWidgets)
    - OpenSSL development libraries
    - nlohmann_json library
    - Build tools (CMake, pkg-config, compilers)

SUPPORTED SYSTEMS:
    - Ubuntu/Debian
    - CentOS/RHEL/Fedora
    - Arch Linux/Manjaro

For RK3588 (Orange Pi 5 Plus/Ultra):
    Use --rockchip flag for additional hardware acceleration support.
EOF
}

# Handle command line arguments
case "${1:-install}" in
    --help|-h|help)
        show_help
        ;;
    --rockchip)
        main --rockchip
        ;;
    install|"")
        main
        ;;
    *)
        echo "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
