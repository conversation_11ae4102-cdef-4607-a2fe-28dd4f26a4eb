#include <iostream>
#include <iomanip>
#include <utils/number_utils.hpp>

int main() {
    std::cout << "=== C-AIBOX Number Utils Demo ===" << std::endl;

    // 1. Number Parsing
    std::cout << "\n1. Number Parsing:" << std::endl;
    auto intResult = utils::NumberUtils::parseInt("123");
    if (intResult.has_value()) {
        std::cout << "Parsed '123' as int: " << intResult.value() << std::endl;
    }

    auto doubleResult = utils::NumberUtils::parseDouble("123.456");
    if (doubleResult.has_value()) {
        std::cout << "Parsed '123.456' as double: " << doubleResult.value() << std::endl;
    }

    auto invalidResult = utils::NumberUtils::parseInt("abc");
    std::cout << "Parsing 'abc' as int: " << (invalidResult.has_value() ? "Success" : "Failed") << std::endl;

    // Convenience functions
    auto convInt = utils::parseInt("789");
    auto convDouble = utils::parseDouble("987.654");
    std::cout << "Convenience parseInt('789'): " << (convInt.has_value() ? std::to_string(convInt.value()) : "Failed") << std::endl;
    std::cout << "Convenience parseDouble('987.654'): " << (convDouble.has_value() ? std::to_string(convDouble.value()) : "Failed") << std::endl;

    // 2. Number to String Conversion
    std::cout << "\n2. Number to String Conversion:" << std::endl;
    std::cout << "123 in base 10: " << utils::NumberUtils::toString(123LL) << std::endl;
    std::cout << "255 in base 16: " << utils::NumberUtils::toString(255LL, 16) << std::endl;
    std::cout << "8 in base 2: " << utils::NumberUtils::toString(8LL, 2) << std::endl;
    std::cout << "123.456 with 2 decimals: " << utils::NumberUtils::toString(123.456, 2, true) << std::endl;
    std::cout << "Convenience toString(456.789): " << utils::toString(456.789) << std::endl;

    // 3. Number Formatting
    std::cout << "\n3. Number Formatting:" << std::endl;
    std::cout << "1234567 with separators: " << utils::NumberUtils::formatWithSeparator(1234567LL) << std::endl;
    std::cout << "1234567.89 with separators: " << utils::NumberUtils::formatWithSeparator(1234567.89, ",", 2) << std::endl;
    std::cout << "1234567 with dots: " << utils::NumberUtils::formatWithSeparator(1234567LL, ".") << std::endl;

    // 4. Floating Point Comparison
    std::cout << "\n4. Floating Point Comparison:" << std::endl;
    std::cout << "1.0 == 1.0000000001? " << (utils::NumberUtils::isEqual(1.0, 1.0000000001) ? "Yes" : "No") << std::endl;
    std::cout << "1.0 == 1.1? " << (utils::NumberUtils::isEqual(1.0, 1.1) ? "Yes" : "No") << std::endl;
    std::cout << "0.0000000001 is zero? " << (utils::NumberUtils::isZero(0.0000000001) ? "Yes" : "No") << std::endl;
    std::cout << "Convenience isEqual(2.0, 2.0): " << (utils::isEqual(2.0, 2.0) ? "Yes" : "No") << std::endl;

    // 5. Number Properties
    std::cout << "\n5. Number Properties:" << std::endl;
    std::cout << "5 is positive? " << (utils::NumberUtils::isPositive(5) ? "Yes" : "No") << std::endl;
    std::cout << "-3 is negative? " << (utils::NumberUtils::isNegative(-3) ? "Yes" : "No") << std::endl;
    std::cout << "4 is even? " << (utils::NumberUtils::isEven(4) ? "Yes" : "No") << std::endl;
    std::cout << "7 is odd? " << (utils::NumberUtils::isOdd(7) ? "Yes" : "No") << std::endl;
    std::cout << "17 is prime? " << (utils::NumberUtils::isPrime(17) ? "Yes" : "No") << std::endl;
    std::cout << "15 is prime? " << (utils::NumberUtils::isPrime(15) ? "Yes" : "No") << std::endl;
    std::cout << "16 is perfect square? " << (utils::NumberUtils::isPerfectSquare(16) ? "Yes" : "No") << std::endl;
    std::cout << "15 is perfect square? " << (utils::NumberUtils::isPerfectSquare(15) ? "Yes" : "No") << std::endl;

    // 6. Math Operations
    std::cout << "\n6. Math Operations:" << std::endl;
    std::cout << "Clamp 15 to [1, 10]: " << utils::NumberUtils::clamp(15, 1, 10) << std::endl;
    std::cout << "Clamp -5 to [1, 10]: " << utils::NumberUtils::clamp(-5, 1, 10) << std::endl;
    std::cout << "Absolute value of -7: " << utils::NumberUtils::abs(-7) << std::endl;
    std::cout << "Min of 3 and 7: " << utils::NumberUtils::min(3, 7) << std::endl;
    std::cout << "Max of 3 and 7: " << utils::NumberUtils::max(3, 7) << std::endl;
    std::cout << "Round 3.14159 to 2 decimals: " << utils::NumberUtils::round(3.14159, 2) << std::endl;
    std::cout << "Ceil 3.14159 to 2 decimals: " << utils::NumberUtils::ceil(3.14159, 2) << std::endl;
    std::cout << "Floor 3.14159 to 2 decimals: " << utils::NumberUtils::floor(3.14159, 2) << std::endl;

    // 7. Percentage Operations
    std::cout << "\n7. Percentage Operations:" << std::endl;
    std::cout << "25 is what % of 100? " << utils::NumberUtils::percentage(25, 100) << "%" << std::endl;
    std::cout << "1 is what % of 4? " << utils::NumberUtils::percentage(1, 4) << "%" << std::endl;
    std::cout << "25% of 200 is: " << utils::NumberUtils::percentageOf(200, 25) << std::endl;
    std::cout << "50% of 100 is: " << utils::NumberUtils::percentageOf(100, 50) << std::endl;

    // 8. Random Numbers
    std::cout << "\n8. Random Numbers:" << std::endl;
    std::cout << "Random int [1, 10]: ";
    for (int i = 0; i < 5; ++i) {
        std::cout << utils::NumberUtils::randomInt(1, 10) << " ";
    }
    std::cout << std::endl;

    std::cout << "Random double [0, 1): ";
    for (int i = 0; i < 5; ++i) {
        std::cout << std::fixed << std::setprecision(3) << utils::NumberUtils::randomDouble() << " ";
    }
    std::cout << std::endl;

    std::cout << "Convenience randomInt(5, 15): " << utils::randomInt(5, 15) << std::endl;

    // 9. Mathematical Functions
    std::cout << "\n9. Mathematical Functions:" << std::endl;
    std::cout << "GCD of 12 and 8: " << utils::NumberUtils::gcd(12, 8) << std::endl;
    std::cout << "LCM of 4 and 6: " << utils::NumberUtils::lcm(4, 6) << std::endl;
    std::cout << "Factorial of 5: " << utils::NumberUtils::factorial(5) << std::endl;
    std::cout << "2^3: " << utils::NumberUtils::power(2.0, 3.0) << std::endl;
    std::cout << "Square root of 16: " << utils::NumberUtils::sqrt(16.0) << std::endl;
    std::cout << "Log10 of 100: " << utils::NumberUtils::log10(100.0) << std::endl;
    std::cout << "Natural log of e: " << utils::NumberUtils::ln(2.718281828) << std::endl;

    // 10. Angle Conversions
    std::cout << "\n10. Angle Conversions:" << std::endl;
    std::cout << "180 degrees to radians: " << utils::NumberUtils::toRadians(180.0) << std::endl;
    std::cout << "π radians to degrees: " << utils::NumberUtils::toDegrees(3.14159265359) << std::endl;
    std::cout << "90 degrees to radians: " << utils::NumberUtils::toRadians(90.0) << std::endl;

    // 11. Range and Mapping
    std::cout << "\n11. Range and Mapping:" << std::endl;
    std::cout << "Is 5 in range [1, 10]? " << (utils::NumberUtils::inRange(5, 1, 10) ? "Yes" : "No") << std::endl;
    std::cout << "Is 15 in range [1, 10]? " << (utils::NumberUtils::inRange(15, 1, 10) ? "Yes" : "No") << std::endl;
    std::cout << "Linear interpolation between 0 and 10 at 0.5: " << utils::NumberUtils::lerp(0.0, 10.0, 0.5) << std::endl;
    std::cout << "Map 5 from [0,10] to [0,100]: " << utils::NumberUtils::map(5.0, 0.0, 10.0, 0.0, 100.0) << std::endl;
    std::cout << "Map 2 from [0,4] to [10,20]: " << utils::NumberUtils::map(2.0, 0.0, 4.0, 10.0, 20.0) << std::endl;

    // 12. Edge Cases and Error Handling
    std::cout << "\n12. Edge Cases and Error Handling:" << std::endl;
    auto overflowResult = utils::NumberUtils::parseInt("999999999999999999999");
    std::cout << "Parsing overflow number: " << (overflowResult.has_value() ? "Success" : "Failed") << std::endl;

    auto emptyResult = utils::NumberUtils::parseDouble("");
    std::cout << "Parsing empty string: " << (emptyResult.has_value() ? "Success" : "Failed") << std::endl;

    std::cout << "Square root of -1: " << utils::NumberUtils::sqrt(-1.0) << " (NaN)" << std::endl;
    std::cout << "Division by zero (percentage): " << utils::NumberUtils::percentage(100, 0) << std::endl;

    // 13. Complex Examples
    std::cout << "\n13. Complex Examples:" << std::endl;

    // Calculate compound interest
    double principal = 1000.0;
    double rate = 0.05; // 5%
    int years = 10;
    double amount = principal * utils::NumberUtils::power(1 + rate, years);
    std::cout << "Compound interest: $" << principal << " at " << (rate * 100) << "% for " << years << " years = $"
              << utils::NumberUtils::round(amount, 2) << std::endl;

    // Distance between two points
    double x1 = 0, y1 = 0, x2 = 3, y2 = 4;
    double distance = utils::NumberUtils::sqrt(utils::NumberUtils::power(x2 - x1, 2) + utils::NumberUtils::power(y2 - y1, 2));
    std::cout << "Distance between (0,0) and (3,4): " << distance << std::endl;

    // Generate a random password-like string using numbers
    std::cout << "Random 6-digit PIN: ";
    for (int i = 0; i < 6; ++i) {
        std::cout << utils::NumberUtils::randomInt(0, 9);
    }
    std::cout << std::endl;

    std::cout << "\n=== Demo completed ===" << std::endl;
    return 0;
}
