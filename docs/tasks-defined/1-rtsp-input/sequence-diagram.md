# RTSP Input Module Sequence Diagrams

This document provides detailed sequence diagrams for the RTSP Input Module, illustrating the logical flow and component interactions.

## Module Initialization Sequence

```mermaid
sequenceDiagram
    participant Main as Main Application
    participant RTSP as RTSPInputModule
    participant Conn<PERSON>g<PERSON> as ConnectionManager
    participant Thread<PERSON><PERSON> as ThreadPool
    participant Queue as PacketQueue

    Main->>RTSP: initialize(config)
    activate RTSP
    RTSP->>ConnMgr: create()
    activate ConnMgr
    RTSP->>ThreadPool: create(num_threads)
    activate ThreadPool
    RTSP->>Queue: createQueueForEachCamera()
    activate Queue
    RTSP-->>Main: initialization complete
    deactivate RTSP
```

## Camera Connection Sequence

```mermaid
sequenceDiagram
    participant App as Application
    participant RTSP as RTSPInputModule
    participant ConnMgr as ConnectionManager
    participant Thread as CameraThread
    participant FFmpeg as FFmpegClient
    participant Queue as PacketQueue

    App->>RTSP: addCamera(rtsp_url, camera_id, params)
    activate RTSP
    RTSP->>ConnMgr: createConnection(rtsp_url, camera_id)
    activate ConnMgr
    ConnMgr->>Thread: spawnThread(camera_id)
    activate Thread
    Thread->>FFmpeg: initializeConnection(rtsp_url, params)
    activate FFmpeg
    
    alt Connection Successful
        FFmpeg-->>Thread: connection established
        Thread->>ConnMgr: updateStatus(camera_id, CONNECTED)
        ConnMgr-->>RTSP: connection success
        RTSP-->>App: camera added successfully
    else Connection Failed
        FFmpeg-->>Thread: connection error
        Thread->>ConnMgr: updateStatus(camera_id, FAILED)
        Thread->>Thread: scheduleRetry(exponentialBackoff)
        ConnMgr-->>RTSP: connection failed
        RTSP-->>App: camera connection failed, retry scheduled
    end
    
    deactivate FFmpeg
    deactivate Thread
    deactivate ConnMgr
    deactivate RTSP
```

## Packet Reception and Processing Sequence

```mermaid
sequenceDiagram
    participant Thread as CameraThread
    participant FFmpeg as FFmpegClient
    participant Parser as NALParser
    participant Buffer as BufferPool
    participant Queue as PacketQueue
    participant Monitor as QueueMonitor
    
    activate Thread
    loop Until Stream Ends or Error
        Thread->>FFmpeg: readPacket()
        activate FFmpeg
        
        alt Packet Available
            FFmpeg-->>Thread: return packet
            Thread->>Parser: extractNALUnits(packet)
            activate Parser
            
            loop For Each NAL Unit
                Parser->>Buffer: getBuffer(required_size)
                activate Buffer
                Buffer-->>Parser: return buffer
                deactivate Buffer
                
                Parser->>Parser: copyNALToBuffer()
                Parser->>Queue: enqueue(nal_buffer, metadata)
                activate Queue
                
                alt Queue Full
                    Queue->>Monitor: reportQueueFull(camera_id)
                    activate Monitor
                    Monitor->>Thread: signalBackpressure()
                    deactivate Monitor
                else Queue Has Space
                    Queue-->>Parser: enqueue success
                end
                
                deactivate Queue
            end
            
            deactivate Parser
            
        else No Packet Available (EOF or Error)
            FFmpeg-->>Thread: return error/EOF
            Thread->>Thread: handleStreamError()
            
            alt Recoverable Error
                Thread->>FFmpeg: resetConnection()
                Thread->>Thread: wait(retryDelay)
            else Fatal Error
                Thread->>ConnMgr: updateStatus(DISCONNECTED)
                Thread->>Thread: terminateThread()
                deactivate Thread
            end
        end
        
        deactivate FFmpeg
    end
```

## Reconnection Sequence

```mermaid
sequenceDiagram
    participant ConnMgr as ConnectionManager
    participant Thread as CameraThread
    participant FFmpeg as FFmpegClient
    participant Timer as RetryTimer
    
    activate ConnMgr
    ConnMgr->>Timer: scheduleReconnection(camera_id, delay)
    activate Timer
    
    Timer-->>ConnMgr: reconnectionTimeout()
    ConnMgr->>Thread: reconnect(camera_id)
    activate Thread
    
    Thread->>FFmpeg: closeConnection()
    Thread->>FFmpeg: initializeConnection(stored_url, params)
    activate FFmpeg
    
    alt Connection Successful
        FFmpeg-->>Thread: connection established
        Thread->>ConnMgr: updateStatus(CONNECTED)
        ConnMgr->>Timer: cancelRetryTimer()
        deactivate Timer
    else Connection Failed
        FFmpeg-->>Thread: connection error
        Thread->>ConnMgr: updateStatus(FAILED)
        ConnMgr->>Timer: increaseRetryDelay(exponentialBackoff)
        Timer->>ConnMgr: scheduleNextRetry()
    end
    
    deactivate FFmpeg
    deactivate Thread
    deactivate ConnMgr
```

## Shutdown Sequence

```mermaid
sequenceDiagram
    participant App as Application
    participant RTSP as RTSPInputModule
    participant ConnMgr as ConnectionManager
    participant Threads as CameraThreads
    participant Queues as PacketQueues
    
    App->>RTSP: shutdown()
    activate RTSP
    RTSP->>ConnMgr: stopAllConnections()
    activate ConnMgr
    
    loop For Each Active Camera
        ConnMgr->>Threads: signalShutdown(camera_id)
        activate Threads
        Threads->>Threads: stopProcessing()
        Threads->>Queues: enqueueEOFMarker()
        deactivate Threads
    end
    
    RTSP->>RTSP: waitForThreadsToTerminate(timeout)
    RTSP->>Queues: finalizeAllQueues()
    
    RTSP-->>App: shutdown complete
    deactivate ConnMgr
    deactivate RTSP
```

## Error Handling Sequence

```mermaid
sequenceDiagram
    participant Thread as CameraThread
    participant ErrorHandler as ErrorHandler
    participant Logger as Logger
    participant ConnMgr as ConnectionManager
    participant App as Application
    
    activate Thread
    
    Thread->>Thread: detectError(error_type)
    Thread->>ErrorHandler: handleError(camera_id, error_type, details)
    activate ErrorHandler
    
    ErrorHandler->>Logger: logError(details)
    activate Logger
    Logger-->>ErrorHandler: logged
    deactivate Logger
    
    alt Network Error
        ErrorHandler->>ConnMgr: scheduleReconnection(camera_id)
        ErrorHandler-->>Thread: return RETRY
    else Protocol Error
        ErrorHandler->>ConnMgr: updateStatus(PROTOCOL_ERROR)
        ErrorHandler-->>Thread: return RESET_CONNECTION
    else Authentication Error
        ErrorHandler->>ConnMgr: updateStatus(AUTH_ERROR)
        ErrorHandler->>App: notifyAuthenticationFailure(camera_id)
        ErrorHandler-->>Thread: return TERMINATE
    else Fatal Error
        ErrorHandler->>ConnMgr: updateStatus(FATAL_ERROR)
        ErrorHandler->>App: notifyCameraFailure(camera_id, details)
        ErrorHandler-->>Thread: return TERMINATE
    end
    
    deactivate ErrorHandler
    
    alt Error Action is RETRY
        Thread->>Thread: wait(retryDelay)
        Thread->>Thread: continueProcessing()
    else Error Action is RESET_CONNECTION
        Thread->>Thread: resetConnection()
        Thread->>Thread: continueProcessing()
    else Error Action is TERMINATE
        Thread->>Thread: cleanupResources()
        Thread->>Thread: terminateThread()
        deactivate Thread
    end
```
