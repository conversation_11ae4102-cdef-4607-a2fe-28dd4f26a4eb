#include <gtest/gtest.h>
#include "rtsp/rtsp_config.hpp"
#include "rtsp/rtsp_types.hpp"

// Test integration with shared library if available
#ifdef HAVE_SHARED_LIBRARY
#include "shared/result.hpp"
#include "shared/error_category.hpp"
#endif

using namespace aibox::rtsp;

class RTSPIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup test environment
    }

    void TearDown() override {
        // Cleanup test environment
    }
};

// Test that RTSP library doesn't interfere with existing libraries
TEST_F(RTSPIntegrationTest, NoLibraryInterference) {
    // Test that we can create RTSP configurations without affecting other libraries
    auto rtsp_config = RTSPConfigManager::createDefault();
    EXPECT_TRUE(rtsp_config.isValid());
    
    // Test that Result<T> type works correctly
    auto validation_result = RTSPConfigManager::validate(rtsp_config);
    EXPECT_TRUE(validation_result || !validation_result);  // Should not crash
    
    // Test that error categories are properly defined
    EXPECT_NE(static_cast<int>(ErrorCategory::NETWORK_ERROR), static_cast<int>(ErrorCategory::CONFIGURATION_ERROR));
    EXPECT_NE(static_cast<int>(ErrorCategory::NETWORK_ERROR), static_cast<int>(ErrorCategory::HARDWARE_ERROR));
}

// Test that RTSP types are properly defined and don't conflict
TEST_F(RTSPIntegrationTest, TypeDefinitions) {
    // Test enum types
    StreamPriority priority = StreamPriority::HIGH;
    EXPECT_EQ(priority, StreamPriority::HIGH);
    
    TransportProtocol transport = TransportProtocol::TCP;
    EXPECT_EQ(transport, TransportProtocol::TCP);
    
    // Test struct types
    RTSPConnectionConfig connection;
    EXPECT_TRUE(connection.rtsp_url.empty());  // Default initialization
    
    RTSPPerformanceConfig performance;
    EXPECT_GT(performance.max_concurrent_streams, 0);  // Should have default value
    
    RTSPNetworkConfig network;
    EXPECT_TRUE(network.prefer_tcp);  // Default should be true
    
    RTSPMonitoringConfig monitoring;
    EXPECT_TRUE(monitoring.enable_statistics);  // Default should be true
}

// Test platform constants and compatibility
TEST_F(RTSPIntegrationTest, PlatformConstants) {
    // Test that platform constants are properly defined
    EXPECT_GT(platform::MEMORY_LIMIT_4GB_MB, 0);
    EXPECT_GT(platform::MEMORY_LIMIT_8GB_MB, platform::MEMORY_LIMIT_4GB_MB);
    EXPECT_GT(platform::MEMORY_LIMIT_16GB_MB, platform::MEMORY_LIMIT_8GB_MB);

    EXPECT_GT(platform::MAX_MPP_DECODER_INSTANCES, 0);
    EXPECT_LE(platform::MAX_MPP_DECODER_INSTANCES, 32);  // Reasonable upper bound

    // Test thermal constants
    EXPECT_GT(platform::MAX_SOC_TEMPERATURE, 0);
    EXPECT_GT(platform::THROTTLE_TEMPERATURE, 0);
    EXPECT_LT(platform::THROTTLE_TEMPERATURE, platform::MAX_SOC_TEMPERATURE);
}

// Test that RTSP library builds correctly with different configurations
TEST_F(RTSPIntegrationTest, BuildConfiguration) {
    // Test that compile-time definitions work
#ifdef RTSP_PLATFORM_RK3588
    EXPECT_TRUE(true);  // RK3588 platform is defined
#else
    EXPECT_TRUE(false);  // Should be defined for this project
#endif

#ifdef RTSP_HARDWARE_ACCELERATION
    EXPECT_TRUE(true);  // Hardware acceleration should be enabled
#else
    EXPECT_TRUE(false);  // Should be enabled for RK3588
#endif

    // Test optional features
#ifdef RTSP_USE_GSTREAMER
    // GStreamer support is available
    EXPECT_TRUE(true);
#endif

#ifdef RTSP_FFMPEG_FALLBACK
    // FFmpeg fallback is available
    EXPECT_TRUE(true);
#endif
}

// Test memory management and resource cleanup
TEST_F(RTSPIntegrationTest, ResourceManagement) {
    // Test that creating and destroying configurations doesn't leak memory
    for (int i = 0; i < 1000; ++i) {
        auto config = RTSPConfigManager::createFor8GB();
        
        // Add streams
        for (int j = 0; j < 10; ++j) {
            RTSPConnectionConfig stream;
            stream.rtsp_url = "rtsp://192.168.1.100:554/stream" + std::to_string(j);
            stream.username = "admin";
            stream.password = "password";
            config.streams.push_back(stream);
        }
        
        // Validate and merge
        auto validation_result = RTSPConfigManager::validate(config);
        auto base = RTSPConfigManager::createDefault();
        auto merged = RTSPConfigManager::merge(base, config);
        
        // Test JSON operations if available
        auto json_result = RTSPConfigManager::toJson(config);
        if (json_result) {
            auto parse_result = RTSPConfigManager::loadFromJson(*json_result);
        }
    }
    
    // If we reach here without crashing, memory management is working
    EXPECT_TRUE(true);
}

// Test that RTSP library works with different compiler optimizations
TEST_F(RTSPIntegrationTest, CompilerOptimizations) {
    // Test that optimized code still works correctly
    auto config = RTSPConfigManager::createFor8GB();
    
    // Perform operations that might be affected by optimization
    for (int i = 0; i < 100; ++i) {
        RTSPConnectionConfig stream;
        stream.rtsp_url = "rtsp://192.168.1." + std::to_string(100 + i) + ":554/stream1";
        stream.enabled = (i % 2 == 0);
        stream.priority = static_cast<StreamPriority>(i % 4);
        stream.transport = static_cast<TransportProtocol>(i % 3);
        config.streams.push_back(stream);
    }
    
    // Test that calculations are still correct
    size_t active_count = config.getActiveStreamCount();
    EXPECT_EQ(active_count, 50);  // Half should be enabled
    
    size_t memory_usage = config.getTotalEstimatedMemoryUsage();
    EXPECT_GT(memory_usage, 0);
    
    // Test validation
    auto result = RTSPConfigManager::validate(config);
    EXPECT_TRUE(result || !result);  // Should not crash
}

// Test error handling robustness
TEST_F(RTSPIntegrationTest, ErrorHandlingRobustness) {
    // Test that error handling doesn't interfere with normal operation
    std::vector<RTSPModuleConfig> configs;
    
    // Create various configurations, some valid, some invalid
    configs.push_back(RTSPConfigManager::createDefault());
    configs.push_back(RTSPConfigManager::createFor4GB());
    configs.push_back(RTSPConfigManager::createFor8GB());
    configs.push_back(RTSPConfigManager::createFor16GB());
    
    // Create invalid configuration
    RTSPModuleConfig invalid_config;
    invalid_config.performance.max_concurrent_streams = -1;
    configs.push_back(invalid_config);
    
    // Test that validation works for all configurations
    int valid_count = 0;
    int invalid_count = 0;
    
    for (const auto& config : configs) {
        auto result = RTSPConfigManager::validate(config);
        if (result) {
            valid_count++;
            EXPECT_TRUE(result.success);
        } else {
            invalid_count++;
            EXPECT_FALSE(result.success);
            EXPECT_NE(result.error_category, ErrorCategory::CONFIGURATION_ERROR);
            EXPECT_FALSE(result.error_message.empty());
        }
    }
    
    EXPECT_EQ(valid_count, 4);   // First 4 should be valid
    EXPECT_EQ(invalid_count, 1); // Last one should be invalid
}

// Test that the library works correctly in different threading scenarios
TEST_F(RTSPIntegrationTest, ThreadingCompatibility) {
    // Test that static functions are thread-safe
    std::vector<std::thread> threads;
    std::atomic<bool> all_success{true};
    
    for (int i = 0; i < 4; ++i) {
        threads.emplace_back([&, i]() {
            try {
                for (int j = 0; j < 50; ++j) {
                    auto config = RTSPConfigManager::createDefault();
                    auto result = RTSPConfigManager::validate(config);
                    
                    if (!result && config.isValid()) {
                        all_success = false;
                        break;
                    }
                }
            } catch (...) {
                all_success = false;
            }
        });
    }
    
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_TRUE(all_success.load());
}

// Test version and compatibility information
TEST_F(RTSPIntegrationTest, VersionCompatibility) {
    auto config = RTSPConfigManager::createDefault();
    
    // Test that version string is properly set
    EXPECT_FALSE(config.version.empty());
    EXPECT_EQ(config.version, "1.0.0");
    
    // Test that configuration is enabled by default
    EXPECT_TRUE(config.enabled);
    
    // Test that auto-detection is enabled by default
    EXPECT_TRUE(config.auto_detect_platform);
    
    // Test that platform override is empty by default
    EXPECT_TRUE(config.platform_override.empty());
}
