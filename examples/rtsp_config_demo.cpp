#include <iostream>
#include <iomanip>
#include "rtsp/rtsp_config.hpp"

using namespace aibox::rtsp;

void printConfigSummary(const RTSPModuleConfig& config) {
    std::cout << "\n=== RTSP Configuration Summary ===" << std::endl;
    std::cout << "Version: " << config.version << std::endl;
    std::cout << "Enabled: " << (config.enabled ? "Yes" : "No") << std::endl;
    std::cout << "Platform: " << (config.platform_override.empty() ? "Auto-detect" : config.platform_override) << std::endl;
    std::cout << "Valid: " << (config.isValid() ? "Yes" : "No") << std::endl;
    
    std::cout << "\n--- Performance Settings ---" << std::endl;
    std::cout << "Max concurrent streams: " << config.performance.max_concurrent_streams << std::endl;
    std::cout << "Memory limit: " << config.performance.max_memory_usage_mb << " MB" << std::endl;
    std::cout << "Thread pool size: " << config.performance.thread_pool_size << std::endl;
    std::cout << "CPU usage limit: " << config.performance.cpu_usage_limit_percent << "%" << std::endl;
    std::cout << "Hardware acceleration: " << (config.performance.enable_mpp_decoder ? "Enabled" : "Disabled") << std::endl;
    std::cout << "Thermal management: " << (config.performance.thermal_management ? "Enabled" : "Disabled") << std::endl;
    
    std::cout << "\n--- Network Settings ---" << std::endl;
    std::cout << "Prefer TCP: " << (config.network.prefer_tcp ? "Yes" : "No") << std::endl;
    std::cout << "Keep-alive interval: " << config.network.keep_alive_interval_ms << " ms" << std::endl;
    std::cout << "SSL verification: " << (config.network.ssl_verify_peer ? "Enabled" : "Disabled") << std::endl;
    
    std::cout << "\n--- Monitoring Settings ---" << std::endl;
    std::cout << "Statistics enabled: " << (config.monitoring.enable_statistics ? "Yes" : "No") << std::endl;
    std::cout << "Log level: " << config.monitoring.log_level << std::endl;
    std::cout << "Performance monitoring: " << (config.monitoring.enable_performance_monitoring ? "Yes" : "No") << std::endl;
    
    std::cout << "\n--- Streams ---" << std::endl;
    std::cout << "Total streams: " << config.streams.size() << std::endl;
    std::cout << "Active streams: " << config.getActiveStreamCount() << std::endl;
    std::cout << "Estimated memory usage: " << (config.getTotalEstimatedMemoryUsage() / (1024 * 1024)) << " MB" << std::endl;
    
    for (size_t i = 0; i < config.streams.size(); ++i) {
        const auto& stream = config.streams[i];
        std::cout << "\n  Stream " << (i + 1) << ":" << std::endl;
        std::cout << "    URL: " << stream.rtsp_url << std::endl;
        std::cout << "    Enabled: " << (stream.enabled ? "Yes" : "No") << std::endl;
        std::cout << "    Priority: ";
        switch (stream.priority) {
            case StreamPriority::LOW: std::cout << "Low"; break;
            case StreamPriority::MEDIUM: std::cout << "Medium"; break;
            case StreamPriority::HIGH: std::cout << "High"; break;
            case StreamPriority::CRITICAL: std::cout << "Critical"; break;
        }
        std::cout << std::endl;
        std::cout << "    Transport: ";
        switch (stream.transport) {
            case TransportProtocol::UDP: std::cout << "UDP"; break;
            case TransportProtocol::TCP: std::cout << "TCP"; break;
            case TransportProtocol::AUTO: std::cout << "Auto"; break;
        }
        std::cout << std::endl;
        std::cout << "    Resolution: " << stream.target_resolution.width << "x" << stream.target_resolution.height << std::endl;
        std::cout << "    Memory estimate: " << (stream.getEstimatedMemoryUsage() / (1024 * 1024)) << " MB" << std::endl;
        
        if (!stream.metadata.empty()) {
            std::cout << "    Metadata:" << std::endl;
            for (const auto& [key, value] : stream.metadata) {
                std::cout << "      " << key << ": " << value << std::endl;
            }
        }
    }
    std::cout << "=================================" << std::endl;
}

int main() {
    std::cout << "RTSP Configuration System Demo" << std::endl;
    std::cout << "==============================" << std::endl;
    
    // Demo 1: Create default configuration
    std::cout << "\n1. Creating default configuration..." << std::endl;
    auto default_config = RTSPConfigManager::createDefault();
    printConfigSummary(default_config);
    
    // Demo 2: Create platform-specific configurations
    std::cout << "\n2. Creating platform-specific configurations..." << std::endl;
    
    auto config_4gb = RTSPConfigManager::createFor4GB();
    std::cout << "\n--- 4GB Configuration ---" << std::endl;
    std::cout << "Max streams: " << config_4gb.performance.max_concurrent_streams << std::endl;
    std::cout << "Memory limit: " << config_4gb.performance.max_memory_usage_mb << " MB" << std::endl;
    
    auto config_8gb = RTSPConfigManager::createFor8GB();
    std::cout << "\n--- 8GB Configuration ---" << std::endl;
    std::cout << "Max streams: " << config_8gb.performance.max_concurrent_streams << std::endl;
    std::cout << "Memory limit: " << config_8gb.performance.max_memory_usage_mb << " MB" << std::endl;
    
    auto config_16gb = RTSPConfigManager::createFor16GB();
    std::cout << "\n--- 16GB Configuration ---" << std::endl;
    std::cout << "Max streams: " << config_16gb.performance.max_concurrent_streams << std::endl;
    std::cout << "Memory limit: " << config_16gb.performance.max_memory_usage_mb << " MB" << std::endl;
    
    // Demo 3: Add sample streams
    std::cout << "\n3. Adding sample streams to 8GB configuration..." << std::endl;
    
    RTSPConnectionConfig stream1;
    stream1.rtsp_url = "rtsp://192.168.1.100:554/stream1";
    stream1.username = "admin";
    stream1.password = "password";
    stream1.enabled = true;
    stream1.priority = StreamPriority::HIGH;
    stream1.transport = TransportProtocol::TCP;
    stream1.target_resolution = {1920, 1080};
    stream1.target_framerate = {30, 1};
    stream1.metadata["location"] = "entrance";
    stream1.metadata["zone"] = "security";
    
    RTSPConnectionConfig stream2;
    stream2.rtsp_url = "rtsp://192.168.1.101:554/stream1";
    stream2.username = "admin";
    stream2.password = "password";
    stream2.enabled = true;
    stream2.priority = StreamPriority::MEDIUM;
    stream2.transport = TransportProtocol::TCP;
    stream2.target_resolution = {1280, 720};
    stream2.target_framerate = {25, 1};
    stream2.metadata["location"] = "parking";
    stream2.metadata["zone"] = "monitoring";
    
    config_8gb.streams = {stream1, stream2};
    printConfigSummary(config_8gb);
    
    // Demo 4: Configuration validation
    std::cout << "\n4. Testing configuration validation..." << std::endl;
    
    auto validation_result = RTSPConfigManager::validate(config_8gb);
    if (validation_result) {
        std::cout << "✓ Configuration is valid" << std::endl;
    } else {
        std::cout << "✗ Configuration is invalid: " << validation_result.error_message << std::endl;
    }
    
    // Demo 5: Configuration merging
    std::cout << "\n5. Testing configuration merging..." << std::endl;
    
    auto base_config = RTSPConfigManager::createFor4GB();
    auto override_config = RTSPConfigManager::createFor8GB();
    override_config.version = "2.0.0";
    override_config.monitoring.log_level = "DEBUG";
    
    auto merged_config = RTSPConfigManager::merge(base_config, override_config);
    std::cout << "Merged configuration:" << std::endl;
    std::cout << "  Version: " << merged_config.version << " (from override)" << std::endl;
    std::cout << "  Max streams: " << merged_config.performance.max_concurrent_streams << " (from override)" << std::endl;
    std::cout << "  Log level: " << merged_config.monitoring.log_level << " (from override)" << std::endl;
    
    // Demo 6: JSON serialization (if available)
    std::cout << "\n6. Testing JSON serialization..." << std::endl;
    
    auto json_result = RTSPConfigManager::toJson(config_8gb);
    if (json_result) {
        std::cout << "✓ JSON serialization successful" << std::endl;
        std::cout << "JSON size: " << json_result->length() << " characters" << std::endl;
        
        // Try to parse it back
        auto parse_result = RTSPConfigManager::loadFromJson(*json_result);
        if (parse_result) {
            std::cout << "✓ JSON parsing successful" << std::endl;
            std::cout << "Parsed " << parse_result->streams.size() << " streams" << std::endl;
        } else {
            std::cout << "✗ JSON parsing failed: " << parse_result.error_message << std::endl;
        }
    } else {
        std::cout << "✗ JSON serialization failed: " << json_result.error_message << std::endl;
    }
    
    // Demo 7: File operations
    std::cout << "\n7. Testing file operations..." << std::endl;
    
    const std::string config_file = "/tmp/rtsp_demo_config.json";
    auto save_result = RTSPConfigManager::saveToFile(config_8gb, config_file);
    if (save_result && *save_result) {
        std::cout << "✓ Configuration saved to " << config_file << std::endl;
        
        auto load_result = RTSPConfigManager::loadFromFile(config_file);
        if (load_result) {
            std::cout << "✓ Configuration loaded from file" << std::endl;
            std::cout << "Loaded " << load_result->streams.size() << " streams" << std::endl;
        } else {
            std::cout << "✗ Failed to load configuration: " << load_result.error_message << std::endl;
        }
    } else {
        std::cout << "✗ Failed to save configuration: " << save_result.error_message << std::endl;
    }
    
    std::cout << "\nDemo completed!" << std::endl;
    return 0;
}
