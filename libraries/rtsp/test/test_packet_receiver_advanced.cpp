#include <gtest/gtest.h>
#include "rtsp/packet_receiver.hpp"
#include "rtsp/rtsp_types.hpp"
#include <thread>
#include <chrono>
#include <vector>
#include <random>

using namespace aibox::rtsp;

class PacketReceiverAdvancedTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration optimized for RK3588
        config_.rtsp_url = "rtsp://test.example.com:554/stream";
        config_.username = "testuser";
        config_.password = "testpass";
        config_.timeout_ms = 5000;
        config_.retry_count = 3;
        config_.transport = TransportProtocol::TCP;
        config_.use_mpp_decoder = true;
        config_.use_rga_scaler = true;
        config_.use_dmabuf_zerocopy = true;
        config_.buffer_size_bytes = 2 * 1024 * 1024;  // 2MB for RK3588
        config_.queue_size = 100;
        config_.priority = StreamPriority::HIGH;
        
        ASSERT_TRUE(config_.isValid());
    }
    
    RTSPConnectionConfig config_;
    
    // Helper to create malformed RTP packet
    std::vector<uint8_t> createMalformedRTPPacket() {
        std::vector<uint8_t> packet;
        packet.resize(8);  // Too small for valid RTP header
        
        // Invalid version
        packet[0] = 0x40;  // Version 1 (should be 2)
        packet[1] = 0x60;  // Payload type 96
        
        return packet;
    }
    
    // Helper to create RTP packet with extension
    std::vector<uint8_t> createRTPPacketWithExtension(uint16_t sequence_number) {
        std::vector<uint8_t> packet;
        packet.resize(16);  // 12 byte header + 4 byte extension
        
        // Version 2, no padding, extension present, no CSRC
        packet[0] = 0x90;  // Extension bit set
        packet[1] = 0x60;  // Payload type 96
        
        // Sequence number
        packet[2] = static_cast<uint8_t>((sequence_number >> 8) & 0xFF);
        packet[3] = static_cast<uint8_t>(sequence_number & 0xFF);

        // Timestamp
        uint32_t timestamp = 1000;
        packet[4] = static_cast<uint8_t>((timestamp >> 24) & 0xFF);
        packet[5] = static_cast<uint8_t>((timestamp >> 16) & 0xFF);
        packet[6] = static_cast<uint8_t>((timestamp >> 8) & 0xFF);
        packet[7] = static_cast<uint8_t>(timestamp & 0xFF);

        // SSRC
        uint32_t ssrc = 0x12345678;
        packet[8] = static_cast<uint8_t>((ssrc >> 24) & 0xFF);
        packet[9] = static_cast<uint8_t>((ssrc >> 16) & 0xFF);
        packet[10] = static_cast<uint8_t>((ssrc >> 8) & 0xFF);
        packet[11] = static_cast<uint8_t>(ssrc & 0xFF);
        
        // Extension header (4 bytes)
        packet[12] = 0x00;  // Extension profile
        packet[13] = 0x01;
        packet[14] = 0x00;  // Extension length (1 word)
        packet[15] = 0x01;
        
        return packet;
    }
};

// Test RTP packet structure validation
TEST_F(PacketReceiverAdvancedTest, RTPPacketStructureValidation) {
    // Test RTPHeader structure
    RTPHeader header;
    EXPECT_EQ(header.version, 2);
    EXPECT_EQ(header.padding, 0);
    EXPECT_EQ(header.extension, 0);
    EXPECT_EQ(header.csrc_count, 0);
    EXPECT_TRUE(header.isValid());
    
    // Test header size calculation
    EXPECT_EQ(header.getHeaderSize(), 12);  // Basic header size
    
    header.csrc_count = 2;
    EXPECT_EQ(header.getHeaderSize(), 20);  // 12 + 2*4 bytes
}

// Test RTP packet creation and parsing
TEST_F(PacketReceiverAdvancedTest, RTPPacketCreationAndParsing) {
    RTPPacket packet;
    
    // Test initial state
    size_t header_size = sizeof(RTPHeader);
    EXPECT_EQ(packet.getTotalSize(), header_size);  // Just header
    EXPECT_FALSE(packet.hasExtension());
    EXPECT_FALSE(packet.hasPadding());
    EXPECT_FALSE(packet.isMarker());

    // Test with payload
    packet.payload = {0x01, 0x02, 0x03, 0x04};
    EXPECT_EQ(packet.getTotalSize(), header_size + 4);  // Header + 4 bytes payload
}

// Test RTCP packet types
TEST_F(PacketReceiverAdvancedTest, RTCPPacketTypes) {
    RTCPPacket packet;
    
    // Test different packet types
    packet.type = RTCPPacketType::SENDER_REPORT;
    EXPECT_EQ(static_cast<int>(packet.type), 200);
    
    packet.type = RTCPPacketType::RECEIVER_REPORT;
    EXPECT_EQ(static_cast<int>(packet.type), 201);
    
    packet.type = RTCPPacketType::SOURCE_DESCRIPTION;
    EXPECT_EQ(static_cast<int>(packet.type), 202);
    
    packet.type = RTCPPacketType::BYE;
    EXPECT_EQ(static_cast<int>(packet.type), 203);
}

// Test packet statistics structure
TEST_F(PacketReceiverAdvancedTest, PacketStatisticsStructure) {
    PacketStatistics stats;
    
    // Test initial values
    EXPECT_EQ(stats.packets_received, 0);
    EXPECT_EQ(stats.packets_lost, 0);
    EXPECT_EQ(stats.bytes_received, 0);
    EXPECT_EQ(stats.duplicate_packets, 0);
    EXPECT_EQ(stats.out_of_order_packets, 0);
    EXPECT_EQ(stats.rtcp_packets_received, 0);
    EXPECT_EQ(stats.hardware_accelerated_count, 0);
    EXPECT_EQ(stats.software_fallback_count, 0);
    
    // Test packet loss rate calculation
    EXPECT_EQ(stats.getPacketLossRate(), 0.0);
    
    // Test with some data
    stats.packets_received = 100;
    stats.packets_lost = 5;
    EXPECT_NEAR(stats.getPacketLossRate(), 0.047619, 0.000001);  // 5/105
}

// Test atomic packet statistics
TEST_F(PacketReceiverAdvancedTest, AtomicPacketStatistics) {
    AtomicPacketStatistics atomic_stats;
    
    // Test atomic operations
    atomic_stats.packets_received = 100;
    atomic_stats.packets_lost = 5;
    atomic_stats.bytes_received = 50000;
    
    EXPECT_EQ(atomic_stats.packets_received.load(), 100);
    EXPECT_EQ(atomic_stats.packets_lost.load(), 5);
    EXPECT_EQ(atomic_stats.bytes_received.load(), 50000);
    
    // Test reset functionality
    atomic_stats.reset();
    EXPECT_EQ(atomic_stats.packets_received.load(), 0);
    EXPECT_EQ(atomic_stats.packets_lost.load(), 0);
    EXPECT_EQ(atomic_stats.bytes_received.load(), 0);
}

// Test RGA context structure
TEST_F(PacketReceiverAdvancedTest, RGAContextStructure) {
    RGAContext rga_context;
    
    // Test initial state
    EXPECT_EQ(rga_context.rga_handle, nullptr);
    EXPECT_FALSE(rga_context.initialized);
    EXPECT_EQ(rga_context.status, HardwareAccelStatus::DISABLED);
    EXPECT_EQ(rga_context.operations_count, 0);
    EXPECT_EQ(rga_context.memory_usage_bytes, 0);
    
    // Test capabilities
    EXPECT_FALSE(rga_context.supports_scaling);
    EXPECT_FALSE(rga_context.supports_format_conversion);
    EXPECT_FALSE(rga_context.supports_rotation);
    
    // Test performance metrics
    EXPECT_EQ(rga_context.average_processing_time_us, 0);
    EXPECT_EQ(rga_context.max_processing_time_us, 0);
    EXPECT_EQ(rga_context.failed_operations, 0);
    
    // Test reset
    rga_context.operations_count = 100;
    rga_context.memory_usage_bytes = 1024;
    rga_context.reset();
    EXPECT_EQ(rga_context.operations_count, 0);
    EXPECT_EQ(rga_context.memory_usage_bytes, 0);
}

// Test DMABUF context structure
TEST_F(PacketReceiverAdvancedTest, DMABufContextStructure) {
    DMABufContext dmabuf_context;
    
    // Test initial state
    EXPECT_EQ(dmabuf_context.dmabuf_fd, -1);
    EXPECT_EQ(dmabuf_context.mapped_address, nullptr);
    EXPECT_EQ(dmabuf_context.buffer_size, 0);
    EXPECT_FALSE(dmabuf_context.is_mapped);
    EXPECT_EQ(dmabuf_context.status, HardwareAccelStatus::DISABLED);
    
    // Test buffer pool
    EXPECT_EQ(dmabuf_context.total_buffer_count, 0);
    EXPECT_EQ(dmabuf_context.max_buffer_count, 32);  // RK3588 limit
    EXPECT_TRUE(dmabuf_context.available_buffers.empty());
    EXPECT_TRUE(dmabuf_context.used_buffers.empty());
    
    // Test performance metrics
    EXPECT_EQ(dmabuf_context.zero_copy_operations, 0);
    EXPECT_EQ(dmabuf_context.memory_copy_fallbacks, 0);
    EXPECT_EQ(dmabuf_context.peak_memory_usage, 0);
    
    // Test reset
    dmabuf_context.zero_copy_operations = 100;
    dmabuf_context.memory_copy_fallbacks = 5;
    dmabuf_context.reset();
    EXPECT_EQ(dmabuf_context.zero_copy_operations, 0);
    EXPECT_EQ(dmabuf_context.memory_copy_fallbacks, 0);
}

// Test hardware acceleration configuration
TEST_F(PacketReceiverAdvancedTest, HardwareAccelConfig) {
    HardwareAccelConfig config;
    
    // Test default RGA configuration
    EXPECT_TRUE(config.enable_rga_scaler);
    EXPECT_EQ(config.rga_max_width, 4096);
    EXPECT_EQ(config.rga_max_height, 4096);
    EXPECT_EQ(config.rga_timeout_ms, 100);
    
    // Test default DMABUF configuration
    EXPECT_TRUE(config.enable_dmabuf_zerocopy);
    EXPECT_EQ(config.dmabuf_buffer_size, 2 * 1024 * 1024);  // 2MB
    EXPECT_EQ(config.dmabuf_buffer_count, 16);
    EXPECT_FALSE(config.dmabuf_cached);
    
    // Test performance tuning
    EXPECT_TRUE(config.enable_hardware_prefetch);
    EXPECT_EQ(config.hardware_queue_depth, 4);
    EXPECT_TRUE(config.enable_async_processing);
    
    // Test fallback behavior
    EXPECT_TRUE(config.allow_software_fallback);
    EXPECT_EQ(config.hardware_error_threshold, 5);
    EXPECT_EQ(config.hardware_retry_delay_ms, 1000);
}

// Test NAL unit structure
TEST_F(PacketReceiverAdvancedTest, NALUnitStructure) {
    NALUnit nal_unit;
    
    // Test initial state
    EXPECT_EQ(nal_unit.type, NALUnitType::UNKNOWN);
    EXPECT_EQ(nal_unit.sequence_number, 0);
    EXPECT_FALSE(nal_unit.is_keyframe);
    EXPECT_TRUE(nal_unit.empty());
    EXPECT_EQ(nal_unit.size(), 0);
    
    // Test with data
    nal_unit.data = {0x00, 0x00, 0x00, 0x01, 0x67};  // H.264 SPS
    nal_unit.type = NALUnitType::H264_SPS;
    nal_unit.sequence_number = 100;
    
    EXPECT_FALSE(nal_unit.empty());
    EXPECT_EQ(nal_unit.size(), 5);
    EXPECT_EQ(nal_unit.type, NALUnitType::H264_SPS);
    EXPECT_EQ(nal_unit.sequence_number, 100);
    
    // Test clear
    nal_unit.clear();
    EXPECT_TRUE(nal_unit.empty());
    EXPECT_EQ(nal_unit.size(), 0);
}

// Test malformed packet handling
TEST_F(PacketReceiverAdvancedTest, MalformedPacketHandling) {
    PacketReceiver receiver(config_);
    
    // Test with malformed packet
    auto malformed_packet = createMalformedRTPPacket();
    
    EXPECT_NO_THROW({
        // The receiver should handle malformed packets gracefully
        // and update error statistics
        PacketStatistics stats = receiver.getStatistics();
        EXPECT_EQ(stats.parsing_errors, 0);  // Initially zero
    });
}

// Test packet with extension header
TEST_F(PacketReceiverAdvancedTest, PacketWithExtensionHeader) {
    PacketReceiver receiver(config_);
    
    // Create packet with extension
    auto packet_with_ext = createRTPPacketWithExtension(1000);
    
    EXPECT_NO_THROW({
        // Should handle extension headers correctly
        EXPECT_GT(packet_with_ext.size(), 12);  // Larger than basic header
    });
}

// Test edge cases for sequence numbers
TEST_F(PacketReceiverAdvancedTest, SequenceNumberEdgeCases) {
    PacketReceiver receiver(config_);
    
    // Test sequence number wraparound
    uint16_t max_seq = 65535;
    uint16_t wrapped_seq = 0;
    (void)max_seq; // Suppress unused variable warning
    (void)wrapped_seq; // Suppress unused variable warning

    EXPECT_NO_THROW({
        // Should handle sequence number wraparound correctly
        PacketStatistics stats = receiver.getStatistics();
        EXPECT_EQ(stats.out_of_order_packets, 0);
    });
}

// Test memory pressure scenarios
TEST_F(PacketReceiverAdvancedTest, MemoryPressureScenarios) {
    PacketReceiver receiver(config_);
    
    // Test memory usage monitoring
    size_t initial_memory = receiver.getMemoryUsage();
    EXPECT_GT(initial_memory, 0);
    
    // Test that memory usage is reasonable
    EXPECT_LT(initial_memory, 10 * 1024 * 1024);  // Less than 10MB initially
}

// Test thermal throttling scenarios
TEST_F(PacketReceiverAdvancedTest, ThermalThrottlingScenarios) {
    PacketReceiver receiver(config_);
    
    // Test different temperature scenarios
    EXPECT_NO_THROW({
        receiver.handleThermalThrottling(60);   // Normal
        receiver.handleThermalThrottling(75);   // Warm
        receiver.handleThermalThrottling(85);   // Hot
        receiver.handleThermalThrottling(95);   // Critical
    });
    
    // Test performance mode changes
    EXPECT_NO_THROW({
        receiver.setPerformanceMode(true);   // High performance
        receiver.setPerformanceMode(false);  // Power saving
    });
}
