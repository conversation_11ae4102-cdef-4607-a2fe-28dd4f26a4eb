#pragma once

#include <memory>
#include <future>
#include "models/inference_request.hpp"
#include "models/inference_response.hpp"
#include "repositories/ai_model_repository.hpp"

namespace server::services {

/**
 * @brief Interface for inference service
 * Contains business logic for AI inference operations
 */
class IInferenceService {
public:
    virtual ~IInferenceService() = default;
    
    // Inference operations
    virtual models::ArcFaceResponse processArcFaceInference(const models::ArcFaceRequest& request) = 0;
    virtual models::YoloResponse processYoloInference(const models::YoloRequest& request) = 0;
    
    // Async inference operations
    virtual std::future<models::ArcFaceResponse> processArcFaceInferenceAsync(const models::ArcFaceRequest& request) = 0;
    virtual std::future<models::YoloResponse> processYoloInferenceAsync(const models::YoloRequest& request) = 0;
    
    // Validation and preprocessing
    virtual bool validateRequest(const models::InferenceRequest& request) = 0;
    virtual std::string preprocessImagePath(const std::string& image_path) = 0;
};

/**
 * @brief Implementation of inference service
 * Handles business logic, validation, and preprocessing
 */
class InferenceService : public IInferenceService {
public:
    explicit InferenceService(std::shared_ptr<repositories::IAIModelRepository> repository);
    ~InferenceService() override = default;
    
    // Inference operations
    models::ArcFaceResponse processArcFaceInference(const models::ArcFaceRequest& request) override;
    models::YoloResponse processYoloInference(const models::YoloRequest& request) override;
    
    // Async inference operations
    std::future<models::ArcFaceResponse> processArcFaceInferenceAsync(const models::ArcFaceRequest& request) override;
    std::future<models::YoloResponse> processYoloInferenceAsync(const models::YoloRequest& request) override;
    
    // Validation and preprocessing
    bool validateRequest(const models::InferenceRequest& request) override;
    std::string preprocessImagePath(const std::string& image_path) override;
    
private:
    std::shared_ptr<repositories::IAIModelRepository> repository_;
    
    // Helper methods
    bool isImageFileValid(const std::string& image_path) const;
    bool isImageFormatSupported(const std::string& image_path) const;
    models::ArcFaceResponse createErrorArcFaceResponse(const std::string& error, const std::string& error_code = "");
    models::YoloResponse createErrorYoloResponse(const std::string& error, const std::string& error_code = "");
    void logInferenceRequest(const std::string& model_name, const std::string& image_path) const;
    void logInferenceResult(const std::string& model_name, bool success, std::chrono::milliseconds duration) const;
};

} // namespace server::services
