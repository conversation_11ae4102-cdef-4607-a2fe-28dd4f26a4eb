#pragma once
#include <string>
#include <vector>
#include <optional>
#include <cstdint>

namespace utils {
    /**
     * @brief Cryptographic utilities for hashing, encryption, and secure operations
     *
     * This class provides cryptographic functions with both OpenSSL and fallback implementations.
     * When CRYPTO_USE_OPENSSL is defined, it uses OpenSSL for secure operations.
     * Otherwise, it falls back to standard library implementations (with reduced security).
     */
    class CryptoUtils {
    public:
        // =============================================================================
        // Hash Functions
        // =============================================================================

        /**
         * @brief Compute SHA-256 hash of string
         * @param data Input string to hash
         * @return Optional hex-encoded hash string
         */
        static std::optional<std::string> sha256(const std::string& data);

        /**
         * @brief Compute SHA-256 hash of byte vector
         * @param data Input bytes to hash
         * @return Optional hex-encoded hash string
         */
        static std::optional<std::string> sha256(const std::vector<uint8_t>& data);

        /**
         * @brief Compute SHA-512 hash of string
         * @param data Input string to hash
         * @return Optional hex-encoded hash string
         */
        static std::optional<std::string> sha512(const std::string& data);

        /**
         * @brief Compute MD5 hash of string (deprecated, use SHA-256 instead)
         * @param data Input string to hash
         * @return Optional hex-encoded hash string
         */
        static std::optional<std::string> md5(const std::string& data);

        // =============================================================================
        // HMAC Functions
        // =============================================================================

        /**
         * @brief Compute HMAC-SHA256
         * @param key Secret key
         * @param data Data to authenticate
         * @return Optional hex-encoded HMAC string
         */
        static std::optional<std::string> hmacSha256(const std::string& key, const std::string& data);

        /**
         * @brief Compute HMAC-SHA512
         * @param key Secret key
         * @param data Data to authenticate
         * @return Optional hex-encoded HMAC string
         */
        static std::optional<std::string> hmacSha512(const std::string& key, const std::string& data);

        // =============================================================================
        // Random Generation
        // =============================================================================

        /**
         * @brief Generate cryptographically secure random bytes
         * @param length Number of bytes to generate
         * @return Optional vector of random bytes
         */
        static std::optional<std::vector<uint8_t>> randomBytes(size_t length);

        /**
         * @brief Generate random string with specified charset
         * @param length Length of string to generate
         * @param charset Character set to use (default: alphanumeric)
         * @return Optional random string
         */
        static std::optional<std::string> randomString(
            size_t length,
            const std::string& charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
        );

        /**
         * @brief Generate random hex string
         * @param length Length of hex string (must be even)
         * @return Optional random hex string
         */
        static std::optional<std::string> randomHex(size_t length);

        /**
         * @brief Generate UUID v4 (random)
         * @return Optional UUID string in format xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
         */
        static std::optional<std::string> generateUuid();

        /**
         * @brief Generate cryptographic salt
         * @param length Length of salt in bytes (default: 16)
         * @return Optional salt bytes
         */
        static std::optional<std::vector<uint8_t>> generateSalt(size_t length = 16);

        /**
         * @brief Generate AES-256 key (32 bytes)
         * @return Optional 256-bit key
         */
        static std::optional<std::vector<uint8_t>> generateAes256Key();

        // =============================================================================
        // Base64 Encoding/Decoding
        // =============================================================================

        /**
         * @brief Encode bytes to Base64 string
         * @param data Bytes to encode
         * @return Base64 encoded string
         */
        static std::string base64Encode(const std::vector<uint8_t>& data);

        /**
         * @brief Encode string to Base64
         * @param data String to encode
         * @return Base64 encoded string
         */
        static std::string base64Encode(const std::string& data);

        /**
         * @brief Decode Base64 string to bytes
         * @param encoded Base64 encoded string
         * @return Optional decoded bytes
         */
        static std::optional<std::vector<uint8_t>> base64Decode(const std::string& encoded);

        /**
         * @brief Decode Base64 string to string
         * @param encoded Base64 encoded string
         * @return Optional decoded string
         */
        static std::optional<std::string> base64DecodeString(const std::string& encoded);

        // =============================================================================
        // Key Derivation
        // =============================================================================

        /**
         * @brief Derive key using PBKDF2-SHA256
         * @param password Password to derive from
         * @param salt Salt bytes
         * @param iterations Number of iterations (default: 100000)
         * @param keyLength Desired key length in bytes (default: 32)
         * @return Optional derived key bytes
         */
        static std::optional<std::vector<uint8_t>> pbkdf2Sha256(
            const std::string& password,
            const std::vector<uint8_t>& salt,
            int iterations = 100000,
            int keyLength = 32
        );

        // =============================================================================
        // Password Hashing
        // =============================================================================

        /**
         * @brief Hash password with salt using PBKDF2
         * @param password Password to hash
         * @param salt Salt bytes (if empty, generates random salt)
         * @param iterations Number of iterations (default: 100000)
         * @return Optional formatted hash string
         */
        static std::optional<std::string> hashPassword(
            const std::string& password,
            const std::vector<uint8_t>& salt = {},
            int iterations = 100000
        );

        /**
         * @brief Verify password against hash
         * @param password Password to verify
         * @param hash Formatted hash string from hashPassword
         * @return true if password matches hash
         */
        static bool verifyPassword(const std::string& password, const std::string& hash);

        // =============================================================================
        // Symmetric Encryption (AES)
        // =============================================================================

        /**
         * @brief Encrypt data using AES-256-GCM
         * @param plaintext Data to encrypt
         * @param key 256-bit (32 byte) encryption key
         * @param iv 96-bit (12 byte) initialization vector
         * @return Optional encrypted data (includes auth tag)
         */
        static std::optional<std::vector<uint8_t>> aes256GcmEncrypt(
            const std::string& plaintext,
            const std::vector<uint8_t>& key,
            const std::vector<uint8_t>& iv
        );

        /**
         * @brief Decrypt data using AES-256-GCM
         * @param ciphertext Encrypted data (includes auth tag)
         * @param key 256-bit (32 byte) encryption key
         * @param iv 96-bit (12 byte) initialization vector
         * @return Optional decrypted plaintext
         */
        static std::optional<std::string> aes256GcmDecrypt(
            const std::vector<uint8_t>& ciphertext,
            const std::vector<uint8_t>& key,
            const std::vector<uint8_t>& iv
        );

        /**
         * @brief Encrypt data using AES-256-CBC
         * @param plaintext Data to encrypt
         * @param key 256-bit (32 byte) encryption key
         * @param iv 128-bit (16 byte) initialization vector
         * @return Optional encrypted data
         */
        static std::optional<std::vector<uint8_t>> aes256CbcEncrypt(
            const std::string& plaintext,
            const std::vector<uint8_t>& key,
            const std::vector<uint8_t>& iv
        );

        /**
         * @brief Decrypt data using AES-256-CBC
         * @param ciphertext Encrypted data
         * @param key 256-bit (32 byte) encryption key
         * @param iv 128-bit (16 byte) initialization vector
         * @return Optional decrypted plaintext
         */
        static std::optional<std::string> aes256CbcDecrypt(
            const std::vector<uint8_t>& ciphertext,
            const std::vector<uint8_t>& key,
            const std::vector<uint8_t>& iv
        );

        // =============================================================================
        // Utility Functions
        // =============================================================================

        /**
         * @brief Check if OpenSSL is available
         * @return true if compiled with OpenSSL support
         */
        static bool isOpenSslAvailable();

        /**
         * @brief Get crypto library version information
         * @return Version string
         */
        static std::string getVersion();

        /**
         * @brief Constant-time comparison of byte arrays
         * @param a First byte array
         * @param b Second byte array
         * @return true if arrays are equal
         */
        static bool secureCompare(const std::vector<uint8_t>& a, const std::vector<uint8_t>& b);

        /**
         * @brief Securely clear sensitive data from memory
         * @param data Byte vector to clear
         */
        static void secureClear(std::vector<uint8_t>& data);

        /**
         * @brief Securely clear sensitive data from memory
         * @param data String to clear
         */
        static void secureClear(std::string& data);

        /**
         * @brief Convert bytes to hexadecimal string
         * @param data Bytes to convert
         * @return Hex string (lowercase)
         */
        static std::string bytesToHex(const std::vector<uint8_t>& data);

        /**
         * @brief Convert hexadecimal string to bytes
         * @param hex Hex string to convert
         * @return Optional byte vector
         */
        static std::optional<std::vector<uint8_t>> hexToBytes(const std::string& hex);

    private:
        /**
         * @brief Internal helper for Base64 encoding
         */
        static const std::string base64Chars;

        /**
         * @brief Check if character is valid Base64
         */
        static bool isBase64(unsigned char c);
    };

    // =============================================================================
    // Convenience Functions
    // =============================================================================

    /**
     * @brief Convenience function for SHA-256 hashing
     * @param data String to hash
     * @return Hex-encoded hash (empty string on error)
     */
    std::string sha256(const std::string& data);

    /**
     * @brief Convenience function for password hashing
     * @param password Password to hash
     * @return Formatted hash string (empty string on error)
     */
    std::string hashPassword(const std::string& password);

    /**
     * @brief Convenience function for password verification
     * @param password Password to verify
     * @param hash Hash to verify against
     * @return true if password matches
     */
    bool verifyPassword(const std::string& password, const std::string& hash);

    /**
     * @brief Convenience function for random string generation
     * @param length Length of string to generate
     * @return Random string (empty string on error)
     */
    std::string randomString(size_t length);

    /**
     * @brief Convenience function for UUID generation
     * @return UUID string (empty string on error)
     */
    std::string generateUuid();

    /**
     * @brief Convenience function for Base64 encoding
     * @param data String to encode
     * @return Base64 encoded string
     */
    std::string base64Encode(const std::string& data);

    /**
     * @brief Convenience function for Base64 decoding
     * @param encoded Base64 string to decode
     * @return Decoded string (empty string on error)
     */
    std::string base64Decode(const std::string& encoded);
}
