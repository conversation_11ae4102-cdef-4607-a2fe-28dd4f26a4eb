# Utility functions and macros for the project

# Function to print build information
function(print_build_info)
    message(STATUS "=== Build Configuration ===")
    message(STATUS "Project: ${PROJECT_NAME}")
    message(STATUS "Version: ${PROJECT_VERSION}")
    message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
    message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
    message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")
    message(STATUS "Generator: ${CMAKE_GENERATOR}")
    message(STATUS "Build directory: ${CMAKE_BINARY_DIR}")
    message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
    message(STATUS "===========================")
endfunction()

# Function to create a library with standard settings
function(add_project_library lib_name)
    set(options HEADER_ONLY SHARED STATIC)
    set(oneValueArgs "")
    set(multiValueArgs SOURCES HEADERS PUBLIC_HEADERS DEPENDENCIES)
    cmake_parse_arguments(LIB "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    if(LIB_HEADER_ONLY)
        add_library(${lib_name} INTERFACE)
        if(LIB_PUBLIC_HEADERS)
            target_sources(${lib_name} INTERFACE ${LIB_PUBLIC_HEADERS})
        endif()
        if(LIB_DEPENDENCIES)
            target_link_libraries(${lib_name} INTERFACE ${LIB_DEPENDENCIES})
        endif()
        target_include_directories(${lib_name} INTERFACE include)
    else()
        if(LIB_SHARED)
            add_library(${lib_name} SHARED ${LIB_SOURCES} ${LIB_HEADERS})
        else()
            add_library(${lib_name} STATIC ${LIB_SOURCES} ${LIB_HEADERS})
        endif()
        
        set_target_properties_common(${lib_name})
        target_include_directories(${lib_name} PUBLIC include)
        target_include_directories(${lib_name} PRIVATE src)
        
        if(LIB_DEPENDENCIES)
            target_link_libraries(${lib_name} PUBLIC ${LIB_DEPENDENCIES})
        endif()
    endif()
    
    message(STATUS "Added library: ${lib_name}")
endfunction()

# Function to create an executable with standard settings
function(add_project_executable exe_name)
    set(options "")
    set(oneValueArgs "")
    set(multiValueArgs SOURCES DEPENDENCIES)
    cmake_parse_arguments(EXE "${options}" "${oneValueArgs}" "${multiValueArgs}" ${ARGN})
    
    add_executable(${exe_name} ${EXE_SOURCES})
    set_target_properties_common(${exe_name})
    
    if(EXE_DEPENDENCIES)
        target_link_libraries(${exe_name} PRIVATE ${EXE_DEPENDENCIES})
    endif()
    
    link_common_dependencies(${exe_name})
    message(STATUS "Added executable: ${exe_name}")
endfunction()

# Function to setup installation rules
function(setup_install_rules)
    # Install executables
    install(TARGETS ${PROJECT_NAME}
        RUNTIME DESTINATION bin
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
    )
    
    # Install headers
    install(DIRECTORY include/
        DESTINATION include
        FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
    )
    
    # Install documentation
    install(FILES README.md
        DESTINATION share/doc/${PROJECT_NAME}
    )
endfunction()

# Macro to set default build type
macro(set_default_build_type)
    if(NOT CMAKE_BUILD_TYPE AND NOT CMAKE_CONFIGURATION_TYPES)
        message(STATUS "Setting build type to 'Debug' as none was specified.")
        set(CMAKE_BUILD_TYPE Debug CACHE STRING "Choose the type of build." FORCE)
        set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "Debug" "Release" "MinSizeRel" "RelWithDebInfo")
    endif()
endmacro()

# Function to enable code coverage
function(enable_coverage target_name)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        target_compile_options(${target_name} PRIVATE --coverage)
        target_link_options(${target_name} PRIVATE --coverage)
        message(STATUS "Code coverage enabled for ${target_name}")
    endif()
endfunction()
