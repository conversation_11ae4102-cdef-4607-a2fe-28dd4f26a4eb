# **Phân Tích Chi Tiết Logic Triển Khai Facial Detection và Face Recognition Đồng Thời trên NPU RK3588 với Đầu Vào RTSP Camera bằng C++**

## **1\. Tóm Lược Báo Cáo**

Báo cáo này trình bày chi tiết kiến trúc và chiến lược triển khai hiệu suất cao cho việc xử lý đồng thời hai tác vụ nhận diện khuôn mặt (facial detection) và định danh khuôn mặt (face recognition) trên Neural Processing Unit (NPU) của Rockchip RK3588. Hệ thống được thiết kế để xử lý đầu vào từ nhiều luồng camera RTSP, sử dụng ngôn ngữ lập trình C++ và tập trung vào việc tối ưu hóa hiệu suất cũng như số lượng camera có thể hỗ trợ. Trọng tâm của giải pháp là tận dụng tối đa khả năng tăng tốc phần cứng của RK3588, bao gồm NPU ba lõi, Media Process Platform (MPP) cho giải mã video, và 2D Raster Graphic Accelerator (RGA) cho tiền xử lý hình ảnh.

Các chiến lược chính được đề xuất bao gồm:

* Lựa chọn và tối ưu hóa mô hình AI: Sử dụng các mô hình hiệu quả như RetinaFace cho phát hiện khuôn mặt và ArcFace/FaceNet cho định danh khuôn mặt, sau đó lượng tử hóa sang INT8 bằng RKNN-Toolkit2 để tăng tốc độ suy luận trên NPU.  
* Thiết kế ứng dụng C++ đa luồng: Xây dựng một kiến trúc module hóa rõ ràng, bao gồm các thành phần riêng biệt cho việc nhận luồng RTSP, giải mã bằng MPP, tiền xử lý bằng RGA, suy luận trên NPU, và hậu xử lý.  
* Tối đa hóa việc sử dụng NPU: Áp dụng các kỹ thuật lập trình NPU tiên tiến như sử dụng đa lõi NPU thông qua API rknn\_set\_core\_mask, xử lý theo lô (batch processing) với cờ RKNN\_FLAG\_BATCH\_MODE và hàm rknn\_inputs\_set, cùng với suy luận bất đồng bộ sử dụng cờ RKNN\_FLAG\_ASYNC\_MODE.  
* Triển khai pipeline dữ liệu zero-copy: Sử dụng DMABUF để chia sẻ bộ đệm giữa MPP, RGA và NPU, nhằm giảm thiểu việc sao chép dữ liệu không cần thiết và giảm tải cho băng thông bộ nhớ.

Kết quả kỳ vọng là một hệ thống có khả năng mở rộng, xử lý được số lượng lớn luồng camera trong thời gian thực, với hướng dẫn chi tiết về cách đạt được hiệu suất tối ưu. Các phân tích và khuyến nghị dựa trên thông số kỹ thuật của RK3588 1, tài liệu SDK của Rockchip (RKNN SDK 3, MPP 5, RGA 7) và các kinh nghiệm thực tiễn trong việc triển khai các hệ thống AI phức tạp trên nền tảng nhúng.

Thách thức cốt lõi không chỉ nằm ở việc chạy các mô hình AI trên NPU, mà là việc điều phối toàn bộ pipeline từ đầu cuối một cách hiệu quả cho *nhiều luồng dữ liệu và nhiều mô hình trên mỗi luồng*. Điều này đòi hỏi một cái nhìn hệ thống toàn diện. Yêu cầu của người dùng về "hiệu quả lớn nhất về cả performance và số lượng camera" cho thấy sự cần thiết phải xem xét vượt ra ngoài các benchmark NPU biệt lập. Mỗi giai đoạn (giải mã, tiền xử lý, phát hiện, định danh) đều có thể trở thành điểm nghẽn. Do đó, việc tối ưu hóa từng giai đoạn và luồng dữ liệu giữa chúng là tối quan trọng. Kiến trúc không đồng nhất của RK3588 (CPU, GPU, NPU, VPU, RGA) gợi ý rằng một giải pháp thành công sẽ phân phối các tác vụ một cách thích hợp cho từng đơn vị xử lý. Báo cáo này sẽ tập trung vào sự tương tác giữa các đơn vị phần cứng này và các chiến lược phần mềm để quản lý chúng.

## **2\. Tìm Hiểu Nền Tảng RK3588 cho AI Thị Giác**

### **2.1. Tổng Quan Kiến Trúc SoC RK3588**

Rockchip RK3588 là một System-on-Chip (SoC) mạnh mẽ, được thiết kế cho các ứng dụng AIoT hiệu suất cao, sản xuất trên tiến trình 8nm LP.2 Để triển khai hiệu quả các tác vụ AI thị giác, việc hiểu rõ các thành phần chính của SoC là rất quan trọng.

* **CPU (Central Processing Unit):** RK3588 tích hợp CPU 8 lõi, bao gồm cụm 4 lõi ARM Cortex-A76 hiệu năng cao (tốc độ lên đến 2.4/2.6 GHz) và cụm 4 lõi ARM Cortex-A55 tiết kiệm năng lượng (1.8 GHz), được cấu hình theo kiến trúc dynamIQ.2 Sự kết hợp này cung cấp sự cân bằng giữa khả năng xử lý mạnh mẽ cho các tác vụ nặng và hiệu quả năng lượng cho các tác vụ nền. CPU đóng vai trò điều phối chính cho toàn bộ pipeline AI, quản lý luồng dữ liệu, và thực thi các phần logic ứng dụng không thể tăng tốc bằng phần cứng chuyên dụng.  
* **GPU (Graphics Processing Unit):** SoC được trang bị GPU ARM Mali-G610 MP4 "Odin", hỗ trợ các API đồ họa hiện đại như OpenGL ES3.2, OpenCL 2.2, và Vulkan 1.2.1 Mặc dù NPU là đơn vị chính cho suy luận AI, GPU có thể được sử dụng cho một số tác vụ tiền xử lý hoặc hậu xử lý hình ảnh nếu RGA không đủ khả năng hoặc cho các tác vụ hiển thị kết quả nếu ứng dụng yêu cầu.  
* **VPU (Video Processing Unit):** Một thành phần quan trọng cho các ứng dụng xử lý video, VPU của RK3588 có khả năng giải mã video độ phân giải lên đến 8Kp60 (cho các codec H.265, VP9, AVS2) và mã hóa video 8Kp30 (H.265/H.264).2 Khả năng này cực kỳ cần thiết để xử lý hiệu quả nhiều luồng RTSP camera độ phân giải cao. Rockchip Media Process Platform (MPP) sẽ tận dụng VPU này.  
* **ISP (Image Signal Processor):** RK3588 tích hợp ISP 48MP hỗ trợ các tính năng như HDR (High Dynamic Range) và 3D NR (3D Noise Reduction).2 ISP chủ yếu liên quan đến việc xử lý tín hiệu từ cảm biến camera thô. Trong trường hợp đầu vào là các luồng RTSP đã được mã hóa, vai trò của ISP sẽ ít trực tiếp hơn, nhưng kiến thức về nó vẫn hữu ích nếu có các kịch bản mở rộng sử dụng camera MIPI trực tiếp.

Sức mạnh của RK3588 nằm ở khả năng tính toán không đồng nhất của nó. Việc sử dụng hiệu quả các đơn vị này một cách phối hợp là chìa khóa để đạt được hiệu suất tối đa, thay vì chỉ dựa vào NPU hoặc CPU. VPU đảm nhận việc giải mã video, RGA xử lý các thao tác hình ảnh, và NPU thực hiện suy luận AI, trong khi CPU điều phối toàn bộ quá trình. Nếu bất kỳ một đơn vị nào trở thành điểm nghẽn, hoặc nếu việc truyền dữ liệu giữa chúng không hiệu quả, hiệu suất tổng thể sẽ bị ảnh hưởng. Yêu cầu về "hiệu quả lớn nhất" đòi hỏi việc sử dụng phối hợp này. Do đó, kiến trúc ứng dụng C++ phải được thiết kế để giao phó các tác vụ cho các đơn vị phần cứng thích hợp và quản lý luồng dữ liệu (lý tưởng là zero-copy) giữa chúng.

**Bảng 1: Thông Số Kỹ Thuật Chính của SoC RK3588 cho Tác Vụ AI Thị Giác**

| Thành Phần | Thông Số Kỹ Thuật |
| :---- | :---- |
| CPU | 4x Cortex-A76 @ 2.4/2.6 GHz \+ 4x Cortex-A55 @ 1.8 GHz (dynamIQ) 2 |
| GPU | ARM Mali-G610 MP4, 450 GFLOPS, OpenGL ES3.2, OpenCL 2.2, Vulkan 1.1 1 |
| NPU | Lên đến 6 TOPS, 3 lõi, hỗ trợ INT4/INT8/INT16/FP16 1 |
| VPU | Giải mã 8Kp60 (H.265/VP9/AVS2), Mã hóa 8Kp30 (H.265/H.264) 2 |
| RGA | Tăng tốc đồ họa 2D (resize, CSC, rotate, blend) 7 |
| Giao Tiếp Bộ Nhớ | LPDDR4/LPDDR4x/LPDDR5, lên đến 32GB 2 |

### **2.2. Phân Tích Sâu về Neural Processing Unit (NPU)**

NPU là trái tim của khả năng xử lý AI trên RK3588, và việc hiểu rõ đặc điểm của nó là tối quan trọng để tối ưu hóa hiệu suất.

* **Năng Lực Tính Toán:** RK3588 tích hợp một NPU mạnh mẽ với khả năng tính toán lên đến **6 TOPS** (Tera Operations Per Second).1 Đây là một nguồn tài nguyên đáng kể để chạy các mô hình AI phức tạp.  
* **Kiến Trúc Ba Lõi (Tri-core):** NPU của RK3588 bao gồm ba lõi xử lý riêng biệt.9 Đây là một chi tiết quan trọng cho các chiến lược xử lý song song. Một số nguồn 9 cho rằng mỗi lõi có công suất 2 TOPS.  
* **Các Kiểu Dữ Liệu Được Hỗ Trợ:** NPU hỗ trợ các phép toán với độ chính xác hỗn hợp bao gồm INT4, INT8, INT16, và FP16.1 INT8 thường là điểm cân bằng tốt nhất giữa hiệu suất và độ chính xác sau khi lượng tử hóa. Mặc dù một số tài liệu 11 chỉ ra rằng RKNN-Toolkit2 (công cụ chuyển đổi mô hình) chỉ hỗ trợ INT8 và FP16, và việc lượng tử hóa INT4 không được công cụ này hỗ trợ, điều này có thể giới hạn hiệu suất đỉnh thực tế xuống còn 3 TOPS (với INT8), phần cứng NPU vẫn hỗ trợ INT4. Đây là một hạn chế của phiên bản toolkit được đề cập tại thời điểm đó.  
* **Hỗ Trợ Framework:** Thông qua RKNN-Toolkit2, NPU hỗ trợ chuyển đổi từ các framework học sâu phổ biến như TensorFlow, PyTorch, Caffe, ONNX, MXNet, DarkNet.1 Điều này mang lại sự linh hoạt trong việc lựa chọn và sử dụng mô hình.  
* **Cấu Trúc Bên Trong NPU** 9**:** Mỗi lõi NPU được cho là bao gồm:  
  * CNA (Convolution Network Accelerator): Cho các phép toán tích chập cốt lõi.  
  * DPU (Data Processing Unit).  
  * PPU (Planar Processing Unit). Điều này cho thấy NPU chủ yếu là một bộ tăng tốc cho mạng CNN (Convolutional Neural Network). NPU được mô tả như một bộ xử lý luồng dữ liệu với pipeline cố định, không thực thi chương trình theo nghĩa truyền thống, mà được cấu hình thông qua các thanh ghi.9  
* **Hạn Chế Truy Cập Bộ Nhớ:** Một hạn chế quan trọng được chỉ ra trong 9 là các con trỏ dữ liệu của NPU là 32-bit và phải tham chiếu đến bộ nhớ vật lý, giới hạn bộ nhớ RAM có thể sử dụng cho các tác vụ NPU ở mức 4GB, bất kể tổng RAM hệ thống (ví dụ, trên các bo mạch có 16/32GB RAM). Điều này có thể ảnh hưởng đến kích thước và số lượng mô hình/context có thể hoạt động đồng thời.

Việc NPU có ba lõi là một tài sản đáng kể. Tuy nhiên, phân tích kỹ thuật 9 cho thấy SDK có thể không luôn sử dụng cả ba lõi cho một tác vụ tích chập đơn lẻ, và các đơn vị DPU/PPU có thể không có khả năng đa lõi tương tự. Nguồn này khuyến nghị coi chúng như các lõi riêng biệt cho các mô hình/tác vụ khác nhau. Ngược lại, các ghi chú phát hành của RKNN SDK 13 cho phiên bản 1.3.0 và 1.4.0 lại khẳng định "RK3588 thêm chức năng chạy một mô hình đơn lẻ trên nhiều lõi cùng lúc" và "RK3588 hỗ trợ chế độ đa lô đa lõi (multi-batch multi-core mode)". Các ghi chú phát hành SDK này là chính thức và mới hơn, cho thấy sự cải thiện hỗ trợ đa lõi theo thời gian. "Chế độ đa lô đa lõi" đặc biệt hứa hẹn cho việc xử lý hiệu quả nhiều đầu vào camera. Do đó, chiến lược triển khai phải khám phá và xác thực khả năng đa lõi thực sự cho các mô hình đã chọn. API rknn\_set\_core\_mask 14 sẽ rất cần thiết. Cách tiếp cận linh hoạt nhất là quản lý nhiều instance rknn\_context, mỗi instance có thể được gán cho các lõi khác nhau hoặc tận dụng các tính năng đa lõi.

Hạn chế về khả năng địa chỉ hóa bộ nhớ 4GB của NPU 9 là một ràng buộc cứng, ảnh hưởng trực tiếp đến số lượng mô hình (và trọng số/kích hoạt liên quan) có thể hoạt động đồng thời, đặc biệt nếu không sử dụng zero-copy cho tensor đầu vào/ra. Việc quản lý bộ nhớ hiệu quả, bao gồm zero-copy (DMABUF) cho tensor I/O và lựa chọn mô hình cẩn thận (về kích thước), là rất quan trọng. Cờ RKNN\_FLAG\_MEM\_ALLOC\_OUTSIDE trong rknn\_init 16 trở nên rất quan trọng.

Bản chất pipeline cố định của NPU 11 có nghĩa là các phép toán không được hỗ trợ sẽ được chuyển về CPU để thực thi 11, điều này có thể gây nghẽn cổ chai hiệu suất đáng kể. Do đó, việc lựa chọn và chuyển đổi mô hình phải ưu tiên các lớp (layer) tương thích với NPU.

### **2.3. Rockchip Media Process Platform (MPP)**

Rockchip Media Process Platform (MPP) là một middleware quan trọng, đóng vai trò trung gian cho việc xử lý video và hình ảnh được tăng tốc phần cứng, chủ yếu tận dụng VPU.5

* **Chức Năng Chính:** MPP xử lý các tác vụ giải mã video (ví dụ: H.264, H.265 từ các luồng RTSP) và mã hóa video. Đối với bài toán này, khả năng giải mã là tối quan trọng.  
* **Hiệu Suất:** Được thiết kế để đạt hiệu suất cao với các giao diện đồng bộ (sync) và bất đồng bộ (async) nhằm giảm thiểu thời gian chặn (blocking) và cho phép phần cứng và phần mềm chạy song song.6  
* **API C (rk\_mpi.h):** Các hàm API chính bao gồm mpp\_create (để lấy context MPP và cấu trúc API), mpp\_init (để thiết lập kiểu và định dạng giải mã), mpi-\>control (để cấu hình các tham số), decode\_put\_packet (để đưa dữ liệu đã mã hóa vào), decode\_get\_frame (để lấy khung hình đã giải mã, ví dụ ở định dạng YUV hoặc RGB), mpi-\>reset, và mpp\_destroy.18  
* **Quản Lý Bộ Đệm (Buffer Management):** MPP quản lý bộ đệm riêng của mình nhưng có thể giao tiếp với các bộ cấp phát bên ngoài, điều này là chìa khóa cho việc tích hợp DMABUF.6 Ví dụ mpi\_dec\_test.c 18 là một tài liệu tham khảo quan trọng.

MPP là giai đoạn quan trọng đầu tiên trong pipeline xử lý đầu vào RTSP. Hiệu quả của nó trong việc giải mã nhiều luồng dữ liệu ảnh hưởng trực tiếp đến số lượng camera có thể được hỗ trợ và tải cho các giai đoạn tiếp theo. Ứng dụng C++ phải sử dụng API C của MPP một cách chính xác, quản lý context và bộ đệm cho từng luồng. Lựa chọn định dạng đầu ra từ MPP (ví dụ: NV12) sẽ ảnh hưởng đến giai đoạn RGA tiếp theo. Việc xuất đầu ra zero-copy (DMABUF) từ MPP là rất được khuyến khích.

### **2.4. Rockchip 2D Raster Graphic Acceleration Unit (RGA)**

Rockchip RGA là một bộ tăng tốc phần cứng 2D độc lập, được thiết kế để thực hiện các tác vụ như thay đổi kích thước hình ảnh, xoay, chuyển đổi không gian màu, trộn alpha, v.v..7

* **Vai Trò Liên Quan:** RGA rất cần thiết cho việc tiền xử lý các khung hình từ MPP (ví dụ, định dạng NV12) thành định dạng và độ phân giải mà các mô hình NPU yêu cầu (ví dụ, BGR, kích thước cụ thể).3  
* **API:** Thư viện librga cung cấp API C (im2d.h) và C++ (im2d.hpp). Các hàm chính bao gồm imresize, imcvt\_color\_space, imcopy, imcrop.7 Hàm im2d\_sync có thể được sử dụng để đảm bảo tác vụ RGA hoàn thành trước khi NPU sử dụng đầu ra.  
* **Hỗ Trợ DMABUF:** RGA có thể làm việc với DMABUF để thực hiện các thao tác zero-copy, nhận DMABUF từ MPP và có khả năng xuất DMABUF cho NPU.22 Các hàm API như imimportbuffer\_fd và imexportbuffer\_fd (hoặc tương tự) sẽ đóng vai trò then chốt.  
* **Hiệu Suất:** RGA trên RK3588 (được gọi là RGA3 "Orion") hỗ trợ độ phân giải cao (lên đến 8176x8176 cho nguồn/đích) và nhiều định dạng khác nhau bao gồm NV12, RGB, BGR.7

RGA đóng vai trò là cầu nối giữa định dạng video đã giải mã (thường là dựa trên YUV như NV12 từ MPP) và định dạng đầu vào yêu cầu của NPU (thường là BGR hoặc RGB, planar hoặc interleaved, kích thước cụ thể). Việc sử dụng RGA hiệu quả là rất quan trọng để tránh tắc nghẽn CPU trong quá trình tiền xử lý. Ứng dụng C++ phải sử dụng librga để thực hiện các chuyển đổi này. Luồng dữ liệu zero-copy qua DMABUF giữa MPP \-\> RGA \-\> NPU là đường dẫn dữ liệu lý tưởng. Việc lựa chọn định dạng đầu ra của RGA phải khớp với thuộc tính tensor đầu vào của NPU (ví dụ: RKNN\_TENSOR\_NHWC hoặc NCHW, RKNN\_TENSOR\_BGR888, v.v.). Tài liệu Rockchip\_Developer\_Guide\_RGA\_EN.md 7 và mã nguồn ví dụ (im2d\_api\_demo 7) là những tài liệu tham khảo quan trọng, mặc dù một số liên kết có thể không truy cập được.24

### **2.5. Rockchip Neural Network (RKNN) SDK**

RKNN SDK là nền tảng cho việc phát triển ứng dụng NPU trên các nền tảng Rockchip.3

* **RKNN-Toolkit2 (Phía PC):** Được sử dụng để chuyển đổi mô hình từ các framework như Caffe, TensorFlow, ONNX, PyTorch sang định dạng .rknn. Nó cũng xử lý việc lượng tử hóa mô hình (ví dụ: FP32 sang INT8), mô phỏng hiệu suất và đánh giá bộ nhớ.3  
* **RKNN Runtime (Phía Thiết Bị):** Cung cấp API C/C++ (librknn\_api.so, rknn\_api.h) và Python (rknn\_toolkit\_lite2) để tải mô hình .rknn và thực hiện suy luận trên NPU. Báo cáo này tập trung vào API C.3  
* **RKNPU Kernel Driver:** Tương tác với phần cứng NPU, là mã nguồn mở và là một phần của kernel Rockchip.3  
* **Model Zoo:** Rockchip cung cấp rknn\_model\_zoo với các ví dụ cho nhiều mô hình khác nhau, bao gồm RetinaFace, và các demo CAPI (API C).3

RKNN SDK là một bộ công cụ toàn diện, nhưng tài liệu và ví dụ của nó nằm rải rác trên nhiều kho lưu trữ và tài liệu.3 Việc sử dụng hiệu quả đòi hỏi phải tổng hợp thông tin này. Tệp tiêu đề rknn\_api.h là nguồn thông tin cuối cùng và chính xác nhất cho các chữ ký hàm API C và định nghĩa enum/struct.17 SDK RKNN (cụ thể là RKNPU2) đã phát triển, với các phiên bản bổ sung các tính năng quan trọng như hỗ trợ đa lõi cho một mô hình đơn lẻ, chế độ đa lô đa lõi và cải thiện zero-copy.13 Do đó, việc sử dụng phiên bản RKNN SDK và RKNPU runtime/driver mới nhất là rất quan trọng để tận dụng các tính năng nâng cao hiệu suất này.

**Bảng 2: Các Hàm API C Chính của RKNN Runtime cho Tương Tác NPU**

| Hàm API | Mô Tả Ngắn Gọn |
| :---- | :---- |
| rknn\_init | Khởi tạo context RKNN và tải mô hình .rknn.34 |
| rknn\_destroy | Hủy một context RKNN và giải phóng tài nguyên.15 |
| rknn\_query | Truy vấn thông tin về mô hình (số lượng I/O, thuộc tính tensor, phiên bản SDK, v.v.).14 |
| rknn\_inputs\_set | Thiết lập dữ liệu đầu vào cho mô hình.34 |
| rknn\_run | Thực hiện suy luận NPU.34 |
| rknn\_outputs\_get | Lấy kết quả đầu ra từ NPU.34 |
| rknn\_outputs\_release | Giải phóng bộ đệm đầu ra do runtime cấp phát.15 |
| rknn\_set\_core\_mask | Gán context NPU cho các lõi NPU cụ thể (chỉ RK3588).14 |
| rknn\_create\_mem | Cấp phát bộ nhớ tensor RKNN.16 |
| rknn\_create\_mem\_from\_fd | Tạo bộ nhớ tensor RKNN từ một file descriptor (cho DMABUF).16 |
| rknn\_destroy\_mem | Giải phóng bộ nhớ tensor RKNN được tạo bởi rknn\_create\_mem hoặc rknn\_create\_mem\_from\_fd.13 |
| rknn\_set\_io\_mem | Gán bộ nhớ tensor bên ngoài (ví dụ: DMABUF) cho đầu vào/đầu ra của mô hình.16 |

## **3\. Chiến Lược Mô Hình AI: Phát Hiện và Định Danh Khuôn Mặt**

### **3.1. Tiêu Chí Lựa Chọn Mô Hình cho Triển Khai NPU**

Việc lựa chọn mô hình AI phù hợp là một bước quan trọng, ảnh hưởng trực tiếp đến hiệu suất và độ chính xác của toàn hệ thống. Các tiêu chí sau cần được xem xét kỹ lưỡng:

* **Độ Chính Xác (Accuracy):** Mục tiêu chính là phát hiện và định danh khuôn mặt một cách đáng tin cậy. Các chỉ số như mAP (mean Average Precision) cho tác vụ phát hiện và độ chính xác rank-1 cho tác vụ định danh là rất quan trọng.  
* **Hiệu Suất (Tốc Độ Suy Luận):** Độ trễ thấp và thông lượng cao (FPS) là yếu tố then chốt cho các hệ thống đa camera thời gian thực. Độ phức tạp của mô hình (số lượng tham số, phép toán) ảnh hưởng trực tiếp đến yếu tố này.28  
* **Tương Thích NPU:** Kiến trúc mô hình phải có khả năng chuyển đổi bởi RKNN-Toolkit2 và tận dụng hiệu quả các lớp được tăng tốc bởi NPU. Cần tránh các mô hình có nhiều lớp tùy chỉnh hoặc phải chạy trên CPU (CPU-fallback).11  
* **Kích Thước Mô Hình:** Các mô hình nhỏ hơn tiêu thụ ít bộ nhớ hơn (quan trọng do giới hạn bộ nhớ 4GB có thể địa chỉ hóa của NPU 9) và có thể có thời gian tải nhanh hơn.  
* **Khả Năng Lượng Tử Hóa (Quantization Friendliness):** Mô hình nên duy trì độ chính xác tốt sau khi lượng tử hóa sang INT8. Một số kiến trúc mô hình có khả năng chống chịu với lượng tử hóa tốt hơn các kiến trúc khác.

Có một sự đánh đổi cố hữu giữa độ chính xác, kích thước mô hình và tốc độ. Đối với các hệ thống nhúng như RK3588, các mô hình nhẹ nhưng vẫn đảm bảo độ chính xác là ưu tiên hàng đầu. Cần phải tìm một điểm cân bằng phù hợp với yêu cầu của ứng dụng. Báo cáo này sẽ đề xuất các biến thể mô hình nhẹ cụ thể và nhấn mạnh việc đánh giá benchmark chúng trên RK3588 sau khi chuyển đổi và lượng tử hóa.

### **3.2. Các Mô Hình Đề Xuất**

Dựa trên các tiêu chí trên và các tài liệu tham khảo, dưới đây là một số mô hình được đề xuất:

* **Phát Hiện Khuôn Mặt (Facial Detection):**  
  * **RetinaFace:** Là một mô hình phát hiện khuôn mặt phổ biến và có độ chính xác cao. Các biến thể như RetinaFace\_mobile320 (định dạng ONNX) có sẵn trong RKNN Model Zoo 30 và cho thấy hiệu suất tốt (ví dụ, khoảng 111 FPS được đề cập trong 40 cho một ví dụ RetinaFace trên RK3588, mặc dù kích thước đầu vào/biến thể không được chỉ định đầy đủ). RetinaFace\_mobile320.onnx và RetinaFace\_resnet50\_320.onnx được liệt kê với hỗ trợ INT8 cho RK3588 trong.30 Các ví dụ cho Luckfox Pico cũng đề cập đến việc chuyển đổi RetinaFace.16  
  * **Các biến thể YOLO nhẹ (ví dụ: YOLOv5s, YOLOv8n, YOLOX-Nano):** Mặc dù là các mô hình phát hiện đối tượng chung, chúng có thể được huấn luyện để phát hiện khuôn mặt. YOLOv5s trên RK3588 đạt khoảng 105 FPS 41, YOLOv8n khoảng 108 FPS.41 YOLOv7-tiny đạt khoảng 23-30 FPS (thời gian suy luận 33-43ms).42 Dusun IoT đề cập YOLO-v8n đạt 59.6 FPS.43 Đây là những lựa chọn thay thế nếu RetinaFace không phù hợp.  
* **Định Danh Khuôn Mặt (Facial Recognition):**  
  * **ArcFace:** Là một hàm mất mát (loss function) tiên tiến cho định danh; các mô hình sử dụng nó rất phổ biến. 38 đề cập đến một triển khai ArcFace với backbone MobileFaceNet, tập trung vào RV1106 nhưng các nguyên tắc có thể áp dụng. 10 đề cập ArcFace trong một pipeline GStreamer trên RK3588.  
  * **FaceNet:** Một mô hình nổi tiếng để học các vector đặc trưng (embedding) của khuôn mặt. Khadas cung cấp một demo C++ chuyển đổi FaceNet từ PyTorch sang RKNN cho Edge2 (dựa trên RK3588).44 Các ví dụ cho Luckfox Pico cũng đề cập đến việc chuyển đổi FaceNet.16  
  * **CosFace:** Một hàm mất mát dựa trên biên (margin-based) phổ biến khác cho hiệu suất cao.  
  * **Các Dự Án Cộng Đồng:** InspireFace SDK hỗ trợ NPU RK3588 cho định danh khuôn mặt và được phát triển bằng C++.45 Đây có thể là một nguồn cung cấp các mô hình đã được tối ưu hóa hoặc một thư viện để tích hợp.

RKNN Model Zoo và các ví dụ từ các nhà cung cấp bo mạch (Khadas, Luckfox) thường xuyên giới thiệu RetinaFace và FaceNet, cho thấy sự hỗ trợ và tối ưu hóa tốt cho các mô hình này trên NPU của Rockchip. Do đó, việc bắt đầu với RetinaFace để phát hiện và FaceNet/ArcFace (với backbone MobileFaceNet) để định danh là một cách tiếp cận thực tế. Nên ưu tiên các mô hình có sẵn trong RKNN Model Zoo 30 vì chúng có khả năng đã được kiểm tra về khả năng tương thích với NPU.

**Bảng 3: So Sánh Các Mô Hình Phát Hiện/Định Danh Khuôn Mặt Phù Hợp cho NPU RK3588**

| Tên Mô Hình | Framework Gốc | Kích Thước Đầu Vào Tiêu Biểu | Có Sẵn Trong RKNN Model Zoo/Ví Dụ | Chỉ Số Hiệu Suất Chính (FPS trên RK3588 nếu có) | Khả Năng Lượng Tử Hóa |
| :---- | :---- | :---- | :---- | :---- | :---- |
| RetinaFace-MobileNet | ONNX/Caffe | 320x320, 640x640 | Có 30 | \~111 FPS (RetinaFace, cấu hình không rõ) 40, 144.8-470.5 FPS (320x320, tùy chip) 30 | Tốt (INT8) |
| RetinaFace-ResNet50 | ONNX/Caffe | 320x320 | Có 30 | 14.6-56.6 FPS (320x320, tùy chip) 30 | Tốt (INT8) |
| FaceNet (MobileFaceNet) | PyTorch/ONNX | 112x112, 160x160 | Có (Khadas Edge2 Demo 44) | Không có số liệu FPS cụ thể từ snippet | Tốt (INT8) |
| ArcFace (MobileFaceNet) | PyTorch/ONNX | 112x112 | Có thể có (InspireFace 45) | Không có số liệu FPS cụ thể từ snippet | Tốt (INT8) |
| YOLOv5s (face) | PyTorch/ONNX | 640x640 | Có (generic object detection) | \~105 FPS (generic) 41 | Tốt (INT8) |
| YOLOv8n (face) | PyTorch/ONNX | 640x640 | Có (generic object detection) | \~108 FPS (generic) 41, \~59.6 FPS 43 | Tốt (INT8) |

### **3.3. Chuyển Đổi và Lượng Tử Hóa Mô Hình với RKNN-Toolkit2**

Quá trình chuyển đổi mô hình từ framework gốc sang định dạng .rknn có thể chạy trên NPU, cùng với việc lượng tử hóa để tối ưu hiệu suất, là một bước không thể thiếu.

* **Quy Trình Làm Việc:** Quy trình chuẩn thường là: Framework Gốc (PyTorch, TensorFlow) \-\> ONNX \-\> RKNN.3  
  * Xuất mô hình từ PyTorch/TensorFlow sang định dạng ONNX là bước đầu tiên phổ biến. Tham số opset\_version cho việc xuất ONNX có thể quan trọng (ví dụ44 đề cập opset 1216 đề cập opset 9).  
* **API config của RKNN-Toolkit2:** Trước khi tải mô hình ONNX, hàm rknn.config() được sử dụng để thiết lập các tham số như mean\_values, std\_values, target\_platform='rk3588', và các tham số lượng tử hóa.16  
* **API load\_onnx của RKNN-Toolkit2:** Dùng để tải mô hình ONNX.16  
* **API build của RKNN-Toolkit2:** Đây là nơi quá trình lượng tử hóa diễn ra. Cần đặt do\_quantization=True. Một dataset (một tệp văn bản liệt kê đường dẫn đến các hình ảnh hiệu chuẩn đại diện) là rất quan trọng để lượng tử hóa INT8 chính xác.16  
  * 47 nhấn mạnh rằng tập dữ liệu hiệu chuẩn nên cố gắng bao gồm càng nhiều loại đầu vào của mô hình càng tốt để đảm bảo chất lượng sau lượng tử hóa.  
* **API export\_rknn của RKNN-Toolkit2:** Lưu mô hình đã được chuyển đổi và lượng tử hóa thành tệp .rknn.16  
* **Chi Tiết Lượng Tử Hóa:**  
  * RKNN-Toolkit2 hỗ trợ lượng tử hóa INT8 bất đối xứng (asymmetric\_quantized-8) theo mặc định.48  
  * NPU hỗ trợ INT4/INT8/INT16/FP16.1 Tuy nhiên, các hạn chế của RKNN-Toolkit2 có thể giới hạn việc sử dụng thực tế; ví dụ11 cho biết công cụ chỉ hỗ trợ chuyển đổi sang INT8/FP16, và INT4 không khả dụng qua công cụ này. Điều này có nghĩa là hiệu suất đỉnh 6 TOPS (có thể dựa trên INT4) có thể không đạt được hoàn toàn với các công cụ hiện tại, mà tối đa là 3 TOPS (INT8) hoặc 1.5 TOPS (FP16) theo.11  
  * Lượng tử hóa có thể làm giảm một chút độ chính xác nhưng cải thiện đáng kể hiệu suất và giảm kích thước mô hình.44 lưu ý rằng đối với yolov5, việc sử dụng ReLU thay vì SiLU (thực hiện trong quá trình tối ưu hóa mô hình trước khi chuyển đổi) làm giảm nhẹ độ chính xác nhưng cải thiện đáng kể hiệu suất.

Chất lượng của tập dữ liệu hiệu chuẩn được cung cấp trong quá trình rknn.build(do\_quantization=True, dataset=...) ảnh hưởng trực tiếp đến độ chính xác của mô hình INT8 đã lượng tử hóa. Lượng tử hóa sau huấn luyện (Post-Training Quantization \- PTQ) điều chỉnh trọng số và các giá trị kích hoạt của mô hình sang độ chính xác thấp hơn (INT8). Quá trình này đòi hỏi phải quan sát sự phân bổ của các giá trị kích hoạt trên dữ liệu đại diện. Nếu tập dữ liệu hiệu chuẩn không đa dạng hoặc không đại diện cho đầu vào thực tế, việc lượng tử hóa có thể dẫn đến suy giảm độ chính xác đáng kể.47 Do đó, người dùng phải chuẩn bị một tập dữ liệu hiệu chuẩn tốt, bao gồm các hình ảnh tương tự như những gì camera sẽ ghi lại trong môi trường triển khai.

Một số tài liệu 44 đề cập đến việc sửa đổi mô hình *trước khi* xuất sang ONNX (ví dụ: loại bỏ các lớp chuẩn hóa, tối ưu hóa các lớp đầu ra). Các NPU có hỗ trợ lớp cụ thể và đặc điểm hiệu suất riêng. Một số phép toán có thể chạy chậm trên NPU hoặc tốt hơn nên được thực hiện trên CPU sau suy luận. Việc sửa đổi đồ thị mô hình (ví dụ: loại bỏ lớp chuẩn hóa cuối cùng trong FaceNet như trong 44, hoặc tối ưu hóa các đầu ra của YOLO như trong 50) có thể cải thiện khả năng tương thích và hiệu suất của NPU. Người dùng nên điều tra xem các mô hình đã chọn của họ có được hưởng lợi từ việc tối ưu hóa đồ thị trước chuyển đổi như vậy cho NPU RK3588 hay không. Điều này thường liên quan đến kiến thức chuyên sâu về mô hình và NPU.

## **4\. Kiến Trúc Triển Khai C++ cho Xử Lý Đồng Thời**

### **4.1. Thiết Kế Pipeline Hệ Thống Tổng Thể**

Để xử lý đồng thời nhiều luồng camera và thực hiện cả phát hiện lẫn định danh khuôn mặt, một kiến trúc pipeline đa luồng, module hóa là cần thiết.

* **Luồng Logic Tổng Thể:**  
  1. **Giai Đoạn Đầu Vào RTSP:** Nhiều luồng (threads), mỗi luồng dành riêng cho một camera, kết nối với các luồng RTSP, nhận các gói video đã mã hóa.  
  2. **Giai Đoạn Giải Mã MPP:** Các gói từ mỗi luồng được đưa đến một instance bộ giải mã phần cứng MPP để lấy các khung hình video thô (ví dụ: định dạng NV12).  
  3. **Bộ Điều Phối Tiền Xử Lý:** Các khung hình đã giải mã (cùng với ID camera liên quan) được gửi đến một bộ điều phối trung tâm hoặc hàng đợi.  
  4. **Tiền Xử Lý RGA (cho Phát Hiện):** Các khung hình được lấy từ hàng đợi. Đối với mỗi khung hình, RGA thực hiện thay đổi kích thước theo kích thước đầu vào của mô hình phát hiện khuôn mặt và chuyển đổi không gian màu (ví dụ: NV12 sang BGR).  
  5. **Phát Hiện Khuôn Mặt trên NPU:** Khung hình đã tiền xử lý được đưa vào mô hình phát hiện khuôn mặt (ví dụ: RetinaFace) chạy trên một lõi/context NPU.  
  6. **Cắt Khuôn Mặt & Tiền Xử Lý RGA (cho Định Danh):** Các bounding box của khuôn mặt được phát hiện được sử dụng để cắt các vùng khuôn mặt từ khung hình gốc đã giải mã (hoặc một khung hình đã được thay đổi kích thước phù hợp). Mỗi khuôn mặt được cắt sau đó được RGA thay đổi kích thước theo kích thước đầu vào của mô hình định danh khuôn mặt và chuyển đổi màu nếu cần.  
  7. **Định Danh Khuôn Mặt trên NPU:** Các vùng khuôn mặt đã tiền xử lý được đưa vào mô hình định danh khuôn mặt (ví dụ: ArcFace/FaceNet) trên một lõi/context NPU để trích xuất các vector đặc trưng (embeddings).  
  8. **Hậu Xử Lý & Logic Ứng Dụng:** Kết quả phát hiện (bounding box, điểm số) và kết quả định danh (embeddings) được sử dụng bởi ứng dụng (ví dụ: xác định các khuôn mặt đã biết, theo dõi đối tượng).  
* **Luồng Dữ Liệu và Quản Lý Bộ Đệm:** Nhấn mạnh việc sử dụng zero-copy bằng DMABUF từ đầu ra MPP đến đầu vào RGA, và từ đầu ra RGA đến đầu vào NPU. Điều này rất quan trọng đối với hiệu suất.22 Các bộ đệm sẽ mang theo file descriptor của DMABUF.  
* **Sơ Đồ Pipeline (Khái Niệm):** \-\> Thread1 \-\> MPPDec1 \-\> DMABUF\_FrameQ1 \-\> ThreadN \-\> MPPDecN \-\> DMABUF\_FrameQN DMABUF\_FrameQ \-\> PreProcPool (RGA cho Phát Hiện) \-\> DetectInputQ DetectInputQ \-\> NPU\_Detect\_Engine \-\> DetectResultQ DetectResultQ \+ KhungHìnhGốc \-\> CropPool (RGA cho Định Danh) \-\> RecogInputQ RecogInputQ \-\> NPU\_Recog\_Engine \-\> RecogResultQ RecogResultQ \-\> LogicỨngDụng

Pipeline bao gồm nhiều giai đoạn với thời gian xử lý và nhu cầu tài nguyên khác nhau. Việc tách rời các giai đoạn này bằng các hàng đợi an toàn luồng (thread-safe queues) cho phép sử dụng tài nguyên tốt hơn và tạo điều kiện cho việc xử lý song song theo kiểu pipeline (pipelining). Đầu vào RTSP bị giới hạn bởi I/O. Giải mã bị giới hạn bởi VPU. Tiền xử lý bị giới hạn bởi RGA. Suy luận bị giới hạn bởi NPU. Hậu xử lý bị giới hạn bởi CPU. Nếu các tác vụ này chạy tuần tự trên mỗi khung hình, nhiều tài nguyên sẽ ở trạng thái nhàn rỗi. Các hàng đợi cho phép mỗi giai đoạn làm việc trên các khung hình/dữ liệu khác nhau một cách đồng thời. Do đó, một thiết kế đa luồng mạnh mẽ với các hàng đợi (ví dụ: std::queue với std::mutex và std::condition\_variable, hoặc các hàng đợi không khóa (lock-free queues)) là rất cần thiết. Kích thước của các hàng đợi này cần được điều chỉnh cẩn thận để cân bằng giữa độ trễ và thông lượng.

Một điểm đáng chú ý là việc sử dụng RGA có thể diễn ra hai lần: một lần để chuẩn bị đầu vào cho mô hình phát hiện, và sau đó là một lần nữa cho mỗi khuôn mặt được phát hiện để chuẩn bị đầu vào cho mô hình định danh. Mô hình phát hiện nhận một khung hình đầy đủ (đã thay đổi kích thước). Mô hình định danh nhận một hình ảnh khuôn mặt nhỏ hơn, đã được cắt. Các mô hình này thường có yêu cầu kích thước đầu vào khác nhau. Do đó, RGA sẽ được sử dụng hai lần cho mỗi khuôn mặt được phát hiện nếu yêu cầu đầu vào khác nhau đáng kể. Việc tối ưu hóa sử dụng RGA (ví dụ: nhóm các thao tác RGA lại nếu có thể, mặc dù librga thường xử lý một hình ảnh tại một thời điểm) là quan trọng.

### **4.2. Module 1: Đầu Vào RTSP Đa Luồng và Giải Mã Phần Cứng (MPP)**

Module này chịu trách nhiệm nhận dữ liệu từ nhiều camera RTSP và giải mã chúng bằng phần cứng để chuẩn bị cho các bước xử lý tiếp theo.

* **Xử Lý RTSP:**  
  * Sử dụng một thư viện mạnh mẽ như **FFmpeg** (libavformat, libavcodec) hoặc **GStreamer** (rtspsrc, rtph264depay/rtph265depay) để kết nối luồng RTSP một cách ổn định, xử lý các vấn đề mạng, và tách các luồng cơ bản (H.264/H.265).53  
  * GStreamer cung cấp phần tử mppvideodec 21, trực tiếp sử dụng MPP. Nếu sử dụng FFmpeg, nó có thể được biên dịch với sự hỗ trợ của Rockchip MPP (ffmpeg-rockchip 22). Một giải pháp khác là trích xuất các đơn vị NAL H.264/H.265 thô và đưa trực tiếp vào API C của MPP.  
* **API C của MPP cho Giải Mã:**  
  * Đối với mỗi luồng camera, tạo một instance MppCtx và MppApi.18  
  * Khởi tạo để giải mã: mpp\_init(ctx, MPP\_CTX\_DEC, coding\_type) với coding\_type là MPP\_VIDEO\_CodingAVC hoặc MPP\_VIDEO\_CodingHEVC.18  
  * Cấu hình bộ giải mã nếu cần bằng mpi-\>control().18  
  * Đưa các gói đã mã hóa vào: Sử dụng mpp\_packet\_init để đóng gói dữ liệu H.264/H.265, sau đó mpi-\>decode\_put\_packet(ctx, packet).18  
  * Lấy các khung hình đã giải mã: Sử dụng mpi-\>decode\_get\_frame(ctx, \&frame). Đối tượng MppFrame sẽ chứa dữ liệu video thô (ví dụ: NV12).18  
  * **Đầu Ra DMABUF:** Cấu hình MPP để xuất các khung hình dưới dạng DMABUF. Điều này thường liên quan đến việc thiết lập các cờ cụ thể trong quá trình khởi tạo MPP hoặc các lệnh gọi control. Đối tượng MppFrame sau đó sẽ cung cấp quyền truy cập vào file descriptor của DMABUF (ví dụ: mpp\_frame\_get\_fd).  
* **Mô Hình Luồng (Threading Model):**  
  * Thông thường, một luồng cho mỗi luồng RTSP. Luồng này xử lý:  
    * Kết nối RTSP và đọc gói.  
    * Đưa các gói vào instance bộ giải mã MPP chuyên dụng của nó.  
    * Lấy các khung hình đã giải mã (file descriptor DMABUF).  
    * Đặt các file descriptor DMABUF (hoặc các cấu trúc chứa chúng và siêu dữ liệu) vào một hàng đợi chia sẻ cho giai đoạn tiền xử lý.

Việc sử dụng trực tiếp API C của MPP mang lại khả năng kiểm soát tốt hơn so với các lớp trừu tượng của GStreamer/FFmpeg nếu cần tối ưu hóa sâu hoặc xử lý bộ đệm cụ thể (như xuất DMABUF) mà các thư viện cấp cao hơn không dễ dàng cung cấp. Tuy nhiên, FFmpeg/GStreamer xử lý các phức tạp mạng tốt hơn. ffmpeg-rockchip 22 dường như là một cầu nối tốt cho vấn đề này. Cần đánh giá ffmpeg-rockchip hoặc mppvideodec của GStreamer về tính dễ sử dụng so với khả năng kiểm soát. Nếu sử dụng API C của MPP trực tiếp, ứng dụng phải xử lý việc phân tích cú pháp gói ở một mức độ nào đó. Mỗi instance bộ giải mã MPP tiêu thụ tài nguyên VPU. VPU của RK3588 rất mạnh mẽ (giải mã 8Kp60 H.265 2), nhưng có một giới hạn về số lượng luồng HD đồng thời. Giới hạn này cần được xác định bằng thực nghiệm.

### **4.3. Module 2: Tiền Xử Lý Tăng Tốc Phần Cứng (RGA)**

Module này nhận các khung hình đã giải mã từ MPP và chuẩn bị chúng cho đầu vào của NPU bằng cách sử dụng RGA.

* **Chức Năng:** Thay đổi kích thước các khung hình đã giải mã thành kích thước đầu vào của mô hình phát hiện (ví dụ: 640x640). Chuyển đổi không gian màu từ đầu ra MPP (ví dụ: NV12) sang đầu vào NPU (ví dụ: BGR, RGB \- cần kiểm tra yêu cầu của mô hình, thường là BGR cho các mô hình được huấn luyện bằng OpenCV). Đối với định danh khuôn mặt, sau khi phát hiện, cắt các vùng khuôn mặt và thay đổi kích thước/chuyển đổi màu cho mô hình định danh.  
* **API C/C++ của RGA (im2d.h/im2d.hpp từ librga):** 7  
  * Các hàm chính: imresize (để thay đổi kích thước), imcvt\_color\_space (để chuyển đổi không gian màu), imcopy, imcrop.7  
  * Sử dụng imcheckHeader để xác minh khả năng tương thích của thư viện.7  
  * Các thao tác đồng bộ như im2d\_sync hoặc imsync có thể được sử dụng để đảm bảo tác vụ RGA hoàn thành trước khi NPU sử dụng đầu ra.7  
* **Xử Lý DMABUF:**  
  * Đầu vào: RGA cần nhập file descriptor DMABUF từ MPP. rga\_buffer\_handle\_t có thể được sử dụng với importbuffer\_fd hoặc API tương tự.7  
  * Đầu ra: RGA nên xuất ra một DMABUF khác, file descriptor của nó sẽ được chuyển cho NPU.  
  * Điều này đảm bảo luồng dữ liệu zero-copy: MPP \-\> RGA \-\> NPU.  
* **Cấu Trúc Dữ Liệu:** Các cấu trúc rga\_info\_t hoặc rga\_buffer\_t được sử dụng để mô tả bộ đệm nguồn và đích, hình chữ nhật, định dạng.7  
* **Định Dạng Màu:** RGA của RK3588 hỗ trợ nhiều định dạng bao gồm RK\_FORMAT\_YCbCr\_420\_SP (NV12), RK\_FORMAT\_RGB\_888, RK\_FORMAT\_BGR\_888.7  
* **Luồng (Threading):** Một nhóm (pool) các luồng có thể xử lý các tác vụ tiền xử lý. Mỗi luồng lấy một khung hình DMABUF từ hàng đợi giải mã, thực hiện các thao tác RGA, và đặt DMABUF kết quả (cho NPU) vào hàng đợi đầu vào NPU.

Các thao tác RGA, mặc dù được tăng tốc phần cứng, vẫn có độ trễ. Đối với nhiều luồng và có khả năng nhiều thao tác RGA trên mỗi khung hình (tiền xử lý phát hiện \+ N\_khuôn\_mặt \* tiền xử lý định danh), thông lượng RGA có thể trở thành điểm nghẽn. Số lượng đơn vị RGA trên RK3588 (thường là một hoặc hai, cần xác minh từ TRM/datasheet7 liệt kê RGA3 cho RK3588) và khả năng xử lý của chúng sẽ giới hạn số lượng tác vụ tiền xử lý có thể chạy song song. Thiết kế nên xem xét một nhóm xử lý RGA chuyên dụng. Việc đảm bảo RGA có thể đọc định dạng DMABUF của MPP (ví dụ: NV12 tiled) và xuất ra định dạng (ví dụ: BGR linear) và bố cục (NHWC/NCHW) phù hợp cho việc nhập DMABUF của NPU là rất quan trọng và đòi hỏi cấu hình cẩn thận các tham số rga\_buffer\_t (chiều rộng, chiều cao, virtual stride, định dạng).

### **4.4. Module 3: Quản Lý Suy Luận NPU (API C của RKNN Runtime)**

Đây là module cốt lõi thực hiện suy luận AI trên NPU, sử dụng API C của RKNN Runtime.

#### **4.4.1. Quản Lý Context (rknn\_context)**

* Một rknn\_context đại diện cho một instance mô hình NPU đã được khởi tạo.16  
* Nên tạo các context riêng biệt cho mô hình phát hiện khuôn mặt và mô hình định danh khuôn mặt: rknn\_context detect\_ctx; rknn\_context recog\_ctx;  
* Tải mô hình bằng rknn\_init(): Hàm này nhận một con trỏ đến dữ liệu mô hình .rknn (được tải từ tệp) và kích thước của nó.17  
  * Chữ ký hàm: int rknn\_init(rknn\_context\* context, void\* model, uint32\_t size, uint32\_t flag, rknn\_init\_extend\* extend);.17  
  * Các cờ (flags) cho rknn\_init:  
    * RKNN\_FLAG\_PRIOR\_HIGH/MEDIUM/LOW: Thiết lập ưu tiên cho context.32  
    * RKNN\_FLAG\_ASYNC\_MODE: Kích hoạt suy luận bất đồng bộ (rknn\_run trả về ngay lập tức, rknn\_outputs\_get có thể lấy kết quả của khung hình trước đó hoặc đợi).17  
    * RKNN\_FLAG\_MEM\_ALLOC\_OUTSIDE: Cho biết bộ đệm đầu vào/đầu ra được cấp phát bên ngoài (ví dụ: DMABUF).16  
    * RKNN\_FLAG\_SHARE\_WEIGHT\_MEM: Để chia sẻ trọng số nếu nhiều context sử dụng cùng một mô hình chính xác.17  
    * RKNN\_FLAG\_BATCH\_MODE: Có khả năng dành cho xử lý theo lô, tuy nhiên việc sử dụng API C chính xác của nó ít được ghi nhận trong các đoạn trích. Changelog 13 đề cập "RK3588 support multi-batch multi-core mode". Cờ này cần được xác minh trong rknn\_api.h.  
* Hủy context bằng rknn\_destroy(ctx).15

#### **4.4.2. Chiến Lược Sử Dụng Đa Lõi NPU**

RK3588 có ba lõi NPU 9, và việc tận dụng chúng là chìa khóa để tối đa hóa thông lượng.

* Sử dụng rknn\_set\_core\_mask(rknn\_context ctx, rknn\_core\_mask core\_mask) để gán một context cho (các) lõi NPU cụ thể.14  
* Các giá trị enum rknn\_core\_mask 15:  
  * RKNN\_NPU\_CORE\_AUTO: Lập lịch tự động bởi runtime.  
  * RKNN\_NPU\_CORE\_0, RKNN\_NPU\_CORE\_1, RKNN\_NPU\_CORE\_2: Gán cho một lõi cụ thể.  
  * RKNN\_NPU\_CORE\_0\_1, RKNN\_NPU\_CORE\_1\_2, RKNN\_NPU\_CORE\_0\_2 (có thể có các kết hợp).  
  * RKNN\_NPU\_CORE\_0\_1\_2: Gán cho cả ba lõi. Đây là nơi tính năng "chạy một mô hình đơn lẻ trên nhiều lõi" hoặc "chế độ đa lô đa lõi" của SDK 13 sẽ được áp dụng.  
* **Cân Nhắc Chiến Lược:**  
  * **Lựa Chọn A (Tập Trung Luồng):** Một lõi NPU cho mỗi luồng camera. Phát hiện và định danh cho luồng đó chạy tuần tự hoặc theo pipeline trên lõi đó. Bị giới hạn bởi 3 luồng nếu 1 lõi/luồng.  
  * **Lựa Chọn B (Tập Trung Tác Vụ):** Dành riêng các lõi cho các loại mô hình. Ví dụ: Lõi 0 cho tất cả các tác vụ phát hiện, Lõi 1 cho tất cả các tác vụ định danh. Lõi 2 cho các tác vụ tràn hoặc một tác vụ khác. Điều này cho phép gom đầu vào từ nhiều camera cho một loại mô hình.  
  * **Lựa Chọn C (Động với Đa Context):** Tạo nhiều context cho phát hiện và định danh. Có một nhóm luồng (thread pool) cho các worker NPU. Mỗi worker chọn một tác vụ (phát hiện khung X, hoặc định danh khuôn mặt Y từ khung X), lấy một context NPU có sẵn (có thể được gán trước cho một lõi hoặc sử dụng RKNN\_NPU\_CORE\_AUTO hoặc RKNN\_NPU\_CORE\_0\_1\_2), chạy suy luận, và giải phóng. Đây là lựa chọn linh hoạt nhất.  
  * 9 gợi ý rằng việc coi các lõi là riêng biệt có thể mạnh mẽ hơn nếu khả năng song song thực sự của một mô hình trên cả 3 lõi bị hạn chế đối với các lớp không phải tích chập. Tuy nhiên13 cho thấy các cải tiến của SDK.

#### **4.4.3. Xử Lý Đầu Vào/Đầu Ra**

* Truy vấn thuộc tính đầu vào/đầu ra của mô hình: rknn\_query(ctx, RKNN\_QUERY\_IN\_OUT\_NUM, \&io\_num, sizeof(io\_num)) để lấy số lượng tensor đầu vào/đầu ra.14  
* Sau đó, rknn\_query(ctx, RKNN\_QUERY\_INPUT\_ATTR, \&input\_attrs\[i\], sizeof(rknn\_tensor\_attr)) và RKNN\_QUERY\_OUTPUT\_ATTR để biết chi tiết về mỗi tensor (kích thước, tên, định dạng, kiểu, kiểu lượng tử hóa, điểm không, tỷ lệ).16  
  * Cấu trúc rknn\_tensor\_attr chứa: index, n\_dims, dims, name, n\_elems, size, fmt (RKNN\_TENSOR\_NHWC, NCHW), type (RKNN\_TENSOR\_UINT8, FP32 v.v.), qnt\_type (RKNN\_TENSOR\_QNT\_AFFINE\_ASYMMETRIC v.v.), zp (zero\_point), scale.  
* Chuẩn bị các cấu trúc rknn\_input 34:  
  * index: Chỉ số tensor đầu vào.  
  * buf: Con trỏ đến dữ liệu đầu vào. Đối với DMABUF, đây sẽ là một con trỏ có thể truy cập từ CPU nếu cần ánh xạ, hoặc là FD nếu sử dụng rknn\_set\_io\_mem với handle DMABUF.  
  * size: Kích thước dữ liệu đầu vào.  
  * pass\_through: true nếu buf đã được định dạng sẵn (ví dụ: đã lượng tử hóa, đúng bố cục); false nếu runtime cần tiền xử lý (ví dụ: float sang int8, chuyển đổi bố cục). Đối với DMABUF từ RGA, cờ này nên là true nếu RGA xuất ra định dạng sẵn sàng cho NPU.  
  * type: Kiểu dữ liệu (RKNN\_TENSOR\_UINT8, v.v.).  
  * fmt: Định dạng (RKNN\_TENSOR\_NHWC, v.v.).  
* Thiết lập đầu vào: rknn\_inputs\_set(ctx, num\_inputs, inputs\_array).16  
* Thực hiện suy luận: rknn\_run(ctx, nullptr) cho đồng bộ, hoặc với rknn\_run\_async / các cờ cụ thể cho bất đồng bộ.16  
* Chuẩn bị các cấu trúc rknn\_output:  
  * want\_float: true để nhận đầu ra float32 (được runtime giải lượng tử hóa), false cho đầu ra lượng tử hóa thô (ví dụ: INT8).  
  * is\_prealloc: true nếu buf được người dùng cấp phát trước, false nếu runtime nên cấp phát.  
* Lấy đầu ra: rknn\_outputs\_get(ctx, num\_outputs, outputs\_array, nullptr).16  
* Giải phóng đầu ra: rknn\_outputs\_release(ctx, num\_outputs, outputs\_array).15

#### **4.4.4. Triển Khai Xử Lý Theo Lô (Batch Processing)**

* RKNN SDK v1.3.0+ cho RK3588 hỗ trợ "chế độ đa lô đa lõi".13  
* Chiều dims (N trong NCHW/NHWC) của rknn\_tensor\_attr thường đại diện cho kích thước lô. Các mô hình phải được chuyển đổi với chiều lô động hoặc kích thước lô cố định \> 1\.  
* Khi sử dụng rknn\_inputs\_set, nếu mô hình hỗ trợ xử lý theo lô, rknn\_input.buf nên trỏ đến một khối bộ nhớ liền kề chứa tất cả các hình ảnh trong lô. rknn\_input.size nên là batch\_size \* single\_image\_size.  
* Ngoài ra, nếu RKNN\_FLAG\_BATCH\_MODE là một cờ hợp lệ cho rknn\_init, nó có thể kích hoạt một cơ chế xử lý lô nội bộ. Điều này cần được xác minh từ rknn\_api.h.17  
* 47 và 61 hiển thị các ví dụ Python nơi chiều lô là một phần của hình dạng đầu vào mô hình.  
* **Chiến Lược:** Đối với phát hiện, gom các khung hình từ nhiều camera thành lô. Đối với định danh, gom nhiều khuôn mặt đã phát hiện thành lô. Điều này cải thiện đáng kể thông lượng NPU.

#### **4.4.5. Suy Luận Bất Đồng Bộ**

* Khởi tạo với RKNN\_FLAG\_ASYNC\_MODE.17  
* rknn\_run() (hoặc một hàm rknn\_run\_async() chuyên dụng) sẽ trả về nhanh chóng.  
* Kết quả thường được lấy trong một hàm gọi lại (callback) riêng biệt hoặc bằng cách thăm dò (polling). Hàm rknn\_wait() có thể liên quan ở đây (15, mặc dù được đánh dấu là không hỗ trợ cho RK3588 trong bảng đó, có thể đã lỗi thời hoặc cụ thể cho một phiên bản). Mô tả cờ cho thấy hành vi của rknn\_outputs\_get thay đổi.  
* Điều này rất quan trọng để tạo pipeline và không chặn các luồng CPU chờ NPU.

Sự tương tác giữa rknn\_set\_core\_mask và xử lý theo lô là rất quan trọng. Liệu một suy luận theo lô trên một context đơn lẻ có thể được trải rộng trên nhiều lõi được chỉ định bởi mask hay không? "Chế độ đa lô đa lõi" 13 gợi ý là có. Đây là kịch bản mong muốn nhất để đạt thông lượng cao. Việc triển khai nên cố gắng sử dụng đầu vào theo lô và cấu hình context để sử dụng tất cả các lõi NPU có sẵn/được gán (RKNN\_NPU\_CORE\_0\_1\_2).

Để thực hiện zero-copy thực sự với DMABUF, sau khi tạo rknn\_tensor\_mem bằng rknn\_create\_mem\_from\_fd, các đối tượng bộ nhớ này phải được liên kết với các tensor I/O của mô hình bằng cách sử dụng rknn\_set\_io\_mem(ctx, mem, attr).16 Điều này tránh việc sao chép nội bộ bởi runtime. Đây là cách hiệu quả nhất để đưa dữ liệu vào NPU. Cờ pass\_through trong rknn\_input nên là true.

### **4.5. Module 4: Hậu Xử Lý và Logic Ứng Dụng**

Sau khi NPU hoàn thành suy luận, kết quả thô cần được xử lý để có ý nghĩa cho ứng dụng.

* **Hậu Xử Lý Phát Hiện Khuôn Mặt:**  
  * Đầu ra từ các mô hình phát hiện (ví dụ: RetinaFace, YOLO) thường bao gồm tọa độ bounding box (ví dụ: x,y,w,h hoặc x1,y1,x2,y2), điểm số đối tượng (objectness scores), và điểm số lớp (class scores) nếu có.  
  * Điều này thường đòi hỏi xử lý CPU đáng kể: giải mã dự đoán hộp, áp dụng ngưỡng điểm số, Non-Maximum Suppression (NMS) để loại bỏ các hộp dư thừa.4  
  * 4 lưu ý rằng đối với yolov5, hậu xử lý được chuyển sang CPU theo mặc định do hạn chế phần cứng (có thể đề cập đến NPU cũ hơn hoặc các lớp mô hình cụ thể). Đây là một chiến lược phổ biến.  
* **Hậu Xử Lý Định Danh Khuôn Mặt:**  
  * Đầu ra thường là một vector đặc trưng (embedding) cho mỗi khuôn mặt.  
  * Các embedding này sau đó được so sánh với một cơ sở dữ liệu các embedding khuôn mặt đã biết bằng một thước đo khoảng cách (ví dụ: cosine similarity, Euclidean distance).44  
  * Chuẩn hóa L2 của các embedding có thể cần thiết nếu không phải là một phần của mô hình.38  
* **Tối Ưu Hóa:**  
  * Các phép toán vector hóa (ví dụ: sử dụng SIMD nếu có trên CPU, hoặc các thư viện như Eigen cho toán ma trận nếu cần các phép tính phức tạp).  
  * Các thuật toán NMS hiệu quả.  
  * Tối ưu hóa việc tìm kiếm trong cơ sở dữ liệu để khớp khuôn mặt (ví dụ: sử dụng các thư viện tìm kiếm lân cận gần đúng như FAISS nếu cơ sở dữ liệu lớn).  
* **Logic Ứng Dụng:** Theo dõi khuôn mặt qua các khung hình, liên kết phát hiện với định danh, tạo sự kiện, giao tiếp với các hệ thống khác.

Hậu xử lý có thể là một điểm nghẽn CPU đáng kể, đặc biệt là NMS cho phát hiện với nhiều hộp ứng cử viên, hoặc khớp với một cơ sở dữ liệu khuôn mặt lớn. NPU tăng tốc suy luận, nhưng kết quả thô của NPU cần được diễn giải. Việc diễn giải này (hậu xử lý) chạy trên CPU. Nếu hậu xử lý chậm, nó sẽ làm mất đi lợi ích tốc độ của NPU. Do đó, việc triển khai C++ hiệu quả các thuật toán hậu xử lý là rất quan trọng. Cần xem xét liệu có phần nào có thể được chuyển sang GPU (OpenCL) nếu CPU bị quá tải, mặc dù điều này làm tăng độ phức tạp.

### **4.6. Luồng Dữ Liệu Zero-Copy với DMABUF**

DMABUF là một cơ chế của Linux kernel cho phép chia sẻ bộ đệm giữa nhiều thiết bị và không gian người dùng mà không cần sao chép dữ liệu. Điều này đạt được bằng cách truyền các file descriptor (FD) trỏ đến bộ nhớ vật lý.

* **Triển Khai Pipeline:**  
  1. **Đầu Ra MPP:** Cấu hình bộ giải mã MPP để xuất các khung hình vào DMABUF. MppFrame nên cung cấp một phương thức để lấy FD (ví dụ: mpp\_frame\_get\_fd(frame)).  
  2. **Đầu Vào/Đầu Ra RGA:**  
     * Nhập FD DMABUF của MPP vào RGA bằng rga\_import\_buffer\_fd (hoặc API tương tự trong librga, ví dụ: imimportbuffer\_fd từ im2d.h). Điều này cung cấp một handle bộ đệm RGA.  
     * Chỉ định đầu ra RGA cũng là một DMABUF (cấp phát một DMABUF, lấy FD của nó, nhập vào RGA làm đích).  
     * Thực hiện các thao tác RGA (thay đổi kích thước, chuyển đổi không gian màu).  
     * FD DMABUF đầu ra sau đó được sử dụng cho NPU.  
  3. **Đầu Vào NPU:**  
     * Sử dụng rknn\_create\_mem\_from\_fd(ctx, fd, virt\_addr, size, offset) để tạo một rknn\_tensor\_mem từ FD DMABUF đầu ra của RGA.16 virt\_addr có thể cần thiết nếu CPU cần truy cập nó, nhưng đối với việc truyền dữ liệu thuần túy giữa các thiết bị, FD là chính.  
     * Đặt rknn\_tensor\_mem này làm đầu vào NPU bằng rknn\_set\_io\_mem(ctx, mem, \&attr).16  
     * Đảm bảo rknn\_input.pass\_through \= true và các kiểu/định dạng khớp nhau.  
* **Các Cờ/API RKNN Chính cho Zero-Copy:**  
  * RKNN\_FLAG\_MEM\_ALLOC\_OUTSIDE trong rknn\_init(): Báo hiệu rằng bộ đệm I/O của NPU sẽ được quản lý bên ngoài.16  
  * RKNN\_FLAG\_PASS\_MEM\_BY\_FD (nếu có và cần thiết, kiểm tra rknn\_api.h): Có thể đơn giản hóa việc truyền trực tiếp FD.  
  * rknn\_create\_mem\_from\_fd(): Đóng gói một FD thành một rknn\_tensor\_mem.16  
  * rknn\_set\_io\_mem(): Liên kết rknn\_tensor\_mem bên ngoài với một đầu vào/đầu ra của mô hình.16

Zero-copy thực sự phức tạp để triển khai một cách chính xác, đòi hỏi đồng bộ hóa cẩn thận và quản lý vòng đời bộ đệm qua các miền trình điều khiển khác nhau (MPP, RGA, NPU). MPP tạo ra một khung hình trong DMABUF. RGA sử dụng nó và tạo ra một DMABUF khác. NPU sử dụng đầu ra của RGA. Câu hỏi đặt ra là ai sở hữu bộ đệm và khi nào nó có thể được giải phóng hoặc tái sử dụng. Một hệ thống quản lý bộ đệm mạnh mẽ là cần thiết. Điều này có thể liên quan đến việc đếm tham chiếu cho các FD DMABUF hoặc một nhóm các DMABUF được cấp phát trước xoay vòng qua các giai đoạn pipeline. Lỗi trong xử lý DMABUF có thể dẫn đến treo máy hoặc deadlock. Mã nguồn của ffmpeg-rockchip 22 có thể là một tài liệu tham khảo vô giá để xem cách DMABUF được xử lý giữa các thành phần FFmpeg (bao bọc MPP/RGA).

### **4.7. Kiến Trúc Đa Luồng**

Một kiến trúc đa luồng được thiết kế tốt là rất quan trọng để đạt được hiệu suất cao và khả năng mở rộng.

* **Nhóm Luồng (Thread Pools):** Sử dụng std::thread và các nhóm luồng để quản lý các tác vụ khác nhau.  
  * **Luồng RTSP/MPP:** Một luồng cho mỗi camera để xử lý đầu vào và giải mã.  
  * **Luồng Tiền Xử Lý RGA:** Một nhóm luồng để xử lý các khung hình từ các hàng đợi đã giải mã. Số lượng luồng có thể được điều chỉnh dựa trên khả năng của RGA và số lượng luồng.  
  * **Luồng Suy Luận NPU:** Một nhóm luồng để chạy suy luận. Mỗi luồng có thể lấy một context NPU, thiết lập đầu vào, chạy, lấy đầu ra.  
  * **Luồng Hậu Xử Lý:** Một nhóm luồng cho các tác vụ hậu xử lý nặng về CPU.  
* **Hàng Đợi (Queues):** Sử dụng các hàng đợi an toàn luồng (ví dụ: std::condition\_variable với std::mutex và std::queue, hoặc thư viện concurrentqueue) để truyền dữ liệu giữa các giai đoạn:  
  * DecodedFrameQueue: Lưu trữ MppFrame (hoặc FD DMABUF) từ các luồng MPP.  
  * DetectionInputQueue: Lưu trữ các khung hình đã được RGA xử lý sẵn sàng cho phát hiện khuôn mặt.  
  * RecognitionInputQueue: Lưu trữ các vùng khuôn mặt đã được RGA xử lý sẵn sàng cho định danh.  
  * ResultsQueue: Lưu trữ kết quả phát hiện và định danh cho logic ứng dụng.  
* **Đồng Bộ Hóa (Synchronization):**  
  * Biến điều kiện (condition variables) để báo hiệu dữ liệu có sẵn trong hàng đợi.  
  * Mutex để bảo vệ các tài nguyên chia sẻ (tuy nhiên, nên cố gắng giảm thiểu trạng thái có thể thay đổi được chia sẻ).  
  * Xem xét std::future và std::promise để quản lý các tác vụ NPU bất đồng bộ nếu không sử dụng trực tiếp RKNN\_FLAG\_ASYNC\_MODE.  
* **Quản Lý Context NPU trong Luồng:**  
  * Nếu các context NPU không an toàn luồng (phổ biến), mỗi luồng worker NPU có thể cần rknn\_context riêng của mình, hoặc các context phải được bảo vệ bằng mutex nếu được chia sẻ.  
  * Một giải pháp khác là tạo trước một nhóm các đối tượng rknn\_context (ví dụ: một cho mỗi lõi NPU, hoặc nhiều hơn nếu thực hiện các suy luận rất nhanh) và gán chúng cho các luồng worker. 64 đề cập đến "Pooled Runtimes" cho go-rknnlite, cho thấy việc gộp context là một mẫu hợp lệ.  
  * Changelog RKNN 13 (phiên bản 1.1.0) đề cập đến "Cải thiện sự ổn định của runtime đa luồng \+ đa tiến trình", cho thấy một số mức độ an toàn luồng hoặc hỗ trợ đã được phát triển.

Số lượng luồng tối ưu cho mỗi nhóm phụ thuộc vào thời gian xử lý tương đối của mỗi giai đoạn và số lượng lõi CPU có sẵn (cho các tác vụ giới hạn bởi CPU) cũng như khả năng của các bộ tăng tốc phần cứng (MPP, RGA, NPU). Quá ít luồng sẽ dẫn đến việc không tận dụng hết phần cứng. Quá nhiều luồng sẽ làm tăng chi phí chuyển đổi context và tranh chấp tài nguyên. Cần có sự cân bằng. Kích thước nhóm luồng nên có thể cấu hình và được điều chỉnh bằng thực nghiệm. Các công cụ phân tích hiệu suất (profiling tools) sẽ rất cần thiết để xác định các điểm nghẽn và điều chỉnh việc phân bổ luồng. Cân nhắc thiết lập CPU affinity cho các luồng vào các lõi CPU cụ thể (ví dụ: các lõi hiệu năng cao cho các worker NPU/RGA, các lõi tiết kiệm năng lượng cho I/O) để tối ưu hóa việc sử dụng cache và giảm jitter.64

## **5\. Tối Ưu Hóa Hiệu Suất và Phân Tích Khả Năng Mở Rộng**

### **5.1. Tối Đa Hóa Thông Lượng NPU**

Để đạt được hiệu suất cao nhất từ NPU, cần áp dụng các chiến lược sau:

* **Cân Bằng Tải Trên Ba Lõi NPU:**  
  * Nếu sử dụng rknn\_set\_core\_mask với RKNN\_NPU\_CORE\_0\_1\_2, runtime lý tưởng sẽ tự động cân bằng tải. Hiệu suất cần được xác minh.14  
  * Nếu gán context cho các lõi cụ thể một cách thủ công (ví dụ: mô hình phát hiện cho Lõi 0, mô hình định danh cho Lõi 1, mô hình phát hiện của luồng khác cho Lõi 2), cần đảm bảo phân phối đều khối lượng công việc.9  
  * "Chế độ đa lô đa lõi" (multi-batch multi-core mode) 13 là chìa khóa ở đây. Nếu một context đơn lẻ xử lý một lô trên nhiều lõi, đây là kịch bản lý tưởng.  
* **Xử Lý Theo Lô (Batch Processing):**  
  * Xử lý nhiều khung hình/khuôn mặt trong một lệnh gọi rknn\_run. Điều này làm giảm chi phí gọi API và có thể dẫn đến việc sử dụng phần cứng NPU tốt hơn.13  
  * Xác định kích thước lô tối ưu thông qua thử nghiệm. Quá nhỏ sẽ không hiệu quả; quá lớn sẽ tăng độ trễ.  
* **Lệnh Gọi NPU Bất Đồng Bộ:** Sử dụng RKNN\_FLAG\_ASYNC\_MODE 17 hoặc quản lý rknn\_run trong các luồng riêng biệt để chồng chéo việc thực thi NPU với các tác vụ CPU (ví dụ: tiền/hậu xử lý của lô tiếp theo).  
* **Độ Chính Xác Mô Hình:** INT8 thường mang lại thông lượng tốt nhất trên NPU so với FP16, với giả định độ chính xác chấp nhận được.1

Hiệu suất NPU không chỉ là TOPS thô, mà còn là mức độ hiệu quả của việc giữ cho pipeline NPU được cung cấp dữ liệu và hoạt động liên tục. Các chu kỳ NPU nhàn rỗi là hiệu suất bị lãng phí. NPU rất nhanh, nhưng việc chuẩn bị dữ liệu (RGA) và truyền dữ liệu có thể chậm hơn. Nếu NPU phải chờ dữ liệu, thông lượng sẽ giảm. Các hoạt động bất đồng bộ và xử lý theo lô giúp giữ cho NPU luôn bận rộn. Toàn bộ pipeline phải được thiết kế để cung cấp dữ liệu cho NPU với tốc độ xử lý của nó. Việc phân tích hiệu suất sẽ cho biết liệu NPU có bị "đói" dữ liệu hay không.

### **5.2. Tối Ưu Hóa Di Chuyển Dữ Liệu**

Giảm thiểu việc sao chép dữ liệu và đảm bảo dữ liệu được định dạng đúng cách là rất quan trọng.

* **Zero-Copy (DMABUF):** Nhấn mạnh lại tầm quan trọng của nó đối với luồng MPP \-\> RGA \-\> NPU.16  
* **Bố Cục Bộ Nhớ (Memory Layout):**  
  * Các mô hình NPU có thể có định dạng đầu vào/đầu ra là NHWC hoặc NCHW (rknn\_tensor\_attr.fmt).13  
  * RGA và MPP cũng có các định dạng đầu ra cụ thể. Đảm bảo khả năng tương thích hoặc sử dụng RGA để chuyển đổi bố cục nếu cần (mặc dù điều này thêm một thao tác). Lý tưởng nhất, RGA xuất trực tiếp theo bố cục yêu cầu của NPU (ví dụ: NHWC cho RKNN\_TENSOR\_UINT8).  
  * Changelog RKNN 13 (phiên bản 1.4.0) đề cập "Thêm bố cục đầu ra mới NHWC (C có các hạn chế về căn chỉnh)". Điều này quan trọng đối với NPU.  
* **Chiến Lược Cấp Phát Bộ Đệm:**  
  * Cấp phát trước một nhóm DMABUF để tái sử dụng qua các giai đoạn pipeline nhằm giảm chi phí cấp phát/hủy cấp phát.  
  * Quản lý vòng đời bộ đệm cẩn thận, đặc biệt trong môi trường đa luồng, bất đồng bộ.

### **5.3. Giảm Thiểu Độ Trễ (Latency)**

Ngoài thông lượng, độ trễ từ khi camera ghi hình đến khi có kết quả cũng rất quan trọng.

* **Tối Ưu Hóa Từng Giai Đoạn:** Xác định và tối ưu hóa giai đoạn có độ trễ lớn nhất trong pipeline (ví dụ: giải mã, tiền xử lý, suy luận NPU, hậu xử lý).  
* **Kích Thước Lô Nhỏ Hơn:** Mặc dù xử lý theo lô lớn tăng thông lượng, nhưng nó cũng có thể tăng độ trễ cho từng khung hình riêng lẻ. Cần tìm sự cân bằng.  
* **Suy Luận Bất Đồng Bộ Hiệu Quả:** Đảm bảo rằng việc xử lý bất đồng bộ không dẫn đến hàng đợi quá dài, làm tăng độ trễ tổng thể.  
* **Lựa Chọn Mô Hình Nhanh:** Các mô hình nhẹ hơn thường có độ trễ suy luận thấp hơn.

### **5.4. Mở Rộng Số Lượng Camera**

Mục tiêu là hỗ trợ càng nhiều camera càng tốt mà vẫn duy trì hiệu suất chấp nhận được.

* **Phân Tích Điểm Nghẽn (Bottleneck Analysis):** Khi tăng số lượng camera, một tài nguyên nào đó (CPU, NPU, VPU, RGA, băng thông bộ nhớ) sẽ trở thành điểm nghẽn đầu tiên.  
  * **NPU:** Giới hạn bởi tổng khả năng tính toán (6 TOPS, thực tế có thể thấp hơn do lượng tử hóa INT8 và hiệu quả sử dụng đa lõi) và băng thông bộ nhớ NPU. Số lượng context NPU đồng thời cũng bị giới hạn bởi bộ nhớ (đặc biệt là giới hạn 4GB có thể địa chỉ hóa cho NPU 9).  
  * **VPU (MPP):** Giới hạn bởi khả năng giải mã đồng thời của VPU. RK3588 có VPU mạnh, nhưng vẫn có giới hạn thực tế về số luồng HD.  
  * **RGA:** Giới hạn bởi thông lượng của (các) đơn vị RGA, đặc biệt nếu mỗi khung hình cần nhiều lượt RGA.  
  * **CPU:** Giới hạn bởi khả năng xử lý các tác vụ không thể tăng tốc (quản lý luồng, hậu xử lý, logic ứng dụng).  
  * **Băng Thông Bộ Nhớ:** Zero-copy giúp giảm tải, nhưng nhiều luồng dữ liệu độ phân giải cao vẫn có thể gây áp lực lên băng thông DDR.  
* **Chiến Lược Mở Rộng:**  
  * **Phân Bổ Tài Nguyên Động:** Nếu có thể, điều chỉnh động số lượng luồng xử lý hoặc ưu tiên cho các camera quan trọng hơn khi tải hệ thống cao.  
  * **Giảm Chất Lượng Đầu Vào:** Đối với một số camera ít quan trọng hơn hoặc khi hệ thống quá tải, có thể giảm độ phân giải hoặc tốc độ khung hình của luồng RTSP đầu vào để giảm tải.  
  * **Tối Ưu Hóa Mô Hình Cực Đoan:** Sử dụng các mô hình nhỏ nhất có thể chấp nhận được về độ chính xác.

### **5.5. Phân Tích và Gỡ Lỗi Hệ Thống (Profiling and Debugging)**

* **Công Cụ Profiling:**  
  * Sử dụng các công cụ profiling của Linux như perf, htop, vmstat để theo dõi việc sử dụng CPU, bộ nhớ, I/O.  
  * RKNN SDK có thể cung cấp các API hoặc công cụ để theo dõi hiệu suất NPU. rknn\_query với RKNN\_QUERY\_PERF\_DETAIL hoặc RKNN\_QUERY\_PERF\_RUN có thể cung cấp thời gian thực thi của NPU.17 Cờ RKNN\_FLAG\_COLLECT\_PERF\_MASK khi gọi rknn\_init là cần thiết cho việc này.  
* **Logging Chi Tiết:** Triển khai logging chi tiết trong ứng dụng C++ để theo dõi thời gian xử lý của từng giai đoạn và luồng dữ liệu.  
* **Kiểm Tra Từng Module:** Kiểm tra và benchmark từng module (MPP, RGA, NPU) một cách riêng biệt trước khi tích hợp toàn bộ hệ thống.  
* **Xác Minh Độ Chính Xác:** Sau khi lượng tử hóa và triển khai trên NPU, cần xác minh lại độ chính xác của mô hình so với mô hình gốc FP32.

## **6\. Các Cân Nhắc Kỹ Thuật và Thực Tiễn Tốt Nhất**

### **6.1. Thiết Lập Hệ Điều Hành và Trình Điều Khiển**

* **Hệ Điều Hành:** Nên sử dụng một bản phân phối Linux được hỗ trợ tốt cho RK3588, như Debian hoặc Ubuntu, với kernel được cung cấp bởi Rockchip hoặc cộng đồng có hỗ trợ driver NPU, MPP, RGA.27  
* **Trình Điều Khiển (Drivers):** Đảm bảo rằng các trình điều khiển kernel cho NPU (RKNPU), VPU (MPP), và RGA được cài đặt và hoạt động chính xác. Kiểm tra phiên bản driver (ví dụ: cat /sys/kernel/debug/rknpu/version 28) và đảm bảo chúng tương thích với phiên bản RKNN Runtime đang sử dụng.  
* **Thư Viện Phụ Thuộc:** Cài đặt các thư viện phụ thuộc cần thiết như librknn\_api.so, libmpp.so, librga.so, OpenCV (nếu dùng cho hậu xử lý), FFmpeg/GStreamer.

### **6.2. Quản Lý Năng Lượng và Nhiệt Độ**

* **Tản Nhiệt:** RK3588 khi hoạt động ở tải cao (đặc biệt là NPU và CPU) sẽ tỏa nhiệt đáng kể. Cần có giải pháp tản nhiệt tốt (heatsink, quạt) để tránh quá nhiệt và suy giảm hiệu suất (thermal throttling).  
* **Quản Lý Tần Số (DVFS):** Hệ điều hành Linux trên RK3588 thường có cơ chế DVFS (Dynamic Voltage and Frequency Scaling) để điều chỉnh tần số CPU/GPU/NPU dựa trên tải. Đảm bảo rằng các cài đặt này được tối ưu cho hiệu suất khi cần thiết (ví dụ: đặt ở chế độ "performance").  
* **Theo Dõi Nhiệt Độ:** Theo dõi nhiệt độ SoC trong quá trình hoạt động để đảm bảo nó nằm trong giới hạn an toàn.

### **6.3. Xử Lý Lỗi và Độ Ổn Định Hệ Thống**

* **Kiểm Tra Lỗi API:** Luôn kiểm tra giá trị trả về của tất cả các lệnh gọi API (MPP, RGA, RKNN) và xử lý lỗi một cách thích hợp.  
* **Quản Lý Tài Nguyên:** Giải phóng tất cả các tài nguyên (bộ nhớ, context, file descriptor) một cách chính xác để tránh rò rỉ tài nguyên.  
* **Xử Lý Ngoại Lệ (Exception Handling):** Sử dụng cơ chế xử lý ngoại lệ của C++ một cách cẩn thận để bắt và xử lý các lỗi không mong muốn.  
* **Watchdog:** Cân nhắc sử dụng watchdog phần cứng hoặc phần mềm để khởi động lại hệ thống nếu ứng dụng bị treo.

### **6.4. Môi Trường Phát Triển và Chuỗi Công Cụ (Toolchain)**

* **Cross-Compilation:** Phát triển ứng dụng C++ trên máy tính x86 và biên dịch chéo (cross-compile) cho kiến trúc aarch64 của RK3588. Sử dụng một chuỗi công cụ GCC aarch64-linux-gnu phù hợp.30  
* **RKNN-Toolkit2:** Cài đặt trên máy tính phát triển để chuyển đổi và lượng tử hóa mô hình.3  
* **Gỡ Lỗi Từ Xa (Remote Debugging):** Sử dụng GDB server trên thiết bị RK3588 và GDB client trên máy tính phát triển để gỡ lỗi ứng dụng từ xa.  
* **Quản Lý Phiên Bản:** Sử dụng hệ thống quản lý phiên bản như Git để theo dõi mã nguồn.

## **7\. Kết Luận và Khuyến Nghị**

Việc triển khai đồng thời tác vụ phát hiện và định danh khuôn mặt trên nhiều luồng camera RTSP bằng NPU RK3588 đòi hỏi một cách tiếp cận hệ thống, tối ưu hóa từng thành phần từ đầu vào đến đầu ra. RK3588 cung cấp một nền tảng phần cứng mạnh mẽ với CPU đa lõi, NPU 6 TOPS ba lõi, VPU và RGA chuyên dụng, tạo điều kiện thuận lợi cho các ứng dụng AI thị giác phức tạp.

**Các khuyến nghị chính bao gồm:**

1. **Kiến Trúc Module Hóa Đa Luồng:** Xây dựng ứng dụng C++ với các module riêng biệt cho từng giai đoạn của pipeline (RTSP input, MPP decode, RGA preprocess, NPU inference, Postprocess). Sử dụng các hàng đợi an toàn luồng để giao tiếp giữa các module, cho phép xử lý song song và tối đa hóa việc sử dụng tài nguyên.  
2. **Tận Dụng Tối Đa Phần Cứng Chuyên Dụng:**  
   * Sử dụng **MPP** để giải mã phần cứng các luồng video H.264/H.265.  
   * Sử dụng **RGA** để tăng tốc các tác vụ tiền xử lý hình ảnh như thay đổi kích thước và chuyển đổi không gian màu.  
   * Tập trung suy luận AI vào **NPU**, sử dụng các mô hình đã được lượng tử hóa INT8.  
3. **Tối Ưu Hóa NPU:**  
   * **Lựa chọn mô hình:** Ưu tiên các mô hình nhẹ, hiệu quả và tương thích tốt với NPU như RetinaFace (cho phát hiện) và ArcFace/FaceNet (cho định danh) với backbone MobileNet. Tham khảo RKNN Model Zoo.  
   * **Chuyển đổi và Lượng tử hóa:** Sử dụng RKNN-Toolkit2 để chuyển đổi mô hình sang định dạng .rknn và thực hiện lượng tử hóa INT8 với một tập dữ liệu hiệu chuẩn chất lượng cao.  
   * **Đa lõi NPU:** Khai thác kiến trúc ba lõi của NPU RK3588 bằng cách sử dụng API rknn\_set\_core\_mask để phân bổ các context NPU cho các lõi cụ thể hoặc cho phép runtime tự động quản lý (ví dụ: RKNN\_NPU\_CORE\_0\_1\_2). "Chế độ đa lô đa lõi" là một tính năng hứa hẹn cần được khai thác.  
   * **Xử lý theo lô (Batch Processing):** Gom nhiều khung hình hoặc nhiều vùng khuôn mặt để suy luận đồng thời trong một lệnh gọi rknn\_run, nhằm tăng thông lượng NPU.  
   * **Suy luận bất đồng bộ:** Sử dụng cờ RKNN\_FLAG\_ASYNC\_MODE khi khởi tạo context NPU để cho phép CPU tiếp tục các tác vụ khác trong khi NPU đang xử lý.  
4. **Zero-Copy Data Flow:** Triển khai triệt để việc sử dụng DMABUF để chia sẻ bộ đệm giữa MPP, RGA và NPU. Điều này yêu cầu sử dụng các API như mpp\_frame\_get\_fd, rga\_import\_buffer\_fd, rknn\_create\_mem\_from\_fd, và rknn\_set\_io\_mem với cờ RKNN\_FLAG\_MEM\_ALLOC\_OUTSIDE.  
5. **Quản Lý Bộ Nhớ Cẩn Thận:** Đặc biệt lưu ý đến giới hạn 4GB bộ nhớ có thể địa chỉ hóa của NPU. Tối ưu hóa kích thước mô hình và số lượng context NPU đồng thời.  
6. **Tối Ưu Hóa Hậu Xử Lý:** Các thuật toán hậu xử lý (đặc biệt là NMS) chạy trên CPU cần được tối ưu hóa cẩn thận để không trở thành điểm nghẽn.  
7. **Profiling và Thử Nghiệm Liên Tục:** Sử dụng các công cụ profiling để xác định các điểm nghẽn trong toàn bộ pipeline và thực hiện thử nghiệm lặp đi lặp lại để tinh chỉnh các tham số (kích thước lô, số lượng luồng, phân bổ lõi NPU) nhằm đạt được sự cân bằng tốt nhất giữa thông lượng, độ trễ và số lượng camera được hỗ trợ.  
8. **Sử Dụng SDK và Driver Mới Nhất:** Đảm bảo sử dụng các phiên bản mới nhất và ổn định của RKNN SDK, RKNPU runtime, trình điều khiển kernel, librga, và libmpp để tận dụng các bản sửa lỗi và cải tiến hiệu suất mới nhất từ Rockchip.

Bằng cách áp dụng các chiến lược và kỹ thuật được trình bày trong báo cáo này, có thể xây dựng một hệ thống phát hiện và định danh khuôn mặt mạnh mẽ, hiệu quả và có khả năng mở rộng cao trên nền tảng RK3588, đáp ứng yêu cầu về hiệu suất và số lượng camera của người dùng. Quá trình này đòi hỏi sự hiểu biết sâu sắc về cả phần cứng và phần mềm, cùng với sự kiên nhẫn trong việc tối ưu hóa và gỡ lỗi.

#### **Works cited**

1. en.t-firefly.com, accessed May 29, 2025, [https://en.t-firefly.com/product/industry/rocrk3588pc](https://en.t-firefly.com/product/industry/rocrk3588pc)  
2. hejiangyan/RK3588-product-data: This repository is RK3588 Development Board Documents \- GitHub, accessed May 29, 2025, [https://github.com/hejiangyan/RK3588-product-data](https://github.com/hejiangyan/RK3588-product-data)  
3. 3\. RKNN SDK Quick Start Guide \- ArmSoM docs, accessed May 29, 2025, [https://docs.armsom.org/advanced-manual/rknn-sdk](https://docs.armsom.org/advanced-manual/rknn-sdk)  
4. Banana Pi Rockchip RKNN SDK quick start Guide \- BananaPi Docs, accessed May 29, 2025, [https://docs.banana-pi.org/en/BPI-CM5\_Pro/BananaPi\_BPI-CM5\_Pro/Rockchip\_RKNN\_Guide](https://docs.banana-pi.org/en/BPI-CM5_Pro/BananaPi_BPI-CM5_Pro/Rockchip_RKNN_Guide)  
5. rockchip-linux/mpp: Media Process Platform (MPP) module \- GitHub, accessed May 29, 2025, [https://github.com/rockchip-linux/mpp](https://github.com/rockchip-linux/mpp)  
6. Mpp \- Rockchip open source Document, accessed May 29, 2025, [https://opensource.rock-chips.com/wiki\_Mpp](https://opensource.rock-chips.com/wiki_Mpp)  
7. librga/docs/Rockchip\_Developer\_Guide\_RGA\_EN.md at main \- GitHub, accessed May 29, 2025, [https://github.com/airockchip/librga/blob/main/docs/Rockchip\_Developer\_Guide\_RGA\_EN.md](https://github.com/airockchip/librga/blob/main/docs/Rockchip_Developer_Guide_RGA_EN.md)  
8. A Comparative Analysis between RK3588 and RK3576 Chips: Unveiling the Technological Distinctions \- ArmSoM, accessed May 29, 2025, [https://www.armsom.org/post/a-comparative-analysis-between-rk3588-and-rk3576-chips-unveiling-the-technological-distinctions](https://www.armsom.org/post/a-comparative-analysis-between-rk3588-and-rk3576-chips-unveiling-the-technological-distinctions)  
9. RK3588 \- Reverse engineering the RKNN (Rockchip Neural Processing Unit) \- Tiny Devices, accessed May 29, 2025, [http://jas-hacks.blogspot.com/2024/02/rk3588-reverse-engineering-rknn.html](http://jas-hacks.blogspot.com/2024/02/rk3588-reverse-engineering-rknn.html)  
10. Edge Vision AI Co-Processing for Dynamic Context Awareness in Mixed Reality, accessed May 29, 2025, [https://www.computer.org/csdl/proceedings-article/vrw/2025/148400b294/26aUIwiHUm4](https://www.computer.org/csdl/proceedings-article/vrw/2025/148400b294/26aUIwiHUm4)  
11. Rockchip NPUs and deploying scikit-learn models on them \- Martin's website/blog thingy, accessed May 29, 2025, [https://clehaxze.tw/gemlog/2023/07-13-rockchip-npus-and-deploying-scikit-learn-models-on-them.gmi](https://clehaxze.tw/gemlog/2023/07-13-rockchip-npus-and-deploying-scikit-learn-models-on-them.gmi)  
12. Orange Pi 5 RK3588S NPU Speeds? : r/OrangePI \- Reddit, accessed May 29, 2025, [https://www.reddit.com/r/OrangePI/comments/1annxuk/orange\_pi\_5\_rk3588s\_npu\_speeds/](https://www.reddit.com/r/OrangePI/comments/1annxuk/orange_pi_5_rk3588s_npu_speeds/)  
13. rockchip-linux/rknpu2 \- GitHub, accessed May 29, 2025, [https://github.com/rockchip-linux/rknpu2](https://github.com/rockchip-linux/rknpu2)  
14. \[RKNN\] 3\. 零拷贝接口推理原创 \- CSDN博客, accessed May 29, 2025, [https://blog.csdn.net/weixin\_43337573/article/details/131817935](https://blog.csdn.net/weixin_43337573/article/details/131817935)  
15. 1\. API 硬件平台支持说明 \- EASY EAI灵眸科技, accessed May 29, 2025, [https://www.easy-eai.com/document\_details/18/614](https://www.easy-eai.com/document_details/18/614)  
16. Guide to Using RKNN Instances \- LUCKFOX WIKI, accessed May 29, 2025, [https://wiki.luckfox.com/Luckfox-Pico/RKNN-example/](https://wiki.luckfox.com/Luckfox-Pico/RKNN-example/)  
17. rknpu2/runtime/RK3588/Linux/librknn\_api/include/rknn\_api.h at master \- GitHub, accessed May 29, 2025, [https://github.com/rockchip-linux/rknpu2/blob/master/runtime/RK3588/Linux/librknn\_api/include/rknn\_api.h](https://github.com/rockchip-linux/rknpu2/blob/master/runtime/RK3588/Linux/librknn_api/include/rknn_api.h)  
18. RK3588 Encoding and Decoding Analysis of Mpp Decoding Demo: mpi\_dec\_test, accessed May 29, 2025, [https://forum.armsom.org/t/rk3588-encoding-and-decoding-analysis-of-mpp-decoding-demo-mpi-dec-test/152](https://forum.armsom.org/t/rk3588-encoding-and-decoding-analysis-of-mpp-decoding-demo-mpi-dec-test/152)  
19. mpp/test/mpi\_dec\_test.c at develop · rockchip-linux/mpp \- GitHub, accessed May 29, 2025, [https://github.com/rockchip-linux/mpp/blob/develop/test/mpi\_dec\_test.c](https://github.com/rockchip-linux/mpp/blob/develop/test/mpi_dec_test.c)  
20. RK3399Pro入门教程（6）硬件编解码器MPP库的使用 \- Toybrick, accessed May 29, 2025, [https://t.rock-chips.com/forum.php?mod=viewthread\&action=printable\&tid=336](https://t.rock-chips.com/forum.php?mod=viewthread&action=printable&tid=336)  
21. GStreamer学习 \- FunnyWii's Zone, accessed May 29, 2025, [https://funnywii.com/archives/1743413572286](https://funnywii.com/archives/1743413572286)  
22. nyanmisaka/ffmpeg-rockchip: FFmpeg with async and zero ... \- GitHub, accessed May 29, 2025, [https://github.com/nyanmisaka/ffmpeg-rockchip](https://github.com/nyanmisaka/ffmpeg-rockchip)  
23. Gstreamer drm sink, zero copy \- Raspberry Pi Forums, accessed May 29, 2025, [https://forums.raspberrypi.com/viewtopic.php?t=375855](https://forums.raspberrypi.com/viewtopic.php?t=375855)  
24. github.com, accessed May 29, 2025, [https://github.com/airockchip/librga/tree/main/samples](https://github.com/airockchip/librga/tree/main/samples)  
25. accessed January 1, 1970, [https://github.com/airockchip/librga/tree/main/samples/im2d\_api\_demo](https://github.com/airockchip/librga/tree/main/samples/im2d_api_demo)  
26. accessed January 1, 1970, [https://github.com/JeffyCN/mirrors/tree/linux-rga-LTS/samples/im2d\_api\_demo](https://github.com/JeffyCN/mirrors/tree/linux-rga-LTS/samples/im2d_api_demo)  
27. RKNN Installation | Radxa Docs, accessed May 29, 2025, [https://docs.radxa.com/en/rock5/rock5c/app-development/rknn\_install](https://docs.radxa.com/en/rock5/rock5c/app-development/rknn_install)  
28. NPU \- FriendlyELEC WiKi, accessed May 29, 2025, [https://wiki.friendlyelec.com/wiki/index.php/NPU](https://wiki.friendlyelec.com/wiki/index.php/NPU)  
29. airockchip/rknn-toolkit2 \- GitHub, accessed May 29, 2025, [https://github.com/airockchip/rknn-toolkit2](https://github.com/airockchip/rknn-toolkit2)  
30. airockchip/rknn\_model\_zoo \- GitHub, accessed May 29, 2025, [https://github.com/airockchip/rknn\_model\_zoo](https://github.com/airockchip/rknn_model_zoo)  
31. README.md \- RKNN Model Zoo \- GitHub, accessed May 29, 2025, [https://github.com/airockchip/rknn\_model\_zoo/blob/main/README.md](https://github.com/airockchip/rknn_model_zoo/blob/main/README.md)  
32. rknn\_api.h \- Index of /, accessed May 29, 2025, [https://repo.rock-chips.com/rk1808/rknn-api/Android/rknn\_api/include/rknn\_api.h](https://repo.rock-chips.com/rk1808/rknn-api/Android/rknn_api/include/rknn_api.h)  
33. rknn-toolkit2/rknpu2/runtime/Linux/librknn\_api/include/rknn\_matmul\_api.h at master, accessed May 29, 2025, [https://github.com/airockchip/rknn-toolkit2/blob/master/rknpu2/runtime/Linux/librknn\_api/include/rknn\_matmul\_api.h](https://github.com/airockchip/rknn-toolkit2/blob/master/rknpu2/runtime/Linux/librknn_api/include/rknn_matmul_api.h)  
34. rknn/rknn\_mobilenet.cpp at master · yijiesun/rknn \- GitHub, accessed May 29, 2025, [https://github.com/yijiesun/rknn/blob/master/rknn\_mobilenet.cpp](https://github.com/yijiesun/rknn/blob/master/rknn_mobilenet.cpp)  
35. Rockchip User Guide RKNN API \- Index of /, accessed May 29, 2025, [https://repo.rock-chips.com/rk1808/doc/Rockchip\_User\_Guide\_RKNN\_API\_EN.pdf](https://repo.rock-chips.com/rk1808/doc/Rockchip_User_Guide_RKNN_API_EN.pdf)  
36. rknpu2/examples/rknn\_api\_demo/src/rknn\_create\_mem\_demo.cpp at master \- GitHub, accessed May 29, 2025, [https://github.com/rockchip-linux/rknpu2/blob/master/examples/rknn\_api\_demo/src/rknn\_create\_mem\_demo.cpp](https://github.com/rockchip-linux/rknpu2/blob/master/examples/rknn_api_demo/src/rknn_create_mem_demo.cpp)  
37. 【9】使用RKNPU2的C API接口将RKNN模型部署在RK3588开发板上 \- CSDN博客, accessed May 29, 2025, [https://blog.csdn.net/m0\_48241022/article/details/141687378](https://blog.csdn.net/m0_48241022/article/details/141687378)  
38. ArcFace RKNN Conversion Failure: Output is Always Zero on RV1106 \- Luckfox Forums, accessed May 29, 2025, [https://forums.luckfox.com/viewtopic.php?t=1724](https://forums.luckfox.com/viewtopic.php?t=1724)  
39. Benchmarking RK3588 NPU matrix multiplication performance EP3 \- Martin's blog\!, accessed May 29, 2025, [https://clehaxze.tw/gemlog/2024/02-14-benchmarking-rk3588-npu-matrix-multiplcation-performance-ep2.gmi](https://clehaxze.tw/gemlog/2024/02-14-benchmarking-rk3588-npu-matrix-multiplcation-performance-ep2.gmi)  
40. From CPU to NPU: The Secret to \~15x Faster AI on Intel's Latest Chips : r/computervision \- Reddit, accessed May 29, 2025, [https://www.reddit.com/r/computervision/comments/1ihhbds/from\_cpu\_to\_npu\_the\_secret\_to\_15x\_faster\_ai\_on/](https://www.reddit.com/r/computervision/comments/1ihhbds/from_cpu_to_npu_the_secret_to_15x_faster_ai_on/)  
41. Use YoloV8 in RK3588 NPU \- ROCK 5 Series \- Radxa Community, accessed May 29, 2025, [https://forum.radxa.com/t/use-yolov8-in-rk3588-npu/15838?page=7](https://forum.radxa.com/t/use-yolov8-in-rk3588-npu/15838?page=7)  
42. YOLOv7-tiny Edge2 Demo \- 1 \- Khadas Docs, accessed May 29, 2025, [https://docs.khadas.com/products/sbc/edge2/npu/demos/yolov7-tiny](https://docs.khadas.com/products/sbc/edge2/npu/demos/yolov7-tiny)  
43. The Feasibility of Using RK3588J to Build Robot Control Board \- Dusun IoT, accessed May 29, 2025, [https://www.dusuniot.com/blog/rk3588-robot-control-board/](https://www.dusuniot.com/blog/rk3588-robot-control-board/)  
44. FaceNet PyTorch Edge2 Demo \- 6 \[Khadas Docs\], accessed May 29, 2025, [https://docs.khadas.com/products/sbc/edge2/npu/demos/facenet](https://docs.khadas.com/products/sbc/edge2/npu/demos/facenet)  
45. HyperInspire/inspireface-android-rk356x-rk3588-sdk \- GitHub, accessed May 29, 2025, [https://github.com/HyperInspire/inspireface-android-rk356x-rk3588-sdk](https://github.com/HyperInspire/inspireface-android-rk356x-rk3588-sdk)  
46. Build for RKNN — mmdeploy 1.3.1 documentation \- Read the Docs, accessed May 29, 2025, [https://mmdeploy.readthedocs.io/en/stable/01-how-to-build/rockchip.html](https://mmdeploy.readthedocs.io/en/stable/01-how-to-build/rockchip.html)  
47. RKNN-Toolkit Trouble Shooting, accessed May 29, 2025, [https://dl.radxa.com/rockpin10/docs/sw/rknn-toolkit/Rockchip\_Trouble\_Shooting\_RKNN\_Toolkit\_V1.3\_EN.pdf](https://dl.radxa.com/rockpin10/docs/sw/rknn-toolkit/Rockchip_Trouble_Shooting_RKNN_Toolkit_V1.3_EN.pdf)  
48. RK Platform NPU SDK \- ESS-WIKI, accessed May 29, 2025, [http://ess-wiki.advantech.com.tw/view/RK\_Platform\_NPU\_SDK](http://ess-wiki.advantech.com.tw/view/RK_Platform_NPU_SDK)  
49. Rockchip User Guide RKNN-Toolkit EN \- Index of /, accessed May 29, 2025, [https://repo.rock-chips.com/rk1808/doc/Rockchip\_User\_Guide\_RKNN\_Toolkit\_EN.pdf](https://repo.rock-chips.com/rk1808/doc/Rockchip_User_Guide_RKNN_Toolkit_EN.pdf)  
50. rknn\_model\_zoo/examples/yolov8/README.md at main \- GitHub, accessed May 29, 2025, [https://github.com/airockchip/rknn\_model\_zoo/blob/main/examples/yolov8/README.md](https://github.com/airockchip/rknn_model_zoo/blob/main/examples/yolov8/README.md)  
51. rknn\_model\_zoo/examples/yolov8\_seg/README.md at main \- GitHub, accessed May 29, 2025, [https://github.com/airockchip/rknn\_model\_zoo/blob/main/examples/yolov8\_seg/README.md](https://github.com/airockchip/rknn_model_zoo/blob/main/examples/yolov8_seg/README.md)  
52. Decoding and rendering 16 1080p streams with Gstreamer \- ROCK 5 Series \- Radxa forum, accessed May 29, 2025, [https://forum.radxa.com/t/decoding-and-rendering-16-1080p-streams-with-gstreamer/16307](https://forum.radxa.com/t/decoding-and-rendering-16-1080p-streams-with-gstreamer/16307)  
53. How to live stream from multiple webcam \- ffmpeg \- Stack Overflow, accessed May 29, 2025, [https://stackoverflow.com/questions/75327892/how-to-live-stream-from-multiple-webcam](https://stackoverflow.com/questions/75327892/how-to-live-stream-from-multiple-webcam)  
54. Receiving RTSP stream using FFMPEG library \- c++ \- Stack Overflow, accessed May 29, 2025, [https://stackoverflow.com/questions/10715170/receiving-rtsp-stream-using-ffmpeg-library](https://stackoverflow.com/questions/10715170/receiving-rtsp-stream-using-ffmpeg-library)  
55. Using Gstreamer to serve RTSP stream, working example sought \- Stack Overflow, accessed May 29, 2025, [https://stackoverflow.com/questions/13744560/using-gstreamer-to-serve-rtsp-stream-working-example-sought](https://stackoverflow.com/questions/13744560/using-gstreamer-to-serve-rtsp-stream-working-example-sought)  
56. Using Gstreamer to decode 4K video to 1080p \- Firefly Open Source CommunityHOME, accessed May 29, 2025, [https://bbs.t-firefly.com/forum.php?mod=viewthread\&tid=2009](https://bbs.t-firefly.com/forum.php?mod=viewthread&tid=2009)  
57. MPP Development Reference \- Cool Pi For You, accessed May 29, 2025, [https://wiki.cool-pi.com/media/rockchip\_developer\_guide\_mpp\_en.pdf](https://wiki.cool-pi.com/media/rockchip_developer_guide_mpp_en.pdf)  
58. librga/CHANGELOG.md at main \- GitHub, accessed May 29, 2025, [https://github.com/airockchip/librga/blob/main/CHANGELOG.md](https://github.com/airockchip/librga/blob/main/CHANGELOG.md)  
59. sravansenthiln1/rga-demos: Examples to use Rockchip RK3588s RGA2-E/RGA3 platform., accessed May 29, 2025, [https://github.com/sravansenthiln1/rga-demos](https://github.com/sravansenthiln1/rga-demos)  
60. 03\_Rockchip\_RKNPU\_API\_Refe... \- Scribd, accessed May 29, 2025, [https://it.scribd.com/document/805027468/03-Rockchip-RKNPU-API-Reference-RKNN-Toolkit2-V1-6-0-EN](https://it.scribd.com/document/805027468/03-Rockchip-RKNPU-API-Reference-RKNN-Toolkit2-V1-6-0-EN)  
61. Rockchip RV1126 模型部署（完整部署流程） 原创 \- CSDN博客, accessed May 29, 2025, [https://blog.csdn.net/kxh123456/article/details/129370265](https://blog.csdn.net/kxh123456/article/details/129370265)  
62. accessed January 1, 1970, [https://github.com/airockchip/rknn-toolkit2/blob/master/rknpu2/runtime/Linux/librknn\_api/include/rknn\_api.h](https://github.com/airockchip/rknn-toolkit2/blob/master/rknpu2/runtime/Linux/librknn_api/include/rknn_api.h)  
63. Accuracy degradation on converted RKNN model \- Zero Series \- Radxa forum, accessed May 29, 2025, [https://forum.radxa.com/t/accuracy-degradation-on-converted-rknn-model/23502](https://forum.radxa.com/t/accuracy-degradation-on-converted-rknn-model/23502)  
64. rknnlite package \- github.com/swdee/go-rknnlite \- Go Packages, accessed May 29, 2025, [https://pkg.go.dev/github.com/swdee/go-rknnlite](https://pkg.go.dev/github.com/swdee/go-rknnlite)  
65. RKLLM Installation \- Radxa Docs, accessed May 29, 2025, [https://docs.radxa.com/en/rock5/rock5c/app-development/rkllm\_install](https://docs.radxa.com/en/rock5/rock5c/app-development/rkllm_install)