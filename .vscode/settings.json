{
  // File Associations
  "files.associations": {
    "iostream": "cpp",
    "sstream": "cpp",
    "fstream": "cpp",
    "qhboxlayout": "cpp",
    "qdebug": "cpp",
    "qframe": "cpp",
    "qscrollarea": "cpp",
    "qstandardpaths": "cpp",
    "qpushbutton": "cpp",
    "qcloseevent": "cpp",
    "qthread": "cpp",
    "*.h": "cpp",
    "*.hpp": "cpp",
    "*.cpp": "cpp",
    "*.cc": "cpp",
    "*.cxx": "cpp",
    "*.c": "c",
    "CMakeLists.txt": "cmake",
    "*.cmake": "cmake",
    "*.pro": "qmake",
    "*.pri": "qmake",
    "*.ui": "xml",
    "*.qrc": "xml"
  },
  // C++ Configuration (using clangd)
  "C_Cpp.intelliSenseEngine": "disabled",
  "C_Cpp.autocomplete": "disabled",
  "C_Cpp.errorSquiggles": "disabled",
  // Clangd Configuration
  "clangd.arguments": [
    "--compile-commands-dir=${workspaceFolder}/build",
    "--background-index",
    "--clang-tidy",
    "--completion-style=detailed",
    "--header-insertion=iwyu",
    "--pch-storage=memory"
  ],
  "clangd.fallbackFlags": [
    "-std=c++20",
    "-I${workspaceFolder}",
    "-I${workspaceFolder}/libraries/shared/include",
    "-I${workspaceFolder}/apps/client/include",
    "-I${workspaceFolder}/apps/server/include"
  ],
  // Code Formatting
  "editor.defaultFormatter": "xaver.clang-format",
  "[cpp]": {
    "editor.defaultFormatter": "xaver.clang-format"
  },
  "[c]": {
    "editor.defaultFormatter": "xaver.clang-format"
  },
  // CMake Configuration
  "cmake.buildDirectory": "${workspaceFolder}/build",
  "cmake.sourceDirectory": "${workspaceFolder}",
  "cmake.configureOnOpen": true,
  "cmake.buildBeforeRun": true,
  "cmake.clearOutputBeforeBuild": true,
  "cmake.generator": "Unix Makefiles",
  "cmake.installPrefix": "${workspaceFolder}/install",
  "cmake.configureArgs": [
    "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
  ],
  // Clang-tidy Configuration
  "clang-tidy.executable": "clang-tidy",
  "clang-tidy.checks": [
    "clang-analyzer-*",
    "cppcoreguidelines-*",
    "modernize-*",
    "performance-*",
    "readability-*",
    "-readability-magic-numbers",
    "-cppcoreguidelines-avoid-magic-numbers"
  ],
  "clang-tidy.compilerArgs": [
    "-std=c++20"
  ],
  // Debug Configuration
  "debug.allowBreakpointsEverywhere": true,
  "debug.showBreakpointsInOverviewRuler": true,
  "debug.showInlineBreakpointCandidates": true,
  "debug.openDebug": "openOnDebugBreak",
  "debug.internalConsoleOptions": "openOnSessionStart",
  "debug.console.fontSize": 14,
  "debug.console.wordWrap": true,
  // Editor Configuration
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.formatOnType": false,
  "editor.rulers": [
    80,
    120
  ],
  "editor.renderWhitespace": "boundary",
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.showFoldingControls": "always",
  "editor.foldingStrategy": "indentation",
  "editor.minimap.enabled": true,
  "editor.minimap.showSlider": "always",
  "editor.tabSize": 4,
  "editor.insertSpaces": true,
  "editor.detectIndentation": true,
  "editor.wordWrap": "off",
  "editor.lineNumbers": "on",
  "editor.cursorBlinking": "blink",
  "editor.cursorStyle": "line",
  // Spell Checker Configuration
  "cSpell.words": [
    "clangd",
    "cpptools",
    "xaver",
    "vadimcn",
    "twxs",
    "notskm",
    "cschlosser",
    "doxdocgen",
    "hexeditor",
    "eamodio",
    "mhutchie",
    "tonka",
    "qtvsctools",
    "formulahendry",
    "vscodeintellicode",
    "pkief",
    "seccomp",
    "autofetch",
    "Wextra",
    "Makefiles",
    "cppcoreguidelines",
    "iwyu",
    "MITSHM",
    "QTWEBENGINE",
    "LIBGL",
    "GLSL",
    "llvmpipe",
    "Expt"
  ],
  "[markdown]": {
    "editor.defaultFormatter": "yzhang.markdown-all-in-one"
  }
}