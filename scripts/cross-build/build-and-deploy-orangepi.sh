#!/bin/bash

# Integrated Orange Pi Docker Build and Deploy Script
# Builds c-aibox using Docker and deploys to Orange Pi device

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
BUILD_IMAGE=false
SKIP_BUILD=false
SKIP_DEPLOY=false
SKIP_TEST=false
DOCKER_TAG="c-aibox-orangepi-compat"
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
SSH_KEY=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --build-image)
            BUILD_IMAGE=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-deploy)
            SKIP_DEPLOY=true
            shift
            ;;
        --skip-test)
            SKIP_TEST=true
            shift
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --port)
            ORANGE_PI_PORT="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --tag)
            DOCKER_TAG="$2"
            shift 2
            ;;
        --help)
            cat << EOF
Integrated Orange Pi Docker Build and Deploy Script

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP         Orange Pi IP address

OPTIONS:
    --build-image   Build Docker image first
    --skip-build    Skip build step (use existing artifacts)
    --skip-deploy   Skip deployment step
    --skip-test     Skip testing step
    --user USER     SSH username (default: orangepi)
    --port PORT     SSH port (default: 22)
    --ssh-key PATH  SSH private key file
    --tag TAG       Docker image tag (default: c-aibox-orangepi-compat)
    --help          Show this help

DESCRIPTION:
    This script provides an integrated workflow for:
    1. Building c-aibox using Docker for Orange Pi compatibility
    2. Deploying the built artifacts to Orange Pi device
    3. Running basic tests on the target device

EXAMPLES:
    $0 --ip ***************                    # Full workflow
    $0 --ip *************** --build-image      # Build image first
    $0 --ip *************** --skip-test        # Build and deploy only
    $0 --ip *************** --skip-build       # Deploy existing build

EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                log_info "Use --help for usage information"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    log_info "Usage: $0 --ip IP_ADDRESS"
    exit 1
fi

# Print configuration
print_config() {
    log_step "Build and Deploy Configuration"
    echo "Orange Pi IP:      $ORANGE_PI_IP"
    echo "Orange Pi User:    $ORANGE_PI_USER"
    echo "Orange Pi Port:    $ORANGE_PI_PORT"
    echo "Docker Tag:        $DOCKER_TAG"
    echo "Build Image:       $BUILD_IMAGE"
    echo "Skip Build:        $SKIP_BUILD"
    echo "Skip Deploy:       $SKIP_DEPLOY"
    echo "Skip Test:         $SKIP_TEST"
    echo "Project Root:      $PROJECT_ROOT"
    echo ""
}

# Build using Docker
run_docker_build() {
    if [[ "$SKIP_BUILD" == "true" ]]; then
        log_info "Skipping build step as requested"
        return
    fi

    log_step "Building c-aibox using Docker"
    
    local build_args=()
    if [[ "$BUILD_IMAGE" == "true" ]]; then
        build_args+=("--build-image")
    fi
    build_args+=("--tag" "$DOCKER_TAG")
    
    log_info "Running Docker build script..."
    if "$SCRIPT_DIR/build-orangepi-docker.sh" "${build_args[@]}"; then
        log_success "Docker build completed successfully"
    else
        log_error "Docker build failed"
        exit 1
    fi
    
    # Verify build artifacts exist
    if [[ ! -d "$PROJECT_ROOT/build-container" ]]; then
        log_error "Build artifacts not found in build-container/"
        exit 1
    fi
    
    log_info "Build artifacts verified in build-container/"
}

# Deploy to Orange Pi
run_deployment() {
    if [[ "$SKIP_DEPLOY" == "true" ]]; then
        log_info "Skipping deployment step as requested"
        return
    fi

    log_step "Deploying to Orange Pi"
    
    local deploy_args=(
        "--ip" "$ORANGE_PI_IP"
        "--user" "$ORANGE_PI_USER"
        "--port" "$ORANGE_PI_PORT"
        "--build-dir" "build-container"
    )
    
    if [[ -n "$SSH_KEY" ]]; then
        deploy_args+=("--ssh-key" "$SSH_KEY")
    fi
    
    log_info "Running deployment script..."
    if "$PROJECT_ROOT/scripts/deploy/deploy-to-orangepi.sh" "${deploy_args[@]}"; then
        log_success "Deployment completed successfully"
    else
        log_error "Deployment failed"
        exit 1
    fi
}

# Run tests on Orange Pi
run_tests() {
    if [[ "$SKIP_TEST" == "true" ]]; then
        log_info "Skipping test step as requested"
        return
    fi

    log_step "Testing on Orange Pi"
    
    local test_args=(
        "--ip" "$ORANGE_PI_IP"
        "--user" "$ORANGE_PI_USER"
        "--port" "$ORANGE_PI_PORT"
    )
    
    if [[ -n "$SSH_KEY" ]]; then
        test_args+=("--ssh-key" "$SSH_KEY")
    fi
    
    log_info "Running basic tests..."
    if "$PROJECT_ROOT/scripts/deploy/test-on-orangepi.sh" "${test_args[@]}"; then
        log_success "Tests completed successfully"
    else
        log_warning "Some tests failed - check output above"
    fi
}

# Show summary
show_summary() {
    log_step "Build and Deploy Summary"
    
    if [[ "$SKIP_BUILD" != "true" ]]; then
        echo "✅ Docker build completed"
    fi
    
    if [[ "$SKIP_DEPLOY" != "true" ]]; then
        echo "✅ Deployment to $ORANGE_PI_IP completed"
    fi
    
    if [[ "$SKIP_TEST" != "true" ]]; then
        echo "✅ Basic tests completed"
    fi
    
    echo ""
    log_info "Orange Pi device is ready at: $ORANGE_PI_IP"
    log_info "SSH access: ssh $ORANGE_PI_USER@$ORANGE_PI_IP"
    log_info "Application directory: /home/<USER>/c-aibox"
    echo ""
    log_info "To run applications manually:"
    log_info "  ssh $ORANGE_PI_USER@$ORANGE_PI_IP"
    log_info "  cd /home/<USER>/c-aibox"
    log_info "  ./server"
}

# Main execution
main() {
    print_config
    run_docker_build
    run_deployment
    run_tests
    show_summary
}

# Run main function
main "$@"
