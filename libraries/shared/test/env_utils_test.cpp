#include <gtest/gtest.h>
#include <utils/env_utils.hpp>
#include <fstream>
#include <filesystem>

class EnvUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Clear any loaded environment variables before each test
        utils::EnvUtils::clearLoadedEnv();

        // Create test directory if it doesn't exist
        std::filesystem::create_directories("test_data");
    }

    void TearDown() override {
        // Clean up test files
        std::filesystem::remove("test_data/test.ini");
        std::filesystem::remove("test_data/invalid.ini");
        std::filesystem::remove("test_data/empty.ini");

        // Clear loaded environment variables
        utils::EnvUtils::clearLoadedEnv();
    }

    void createTestConfigFile(const std::string& filename, const std::string& content) {
        std::ofstream file(filename);
        file << content;
        file.close();
    }
};

TEST_F(EnvUtilsTest, LoadConfigFromValidIniFile) {
    std::string iniContent = R"(
# This is a comment
; This is also a comment

[database]
url=postgresql://localhost:5432/mydb
host="localhost"
port=5432

[api]
key=abc123def456
debug=true
secret='my-secret-key'

[app]
name="C-AIBOX"
version=1.0.0
empty_value=
)";

    createTestConfigFile("test_data/test.ini", iniContent);

    EXPECT_TRUE(utils::EnvUtils::loadConfigFromFile("test_data/test.ini"));

    EXPECT_EQ(utils::EnvUtils::getEnv("database.url"), "postgresql://localhost:5432/mydb");
    EXPECT_EQ(utils::EnvUtils::getEnv("database.host"), "localhost");
    EXPECT_EQ(utils::EnvUtils::getEnv("database.port"), "5432");
    EXPECT_EQ(utils::EnvUtils::getEnv("api.key"), "abc123def456");
    EXPECT_EQ(utils::EnvUtils::getEnv("api.debug"), "true");
    EXPECT_EQ(utils::EnvUtils::getEnv("api.secret"), "my-secret-key");
    EXPECT_EQ(utils::EnvUtils::getEnv("app.name"), "C-AIBOX");
    EXPECT_EQ(utils::EnvUtils::getEnv("app.version"), "1.0.0");
    EXPECT_EQ(utils::EnvUtils::getEnv("app.empty_value"), "");
}

TEST_F(EnvUtilsTest, LoadConfigFromString) {
    std::string iniContent = R"(
[test]
var=test_value
another_var="quoted value"
third_var='single quoted'

[section2]
key=value
)";

    EXPECT_TRUE(utils::EnvUtils::loadConfigFromString(iniContent));

    EXPECT_EQ(utils::EnvUtils::getEnv("test.var"), "test_value");
    EXPECT_EQ(utils::EnvUtils::getEnv("test.another_var"), "quoted value");
    EXPECT_EQ(utils::EnvUtils::getEnv("test.third_var"), "single quoted");
    EXPECT_EQ(utils::EnvUtils::getEnv("section2.key"), "value");
}

TEST_F(EnvUtilsTest, LoadConfigFromNonExistentFile) {
    EXPECT_FALSE(utils::EnvUtils::loadConfigFromFile("non_existent.ini"));
}



TEST_F(EnvUtilsTest, GetEnvWithDefault) {
    EXPECT_EQ(utils::EnvUtils::getEnv("NON_EXISTENT_VAR", "default"), "default");
    EXPECT_EQ(utils::EnvUtils::getEnv("NON_EXISTENT_VAR"), "");
}

TEST_F(EnvUtilsTest, GetEnvOptional) {
    auto result = utils::EnvUtils::getEnvOptional("NON_EXISTENT_VAR");
    EXPECT_FALSE(result.has_value());

    utils::EnvUtils::setEnv("TEST_VAR", "test_value");
    result = utils::EnvUtils::getEnvOptional("TEST_VAR");
    EXPECT_TRUE(result.has_value());
    EXPECT_EQ(result.value(), "test_value");
}

TEST_F(EnvUtilsTest, SetAndGetEnv) {
    EXPECT_TRUE(utils::EnvUtils::setEnv("TEST_SET_VAR", "test_value"));
    EXPECT_EQ(utils::EnvUtils::getEnv("TEST_SET_VAR"), "test_value");
    EXPECT_TRUE(utils::EnvUtils::hasEnv("TEST_SET_VAR"));
}

TEST_F(EnvUtilsTest, SetEnvWithOverwrite) {
    utils::EnvUtils::setEnv("OVERWRITE_TEST", "original");
    EXPECT_EQ(utils::EnvUtils::getEnv("OVERWRITE_TEST"), "original");

    // Test overwrite = true (default)
    utils::EnvUtils::setEnv("OVERWRITE_TEST", "new_value");
    EXPECT_EQ(utils::EnvUtils::getEnv("OVERWRITE_TEST"), "new_value");

    // Test overwrite = false
    utils::EnvUtils::setEnv("OVERWRITE_TEST", "another_value", false);
    EXPECT_EQ(utils::EnvUtils::getEnv("OVERWRITE_TEST"), "new_value"); // Should not change
}

TEST_F(EnvUtilsTest, HasEnv) {
    EXPECT_FALSE(utils::EnvUtils::hasEnv("NON_EXISTENT_VAR"));

    utils::EnvUtils::setEnv("HAS_TEST_VAR", "value");
    EXPECT_TRUE(utils::EnvUtils::hasEnv("HAS_TEST_VAR"));
}

TEST_F(EnvUtilsTest, UnsetEnv) {
    utils::EnvUtils::setEnv("UNSET_TEST_VAR", "value");
    EXPECT_TRUE(utils::EnvUtils::hasEnv("UNSET_TEST_VAR"));

    EXPECT_TRUE(utils::EnvUtils::unsetEnv("UNSET_TEST_VAR"));
    EXPECT_FALSE(utils::EnvUtils::hasEnv("UNSET_TEST_VAR"));
}

TEST_F(EnvUtilsTest, GetAllEnv) {
    utils::EnvUtils::setEnv("VAR1", "value1");
    utils::EnvUtils::setEnv("VAR2", "value2");

    auto allVars = utils::EnvUtils::getAllEnv();
    EXPECT_GE(allVars.size(), 2);
    EXPECT_EQ(allVars["VAR1"], "value1");
    EXPECT_EQ(allVars["VAR2"], "value2");
}

TEST_F(EnvUtilsTest, ClearLoadedEnv) {
    utils::EnvUtils::setEnv("CLEAR_TEST_VAR", "value");
    EXPECT_FALSE(utils::EnvUtils::getAllEnv().empty());

    utils::EnvUtils::clearLoadedEnv();
    EXPECT_TRUE(utils::EnvUtils::getAllEnv().empty());
}

TEST_F(EnvUtilsTest, ParseInvalidIniLines) {
    std::string invalidContent = R"(
[valid_section]
valid_var=valid_value
INVALID_LINE_NO_EQUALS
=NO_KEY
KEY_WITH_SPACES IN NAME=value
)";

    // Should return false due to invalid lines, but still parse valid ones
    // Use silent mode to suppress warnings in test output
    EXPECT_FALSE(utils::EnvUtils::loadConfigFromString(invalidContent, true));

    // Valid line should still be parsed
    EXPECT_EQ(utils::EnvUtils::getEnv("valid_section.valid_var"), "valid_value");
}

TEST_F(EnvUtilsTest, ParseInvalidIniLinesWithWarnings) {
    // Test that warnings are shown when silent=false (default behavior)
    std::string invalidContent = R"(
[test]
INVALID_LINE_NO_EQUALS
)";

    // Capture stderr to verify warning is printed
    testing::internal::CaptureStderr();
    EXPECT_FALSE(utils::EnvUtils::loadConfigFromString(invalidContent, false));
    std::string stderr_output = testing::internal::GetCapturedStderr();

    // Should contain warning message
    EXPECT_NE(stderr_output.find("Warning: Invalid line"), std::string::npos);
}

TEST_F(EnvUtilsTest, ConvenienceFunctions) {
    // Test INI convenience functions
    std::string iniContent = R"(
[test]
convenience_test=convenience_value
)";
    createTestConfigFile("test_data/convenience.ini", iniContent);

    EXPECT_TRUE(utils::loadConfig("test_data/convenience.ini"));
    EXPECT_EQ(utils::getEnv("test.convenience_test"), "convenience_value");
    EXPECT_TRUE(utils::hasEnv("test.convenience_test"));

    EXPECT_TRUE(utils::setEnv("NEW_CONVENIENCE_VAR", "new_value"));
    EXPECT_EQ(utils::getEnv("NEW_CONVENIENCE_VAR"), "new_value");

    std::filesystem::remove("test_data/convenience.ini");
}

TEST_F(EnvUtilsTest, EmptyFile) {
    // Test empty INI file
    createTestConfigFile("test_data/empty.ini", "");
    EXPECT_TRUE(utils::EnvUtils::loadConfigFromFile("test_data/empty.ini"));
}

TEST_F(EnvUtilsTest, OnlyCommentsAndEmptyLines) {
    // Test INI file with only comments and empty lines
    std::string iniContent = R"(
# This is a comment
; This is also a comment
   # Another comment with spaces

# Yet another comment
)";

    createTestConfigFile("test_data/comments_only.ini", iniContent);
    EXPECT_TRUE(utils::EnvUtils::loadConfigFromFile("test_data/comments_only.ini"));

    std::filesystem::remove("test_data/comments_only.ini");
}

TEST_F(EnvUtilsTest, IniEdgeCasesAndSpecialCharacters) {
    std::string iniContent = R"(
# Test various edge cases for INI format
[section1]
normal_var=normal_value
var_with_underscore=underscore_value
var123=numeric_suffix
_leading_underscore=leading_underscore
var_with_equals_in_value=key=value=more
var_with_quotes_in_value="value with 'single' quotes"
var_with_single_quotes_in_value='value with "double" quotes'
empty_quoted=""
empty_single_quoted=''
var_with_spaces_in_value="value with spaces"
var_with_special_chars="!@#$%^&*()_+-=[]{}|;:,.<>?"

[section2]
key1=value1
key2=value2

[section_with_underscore]
test_key=test_value
)";

    EXPECT_TRUE(utils::EnvUtils::loadConfigFromString(iniContent));

    EXPECT_EQ(utils::EnvUtils::getEnv("section1.normal_var"), "normal_value");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.var_with_underscore"), "underscore_value");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.var123"), "numeric_suffix");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1._leading_underscore"), "leading_underscore");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.var_with_equals_in_value"), "key=value=more");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.var_with_quotes_in_value"), "value with 'single' quotes");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.var_with_single_quotes_in_value"), "value with \"double\" quotes");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.empty_quoted"), "");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.empty_single_quoted"), "");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.var_with_spaces_in_value"), "value with spaces");
    EXPECT_EQ(utils::EnvUtils::getEnv("section1.var_with_special_chars"), "!@#$%^&*()_+-=[]{}|;:,.<>?");
    EXPECT_EQ(utils::EnvUtils::getEnv("section2.key1"), "value1");
    EXPECT_EQ(utils::EnvUtils::getEnv("section2.key2"), "value2");
    EXPECT_EQ(utils::EnvUtils::getEnv("section_with_underscore.test_key"), "test_value");
}


