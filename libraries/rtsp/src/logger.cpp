#include "rtsp/logger.hpp"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <algorithm>

namespace aibox {
namespace rtsp {

// Global logger instance
std::shared_ptr<Logger> g_logger = nullptr;

/**
 * @brief Internal implementation of Logger using PIMPL pattern
 */
class Logger::Impl {
public:
    explicit Impl(const LoggerConfig& config) : config_(config), statistics_() {
        // Initialize log file if file output is enabled
        if (config_.output == LogOutput::FILE || config_.output == LogOutput::BOTH) {
            initializeLogFile();
        }
        
        // Start async logging thread if enabled
        if (config_.enable_async_logging) {
            startAsyncLogging();
        }
    }
    
    ~Impl() {
        stopAsyncLogging();
        flush();
    }
    
    void log(const LogEntry& entry) {
        // Check if level is enabled
        if (entry.level < config_.min_level) {
            return;
        }
        
        // Update statistics
        updateStatistics(entry);
        
        // Format log message
        std::string formatted_message = formatLogEntry(entry);
        
        if (config_.enable_async_logging) {
            // Add to async queue
            std::lock_guard<std::mutex> lock(queue_mutex_);
            if (log_queue_.size() < config_.async_queue_size) {
                log_queue_.push(formatted_message);
                queue_condition_.notify_one();
            } else {
                statistics_.dropped_entries++;
            }
        } else {
            // Write synchronously
            writeLogMessage(formatted_message);
        }
    }
    
    void updateConfig(const LoggerConfig& config) {
        std::lock_guard<std::mutex> lock(config_mutex_);
        config_ = config;
        
        // Reinitialize log file if needed
        if (config_.output == LogOutput::FILE || config_.output == LogOutput::BOTH) {
            initializeLogFile();
        }
    }
    
    LoggerConfig getConfig() const {
        std::lock_guard<std::mutex> lock(config_mutex_);
        return config_;
    }
    
    void setLogLevel(LogLevel level) {
        std::lock_guard<std::mutex> lock(config_mutex_);
        config_.min_level = level;
    }
    
    LogLevel getLogLevel() const {
        std::lock_guard<std::mutex> lock(config_mutex_);
        return config_.min_level;
    }
    
    void rotateLog() {
        std::lock_guard<std::mutex> lock(file_mutex_);
        if (log_file_.is_open()) {
            log_file_.close();
        }
        
        // Rotate existing log files
        rotateLogFiles();
        
        // Open new log file
        initializeLogFile();
    }
    
    void flush() {
        // Flush async queue
        if (config_.enable_async_logging) {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_condition_.wait(lock, [this] { return log_queue_.empty(); });
        }
        
        // Flush file buffer
        std::lock_guard<std::mutex> file_lock(file_mutex_);
        if (log_file_.is_open()) {
            log_file_.flush();
        }
    }
    
    size_t getLogFileSize() const {
        std::lock_guard<std::mutex> lock(file_mutex_);
        if (!log_file_.is_open()) {
            return 0;
        }
        
        try {
            return std::filesystem::file_size(config_.log_file_path);
        } catch (const std::exception&) {
            return 0;
        }
    }
    
    Logger::LoggingStatistics getStatistics() const {
        Logger::LoggingStatistics stats;
        stats.total_entries = statistics_.total_entries.load();
        stats.trace_entries = statistics_.trace_entries.load();
        stats.debug_entries = statistics_.debug_entries.load();
        stats.info_entries = statistics_.info_entries.load();
        stats.warning_entries = statistics_.warning_entries.load();
        stats.error_entries = statistics_.error_entries.load();
        stats.critical_entries = statistics_.critical_entries.load();
        stats.fatal_entries = statistics_.fatal_entries.load();
        stats.dropped_entries = statistics_.dropped_entries.load();
        stats.bytes_written = statistics_.bytes_written.load();
        stats.start_time = statistics_.start_time;
        return stats;
    }

    void resetStatistics() {
        statistics_.total_entries = 0;
        statistics_.trace_entries = 0;
        statistics_.debug_entries = 0;
        statistics_.info_entries = 0;
        statistics_.warning_entries = 0;
        statistics_.error_entries = 0;
        statistics_.critical_entries = 0;
        statistics_.fatal_entries = 0;
        statistics_.dropped_entries = 0;
        statistics_.bytes_written = 0;
        statistics_.start_time = std::chrono::steady_clock::now();
    }
    
    bool isLevelEnabled(LogLevel level) const {
        std::lock_guard<std::mutex> lock(config_mutex_);
        return level >= config_.min_level;
    }

private:
    // Configuration and state
    LoggerConfig config_;
    Logger::LoggingStatistics statistics_;
    
    // File handling
    std::ofstream log_file_;
    mutable std::mutex file_mutex_;
    
    // Async logging
    std::queue<std::string> log_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_condition_;
    std::atomic<bool> async_active_{false};
    std::thread async_thread_;
    
    // Thread safety
    mutable std::mutex config_mutex_;
    
    void initializeLogFile() {
        try {
            // Create directory if it doesn't exist
            std::filesystem::path log_path(config_.log_file_path);
            std::filesystem::create_directories(log_path.parent_path());
            
            // Open log file
            log_file_.open(config_.log_file_path, std::ios::app);
            if (!log_file_.is_open()) {
                std::cerr << "Failed to open log file: " << config_.log_file_path << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "Error initializing log file: " << e.what() << std::endl;
        }
    }
    
    void startAsyncLogging() {
        async_active_ = true;
        async_thread_ = std::thread([this]() {
            while (async_active_) {
                std::unique_lock<std::mutex> lock(queue_mutex_);
                queue_condition_.wait(lock, [this] { 
                    return !log_queue_.empty() || !async_active_; 
                });
                
                while (!log_queue_.empty()) {
                    std::string message = log_queue_.front();
                    log_queue_.pop();
                    lock.unlock();
                    
                    writeLogMessage(message);
                    
                    lock.lock();
                }
            }
        });
    }
    
    void stopAsyncLogging() {
        async_active_ = false;
        queue_condition_.notify_all();
        if (async_thread_.joinable()) {
            async_thread_.join();
        }
    }
    
    void writeLogMessage(const std::string& message) {
        // Write to console if enabled
        if (config_.output == LogOutput::CONSOLE || config_.output == LogOutput::BOTH) {
            std::cout << message << std::endl;
        }
        
        // Write to file if enabled
        if ((config_.output == LogOutput::FILE || config_.output == LogOutput::BOTH) && 
            log_file_.is_open()) {
            std::lock_guard<std::mutex> lock(file_mutex_);
            log_file_ << message << std::endl;
            
            // Check if rotation is needed
            if (config_.enable_rotation && getLogFileSize() > config_.max_file_size_mb * 1024 * 1024) {
                rotateLog();
            }
        }
        
        statistics_.bytes_written += message.length() + 1; // +1 for newline
    }
    
    std::string formatLogEntry(const LogEntry& entry) {
        std::stringstream ss;
        
        // Timestamp
        if (config_.enable_timestamps) {
            ss << "[" << formatTimestamp(entry.timestamp) << "] ";
        }
        
        // Log level
        ss << "[" << levelToString(entry.level) << "] ";
        
        // Component
        ss << "[" << entry.component << "] ";
        
        // Stream ID if present
        if (!entry.stream_id.empty()) {
            ss << "[" << entry.stream_id << "] ";
        }
        
        // Thread ID if enabled
        if (config_.enable_thread_ids) {
            ss << "[" << entry.thread_id << "] ";
        }
        
        // Message
        ss << entry.message;
        
        // Context if present
        if (!entry.context.empty()) {
            ss << " {";
            bool first = true;
            for (const auto& [key, value] : entry.context) {
                if (!first) ss << ", ";
                ss << key << "=" << value;
                first = false;
            }
            ss << "}";
        }
        
        return ss.str();
    }
    
    std::string formatTimestamp(const std::chrono::steady_clock::time_point& tp) {
        auto system_time = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(system_time);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            system_time.time_since_epoch()) % 1000;
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), config_.timestamp_format.c_str());
        ss << "." << std::setfill('0') << std::setw(3) << ms.count();
        return ss.str();
    }
    
    void updateStatistics(const LogEntry& entry) {
        statistics_.total_entries++;
        
        switch (entry.level) {
            case LogLevel::TRACE:
                statistics_.trace_entries++;
                break;
            case LogLevel::DEBUG_LEVEL:
                statistics_.debug_entries++;
                break;
            case LogLevel::INFO:
                statistics_.info_entries++;
                break;
            case LogLevel::WARNING:
                statistics_.warning_entries++;
                break;
            case LogLevel::ERROR:
                statistics_.error_entries++;
                break;
            case LogLevel::CRITICAL:
                statistics_.critical_entries++;
                break;
            case LogLevel::FATAL:
                statistics_.fatal_entries++;
                break;
        }
    }
    
    void rotateLogFiles() {
        try {
            std::filesystem::path log_path(config_.log_file_path);
            
            // Remove oldest backup if we've reached the limit
            std::string oldest_backup = config_.log_file_path + "." + 
                std::to_string(config_.max_backup_files);
            if (std::filesystem::exists(oldest_backup)) {
                std::filesystem::remove(oldest_backup);
            }
            
            // Rotate existing backups
            for (int i = config_.max_backup_files - 1; i >= 1; --i) {
                std::string current_backup = config_.log_file_path + "." + std::to_string(i);
                std::string next_backup = config_.log_file_path + "." + std::to_string(i + 1);
                
                if (std::filesystem::exists(current_backup)) {
                    std::filesystem::rename(current_backup, next_backup);
                }
            }
            
            // Move current log to backup
            if (std::filesystem::exists(config_.log_file_path)) {
                std::string first_backup = config_.log_file_path + ".1";
                std::filesystem::rename(config_.log_file_path, first_backup);
            }
        } catch (const std::exception& e) {
            std::cerr << "Error rotating log files: " << e.what() << std::endl;
        }
    }
};

// Logger public interface implementation

Logger::Logger(const LoggerConfig& config) : pImpl(std::make_unique<Impl>(config)) {
}

Logger::~Logger() = default;

void Logger::log(LogLevel level,
                 const std::string& message,
                 const std::string& component,
                 const std::string& stream_id,
                 const std::unordered_map<std::string, std::string>& context) {
    LogEntry entry;
    entry.level = level;
    entry.message = message;
    entry.component = component;
    entry.stream_id = stream_id;
    entry.context = context;
    entry.timestamp = std::chrono::steady_clock::now();
    entry.thread_id = std::this_thread::get_id();

    pImpl->log(entry);
}

void Logger::log(const LogEntry& entry) {
    pImpl->log(entry);
}

void Logger::trace(const std::string& message, const std::string& component,
                   const std::string& stream_id) {
    log(LogLevel::TRACE, message, component, stream_id);
}

void Logger::debug(const std::string& message, const std::string& component,
                   const std::string& stream_id) {
    log(LogLevel::DEBUG_LEVEL, message, component, stream_id);
}

void Logger::info(const std::string& message, const std::string& component,
                  const std::string& stream_id) {
    log(LogLevel::INFO, message, component, stream_id);
}

void Logger::warning(const std::string& message, const std::string& component,
                     const std::string& stream_id) {
    log(LogLevel::WARNING, message, component, stream_id);
}

void Logger::error(const std::string& message, const std::string& component,
                   const std::string& stream_id) {
    log(LogLevel::ERROR, message, component, stream_id);
}

void Logger::critical(const std::string& message, const std::string& component,
                      const std::string& stream_id) {
    log(LogLevel::CRITICAL, message, component, stream_id);
}

void Logger::fatal(const std::string& message, const std::string& component,
                   const std::string& stream_id) {
    log(LogLevel::FATAL, message, component, stream_id);
}

void Logger::logError(const ErrorInfo& error_info) {
    std::unordered_map<std::string, std::string> context = error_info.context;
    context["error_code"] = std::to_string(error_info.error_code);
    context["recoverable"] = error_info.is_recoverable ? "true" : "false";

    LogLevel level;
    switch (error_info.severity) {
        case ErrorSeverity::TRACE: level = LogLevel::TRACE; break;
        case ErrorSeverity::DEBUG_LEVEL: level = LogLevel::DEBUG_LEVEL; break;
        case ErrorSeverity::INFO: level = LogLevel::INFO; break;
        case ErrorSeverity::WARNING: level = LogLevel::WARNING; break;
        case ErrorSeverity::ERROR: level = LogLevel::ERROR; break;
        case ErrorSeverity::CRITICAL: level = LogLevel::CRITICAL; break;
        case ErrorSeverity::FATAL: level = LogLevel::FATAL; break;
        default: level = LogLevel::ERROR; break;
    }

    log(level, error_info.message, error_info.component, error_info.stream_id, context);
}

void Logger::updateConfig(const LoggerConfig& config) {
    pImpl->updateConfig(config);
}

LoggerConfig Logger::getConfig() const {
    return pImpl->getConfig();
}

void Logger::setLogLevel(LogLevel level) {
    pImpl->setLogLevel(level);
}

LogLevel Logger::getLogLevel() const {
    return pImpl->getLogLevel();
}

void Logger::rotateLog() {
    pImpl->rotateLog();
}

void Logger::flush() {
    pImpl->flush();
}

size_t Logger::getLogFileSize() const {
    return pImpl->getLogFileSize();
}

Logger::LoggingStatistics Logger::getStatistics() const {
    return pImpl->getStatistics();
}

void Logger::resetStatistics() {
    pImpl->resetStatistics();
}

bool Logger::isLevelEnabled(LogLevel level) const {
    return pImpl->isLevelEnabled(level);
}

std::string Logger::levelToString(LogLevel level) {
    switch (level) {
        case LogLevel::TRACE: return "TRACE";
        case LogLevel::DEBUG_LEVEL: return "DEBUG";
        case LogLevel::INFO: return "INFO";
        case LogLevel::WARNING: return "WARNING";
        case LogLevel::ERROR: return "ERROR";
        case LogLevel::CRITICAL: return "CRITICAL";
        case LogLevel::FATAL: return "FATAL";
        default: return "UNKNOWN";
    }
}

LogLevel Logger::stringToLevel(const std::string& level_str) {
    std::string upper_str = level_str;
    std::transform(upper_str.begin(), upper_str.end(), upper_str.begin(), ::toupper);

    if (upper_str == "TRACE") return LogLevel::TRACE;
    if (upper_str == "DEBUG") return LogLevel::DEBUG_LEVEL;
    if (upper_str == "INFO") return LogLevel::INFO;
    if (upper_str == "WARNING") return LogLevel::WARNING;
    if (upper_str == "ERROR") return LogLevel::ERROR;
    if (upper_str == "CRITICAL") return LogLevel::CRITICAL;
    if (upper_str == "FATAL") return LogLevel::FATAL;

    return LogLevel::INFO; // Default
}

std::string Logger::getCurrentTimestamp(const std::string& format) {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), format.c_str());
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

// Global logger functions

void initializeLogger(const LoggerConfig& config) {
    g_logger = std::make_shared<Logger>(config);
}

std::shared_ptr<Logger> getLogger() {
    if (!g_logger) {
        // Initialize with default config if not already initialized
        g_logger = std::make_shared<Logger>();
    }
    return g_logger;
}

} // namespace rtsp
} // namespace aibox
