# RGA Pre-Processing Module Sequence Diagrams

This document provides detailed sequence diagrams for the RGA Pre-Processing Module, illustrating the logical flow and component interactions for hardware-accelerated image processing.

## Module Initialization Sequence

```mermaid
sequenceDiagram
    participant Main as Main Application
    participant PreProc as RGAPreProcessingModule
    participant R<PERSON> as RGAManager
    participant BufferMgr as BufferManager
    participant ThreadPool as WorkerThreadPool
    
    Main->>PreProc: initialize(config)
    activate PreProc
    
    PreProc->>RGA: initializeRGA()
    activate RGA
    RGA->>RGA: c_RkRgaInit()
    RGA->>RGA: queryHardwareCapabilities()
    RGA-->>PreProc: initialization status
    deactivate RGA
    
    PreProc->>BufferMgr: initialize(buffer_config)
    activate BufferMgr
    BufferMgr->>BufferMgr: setupDMABUFSupport()
    BufferMgr->>BufferMgr: createBufferPools()
    BufferMgr-->>PreProc: buffer manager ready
    deactivate BufferMgr
    
    PreProc->>ThreadPool: createThreadPool(worker_count)
    activate ThreadPool
    ThreadPool-->>PreProc: thread pool ready
    deactivate ThreadPool
    
    PreProc-->>Main: initialization complete
    deactivate PreProc
```

## Buffer Import Sequence

```mermaid
sequenceDiagram
    participant InQueue as InputQueue
    participant Worker as PreProcWorker
    participant BufferMgr as BufferManager
    participant RGA as RGAManager
    
    activate Worker
    Worker->>InQueue: dequeueFrame()
    activate InQueue
    InQueue-->>Worker: frame with DMABUF fd
    deactivate InQueue
    
    Worker->>BufferMgr: importDMABUF(fd, frame_info)
    activate BufferMgr
    
    BufferMgr->>RGA: importbuffer_fd(fd, &param)
    activate RGA
    RGA-->>BufferMgr: rga_buffer_handle
    deactivate RGA
    
    BufferMgr->>BufferMgr: createBufferReference(handle, info)
    BufferMgr-->>Worker: input_buffer
    deactivate BufferMgr
    
    Worker->>Worker: proceedWithProcessing(input_buffer)
```

## Output Buffer Allocation Sequence

```mermaid
sequenceDiagram
    participant Worker as PreProcWorker
    participant BufferMgr as BufferManager
    participant DRM as DRMAllocator
    participant RGA as RGAManager
    
    activate Worker
    Worker->>BufferMgr: allocateOutputBuffer(width, height, format)
    activate BufferMgr
    
    BufferMgr->>DRM: drmModeCreateDumbBuffer(...)
    activate DRM
    DRM-->>BufferMgr: drm_buffer
    
    BufferMgr->>DRM: drmPrimeHandleToFD(drm_buffer)
    DRM-->>BufferMgr: dmabuf_fd
    deactivate DRM
    
    BufferMgr->>RGA: importbuffer_fd(dmabuf_fd, &param)
    activate RGA
    RGA-->>BufferMgr: rga_buffer_handle
    deactivate RGA
    
    BufferMgr->>BufferMgr: registerBuffer(dmabuf_fd, rga_buffer_handle)
    BufferMgr-->>Worker: output_buffer
    deactivate BufferMgr
    
    Worker->>Worker: proceedWithRGAOperation(output_buffer)
    deactivate Worker
```

## RGA Processing Sequence

```mermaid
sequenceDiagram
    participant Worker as PreProcWorker
    participant RGA as RGAManager
    participant OutQueue as OutputQueue
    
    activate Worker
    Worker->>Worker: prepareRGAParameters(input_buffer, output_buffer)
    
    Worker->>RGA: imcheck(src_info, dst_info, usage)
    activate RGA
    
    alt Operation Supported
        RGA-->>Worker: operation supported
        
        Worker->>RGA: imresize(src_info, dst_info)
        
        alt Resize Only
            RGA->>RGA: performResize()
            RGA-->>Worker: resize complete
        else Resize and Color Conversion
            Worker->>RGA: imcvtcolor(src_info, dst_info, color_format)
            RGA->>RGA: performColorConversion()
            RGA-->>Worker: conversion complete
        end
        
        Worker->>RGA: imsync(handle)
        RGA-->>Worker: operation synchronized
        
        Worker->>OutQueue: enqueueProcessedBuffer(output_buffer, metadata)
        activate OutQueue
        OutQueue-->>Worker: enqueue status
        deactivate OutQueue
        
    else Operation Not Supported
        RGA-->>Worker: operation not supported
        Worker->>Worker: fallbackToSoftwareProcessing()
    end
    
    deactivate RGA
    deactivate Worker
```

## Batch Processing Sequence

```mermaid
sequenceDiagram
    participant InQueue as InputQueue
    participant Scheduler as BatchScheduler
    participant Worker as PreProcWorker
    participant RGA as RGAManager
    participant OutQueue as OutputQueue
    
    activate Scheduler
    loop Until Shutdown
        Scheduler->>InQueue: collectFrameBatch(max_batch_size, timeout)
        activate InQueue
        InQueue-->>Scheduler: batch of frames
        deactivate InQueue
        
        alt Batch Available
            loop For Each Frame in Batch
                Scheduler->>Worker: assignProcessingTask(frame)
                activate Worker
                
                Worker->>RGA: processFrameAsync(frame)
                activate RGA
                RGA-->>Worker: job_handle
                deactivate RGA
                
                Worker->>Worker: trackJobHandle(job_handle)
                deactivate Worker
            end
            
            Scheduler->>Scheduler: waitForAllJobsCompletion()
            
            loop For Each Completed Job
                Scheduler->>Worker: getProcessedFrame(job_handle)
                activate Worker
                Worker-->>Scheduler: processed_frame
                deactivate Worker
                
                Scheduler->>OutQueue: enqueueProcessedFrame(processed_frame)
                activate OutQueue
                OutQueue-->>Scheduler: enqueue status
                deactivate OutQueue
            end
            
        else No Frames Available
            Scheduler->>Scheduler: wait(polling_interval)
        end
    end
    deactivate Scheduler
```

## Error Handling Sequence

```mermaid
sequenceDiagram
    participant Worker as PreProcWorker
    participant ErrorHandler as ErrorHandler
    participant RGA as RGAManager
    participant BufferMgr as BufferManager
    participant App as Application
    
    activate Worker
    Worker->>Worker: detectError(error_type)
    Worker->>ErrorHandler: handlePreProcessError(error_type, details)
    activate ErrorHandler
    
    alt RGA Hardware Error
        ErrorHandler->>RGA: resetRGAState()
        activate RGA
        RGA-->>ErrorHandler: reset status
        deactivate RGA
        ErrorHandler-->>Worker: RETRY_OPERATION
        
    else Unsupported Operation
        ErrorHandler->>ErrorHandler: logUnsupportedOperation(details)
        ErrorHandler-->>Worker: USE_FALLBACK
        
    else Buffer Error
        ErrorHandler->>BufferMgr: releaseCorruptBuffer(buffer_handle)
        activate BufferMgr
        BufferMgr-->>ErrorHandler: buffer released
        deactivate BufferMgr
        ErrorHandler-->>Worker: ALLOCATE_NEW_BUFFER
        
    else Fatal Error
        ErrorHandler->>App: notifyPreProcessFailure(details)
        ErrorHandler-->>Worker: ABORT_PROCESSING
    end
    
    deactivate ErrorHandler
    
    alt Action is RETRY_OPERATION
        Worker->>Worker: retryRGAOperation()
    else Action is USE_FALLBACK
        Worker->>Worker: switchToSoftwareProcessing()
    else Action is ALLOCATE_NEW_BUFFER
        Worker->>Worker: reallocateBuffers()
        Worker->>Worker: retryOperation()
    else Action is ABORT_PROCESSING
        Worker->>Worker: cleanupResources()
        Worker->>Worker: skipCurrentFrame()
    end
    deactivate Worker
```

## Multi-Operation Pipeline Sequence

```mermaid
sequenceDiagram
    participant Worker as PreProcWorker
    participant RGA as RGAManager
    participant OutQueue as OutputQueue
    
    activate Worker
    
    Worker->>Worker: analyzeProcessingRequirements(frame)
    
    alt Single RGA Operation Sufficient
        Worker->>RGA: performSingleOperation(src, dst, params)
        activate RGA
        RGA-->>Worker: operation complete
        deactivate RGA
        
    else Multiple Operations Required
        Worker->>RGA: imcrop(src, tmp_buffer, crop_rect)
        activate RGA
        RGA-->>Worker: crop complete
        
        Worker->>RGA: imresize(tmp_buffer, dst_buffer, target_size)
        RGA-->>Worker: resize complete
        
        Worker->>RGA: imcvtcolor(dst_buffer, dst_buffer, color_format)
        RGA-->>Worker: color conversion complete
        deactivate RGA
    end
    
    Worker->>OutQueue: enqueueProcessedFrame(dst_buffer, metadata)
    activate OutQueue
    OutQueue-->>Worker: enqueue status
    deactivate OutQueue
    
    deactivate Worker
```

## Module Shutdown Sequence

```mermaid
sequenceDiagram
    participant App as Application
    participant PreProc as RGAPreProcessingModule
    participant ThreadPool as WorkerThreadPool
    participant RGA as RGAManager
    participant BufferMgr as BufferManager
    
    App->>PreProc: shutdown()
    activate PreProc
    
    PreProc->>ThreadPool: shutdownWorkers(timeout)
    activate ThreadPool
    ThreadPool-->>PreProc: workers terminated
    deactivate ThreadPool
    
    PreProc->>RGA: finalizeRGA()
    activate RGA
    RGA->>RGA: c_RkRgaDeInit()
    RGA-->>PreProc: RGA finalized
    deactivate RGA
    
    PreProc->>BufferMgr: releaseAllBuffers()
    activate BufferMgr
    
    loop For Each Buffer
        BufferMgr->>BufferMgr: releaseBuffer(handle)
    end
    
    BufferMgr-->>PreProc: all buffers released
    deactivate BufferMgr
    
    PreProc-->>App: shutdown complete
    deactivate PreProc
```
