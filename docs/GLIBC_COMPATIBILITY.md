# GLIBC Compatibility for Orange Pi

This document explains the GLIBC compatibility issue with Orange Pi devices and provides multiple solutions to build binaries that work with older GLIBC versions.

## Problem Description

Cross-compiled binaries built with newer toolchains (Ubuntu 24.04) require GLIBC 2.38, but Orange Pi devices typically have GLIBC 2.36. This causes the following error when running binaries:

```
./your_binary: /lib/aarch64-linux-gnu/libc.so.6: version `GLIBC_2.38' not found
```

## Solutions Overview

We provide three different approaches to solve this compatibility issue:

1. **GLIBC Compatibility Mode** - Use version scripts to limit GLIBC symbols
2. **Docker-based Build** - Use Ubuntu 22.04 container with compatible toolchain
3. **Static Linking** - Link libraries statically to avoid GLIBC dependencies

## Solution 1: GLIBC Compatibility Mode

### Usage

```bash
# Enable GLIBC compatibility in existing build script
./scripts/cross-build/build-orangepi.sh --glibc-compat

# Or use dedicated compatibility build script
./scripts/cross-build/build-orangepi-glibc-compat.sh
```

### How it works

- Uses version scripts to limit GLIBC symbols to 2.36 and earlier
- Adds compiler flags to target older GLIBC versions
- Enables static linking for libgcc and libstdc++

### Files involved

- `cmake/toolchains/aarch64-linux-gnu.cmake` - Updated with compatibility mode
- `cmake/version-scripts/glibc-2.36.map` - Version script limiting GLIBC symbols
- `scripts/cross-build/build-orangepi-glibc-compat.sh` - Dedicated compatibility build

## Solution 2: Docker-based Build

### Usage

```bash
# Build Docker image and compile
./scripts/cross-build/build-orangepi-docker.sh --build-image

# Use existing image
./scripts/cross-build/build-orangepi-docker.sh

# Interactive development
./scripts/cross-build/build-orangepi-docker.sh --interactive
```

### How it works

- Uses Ubuntu 22.04 Docker container with GLIBC 2.35/2.36 compatible toolchain
- Isolates build environment from host system
- Ensures consistent GLIBC version targeting

### Files involved

- `docker/Dockerfile.orangepi-compat` - Docker image definition
- `scripts/cross-build/build-orangepi-docker.sh` - Docker build script

## Solution 3: Static Linking (Default)

The default toolchain already uses static linking for libgcc and libstdc++:

```cmake
set(CMAKE_EXE_LINKER_FLAGS_RELEASE "-static-libgcc -static-libstdc++ -Wl,--gc-sections")
```

This reduces GLIBC dependencies but doesn't eliminate them completely.

## Checking Compatibility

### Check Local Binaries

```bash
# Check specific directory
./scripts/cross-build/check-glibc-compat.sh --dir build-orangepi

# Check with verbose output
./scripts/cross-build/check-glibc-compat.sh --dir build-orangepi --verbose

# Check for different GLIBC version
./scripts/cross-build/check-glibc-compat.sh --target-glibc 2.35
```

### Test on Actual Orange Pi

```bash
# Check Orange Pi GLIBC version
./scripts/cross-build/check-glibc-compat.sh --remote *************

# Test with SSH key
./scripts/cross-build/check-glibc-compat.sh --remote ************* --ssh-key ~/.ssh/orangepi_key
```

## Manual GLIBC Check

You can manually check GLIBC requirements:

```bash
# Check required GLIBC versions
aarch64-linux-gnu-objdump -T your_binary | grep GLIBC_ | sort -V | uniq

# Check Orange Pi GLIBC version (on device)
ldd --version
strings /lib/aarch64-linux-gnu/libc.so.6 | grep GLIBC_ | sort -V | tail -10
```

## Troubleshooting

### Binary still requires newer GLIBC

1. **Check version script**: Ensure `cmake/version-scripts/glibc-2.36.map` exists
2. **Verify compatibility mode**: Check CMake output for "GLIBC compatibility mode enabled"
3. **Use Docker build**: Try the Docker-based approach for guaranteed compatibility

### Docker build fails

1. **Check Docker installation**: `docker --version`
2. **Build image manually**: `docker build -f docker/Dockerfile.orangepi-compat -t c-aibox-orangepi-compat .`
3. **Check permissions**: Ensure user can run Docker commands

### SSH connection fails

1. **Check IP address**: Ping the Orange Pi device
2. **Verify SSH key**: Test SSH connection manually
3. **Check SSH service**: Ensure SSH is running on Orange Pi

## Best Practices

### For Development

1. **Use Docker build** for guaranteed compatibility
2. **Test on actual device** before deployment
3. **Check GLIBC compatibility** after each build

### For Production

1. **Use GLIBC compatibility mode** for smaller binaries
2. **Static link critical dependencies** when possible
3. **Document GLIBC requirements** in deployment guides

## Examples

### Basic Compatible Build

```bash
# Simple compatibility build
./scripts/cross-build/build-orangepi-glibc-compat.sh

# With custom sysroot
./scripts/cross-build/build-orangepi-glibc-compat.sh --sysroot /opt/orangepi-sysroot

# Clean build with package
./scripts/cross-build/build-orangepi-glibc-compat.sh --clean --package
```

### Docker-based Build

```bash
# First time setup
./scripts/cross-build/build-orangepi-docker.sh --build-image

# Regular builds
./scripts/cross-build/build-orangepi-docker.sh

# Development with shell access
./scripts/cross-build/build-orangepi-docker.sh --interactive
```

### Testing Workflow

```bash
# 1. Build with compatibility
./scripts/cross-build/build-orangepi-glibc-compat.sh

# 2. Check local compatibility
./scripts/cross-build/check-glibc-compat.sh --dir build-orangepi-compat

# 3. Test on Orange Pi
./scripts/cross-build/check-glibc-compat.sh --remote *************

# 4. Deploy if tests pass
./scripts/deploy/deploy-to-orangepi.sh *************
```

## Technical Details

### Version Script Format

The version script (`glibc-2.36.map`) defines which GLIBC symbols are allowed:

```
GLIBC_2.36 {
    global: *;
} GLIBC_2.35;

# GLIBC_2.37 and later are excluded
local: *;
```

### Compiler Flags

GLIBC compatibility mode adds these flags:

```bash
-D__GLIBC_MINOR__=36                    # Target GLIBC 2.36
-static-libgcc -static-libstdc++        # Static link standard libraries
-Wl,--version-script=glibc-2.36.map    # Limit GLIBC symbols
```

### Docker Environment

The Docker container uses:

- **Base**: Ubuntu 22.04 (GLIBC 2.35/2.36 compatible)
- **Toolchain**: gcc-aarch64-linux-gnu from Ubuntu 22.04 repos
- **Environment**: Isolated from host GLIBC version

This ensures consistent, compatible builds regardless of the host system.
