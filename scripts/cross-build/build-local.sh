#!/bin/bash

# Local Development Build Script
# Builds the project for the current host platform (x86_64 or ARM64)

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
BUILD_TYPE="Debug"
BUILD_DIR="build-local"
CLEAN_BUILD=false
VERBOSE=false
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo "4")
INSTALL_DEPS=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --release)
            BUILD_TYPE="Release"
            shift
            ;;
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        --install-deps)
            INSTALL_DEPS=true
            shift
            ;;
        --help)
            cat << EOF
Local Development Build Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --release       Build in Release mode (default: Debug)
    --debug         Build in Debug mode
    --clean         Clean build directory before building
    --verbose       Enable verbose build output
    --jobs N        Number of parallel jobs (default: auto-detect)
    --install-deps  Install dependencies before building
    --help          Show this help

EXAMPLES:
    $0                          # Debug build
    $0 --release --clean        # Clean Release build
    $0 --verbose --jobs 8       # Verbose build with 8 jobs
    $0 --install-deps           # Install deps and build

ENVIRONMENT VARIABLES:
    CC              C compiler to use
    CXX             C++ compiler to use
    CMAKE_ARGS      Additional CMake arguments
EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Detect platform
detect_platform() {
    local os=$(uname -s)
    local arch=$(uname -m)
    
    case "$os" in
        Linux)
            PLATFORM="linux"
            ;;
        Darwin)
            PLATFORM="macos"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            PLATFORM="windows"
            ;;
        *)
            log_error "Unsupported platform: $os"
            exit 1
            ;;
    esac
    
    case "$arch" in
        x86_64|amd64)
            ARCH="x86_64"
            ;;
        arm64|aarch64)
            ARCH="arm64"
            ;;
        *)
            log_error "Unsupported architecture: $arch"
            exit 1
            ;;
    esac
    
    log_info "Detected platform: $PLATFORM $ARCH"
}

# Install dependencies if requested
install_dependencies() {
    if [[ "$INSTALL_DEPS" == "true" ]]; then
        log_step "Installing Dependencies"
        
        if [[ -f "$PROJECT_ROOT/scripts/install-rtsp-deps.sh" ]]; then
            log_info "Installing RTSP dependencies..."
            sudo "$PROJECT_ROOT/scripts/install-rtsp-deps.sh"
        else
            log_warning "Dependency installation script not found"
        fi
    fi
}

# Check build requirements
check_requirements() {
    log_step "Checking Build Requirements"
    
    # Check CMake
    if ! command -v cmake >/dev/null 2>&1; then
        log_error "CMake is not installed"
        exit 1
    fi
    local cmake_version=$(cmake --version | head -n1 | cut -d' ' -f3)
    log_info "CMake version: $cmake_version"
    
    # Check compiler
    if [[ -n "${CXX:-}" ]]; then
        log_info "Using CXX compiler: $CXX"
    elif command -v g++ >/dev/null 2>&1; then
        export CXX=g++
        log_info "Using g++ compiler"
    elif command -v clang++ >/dev/null 2>&1; then
        export CXX=clang++
        log_info "Using clang++ compiler"
    else
        log_error "No C++ compiler found"
        exit 1
    fi
    
    # Check make/ninja
    if command -v ninja >/dev/null 2>&1; then
        CMAKE_GENERATOR="Ninja"
        MAKE_COMMAND="ninja"
        log_info "Using Ninja build system"
    else
        CMAKE_GENERATOR="Unix Makefiles"
        MAKE_COMMAND="make"
        log_info "Using Make build system"
    fi
}

# Configure CMake
configure_cmake() {
    log_step "Configuring CMake"
    
    cd "$PROJECT_ROOT"
    
    # Clean build directory if requested
    if [[ "$CLEAN_BUILD" == "true" && -d "$BUILD_DIR" ]]; then
        log_info "Cleaning build directory..."
        rm -rf "$BUILD_DIR"
    fi
    
    # Create build directory
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # Prepare CMake arguments
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=$BUILD_TYPE"
        "-G$CMAKE_GENERATOR"
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
        "-DBUILD_TESTING=ON"
        "-DBUILD_EXAMPLES=ON"
    )
    
    # Platform-specific settings
    case "$PLATFORM" in
        linux)
            cmake_args+=("-DCMAKE_INSTALL_PREFIX=/usr/local")
            ;;
        macos)
            cmake_args+=("-DCMAKE_INSTALL_PREFIX=/usr/local")
            # macOS specific settings
            if [[ "$ARCH" == "arm64" ]]; then
                cmake_args+=("-DCMAKE_OSX_ARCHITECTURES=arm64")
            fi
            ;;
        windows)
            cmake_args+=("-DCMAKE_INSTALL_PREFIX=C:/Program Files/c-aibox")
            ;;
    esac
    
    # Add custom CMake arguments
    if [[ -n "${CMAKE_ARGS:-}" ]]; then
        cmake_args+=($CMAKE_ARGS)
    fi
    
    # Run CMake configuration
    log_info "Running CMake configuration..."
    log_info "Build type: $BUILD_TYPE"
    log_info "Generator: $CMAKE_GENERATOR"
    log_info "Jobs: $JOBS"
    
    if [[ "$VERBOSE" == "true" ]]; then
        cmake "${cmake_args[@]}" ..
    else
        cmake "${cmake_args[@]}" .. > cmake_config.log 2>&1 || {
            log_error "CMake configuration failed. Check cmake_config.log for details."
            tail -20 cmake_config.log
            exit 1
        }
    fi
    
    log_success "CMake configuration completed"
}

# Build the project
build_project() {
    log_step "Building Project"
    
    cd "$PROJECT_ROOT/$BUILD_DIR"
    
    # Build command
    local build_args=()
    
    if [[ "$CMAKE_GENERATOR" == "Ninja" ]]; then
        build_args+=("-j$JOBS")
        if [[ "$VERBOSE" == "true" ]]; then
            build_args+=("-v")
        fi
    else
        build_args+=("-j$JOBS")
        if [[ "$VERBOSE" == "true" ]]; then
            build_args+=("VERBOSE=1")
        fi
    fi
    
    log_info "Building with $JOBS parallel jobs..."
    
    if [[ "$VERBOSE" == "true" ]]; then
        $MAKE_COMMAND "${build_args[@]}"
    else
        $MAKE_COMMAND "${build_args[@]}" > build.log 2>&1 || {
            log_error "Build failed. Check build.log for details."
            tail -20 build.log
            exit 1
        }
    fi
    
    log_success "Build completed successfully"
}

# Show build summary
show_summary() {
    log_step "Build Summary"
    
    cd "$PROJECT_ROOT/$BUILD_DIR"
    
    echo "Platform: $PLATFORM $ARCH"
    echo "Build Type: $BUILD_TYPE"
    echo "Build Directory: $BUILD_DIR"
    echo "Generator: $CMAKE_GENERATOR"
    echo "Jobs: $JOBS"
    echo ""
    
    # Show built executables
    log_info "Built executables:"
    find . -type f -executable -name "*" | grep -E "(client_app|server|test)" | head -10 || echo "  No executables found"
    
    # Show libraries
    log_info "Built libraries:"
    find . -name "*.a" -o -name "*.so" -o -name "*.dylib" | head -10 || echo "  No libraries found"
    
    # Show build size
    local build_size=$(du -sh . 2>/dev/null | cut -f1 || echo "unknown")
    log_info "Build directory size: $build_size"
    
    echo ""
    log_success "Local build completed successfully!"
    log_info "To run tests: cd $BUILD_DIR && ctest"
    log_info "To run client: ./$BUILD_DIR/apps/client/client_app"
    log_info "To run server: ./$BUILD_DIR/apps/server/server"
}

# Main execution
main() {
    log_step "Local Development Build"
    
    detect_platform
    install_dependencies
    check_requirements
    configure_cmake
    build_project
    show_summary
}

# Run main function
main "$@"
