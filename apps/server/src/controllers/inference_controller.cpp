#include "controllers/inference_controller.hpp"
#include "shared/config.hpp"
#include <chrono>

namespace server::controllers {

InferenceController::InferenceController(std::shared_ptr<shared::ServerConfig> config,
                                       std::shared_ptr<handlers::InferenceHandler> inference_handler)
    : BaseController(config), inference_handler_(inference_handler) {
}

void InferenceController::handleArcFaceInference(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "InferenceController::ArcFace");

    try {
        // Parse request body to JSON
        auto request_json = parseRequestBody(req, res);
        if (request_json.isNull()) {
            return; // Error response already sent
        }

        // Delegate to handler
        auto response_json = inference_handler_->handleArcFaceInference(request_json);

        // Send JSON response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("ArcFace inference exception: " + std::string(e.what()), "InferenceController");
        sendErrorResponse(res, "Internal server error",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "InferenceController::ArcFace", duration);
}

void InferenceController::handleYoloDetection(const httplib::Request& req, httplib::Response& res) {
    auto start_time = std::chrono::high_resolution_clock::now();
    logRequest(req, "InferenceController::YOLO");

    try {
        // Parse request body to JSON
        auto request_json = parseRequestBody(req, res);
        if (request_json.isNull()) {
            return; // Error response already sent
        }

        // Delegate to handler
        auto response_json = inference_handler_->handleYoloDetection(request_json);

        // Send JSON response
        sendJsonResponse(res, response_json);

    } catch (const std::exception& e) {
        logError("YOLO detection exception: " + std::string(e.what()), "InferenceController");
        sendErrorResponse(res, "Internal server error",
                         shared::AppConstants::ERROR_INTERNAL,
                         shared::AppConstants::HTTP_INTERNAL_ERROR);
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    logResponse(res, "InferenceController::YOLO", duration);
}

void InferenceController::handleArcFaceInferenceAsync(const httplib::Request& /*req*/, httplib::Response& res) {
    sendErrorResponse(res, "Async inference not yet implemented", "NOT_IMPLEMENTED", 501);
}

void InferenceController::handleYoloDetectionAsync(const httplib::Request& /*req*/, httplib::Response& res) {
    sendErrorResponse(res, "Async inference not yet implemented", "NOT_IMPLEMENTED", 501);
}

void InferenceController::registerRoutes(httplib::Server& server) {
    // ArcFace inference routes
    server.Post("/api/v1/arcface/infer", [this](const httplib::Request& req, httplib::Response& res) {
        handleArcFaceInference(req, res);
    });

    // YOLO detection routes
    server.Post("/api/v1/yolo/detect", [this](const httplib::Request& req, httplib::Response& res) {
        handleYoloDetection(req, res);
    });

    // Future async routes
    server.Post("/api/v1/arcface/infer/async", [this](const httplib::Request& req, httplib::Response& res) {
        handleArcFaceInferenceAsync(req, res);
    });

    server.Post("/api/v1/yolo/detect/async", [this](const httplib::Request& req, httplib::Response& res) {
        handleYoloDetectionAsync(req, res);
    });
}

// HTTP processing helpers
utils::JsonValue InferenceController::parseRequestBody(const httplib::Request& req, httplib::Response& res) {
    // Validate request body
    if (isRequestBodyEmpty(req)) {
        sendErrorResponse(res, "Request body is required", shared::AppConstants::ERROR_INVALID_REQUEST);
        return utils::JsonValue(); // null
    }

    // Validate content type
    if (!validateContentType(req)) {
        sendErrorResponse(res, "Content-Type must be application/json", shared::AppConstants::ERROR_INVALID_REQUEST);
        return utils::JsonValue(); // null
    }

    // Validate request size
    if (!validateRequestSize(req)) {
        sendErrorResponse(res, "Request body too large", shared::AppConstants::ERROR_INVALID_REQUEST);
        return utils::JsonValue(); // null
    }

    // Parse JSON
    auto json_opt = utils::JsonUtils::parse(req.body);
    if (!json_opt.has_value()) {
        sendErrorResponse(res, "Invalid JSON format", shared::AppConstants::ERROR_INVALID_REQUEST);
        return utils::JsonValue(); // null
    }

    return json_opt.value();
}

void InferenceController::sendJsonResponse(httplib::Response& res, const utils::JsonValue& json_response) {
    // Determine status code from response
    int status_code = shared::AppConstants::HTTP_OK;

    if (json_response.hasKey("success") && !json_response["success"].asBool()) {
        if (json_response.hasKey("code")) {
            status_code = json_response["code"].asInt();
        } else {
            status_code = shared::AppConstants::HTTP_BAD_REQUEST;
        }
    }

    // Convert to string and send using BaseController method
    std::string json_str = utils::JsonUtils::stringify(json_response);
    BaseController::sendJsonResponse(res, json_str, status_code);
}

} // namespace server::controllers
