# RTSP Input Module - CMakeLists.txt
# Optimized for Orange Pi 5 Plus/Ultra with RK3588 chip

cmake_minimum_required(VERSION 3.18)
project(rtsp_library VERSION 1.0.0 LANGUAGES CXX)

# Dependencies are now handled centrally in cmake/dependencies.cmake
# The following dependencies are available:
# - GSTREAMER_* variables for GStreamer
# - GSTREAMER_ROCKCHIP_* variables for RockChip plugins
# - FFMPEG_* variables for FFmpeg
# - OpenSSL through find_package
# - Threading support

# JSON library for configuration
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    # Fallback to system package
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(NLOHMANN_JSON nlohmann_json)
endif()

# Collect source files following project structure guidelines
file(GLOB_RECURSE RTSP_HEADERS
    include/rtsp/*.hpp
)

file(GLOB_RECURSE RTSP_SOURCES
    src/*.cpp
)

# Create RTSP library
add_library(rtsp ${RTSP_HEADERS} ${RTSP_SOURCES})

# Include directories
target_include_directories(rtsp
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Link libraries using centralized dependency functions
target_link_libraries(rtsp
    PUBLIC
        Threads::Threads
)

# Link shared library if available
if(TARGET shared)
    target_link_libraries(rtsp PUBLIC shared)
endif()

# Link common dependencies (includes OpenSSL if available)
link_common_dependencies(rtsp)

# Link RTSP-specific dependencies (GStreamer, FFmpeg, etc.)
link_rtsp_dependencies(rtsp)

# Add JSON library if found
if(nlohmann_json_FOUND)
    target_link_libraries(rtsp PUBLIC nlohmann_json::nlohmann_json)
elseif(NLOHMANN_JSON_FOUND)
    target_link_libraries(rtsp PUBLIC ${NLOHMANN_JSON_LIBRARIES})
    target_include_directories(rtsp PUBLIC ${NLOHMANN_JSON_INCLUDE_DIRS})
endif()

# Compiler definitions
target_compile_definitions(rtsp
    PRIVATE
        RTSP_PLATFORM_RK3588=1
)

# Additional RTSP-specific definitions based on available dependencies
if(GSTREAMER_FOUND)
    target_compile_definitions(rtsp PRIVATE RTSP_USE_GSTREAMER=1)
endif()

if(FFMPEG_FOUND)
    target_compile_definitions(rtsp PRIVATE RTSP_FFMPEG_FALLBACK=1)
endif()

# Note: HAVE_GSTREAMER, HAVE_GSTREAMER_ROCKCHIP, HAVE_FFMPEG are set by link_rtsp_dependencies()

# RK3588 specific optimizations
target_compile_definitions(rtsp PRIVATE
    RTSP_HARDWARE_ACCELERATION=1
    RTSP_USE_MPP_DECODER=1
    RTSP_USE_RGA_SCALER=1
)

# Note: RTSP_FFMPEG_FALLBACK is already defined above if FFMPEG is found

# Compiler flags
target_compile_options(rtsp PRIVATE
    -Wall
    -Wextra
    -Wpedantic
    -Wno-unused-parameter
    -O3
    -ftree-vectorize               # Auto-vectorization
)

# RK3588 specific compiler flags for ARM64 optimization (only on ARM64)
if(CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm64")
    target_compile_options(rtsp PRIVATE
        -march=armv8-a+crc+crypto      # RK3588 architecture
        -mtune=cortex-a76              # Optimize for big cores
        -ffast-math                    # ARM math optimizations
    )
    message(STATUS "ARM64 optimizations enabled for RK3588")
else()
    message(STATUS "Building for ${CMAKE_SYSTEM_PROCESSOR} - ARM64 optimizations disabled")
endif()

# Memory configuration for Orange Pi variants
if(ORANGE_PI_RAM_4GB)
    target_compile_definitions(rtsp PRIVATE RTSP_MAX_MEMORY_MB=1200)
elseif(ORANGE_PI_RAM_8GB)
    target_compile_definitions(rtsp PRIVATE RTSP_MAX_MEMORY_MB=2500)
elseif(ORANGE_PI_RAM_16GB)
    target_compile_definitions(rtsp PRIVATE RTSP_MAX_MEMORY_MB=6000)
else()
    # Default to 4GB configuration
    target_compile_definitions(rtsp PRIVATE RTSP_MAX_MEMORY_MB=1200)
endif()

# Debug flags (lighter for ARM platform)
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(rtsp PRIVATE
        -g
        -O1  # O1 instead of O0 for ARM performance
        -DDEBUG
        -fno-omit-frame-pointer
    )
endif()

# Release optimizations
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    target_compile_options(rtsp PRIVATE
        -DNDEBUG
        -flto
        -fuse-linker-plugin
    )
    target_link_options(rtsp PRIVATE
        -flto
        -Wl,--gc-sections
    )
endif()

# Unit tests
option(BUILD_RTSP_TESTS "Build tests for RTSP library" ON)

if(BUILD_RTSP_TESTS AND BUILD_TESTING)
    # Use FetchContent for GoogleTest if not already available
    if(NOT TARGET gtest)
        include(FetchContent)
        FetchContent_Declare(
            googletest
            URL https://github.com/google/googletest/archive/refs/heads/main.zip
        )
        FetchContent_MakeAvailable(googletest)
    endif()

    enable_testing()

    # Find all test files
    file(GLOB_RECURSE TEST_SOURCES test/*.cpp)

    # Create test executables for each test file
    foreach(TEST_SOURCE ${TEST_SOURCES})
        # Get the filename without extension
        get_filename_component(TEST_NAME ${TEST_SOURCE} NAME_WE)

        # Create executable
        add_executable(rtsp_${TEST_NAME} ${TEST_SOURCE})

        # Link with RTSP library and GoogleTest
        target_link_libraries(rtsp_${TEST_NAME}
            PRIVATE
                rtsp
                gtest_main
                gtest
        )

        # Add test to CTest
        add_test(NAME rtsp_${TEST_NAME} COMMAND rtsp_${TEST_NAME})
    endforeach()
endif()

# Print configuration summary
message(STATUS "RTSP Library Configuration:")
message(STATUS "  GStreamer found: ${GSTREAMER_FOUND}")
message(STATUS "  RockChip plugins found: ${GSTREAMER_ROCKCHIP_FOUND}")
message(STATUS "  FFmpeg fallback: ${FFMPEG_FOUND}")
message(STATUS "  OpenSSL found: ${OPENSSL_FOUND}")
message(STATUS "  Build tests: ${BUILD_RTSP_TESTS}")
message(STATUS "  Target platform: RK3588 (Orange Pi 5 Plus/Ultra)")
