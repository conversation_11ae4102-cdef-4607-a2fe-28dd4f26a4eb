#!/bin/bash

# Script to build and run the Qt5 client application
# Supports both GUI and headless modes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
BUILD_TYPE="Debug"
HEADLESS=false
REBUILD=false
SHOW_HELP=false

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Build and run the Qt5 client application.

OPTIONS:
    -h, --help          Show this help message
    -r, --rebuild       Clean and rebuild the project
    -t, --type TYPE     Build type (Debug|Release) [default: Debug]
    --headless          Run in headless mode (no GUI)
    --gui               Force GUI mode (default if DISPLAY is available)

EXAMPLES:
    $0                  # Build and run with GUI (if DISPLAY available)
    $0 --headless       # Build and run in headless mode
    $0 -r -t Release    # Clean rebuild in Release mode
    $0 --gui            # Force GUI mode even if DISPLAY not set

ENVIRONMENT:
    DISPLAY             X11 display for GUI mode
    QT_QPA_PLATFORM     Qt platform abstraction (auto-detected)

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -r|--rebuild)
            REBUILD=true
            shift
            ;;
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        --headless)
            HEADLESS=true
            shift
            ;;
        --gui)
            HEADLESS=false
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate build type
if [[ ! "$BUILD_TYPE" =~ ^(Debug|Release|RelWithDebInfo|MinSizeRel)$ ]]; then
    print_error "Invalid build type: $BUILD_TYPE"
    print_info "Valid types: Debug, Release, RelWithDebInfo, MinSizeRel"
    exit 1
fi

# Check if we're in the right directory
if [[ ! -f "CMakeLists.txt" ]]; then
    print_error "CMakeLists.txt not found. Please run this script from the project root."
    exit 1
fi

print_info "Starting C-AIBOX Client build and run process..."
print_info "Build type: $BUILD_TYPE"
print_info "Headless mode: $HEADLESS"

# Create build directory
BUILD_DIR="build"
if [[ "$REBUILD" == true ]]; then
    print_info "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
fi

mkdir -p "$BUILD_DIR"

# Configure CMake
print_info "Configuring CMake..."
cd "$BUILD_DIR"
cmake -DCMAKE_BUILD_TYPE="$BUILD_TYPE" ..

# Build the project
print_info "Building the project..."
cmake --build . --target client_app --parallel

# Check if build was successful
if [[ ! -f "apps/client/client_app" ]]; then
    print_error "Build failed! client_app executable not found."
    exit 1
fi

print_success "Build completed successfully!"

# Prepare to run the application
cd ..
CLIENT_EXECUTABLE="./build/apps/client/client_app"

# Check display availability and set run mode
if [[ "$HEADLESS" == true ]]; then
    print_info "Running in headless mode..."
    RUN_ARGS="--headless"
elif [[ -z "$DISPLAY" ]]; then
    print_warning "No DISPLAY environment variable found."
    print_info "Running in headless mode..."
    RUN_ARGS="--headless"
else
    print_info "DISPLAY found: $DISPLAY"
    print_info "Running in GUI mode..."
    RUN_ARGS=""
    
    # Test X11 connection
    if command -v xset >/dev/null 2>&1; then
        if ! xset q >/dev/null 2>&1; then
            print_warning "Cannot connect to X11 display. Falling back to headless mode."
            RUN_ARGS="--headless"
        fi
    fi
fi

# Run the application
print_info "Starting C-AIBOX Client..."
print_info "Command: $CLIENT_EXECUTABLE $RUN_ARGS"

# Set environment variables for Qt
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false"

# Load OpenGL configuration if available
if [[ -f "/tmp/gui_env_complete.sh" ]]; then
    print_info "Loading OpenGL configuration..."
    source /tmp/gui_env_complete.sh
elif [[ -f "/tmp/qt_env.sh" ]]; then
    print_info "Loading Qt environment..."
    source /tmp/qt_env.sh
fi

# Run the application
print_info "Environment variables:"
print_info "DISPLAY: ${DISPLAY:-not set}"
print_info "QT_QPA_PLATFORM: ${QT_QPA_PLATFORM:-not set}"
print_info "QT_OPENGL: ${QT_OPENGL:-not set}"
print_info "LIBGL_ALWAYS_SOFTWARE: ${LIBGL_ALWAYS_SOFTWARE:-not set}"

if [[ -n "$RUN_ARGS" ]]; then
    "$CLIENT_EXECUTABLE" $RUN_ARGS
else
    "$CLIENT_EXECUTABLE"
fi

print_success "Application finished."
