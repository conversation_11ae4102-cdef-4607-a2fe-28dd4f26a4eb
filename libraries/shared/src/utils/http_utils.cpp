#include <utils/http_utils.hpp>
#include <iostream>
#include <fstream>
#include <sstream>
#include <chrono>
#include <algorithm>
#include <cctype>

#ifdef HTTP_USE_CURL
#include <curl/curl.h>
#endif

namespace utils {

    // Static initialization for CURL
    class CurlGlobalInit {
    public:
        CurlGlobalInit() {
#ifdef HTTP_USE_CURL
            curl_global_init(CURL_GLOBAL_DEFAULT);
#endif
        }

        ~CurlGlobalInit() {
#ifdef HTTP_USE_CURL
            curl_global_cleanup();
#endif
        }
    };

    static CurlGlobalInit curlInit;

    bool HttpUtils::isAvailable() {
#ifdef HTTP_USE_CURL
        return true;
#else
        return false;
#endif
    }

    std::string HttpUtils::methodToString(HttpMethod method) {
        switch (method) {
            case HttpMethod::GET: return "GET";
            case HttpMethod::POST: return "POST";
            case HttpMethod::PUT: return "PUT";
            case HttpMethod::DELETE: return "DELETE";
            case HttpMethod::PATCH: return "PATCH";
            case HttpMethod::HEAD: return "HEAD";
            case HttpMethod::OPTIONS: return "OPTIONS";
            default: return "GET";
        }
    }

    std::optional<HttpMethod> HttpUtils::stringToMethod(const std::string& methodStr) {
        std::string upper = methodStr;
        std::transform(upper.begin(), upper.end(), upper.begin(), ::toupper);

        if (upper == "GET") return HttpMethod::GET;
        if (upper == "POST") return HttpMethod::POST;
        if (upper == "PUT") return HttpMethod::PUT;
        if (upper == "DELETE") return HttpMethod::DELETE;
        if (upper == "PATCH") return HttpMethod::PATCH;
        if (upper == "HEAD") return HttpMethod::HEAD;
        if (upper == "OPTIONS") return HttpMethod::OPTIONS;

        return std::nullopt;
    }

    HttpResponse HttpUtils::get(const std::string& url, const HttpConfig& config) {
        return request(HttpMethod::GET, url, config);
    }

    HttpResponse HttpUtils::post(const std::string& url, const std::string& body, const HttpConfig& config) {
        HttpConfig configWithBody = config;
        configWithBody.body = body;
        return request(HttpMethod::POST, url, configWithBody);
    }

    HttpResponse HttpUtils::put(const std::string& url, const std::string& body, const HttpConfig& config) {
        HttpConfig configWithBody = config;
        configWithBody.body = body;
        return request(HttpMethod::PUT, url, configWithBody);
    }

    HttpResponse HttpUtils::del(const std::string& url, const HttpConfig& config) {
        return request(HttpMethod::DELETE, url, config);
    }

    HttpResponse HttpUtils::patch(const std::string& url, const std::string& body, const HttpConfig& config) {
        HttpConfig configWithBody = config;
        configWithBody.body = body;
        return request(HttpMethod::PATCH, url, configWithBody);
    }

    HttpResponse HttpUtils::head(const std::string& url, const HttpConfig& config) {
        return request(HttpMethod::HEAD, url, config);
    }

    HttpResponse HttpUtils::request(HttpMethod method, const std::string& url, const HttpConfig& config) {
        return performRequest(method, url, config);
    }

    std::string HttpUtils::urlEncode(const std::string& str) {
#ifdef HTTP_USE_CURL
        CURL* curl = curl_easy_init();
        if (curl) {
            char* encoded = curl_easy_escape(curl, str.c_str(), static_cast<int>(str.length()));
            if (encoded) {
                std::string result(encoded);
                curl_free(encoded);
                curl_easy_cleanup(curl);
                return result;
            }
            curl_easy_cleanup(curl);
        }
#endif
        // Fallback simple encoding
        std::ostringstream encoded;
        for (char c : str) {
            if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
                encoded << c;
            } else {
                encoded << '%' << std::hex << std::uppercase << (unsigned char)c;
            }
        }
        return encoded.str();
    }

    std::string HttpUtils::urlDecode(const std::string& str) {
#ifdef HTTP_USE_CURL
        CURL* curl = curl_easy_init();
        if (curl) {
            int outlength;
            char* decoded = curl_easy_unescape(curl, str.c_str(), static_cast<int>(str.length()), &outlength);
            if (decoded) {
                std::string result(decoded, outlength);
                curl_free(decoded);
                curl_easy_cleanup(curl);
                return result;
            }
            curl_easy_cleanup(curl);
        }
#endif
        // Fallback simple decoding
        std::string decoded;
        for (size_t i = 0; i < str.length(); ++i) {
            if (str[i] == '%' && i + 2 < str.length()) {
                int value;
                std::istringstream is(str.substr(i + 1, 2));
                if (is >> std::hex >> value) {
                    decoded += static_cast<char>(value);
                    i += 2;
                } else {
                    decoded += str[i];
                }
            } else if (str[i] == '+') {
                decoded += ' ';
            } else {
                decoded += str[i];
            }
        }
        return decoded;
    }

    std::string HttpUtils::buildQueryString(const std::unordered_map<std::string, std::string>& params) {
        if (params.empty()) {
            return "";
        }

        std::ostringstream query;
        bool first = true;
        for (const auto& pair : params) {
            if (!first) {
                query << "&";
            }
            query << urlEncode(pair.first) << "=" << urlEncode(pair.second);
            first = false;
        }
        return query.str();
    }

    bool HttpUtils::parseUrl(const std::string& url, std::string& scheme, std::string& host,
                           int& port, std::string& path, std::string& query) {
        // Simple URL parsing
        size_t schemeEnd = url.find("://");
        if (schemeEnd == std::string::npos) {
            return false;
        }

        scheme = url.substr(0, schemeEnd);
        std::transform(scheme.begin(), scheme.end(), scheme.begin(), ::tolower);

        size_t hostStart = schemeEnd + 3;
        size_t pathStart = url.find('/', hostStart);
        size_t queryStart = url.find('?', hostStart);

        if (pathStart == std::string::npos) {
            pathStart = url.length();
        }
        if (queryStart == std::string::npos) {
            queryStart = url.length();
        }

        std::string hostPort = url.substr(hostStart, std::min(pathStart, queryStart) - hostStart);
        size_t portPos = hostPort.find(':');

        if (portPos != std::string::npos) {
            host = hostPort.substr(0, portPos);
            try {
                port = std::stoi(hostPort.substr(portPos + 1));
            } catch (...) {
                port = (scheme == "https") ? 443 : 80;
            }
        } else {
            host = hostPort;
            port = (scheme == "https") ? 443 : 80;
        }

        if (pathStart < url.length()) {
            size_t pathEnd = (queryStart < url.length()) ? queryStart : url.length();
            path = url.substr(pathStart, pathEnd - pathStart);
        } else {
            path = "/";
        }

        if (queryStart < url.length()) {
            query = url.substr(queryStart + 1);
        } else {
            query = "";
        }

        return true;
    }

#ifdef HTTP_USE_CURL
    size_t HttpUtils::writeCallback(void* contents, size_t size, size_t nmemb, std::string* response) {
        size_t totalSize = size * nmemb;
        response->append(static_cast<char*>(contents), totalSize);
        return totalSize;
    }

    size_t HttpUtils::headerCallback(void* contents, size_t size, size_t nmemb,
                                   std::unordered_map<std::string, std::string>* headers) {
        size_t totalSize = size * nmemb;
        std::string headerLine(static_cast<char*>(contents), totalSize);
        parseHeaderLine(headerLine, *headers);
        return totalSize;
    }

    void HttpUtils::parseHeaderLine(const std::string& headerLine,
                                  std::unordered_map<std::string, std::string>& headers) {
        size_t colonPos = headerLine.find(':');
        if (colonPos != std::string::npos) {
            std::string key = headerLine.substr(0, colonPos);
            std::string value = headerLine.substr(colonPos + 1);

            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t\r\n"));
            key.erase(key.find_last_not_of(" \t\r\n") + 1);
            value.erase(0, value.find_first_not_of(" \t\r\n"));
            value.erase(value.find_last_not_of(" \t\r\n") + 1);

            if (!key.empty()) {
                headers[key] = value;
            }
        }
    }
#endif

    // Convenience functions implementation
    HttpResponse httpGet(const std::string& url, const HttpConfig& config) {
        return HttpUtils::get(url, config);
    }

    HttpResponse httpPost(const std::string& url, const std::string& body, const HttpConfig& config) {
        return HttpUtils::post(url, body, config);
    }

    HttpResponse httpPut(const std::string& url, const std::string& body, const HttpConfig& config) {
        return HttpUtils::put(url, body, config);
    }

    HttpResponse httpDelete(const std::string& url, const HttpConfig& config) {
        return HttpUtils::del(url, config);
    }

    bool downloadFile(const std::string& url, const std::string& filepath, const HttpConfig& config) {
        return HttpUtils::downloadFile(url, filepath, config);
    }

    HttpResponse HttpUtils::performRequest(HttpMethod method, const std::string& url, const HttpConfig& config) {
        HttpResponse response;

#ifndef HTTP_USE_CURL
        response.errorMessage = "HTTP utils not available - CURL not compiled";
        return response;
#else
        CURL* curl = curl_easy_init();
        if (!curl) {
            response.errorMessage = "Failed to initialize CURL";
            return response;
        }

        auto startTime = std::chrono::high_resolution_clock::now();

        try {
            setupCurlHandle(curl, method, url, config);

            // Set callbacks for response data and headers
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, writeCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response.body);
            curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, headerCallback);
            curl_easy_setopt(curl, CURLOPT_HEADERDATA, &response.headers);

            // Perform the request
            CURLcode res = curl_easy_perform(curl);

            auto endTime = std::chrono::high_resolution_clock::now();
            response.responseTime = std::chrono::duration<double>(endTime - startTime).count();

            if (res != CURLE_OK) {
                response.errorMessage = curl_easy_strerror(res);
                response.success = false;
            } else {
                long responseCode;
                curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &responseCode);
                response.statusCode = static_cast<int>(responseCode);
                response.success = true;

                // Set status text based on common HTTP status codes
                switch (response.statusCode) {
                    case 200: response.statusText = "OK"; break;
                    case 201: response.statusText = "Created"; break;
                    case 204: response.statusText = "No Content"; break;
                    case 400: response.statusText = "Bad Request"; break;
                    case 401: response.statusText = "Unauthorized"; break;
                    case 403: response.statusText = "Forbidden"; break;
                    case 404: response.statusText = "Not Found"; break;
                    case 500: response.statusText = "Internal Server Error"; break;
                    default: response.statusText = "Unknown"; break;
                }
            }
        } catch (const std::exception& e) {
            response.errorMessage = e.what();
            response.success = false;
        }

        curl_easy_cleanup(curl);
        return response;
#endif
    }

#ifdef HTTP_USE_CURL
    void HttpUtils::setupCurlHandle(void* curlPtr, HttpMethod method, const std::string& url, const HttpConfig& config) {
        CURL* curl = static_cast<CURL*>(curlPtr);

        // Basic setup
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_USERAGENT, config.userAgent.c_str());
        curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, config.timeoutMs);
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, config.followRedirects ? 1L : 0L);
        curl_easy_setopt(curl, CURLOPT_MAXREDIRS, config.maxRedirects);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, config.verifySSL ? 1L : 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, config.verifySSL ? 2L : 0L);

        // Set HTTP method
        switch (method) {
            case HttpMethod::GET:
                curl_easy_setopt(curl, CURLOPT_HTTPGET, 1L);
                break;
            case HttpMethod::POST:
                curl_easy_setopt(curl, CURLOPT_POST, 1L);
                if (!config.body.empty()) {
                    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, config.body.c_str());
                    curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, config.body.length());
                }
                break;
            case HttpMethod::PUT:
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PUT");
                if (!config.body.empty()) {
                    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, config.body.c_str());
                    curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, config.body.length());
                }
                break;
            case HttpMethod::DELETE:
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
                break;
            case HttpMethod::PATCH:
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "PATCH");
                if (!config.body.empty()) {
                    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, config.body.c_str());
                    curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, config.body.length());
                }
                break;
            case HttpMethod::HEAD:
                curl_easy_setopt(curl, CURLOPT_NOBODY, 1L);
                break;
            case HttpMethod::OPTIONS:
                curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "OPTIONS");
                break;
        }

        // Set headers
        struct curl_slist* headers = nullptr;
        for (const auto& header : config.headers) {
            std::string headerStr = header.first + ": " + header.second;
            headers = curl_slist_append(headers, headerStr.c_str());
        }

        if (headers) {
            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        }

        // Authentication
        if (!config.basicAuth.empty()) {
            curl_easy_setopt(curl, CURLOPT_USERPWD, config.basicAuth.c_str());
        }

        // Proxy settings
        if (!config.proxy.empty()) {
            curl_easy_setopt(curl, CURLOPT_PROXY, config.proxy.c_str());
            if (!config.proxyAuth.empty()) {
                curl_easy_setopt(curl, CURLOPT_PROXYUSERPWD, config.proxyAuth.c_str());
            }
        }
    }
#endif

    bool HttpUtils::downloadFile(const std::string& url, const std::string& filepath, const HttpConfig& config) {
#ifndef HTTP_USE_CURL
        return false;
#else
        std::ofstream file(filepath, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }

        CURL* curl = curl_easy_init();
        if (!curl) {
            return false;
        }

        // Setup CURL for file download
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, [](void* contents, size_t size, size_t nmemb, std::ofstream* file) -> size_t {
            size_t totalSize = size * nmemb;
            file->write(static_cast<char*>(contents), totalSize);
            return totalSize;
        });
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &file);
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, config.followRedirects ? 1L : 0L);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, config.timeoutMs);

        CURLcode res = curl_easy_perform(curl);
        curl_easy_cleanup(curl);
        file.close();

        return res == CURLE_OK;
#endif
    }

    HttpResponse HttpUtils::uploadFile(const std::string& url, const std::string& filepath,
                                     const std::string& fieldName, const HttpConfig& config) {
        HttpResponse response;

#ifndef HTTP_USE_CURL
        response.errorMessage = "HTTP utils not available - CURL not compiled";
        return response;
#else
        CURL* curl = curl_easy_init();
        if (!curl) {
            response.errorMessage = "Failed to initialize CURL";
            return response;
        }

        curl_mime* mime = curl_mime_init(curl);
        curl_mimepart* part = curl_mime_addpart(mime);

        curl_mime_name(part, fieldName.c_str());
        curl_mime_filedata(part, filepath.c_str());

        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_MIMEPOST, mime);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, writeCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response.body);

        CURLcode res = curl_easy_perform(curl);

        if (res != CURLE_OK) {
            response.errorMessage = curl_easy_strerror(res);
            response.success = false;
        } else {
            long responseCode;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &responseCode);
            response.statusCode = static_cast<int>(responseCode);
            response.success = true;
        }

        curl_mime_free(mime);
        curl_easy_cleanup(curl);
        return response;
#endif
    }
}
