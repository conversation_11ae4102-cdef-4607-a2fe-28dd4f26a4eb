#include "utils/number_utils.hpp"
#include <sstream>
#include <iomanip>
#include <random>
#include <algorithm>
#include <regex>
#include <cmath>

namespace utils {

    std::optional<int> NumberUtils::parseInt(const std::string& str) {
        try {
            size_t pos;
            int result = std::stoi(str, &pos);
            // Check if entire string was consumed
            if (pos == str.length()) {
                return result;
            }
        } catch (const std::exception&) {
            // Parsing failed
        }
        return std::nullopt;
    }

    std::optional<long> NumberUtils::parseLong(const std::string& str) {
        try {
            size_t pos;
            long result = std::stol(str, &pos);
            if (pos == str.length()) {
                return result;
            }
        } catch (const std::exception&) {
            // Parsing failed
        }
        return std::nullopt;
    }

    std::optional<long long> NumberUtils::parseLongLong(const std::string& str) {
        try {
            size_t pos;
            long long result = std::stoll(str, &pos);
            if (pos == str.length()) {
                return result;
            }
        } catch (const std::exception&) {
            // Parsing failed
        }
        return std::nullopt;
    }

    std::optional<float> NumberUtils::parseFloat(const std::string& str) {
        try {
            size_t pos;
            float result = std::stof(str, &pos);
            if (pos == str.length()) {
                return result;
            }
        } catch (const std::exception&) {
            // Parsing failed
        }
        return std::nullopt;
    }

    std::optional<double> NumberUtils::parseDouble(const std::string& str) {
        try {
            size_t pos;
            double result = std::stod(str, &pos);
            if (pos == str.length()) {
                return result;
            }
        } catch (const std::exception&) {
            // Parsing failed
        }
        return std::nullopt;
    }

    std::optional<unsigned int> NumberUtils::parseUInt(const std::string& str) {
        try {
            size_t pos;
            unsigned long result = std::stoul(str, &pos);
            if (pos == str.length() && result <= std::numeric_limits<unsigned int>::max()) {
                return static_cast<unsigned int>(result);
            }
        } catch (const std::exception&) {
            // Parsing failed
        }
        return std::nullopt;
    }

    std::optional<unsigned long> NumberUtils::parseULong(const std::string& str) {
        try {
            size_t pos;
            unsigned long result = std::stoul(str, &pos);
            if (pos == str.length()) {
                return result;
            }
        } catch (const std::exception&) {
            // Parsing failed
        }
        return std::nullopt;
    }

    std::optional<unsigned long long> NumberUtils::parseULongLong(const std::string& str) {
        try {
            size_t pos;
            unsigned long long result = std::stoull(str, &pos);
            if (pos == str.length()) {
                return result;
            }
        } catch (const std::exception&) {
            // Parsing failed
        }
        return std::nullopt;
    }

    std::string NumberUtils::toString(long long value, int base) {
        if (base < 2 || base > 36) {
            base = 10;
        }

        if (value == 0) {
            return "0";
        }

        std::string result;
        bool negative = value < 0;
        if (negative) {
            value = -value;
        }

        const char digits[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        while (value > 0) {
            result = digits[value % base] + result;
            value /= base;
        }

        if (negative) {
            result = "-" + result;
        }

        return result;
    }

    std::string NumberUtils::toString(unsigned long long value, int base) {
        if (base < 2 || base > 36) {
            base = 10;
        }

        if (value == 0) {
            return "0";
        }

        std::string result;
        const char digits[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

        while (value > 0) {
            result = digits[value % static_cast<unsigned long long>(base)] + result;
            value /= static_cast<unsigned long long>(base);
        }

        return result;
    }

    std::string NumberUtils::toString(double value, int precision, bool fixed) {
        std::ostringstream oss;
        if (fixed) {
            oss << std::fixed;
        }
        oss << std::setprecision(precision) << value;
        return oss.str();
    }

    std::string NumberUtils::formatWithSeparator(long long value, const std::string& separator) {
        std::string str = std::to_string(std::abs(value));
        std::string result;

        int count = 0;
        for (int i = static_cast<int>(str.length()) - 1; i >= 0; --i) {
            if (count > 0 && count % 3 == 0) {
                result = separator + result;
            }
            result = str[static_cast<size_t>(i)] + result;
            count++;
        }

        if (value < 0) {
            result = "-" + result;
        }

        return result;
    }

    std::string NumberUtils::formatWithSeparator(double value, const std::string& separator, int precision) {
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(precision) << std::abs(value);
        std::string str = oss.str();

        size_t dotPos = str.find('.');
        std::string intPart = str.substr(0, dotPos);
        std::string fracPart = (dotPos != std::string::npos) ? str.substr(dotPos) : "";

        std::string result;
        int count = 0;
        for (int i = static_cast<int>(intPart.length()) - 1; i >= 0; --i) {
            if (count > 0 && count % 3 == 0) {
                result = separator + result;
            }
            result = intPart[static_cast<size_t>(i)] + result;
            count++;
        }

        result += fracPart;

        if (value < 0) {
            result = "-" + result;
        }

        return result;
    }

    bool NumberUtils::isEqual(double a, double b, double tolerance) {
        return std::abs(a - b) <= tolerance;
    }

    bool NumberUtils::isZero(double value, double tolerance) {
        return std::abs(value) <= tolerance;
    }

    bool NumberUtils::isPrime(long long value) {
        if (value < 2) {
            return false;
        }
        if (value == 2) {
            return true;
        }
        if (value % 2 == 0) {
            return false;
        }

        for (long long i = 3; i * i <= value; i += 2) {
            if (value % i == 0) {
                return false;
            }
        }

        return true;
    }

    bool NumberUtils::isPerfectSquare(long long value) {
        if (value < 0) {
            return false;
        }

        long long root = static_cast<long long>(std::sqrt(value));
        return root * root == value;
    }

    double NumberUtils::round(double value, int decimals) {
        double factor = std::pow(10.0, decimals);
        return std::round(value * factor) / factor;
    }

    double NumberUtils::ceil(double value, int decimals) {
        double factor = std::pow(10.0, decimals);
        return std::ceil(value * factor) / factor;
    }

    double NumberUtils::floor(double value, int decimals) {
        double factor = std::pow(10.0, decimals);
        return std::floor(value * factor) / factor;
    }

    double NumberUtils::percentage(double part, double total) {
        if (isZero(total)) {
            return 0.0;
        }
        return (part / total) * 100.0;
    }

    double NumberUtils::percentageOf(double value, double percent) {
        return (value * percent) / 100.0;
    }

    int NumberUtils::randomInt(int min, int max) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(min, max);
        return dis(gen);
    }

    double NumberUtils::randomDouble(double min, double max) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        std::uniform_real_distribution<> dis(min, max);
        return dis(gen);
    }

    long long NumberUtils::gcd(long long a, long long b) {
        a = std::abs(a);
        b = std::abs(b);

        while (b != 0) {
            long long temp = b;
            b = a % b;
            a = temp;
        }

        return a;
    }

    long long NumberUtils::lcm(long long a, long long b) {
        if (a == 0 || b == 0) {
            return 0;
        }

        return std::abs(a * b) / gcd(a, b);
    }

    long long NumberUtils::factorial(int n) {
        if (n < 0) {
            return 0;
        }
        if (n <= 1) {
            return 1;
        }

        long long result = 1;
        for (int i = 2; i <= n; ++i) {
            result *= i;
        }

        return result;
    }

    double NumberUtils::power(double base, double exponent) {
        return std::pow(base, exponent);
    }

    double NumberUtils::sqrt(double value) {
        return std::sqrt(value);
    }

    double NumberUtils::log10(double value) {
        return std::log10(value);
    }

    double NumberUtils::ln(double value) {
        return std::log(value);
    }

    double NumberUtils::toRadians(double degrees) {
        return degrees * M_PI / 180.0;
    }

    double NumberUtils::toDegrees(double radians) {
        return radians * 180.0 / M_PI;
    }

    double NumberUtils::lerp(double a, double b, double t) {
        return a + t * (b - a);
    }

    double NumberUtils::map(double value, double fromMin, double fromMax, double toMin, double toMax) {
        if (isEqual(fromMax, fromMin)) {
            return toMin;
        }

        double ratio = (value - fromMin) / (fromMax - fromMin);
        return toMin + ratio * (toMax - toMin);
    }

    bool NumberUtils::isValidNumber(const std::string& str) {
        std::regex numberRegex(R"(^[+-]?(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?$)");
        return std::regex_match(str, numberRegex);
    }

    // Convenience functions implementation
    std::optional<int> parseInt(const std::string& str) {
        return NumberUtils::parseInt(str);
    }

    std::optional<double> parseDouble(const std::string& str) {
        return NumberUtils::parseDouble(str);
    }

    std::string toString(double value) {
        return NumberUtils::toString(value);
    }

    bool isEqual(double a, double b) {
        return NumberUtils::isEqual(a, b);
    }

    int randomInt(int min, int max) {
        return NumberUtils::randomInt(min, max);
    }
}
