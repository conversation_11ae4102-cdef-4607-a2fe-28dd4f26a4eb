#!/bin/bash

# Script to setup OpenGL support in container for Qt GUI applications
# This script configures Mesa drivers and OpenGL environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "Setting up OpenGL support for Qt GUI applications in container..."

# Check if we're in a container
if [[ -f /.dockerenv ]]; then
    print_info "Running inside a container"
else
    print_warning "Not running in a container - some steps may not be necessary"
fi

# Install additional OpenGL packages if needed
print_info "Checking OpenGL dependencies..."
OPENGL_PACKAGES=(
    "mesa-utils"
    "libgl1-mesa-dri"
    "libglu1-mesa"
    "libegl1"
    "libgles2"
    "mesa-vulkan-drivers"
    "libglx-mesa0"
)

MISSING_PACKAGES=()
for package in "${OPENGL_PACKAGES[@]}"; do
    if ! dpkg -l | grep -q "^ii.*$package"; then
        MISSING_PACKAGES+=("$package")
    fi
done

if [[ ${#MISSING_PACKAGES[@]} -gt 0 ]]; then
    print_info "Installing missing OpenGL packages: ${MISSING_PACKAGES[*]}"
    apt-get update && apt-get install -y "${MISSING_PACKAGES[@]}"
else
    print_success "All OpenGL packages are installed"
fi

# Configure Mesa for software rendering
print_info "Configuring Mesa for container environment..."

# Create Mesa configuration
cat > /tmp/mesa_config.sh << 'EOF'
#!/bin/bash
# Mesa OpenGL configuration for container

# Force software rendering with llvmpipe
export MESA_GL_VERSION_OVERRIDE=3.3
export MESA_GLSL_VERSION_OVERRIDE=330
export GALLIUM_DRIVER=llvmpipe
export LP_NUM_THREADS=1

# Disable hardware acceleration
export LIBGL_ALWAYS_SOFTWARE=1
export LIBGL_ALWAYS_INDIRECT=1

# Mesa debugging (optional)
export MESA_DEBUG=0
export LIBGL_DEBUG=quiet

# EGL configuration
export EGL_PLATFORM=x11

echo "Mesa OpenGL configuration loaded:"
echo "MESA_GL_VERSION_OVERRIDE: $MESA_GL_VERSION_OVERRIDE"
echo "GALLIUM_DRIVER: $GALLIUM_DRIVER"
echo "LIBGL_ALWAYS_SOFTWARE: $LIBGL_ALWAYS_SOFTWARE"
EOF

chmod +x /tmp/mesa_config.sh
print_success "Mesa configuration created: /tmp/mesa_config.sh"

# Test OpenGL functionality
print_info "Testing OpenGL functionality..."

# Source Mesa config
source /tmp/mesa_config.sh

# Test with glxinfo
if command -v glxinfo >/dev/null 2>&1; then
    print_info "Testing OpenGL with glxinfo..."
    if glxinfo | grep -q "OpenGL renderer"; then
        RENDERER=$(glxinfo | grep "OpenGL renderer" | cut -d: -f2 | xargs)
        print_success "OpenGL renderer: $RENDERER"
    else
        print_warning "Could not detect OpenGL renderer"
    fi
else
    print_info "glxinfo not available, installing mesa-utils..."
    apt-get update && apt-get install -y mesa-utils
fi

# Test with glxgears
if command -v glxgears >/dev/null 2>&1; then
    print_info "OpenGL test available: glxgears"
    print_info "You can test OpenGL with: timeout 5s glxgears"
else
    print_warning "glxgears not available for testing"
fi

# Create Qt-specific OpenGL configuration
print_info "Creating Qt OpenGL configuration..."

cat > /tmp/qt_opengl_config.sh << 'EOF'
#!/bin/bash
# Qt OpenGL configuration for container

# Load Mesa configuration
source /tmp/mesa_config.sh

# Qt OpenGL settings
export QT_OPENGL=software
export QT_QUICK_BACKEND=software

# Disable Qt OpenGL optimizations that may cause issues
export QT_XCB_GL_INTEGRATION=none
export QT_OPENGL_ES_2_ANGLE_PLATFORM=d3d11

# Qt WebEngine OpenGL settings
export QTWEBENGINE_CHROMIUM_FLAGS="--disable-gpu --disable-software-rasterizer --disable-dev-shm-usage --no-sandbox"

# Additional Qt settings for container
export QT_X11_NO_MITSHM=1
export QT_QPA_PLATFORM=xcb
export QTWEBENGINE_DISABLE_SANDBOX=1

echo "Qt OpenGL configuration loaded"
echo "QT_OPENGL: $QT_OPENGL"
echo "QT_QUICK_BACKEND: $QT_QUICK_BACKEND"
EOF

chmod +x /tmp/qt_opengl_config.sh
print_success "Qt OpenGL configuration created: /tmp/qt_opengl_config.sh"

# Create comprehensive environment setup
print_info "Creating comprehensive GUI environment setup..."

cat > /tmp/gui_env_complete.sh << 'EOF'
#!/bin/bash
# Complete GUI environment setup for container

# Load Mesa OpenGL configuration
source /tmp/mesa_config.sh

# Load Qt OpenGL configuration  
source /tmp/qt_opengl_config.sh

# X11 configuration
export DISPLAY=${DISPLAY:-:0}
export XAUTHORITY=${XAUTHORITY:-$HOME/.Xauthority}

# Additional environment variables
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false;qt.qpa.gl.warning=false"
export QT_AUTO_SCREEN_SCALE_FACTOR=0
export QT_SCALE_FACTOR=1
export QT_FONT_DPI=96

echo "Complete GUI environment configured for container"
echo "Ready to run Qt GUI applications"
EOF

chmod +x /tmp/gui_env_complete.sh
print_success "Complete GUI environment setup created: /tmp/gui_env_complete.sh"

# Create OpenGL test script
cat > /tmp/test_opengl.sh << 'EOF'
#!/bin/bash
echo "Testing OpenGL in container..."

# Load environment
source /tmp/gui_env_complete.sh

echo "Environment variables:"
echo "DISPLAY: $DISPLAY"
echo "MESA_GL_VERSION_OVERRIDE: $MESA_GL_VERSION_OVERRIDE"
echo "GALLIUM_DRIVER: $GALLIUM_DRIVER"
echo "QT_OPENGL: $QT_OPENGL"

echo ""
echo "Testing OpenGL info..."
if command -v glxinfo >/dev/null 2>&1; then
    echo "OpenGL vendor: $(glxinfo | grep "OpenGL vendor" | cut -d: -f2 | xargs)"
    echo "OpenGL renderer: $(glxinfo | grep "OpenGL renderer" | cut -d: -f2 | xargs)"
    echo "OpenGL version: $(glxinfo | grep "OpenGL version" | cut -d: -f2 | xargs)"
else
    echo "glxinfo not available"
fi

echo ""
echo "Testing X11 connection..."
if command -v xdpyinfo >/dev/null 2>&1; then
    if timeout 3s xdpyinfo >/dev/null 2>&1; then
        echo "✅ X11 connection successful"
    else
        echo "❌ X11 connection failed"
    fi
else
    echo "xdpyinfo not available"
fi

echo ""
echo "You can test OpenGL rendering with:"
echo "  timeout 5s glxgears"
echo "  timeout 5s xclock"
EOF

chmod +x /tmp/test_opengl.sh
print_success "OpenGL test script created: /tmp/test_opengl.sh"

print_info ""
print_success "OpenGL setup completed!"
print_info ""
print_info "Usage:"
print_info "1. Load complete environment: source /tmp/gui_env_complete.sh"
print_info "2. Test OpenGL: /tmp/test_opengl.sh"
print_info "3. Test with simple apps: timeout 5s glxgears"
print_info "4. Run Qt application: ./build/apps/client/client_app"
print_info ""
print_info "Environment files created:"
print_info "- /tmp/mesa_config.sh - Mesa OpenGL configuration"
print_info "- /tmp/qt_opengl_config.sh - Qt OpenGL configuration"
print_info "- /tmp/gui_env_complete.sh - Complete GUI environment"
print_info "- /tmp/test_opengl.sh - OpenGL testing script"
