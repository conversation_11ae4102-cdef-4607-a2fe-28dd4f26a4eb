#!/bin/bash

# Clean script for c-aibox-v1 project
# Usage: ./scripts/clean.sh [options]

set -e  # Exit on any error

# Default values
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CLEAN_BUILD=true
CLEAN_CACHE=false
CLEAN_DEPS=false
CLEAN_ALL=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "OPTIONS:"
    echo "  --build      Clean build directories (default)"
    echo "  --cache      Clean CMake cache files"
    echo "  --deps       Clean downloaded dependencies"
    echo "  --all        Clean everything (build + cache + deps)"
    echo "  --help, -h   Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Clean build directories"
    echo "  $0 --cache      # Clean CMake cache"
    echo "  $0 --all        # Clean everything"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --build)
            CLEAN_BUILD=true
            shift
            ;;
        --cache)
            CLEAN_CACHE=true
            CLEAN_BUILD=false
            shift
            ;;
        --deps)
            CLEAN_DEPS=true
            CLEAN_BUILD=false
            shift
            ;;
        --all)
            CLEAN_ALL=true
            CLEAN_BUILD=true
            CLEAN_CACHE=true
            CLEAN_DEPS=true
            shift
            ;;
        --help|-h)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Change to project root directory
cd "$PROJECT_ROOT"

print_info "Cleaning c-aibox-v1 project"
print_info "Project root: $PROJECT_ROOT"

# Function to safely remove directory
safe_remove() {
    local dir="$1"
    local description="$2"
    
    if [ -d "$dir" ]; then
        print_info "Removing $description: $dir"
        rm -rf "$dir"
        print_success "Removed $description"
    else
        print_info "$description not found: $dir"
    fi
}

# Function to safely remove file
safe_remove_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        print_info "Removing $description: $file"
        rm -f "$file"
        print_success "Removed $description"
    else
        print_info "$description not found: $file"
    fi
}

# Clean build directories
if [ "$CLEAN_BUILD" = true ]; then
    print_info "Cleaning build directories..."
    
    # Common build directory names
    safe_remove "build" "build directory"
    safe_remove "build-debug" "debug build directory"
    safe_remove "build-release" "release build directory"
    safe_remove "out" "output directory"
    safe_remove "bin" "binary directory"
    safe_remove "lib" "library directory"
    
    # CMake build directories
    find . -maxdepth 2 -type d -name "cmake-build-*" -exec rm -rf {} + 2>/dev/null || true
    find . -maxdepth 2 -type d -name "build-*" -exec rm -rf {} + 2>/dev/null || true
fi

# Clean CMake cache files
if [ "$CLEAN_CACHE" = true ]; then
    print_info "Cleaning CMake cache files..."
    
    safe_remove_file "CMakeCache.txt" "CMake cache"
    safe_remove "CMakeFiles" "CMake files directory"
    safe_remove_file "cmake_install.cmake" "CMake install script"
    safe_remove_file "Makefile" "generated Makefile"
    
    # Find and remove CMake cache files recursively
    find . -name "CMakeCache.txt" -delete 2>/dev/null || true
    find . -name "CMakeFiles" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name "cmake_install.cmake" -delete 2>/dev/null || true
    find . -name "Makefile" -not -path "./third_party/*" -delete 2>/dev/null || true
fi

# Clean downloaded dependencies
if [ "$CLEAN_DEPS" = true ]; then
    print_info "Cleaning downloaded dependencies..."
    
    # Conan files
    safe_remove_file "conandata.yml" "Conan data file"
    safe_remove_file "conaninfo.txt" "Conan info file"
    safe_remove_file "conanbuildinfo.cmake" "Conan build info"
    safe_remove_file "conan.lock" "Conan lock file"
    safe_remove ".conan" "Conan cache directory"
    
    # vcpkg files
    safe_remove "vcpkg_installed" "vcpkg installed directory"
    
    # FetchContent cache
    safe_remove "_deps" "FetchContent dependencies"
    
    # Node modules (if any)
    safe_remove "node_modules" "Node modules directory"
fi

# Clean temporary and generated files
print_info "Cleaning temporary files..."

# Object files and libraries
find . -name "*.o" -delete 2>/dev/null || true
find . -name "*.obj" -delete 2>/dev/null || true
find . -name "*.a" -delete 2>/dev/null || true
find . -name "*.lib" -delete 2>/dev/null || true
find . -name "*.so" -delete 2>/dev/null || true
find . -name "*.dll" -delete 2>/dev/null || true
find . -name "*.dylib" -delete 2>/dev/null || true

# Executable files (be careful not to delete scripts)
find . -name "*.exe" -delete 2>/dev/null || true

# Debug files
find . -name "*.pdb" -delete 2>/dev/null || true
find . -name "*.dSYM" -type d -exec rm -rf {} + 2>/dev/null || true

# Coverage files
find . -name "*.gcov" -delete 2>/dev/null || true
find . -name "*.gcda" -delete 2>/dev/null || true
find . -name "*.gcno" -delete 2>/dev/null || true

# Temporary files
find . -name "*.tmp" -delete 2>/dev/null || true
find . -name "*.temp" -delete 2>/dev/null || true
find . -name "*~" -delete 2>/dev/null || true

# IDE files
safe_remove ".vs" "Visual Studio directory"
safe_remove ".vscode/ipch" "VSCode IntelliSense cache"

print_success "Cleaning completed!"

# Show remaining disk usage
if command -v du >/dev/null 2>&1; then
    print_info "Current project size: $(du -sh . 2>/dev/null | cut -f1)"
fi
