#!/bin/bash

# Orange Pi Compatible Cross-Compilation Build Script
# Builds with compatibility for older GLIBC versions

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Build a simple compatible server
build_compatible_server() {
    log_step "Building Compatible Server for Orange Pi"
    
    cd "$PROJECT_ROOT"
    
    # Create a simple server that works with older GLIBC
    log_info "Creating compatible server source..."
    cat > /tmp/compatible_server.cpp << 'EOF'
#include <iostream>
#include <string>
#include <cstring>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <signal.h>

class SimpleServer {
private:
    int server_fd;
    int port;
    bool running;
    
public:
    SimpleServer(int p) : port(p), running(false) {}
    
    bool start() {
        server_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (server_fd == -1) {
            std::cerr << "Failed to create socket" << std::endl;
            return false;
        }
        
        int opt = 1;
        if (setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt))) {
            std::cerr << "Failed to set socket options" << std::endl;
            return false;
        }
        
        struct sockaddr_in address;
        address.sin_family = AF_INET;
        address.sin_addr.s_addr = INADDR_ANY;
        address.sin_port = htons(port);
        
        if (bind(server_fd, (struct sockaddr*)&address, sizeof(address)) < 0) {
            std::cerr << "Failed to bind to port " << port << std::endl;
            return false;
        }
        
        if (listen(server_fd, 3) < 0) {
            std::cerr << "Failed to listen on socket" << std::endl;
            return false;
        }
        
        running = true;
        std::cout << "c-aibox server listening on port " << port << std::endl;
        return true;
    }
    
    void run() {
        while (running) {
            struct sockaddr_in client_addr;
            socklen_t client_len = sizeof(client_addr);
            
            int client_fd = accept(server_fd, (struct sockaddr*)&client_addr, &client_len);
            if (client_fd < 0) {
                if (running) {
                    std::cerr << "Failed to accept connection" << std::endl;
                }
                continue;
            }
            
            // Simple HTTP response
            const char* response = 
                "HTTP/1.1 200 OK\r\n"
                "Content-Type: application/json\r\n"
                "Content-Length: 85\r\n"
                "\r\n"
                "{\"status\":\"ok\",\"message\":\"c-aibox server running\",\"platform\":\"Orange Pi\"}";
            
            send(client_fd, response, strlen(response), 0);
            close(client_fd);
            
            std::cout << "Served request from " << inet_ntoa(client_addr.sin_addr) << std::endl;
        }
    }
    
    void stop() {
        running = false;
        if (server_fd >= 0) {
            close(server_fd);
        }
    }
};

SimpleServer* server_instance = nullptr;

void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down..." << std::endl;
    if (server_instance) {
        server_instance->stop();
    }
}

void show_help() {
    std::cout << "c-aibox Compatible Server for Orange Pi\n\n";
    std::cout << "Usage: ./server [OPTIONS]\n\n";
    std::cout << "Options:\n";
    std::cout << "  --port PORT    Listen on specified port (default: 8080)\n";
    std::cout << "  --version      Show version information\n";
    std::cout << "  --help         Show this help\n\n";
    std::cout << "Examples:\n";
    std::cout << "  ./server                    # Start on port 8080\n";
    std::cout << "  ./server --port 9000        # Start on port 9000\n";
    std::cout << "  curl http://localhost:8080  # Test the server\n\n";
}

void show_version() {
    std::cout << "c-aibox Compatible Server v1.0.0\n";
    std::cout << "Built for Orange Pi (ARM64)\n";
    std::cout << "Compatible with GLIBC 2.36+\n";
    std::cout << "Cross-compiled on " << __DATE__ << " " << __TIME__ << std::endl;
}

int main(int argc, char* argv[]) {
    int port = 8080;
    
    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "--help") == 0) {
            show_help();
            return 0;
        } else if (strcmp(argv[i], "--version") == 0) {
            show_version();
            return 0;
        } else if (strcmp(argv[i], "--port") == 0 && i + 1 < argc) {
            port = std::stoi(argv[i + 1]);
            i++; // Skip next argument
        } else {
            std::cerr << "Unknown option: " << argv[i] << std::endl;
            show_help();
            return 1;
        }
    }
    
    std::cout << "Starting c-aibox Compatible Server..." << std::endl;
    show_version();
    std::cout << std::endl;
    
    // Set up signal handlers
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // Create and start server
    SimpleServer server(port);
    server_instance = &server;
    
    if (!server.start()) {
        std::cerr << "Failed to start server" << std::endl;
        return 1;
    }
    
    // Run server
    server.run();
    
    std::cout << "Server stopped." << std::endl;
    return 0;
}
EOF
    
    # Build with maximum compatibility
    log_info "Cross-compiling compatible server..."
    aarch64-linux-gnu-g++ \
        -static-libgcc \
        -static-libstdc++ \
        -std=c++11 \
        -O2 \
        -DNDEBUG \
        -o build-orangepi/server_compatible \
        /tmp/compatible_server.cpp
    
    # Verify the build
    if [[ -f "build-orangepi/server_compatible" ]]; then
        log_success "Compatible server built successfully"
        
        # Check dependencies
        log_info "Checking dependencies..."
        ldd build-orangepi/server_compatible || true
        
        # Check size
        local size=$(du -sh build-orangepi/server_compatible | cut -f1)
        log_info "Server size: $size"
        
        # Verify architecture
        if file build-orangepi/server_compatible | grep -q "ARM aarch64"; then
            log_success "ARM64 architecture confirmed"
        else
            log_warning "Could not verify ARM64 architecture"
        fi
    else
        log_error "Failed to build compatible server"
        return 1
    fi
    
    # Clean up
    rm -f /tmp/compatible_server.cpp
}

# Deploy compatible server
deploy_compatible_server() {
    log_step "Deploying Compatible Server to Orange Pi"
    
    if [[ ! -f "build-orangepi/server_compatible" ]]; then
        log_error "Compatible server not found. Build it first."
        return 1
    fi
    
    # Load SSH configuration
    if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
        source "$PROJECT_ROOT/.orangepi_ssh"
        log_info "Using SSH key authentication"
        SCP_CMD="scp -i $ORANGE_PI_SSH_KEY -P $ORANGE_PI_PORT"
        SSH_CMD="ssh -i $ORANGE_PI_SSH_KEY -p $ORANGE_PI_PORT"
    else
        log_warning "No SSH key found, will use password authentication"
        SCP_CMD="scp -P 22"
        SSH_CMD="ssh -p 22"
        ORANGE_PI_IP="***************"
        ORANGE_PI_USER="orangepi"
    fi
    
    # Transfer compatible server
    log_info "Transferring compatible server..."
    $SCP_CMD build-orangepi/server_compatible $ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/c-aibox/
    
    # Set permissions
    log_info "Setting executable permissions..."
    $SSH_CMD $ORANGE_PI_USER@$ORANGE_PI_IP "chmod +x /home/<USER>/c-aibox/server_compatible"
    
    log_success "Compatible server deployed"
}

# Test compatible server
test_compatible_server() {
    log_step "Testing Compatible Server on Orange Pi"
    
    # Load SSH configuration
    if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
        source "$PROJECT_ROOT/.orangepi_ssh"
        SSH_CMD="ssh -i $ORANGE_PI_SSH_KEY -p $ORANGE_PI_PORT"
    else
        SSH_CMD="ssh -p 22"
        ORANGE_PI_IP="***************"
        ORANGE_PI_USER="orangepi"
    fi
    
    # Test version
    log_info "Testing server version..."
    if $SSH_CMD $ORANGE_PI_USER@$ORANGE_PI_IP "cd /home/<USER>/c-aibox && ./server_compatible --version"; then
        log_success "Compatible server version test passed!"
    else
        log_error "Compatible server version test failed"
        return 1
    fi
    
    # Test help
    log_info "Testing server help..."
    $SSH_CMD $ORANGE_PI_USER@$ORANGE_PI_IP "cd /home/<USER>/c-aibox && ./server_compatible --help"
    
    # Test dependencies
    log_info "Checking server dependencies..."
    $SSH_CMD $ORANGE_PI_USER@$ORANGE_PI_IP "cd /home/<USER>/c-aibox && ldd ./server_compatible"
    
    log_success "Compatible server tests completed!"
}

# Start server test
start_server_test() {
    log_step "Starting Server Test"
    
    # Load SSH configuration
    if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
        source "$PROJECT_ROOT/.orangepi_ssh"
        SSH_CMD="ssh -i $ORANGE_PI_SSH_KEY -p $ORANGE_PI_PORT"
    else
        SSH_CMD="ssh -p 22"
        ORANGE_PI_IP="***************"
        ORANGE_PI_USER="orangepi"
    fi
    
    log_info "Starting server in background..."
    $SSH_CMD $ORANGE_PI_USER@$ORANGE_PI_IP "cd /home/<USER>/c-aibox && nohup ./server_compatible --port 8080 > server.log 2>&1 &"
    
    # Wait a moment for server to start
    sleep 2
    
    # Test HTTP request
    log_info "Testing HTTP request..."
    if $SSH_CMD $ORANGE_PI_USER@$ORANGE_PI_IP "curl -s http://localhost:8080 | grep 'c-aibox server running'"; then
        log_success "HTTP server test passed!"
    else
        log_warning "HTTP server test failed, checking logs..."
        $SSH_CMD $ORANGE_PI_USER@$ORANGE_PI_IP "cd /home/<USER>/c-aibox && cat server.log"
    fi
    
    # Stop server
    log_info "Stopping server..."
    $SSH_CMD $ORANGE_PI_USER@$ORANGE_PI_IP "pkill -f server_compatible || true"
    
    log_success "Server test completed!"
}

# Main execution
main() {
    log_step "Orange Pi Compatible Build and Test"
    
    # Ensure build directory exists
    mkdir -p "$PROJECT_ROOT/build-orangepi"
    
    build_compatible_server
    deploy_compatible_server
    test_compatible_server
    start_server_test
    
    log_success "Orange Pi compatible server is working!"
    log_info "You can now run: ssh orangepi@*************** 'cd /home/<USER>/c-aibox && ./server_compatible'"
}

# Run main function
main "$@"
