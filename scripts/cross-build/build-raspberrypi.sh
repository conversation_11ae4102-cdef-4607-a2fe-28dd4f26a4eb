#!/bin/bash

# Raspberry Pi Cross-Compilation Build Script
# Cross-compiles the project for Raspberry Pi 3/4/5 ARM architecture

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
BUILD_TYPE="Release"
BUILD_DIR="build-raspberrypi"
CLEAN_BUILD=false
VERBOSE=false
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo "4")
RPI_MODEL="4"
RPI_ARCH="arm64"
PACKAGE_BUILD=false
SYSROOT=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --release)
            BUILD_TYPE="Release"
            shift
            ;;
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        --model)
            RPI_MODEL="$2"
            shift 2
            ;;
        --arch)
            RPI_ARCH="$2"
            shift 2
            ;;
        --sysroot)
            SYSROOT="$2"
            shift 2
            ;;
        --package)
            PACKAGE_BUILD=true
            shift
            ;;
        --help)
            cat << EOF
Raspberry Pi Cross-Compilation Build Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --release       Build in Release mode (default)
    --debug         Build in Debug mode
    --clean         Clean build directory before building
    --verbose       Enable verbose build output
    --jobs N        Number of parallel jobs (default: auto-detect)
    --model N       Raspberry Pi model: 3, 4, 5 (default: 4)
    --arch ARCH     Target architecture: arm64, armhf (default: arm64)
    --sysroot PATH  Path to Raspberry Pi sysroot for dependencies
    --package       Create deployment package after build
    --help          Show this help

EXAMPLES:
    $0                              # Release build for Pi 4 ARM64
    $0 --debug --model 3            # Debug build for Pi 3
    $0 --arch armhf --model 3       # 32-bit build for Pi 3
    $0 --clean --package            # Clean build with packaging

ENVIRONMENT VARIABLES:
    RPI_SYSROOT         Path to Raspberry Pi sysroot
    CROSS_COMPILE       Cross-compiler prefix
    CMAKE_ARGS          Additional CMake arguments
EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Detect host platform
detect_host_platform() {
    local os=$(uname -s)
    local arch=$(uname -m)
    
    case "$os" in
        Linux)
            HOST_PLATFORM="linux"
            ;;
        Darwin)
            HOST_PLATFORM="macos"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            HOST_PLATFORM="windows"
            ;;
        *)
            log_error "Unsupported host platform: $os"
            exit 1
            ;;
    esac
    
    log_info "Host platform: $HOST_PLATFORM $arch"
}

# Check cross-compilation requirements
check_cross_compile_requirements() {
    log_step "Checking Cross-Compilation Requirements"
    
    # Check CMake
    if ! command -v cmake >/dev/null 2>&1; then
        log_error "CMake is not installed"
        exit 1
    fi
    
    # Determine cross-compiler based on architecture
    case "$RPI_ARCH" in
        arm64|aarch64)
            CROSS_COMPILER="aarch64-linux-gnu-gcc"
            TOOLCHAIN_FILE="aarch64-linux-gnu.cmake"
            ;;
        armhf|arm)
            CROSS_COMPILER="arm-linux-gnueabihf-gcc"
            TOOLCHAIN_FILE="arm-linux-gnueabihf.cmake"
            ;;
        *)
            log_error "Unsupported architecture: $RPI_ARCH"
            exit 1
            ;;
    esac
    
    # Check cross-compiler
    if [[ -n "${CROSS_COMPILE:-}" ]]; then
        CROSS_COMPILER="${CROSS_COMPILE}gcc"
    fi
    
    if ! command -v "$CROSS_COMPILER" >/dev/null 2>&1; then
        log_error "Cross-compiler not found: $CROSS_COMPILER"
        log_info "Install with:"
        case "$HOST_PLATFORM" in
            linux)
                if [[ "$RPI_ARCH" == "arm64" ]]; then
                    log_info "  sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu"
                else
                    log_info "  sudo apt-get install gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf"
                fi
                ;;
            macos)
                log_info "  brew install arm-none-eabi-gcc"
                ;;
            windows)
                log_info "  Install in WSL2 or use MSYS2"
                ;;
        esac
        exit 1
    fi
    
    log_success "Cross-compiler found: $CROSS_COMPILER"
    
    # Set sysroot
    if [[ -n "$SYSROOT" ]]; then
        export RPI_SYSROOT="$SYSROOT"
    elif [[ -n "${RPI_SYSROOT:-}" ]]; then
        log_info "Using sysroot: $RPI_SYSROOT"
    else
        log_warning "No sysroot specified. Dependencies may not be found."
        log_info "Set RPI_SYSROOT environment variable or use --sysroot option"
    fi
}

# Configure CMake for cross-compilation
configure_cmake() {
    log_step "Configuring CMake for Raspberry Pi Cross-Compilation"
    
    cd "$PROJECT_ROOT"
    
    # Clean build directory if requested
    if [[ "$CLEAN_BUILD" == "true" && -d "$BUILD_DIR" ]]; then
        log_info "Cleaning build directory..."
        rm -rf "$BUILD_DIR"
    fi
    
    # Create build directory
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # Prepare CMake arguments
    local cmake_args=(
        "-DCMAKE_BUILD_TYPE=$BUILD_TYPE"
        "-DCMAKE_TOOLCHAIN_FILE=../cmake/toolchains/$TOOLCHAIN_FILE"
        "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
        "-DRASPBERRY_PI_TARGET=ON"
        "-DRASPBERRY_PI_MODEL=$RPI_MODEL"
        "-DTARGET_ARCH=$RPI_ARCH"
        "-DBUILD_TESTING=ON"
        "-DBUILD_EXAMPLES=ON"
    )
    
    # Add custom CMake arguments
    if [[ -n "${CMAKE_ARGS:-}" ]]; then
        cmake_args+=($CMAKE_ARGS)
    fi
    
    # Run CMake configuration
    log_info "Running CMake configuration..."
    log_info "Build type: $BUILD_TYPE"
    log_info "Target: Raspberry Pi $RPI_MODEL ($RPI_ARCH)"
    log_info "Jobs: $JOBS"
    
    if [[ "$VERBOSE" == "true" ]]; then
        cmake "${cmake_args[@]}" ..
    else
        cmake "${cmake_args[@]}" .. > cmake_config.log 2>&1 || {
            log_error "CMake configuration failed. Check cmake_config.log for details."
            tail -20 cmake_config.log
            exit 1
        }
    fi
    
    log_success "CMake configuration completed"
}

# Build the project
build_project() {
    log_step "Building Project for Raspberry Pi"
    
    cd "$PROJECT_ROOT/$BUILD_DIR"
    
    # Build command
    local build_args=("-j$JOBS")
    
    if [[ "$VERBOSE" == "true" ]]; then
        build_args+=("VERBOSE=1")
    fi
    
    log_info "Cross-compiling with $JOBS parallel jobs..."
    
    if [[ "$VERBOSE" == "true" ]]; then
        make "${build_args[@]}"
    else
        make "${build_args[@]}" > build.log 2>&1 || {
            log_error "Build failed. Check build.log for details."
            tail -20 build.log
            exit 1
        }
    fi
    
    log_success "Cross-compilation completed successfully"
}

# Create deployment package
create_package() {
    if [[ "$PACKAGE_BUILD" != "true" ]]; then
        return
    fi
    
    log_step "Creating Deployment Package"
    
    cd "$PROJECT_ROOT/$BUILD_DIR"
    
    local package_dir="raspberrypi-deployment"
    local package_name="c-aibox-raspberrypi-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    # Create package directory
    mkdir -p "$package_dir"
    
    # Copy executables
    log_info "Packaging executables..."
    find . -type f -executable -name "*" | grep -E "(client_app|server)" | while read -r exe; do
        cp "$exe" "$package_dir/"
    done
    
    # Copy libraries
    log_info "Packaging libraries..."
    find . -name "*.so" -o -name "*.a" | while read -r lib; do
        cp "$lib" "$package_dir/"
    done
    
    # Create package info
    cat > "$package_dir/package-info.txt" << EOF
Raspberry Pi Deployment Package
===============================
Build Date: $(date)
Build Type: $BUILD_TYPE
Model: Raspberry Pi $RPI_MODEL
Architecture: $RPI_ARCH
Host Platform: $HOST_PLATFORM
Cross-Compiler: $($CROSS_COMPILER --version | head -n1)

Installation:
1. Transfer this package to Raspberry Pi
2. Extract: tar -xzf $package_name
3. Run: ./install-on-raspberrypi.sh

Contents:
$(ls -la)
EOF
    
    # Create tarball
    log_info "Creating package archive..."
    tar -czf "$package_name" "$package_dir"
    
    log_success "Deployment package created: $package_name"
    log_info "Package size: $(du -sh "$package_name" | cut -f1)"
}

# Show build summary
show_summary() {
    log_step "Cross-Compilation Summary"
    
    cd "$PROJECT_ROOT/$BUILD_DIR"
    
    echo "Host Platform: $HOST_PLATFORM"
    echo "Target Platform: Raspberry Pi $RPI_MODEL ($RPI_ARCH)"
    echo "Build Type: $BUILD_TYPE"
    echo "Build Directory: $BUILD_DIR"
    echo "Jobs: $JOBS"
    echo ""
    
    # Show built executables
    log_info "Cross-compiled executables:"
    find . -type f -executable -name "*" | grep -E "(client_app|server|test)" | head -10 || echo "  No executables found"
    
    # Show libraries
    log_info "Cross-compiled libraries:"
    find . -name "*.a" -o -name "*.so" | head -10 || echo "  No libraries found"
    
    # Show build size
    local build_size=$(du -sh . 2>/dev/null | cut -f1 || echo "unknown")
    log_info "Build directory size: $build_size"
    
    # Check binary architecture
    local sample_binary=$(find . -type f -executable -name "*" | grep -E "(client_app|server)" | head -n1)
    if [[ -n "$sample_binary" ]]; then
        log_info "Binary architecture check:"
        if [[ "$RPI_ARCH" == "arm64" ]]; then
            file "$sample_binary" | grep -o "ARM aarch64" && log_success "✓ ARM64 architecture confirmed" || log_warning "⚠ Architecture verification failed"
        else
            file "$sample_binary" | grep -o "ARM" && log_success "✓ ARM architecture confirmed" || log_warning "⚠ Architecture verification failed"
        fi
    fi
    
    echo ""
    log_success "Raspberry Pi cross-compilation completed successfully!"
    log_info "Next steps:"
    log_info "  1. Transfer build to Raspberry Pi: scp -r $BUILD_DIR pi@<IP>:~/c-aibox"
    log_info "  2. Test on Raspberry Pi: ssh pi@<IP> 'cd ~/c-aibox && ./server --version'"
    
    if [[ "$PACKAGE_BUILD" == "true" ]]; then
        log_info "  3. Or use deployment package: $(ls *.tar.gz 2>/dev/null | head -n1)"
    fi
}

# Main execution
main() {
    log_step "Raspberry Pi Cross-Compilation Build"
    
    detect_host_platform
    check_cross_compile_requirements
    configure_cmake
    build_project
    create_package
    show_summary
}

# Run main function
main "$@"
