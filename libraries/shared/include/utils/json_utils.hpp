#pragma once
#include <string>
#include <vector>
#include <map>
#include <optional>
#include <variant>
#include <memory>

namespace utils {
    /**
     * @brief JSON value types
     */
    enum class JsonType {
        Null,
        Boolean,
        Number,
        String,
        Array,
        Object
    };

    /**
     * @brief Forward declaration
     */
    class JsonValue;

    /**
     * @brief JSON array type
     */
    using JsonArray = std::vector<JsonValue>;

    /**
     * @brief JSON object type
     */
    using JsonObject = std::map<std::string, JsonValue>;

    /**
     * @brief JSON value class that can hold any JSON type
     */
    class JsonValue {
    public:
        using ValueType = std::variant<
            std::nullptr_t,
            bool,
            double,
            std::string,
            JsonArray,
            JsonObject
        >;

        /**
         * @brief Default constructor (null value)
         */
        JsonValue();

        /**
         * @brief Constructor for boolean
         */
        JsonValue(bool value);

        /**
         * @brief Constructor for numeric types
         */
        JsonValue(int value);
        JsonValue(long value);
        JsonValue(long long value);
        JsonValue(float value);
        JsonValue(double value);

        /**
         * @brief Constructor for string
         */
        JsonValue(const std::string& value);
        JsonValue(const char* value);

        /**
         * @brief Constructor for array
         */
        JsonValue(const JsonArray& value);
        JsonValue(JsonArray&& value);

        /**
         * @brief Constructor for object
         */
        JsonValue(const JsonObject& value);
        JsonValue(JsonObject&& value);

        /**
         * @brief Get the type of this JSON value
         */
        JsonType getType() const;

        /**
         * @brief Check if value is null
         */
        bool isNull() const;

        /**
         * @brief Check if value is boolean
         */
        bool isBool() const;

        /**
         * @brief Check if value is number
         */
        bool isNumber() const;

        /**
         * @brief Check if value is string
         */
        bool isString() const;

        /**
         * @brief Check if value is array
         */
        bool isArray() const;

        /**
         * @brief Check if value is object
         */
        bool isObject() const;

        /**
         * @brief Get as boolean (throws if not boolean)
         */
        bool asBool() const;

        /**
         * @brief Get as number (throws if not number)
         */
        double asNumber() const;

        /**
         * @brief Get as integer (throws if not number)
         */
        int asInt() const;

        /**
         * @brief Get as string (throws if not string)
         */
        const std::string& asString() const;

        /**
         * @brief Get as array (throws if not array)
         */
        const JsonArray& asArray() const;
        JsonArray& asArray();

        /**
         * @brief Get as object (throws if not object)
         */
        const JsonObject& asObject() const;
        JsonObject& asObject();

        /**
         * @brief Array access operator
         */
        JsonValue& operator[](size_t index);
        const JsonValue& operator[](size_t index) const;

        /**
         * @brief Object access operator
         */
        JsonValue& operator[](const std::string& key);
        const JsonValue& operator[](const std::string& key) const;

        /**
         * @brief Get array size (returns 0 if not array)
         */
        size_t size() const;

        /**
         * @brief Check if object has key
         */
        bool hasKey(const std::string& key) const;

        /**
         * @brief Get object keys
         */
        std::vector<std::string> getKeys() const;

        /**
         * @brief Convert to string representation
         */
        std::string toString(bool pretty = false, int indent = 0) const;

    private:
        ValueType value_;
        
        std::string escapeString(const std::string& str) const;
        std::string getIndent(int level) const;
    };

    /**
     * @brief JSON utilities for parsing and generating JSON
     */
    class JsonUtils {
    public:
        /**
         * @brief Parse JSON string to JsonValue
         * @param jsonStr JSON string to parse
         * @return Optional JsonValue (nullopt if parsing fails)
         */
        static std::optional<JsonValue> parse(const std::string& jsonStr);

        /**
         * @brief Parse JSON from file
         * @param filename Path to JSON file
         * @return Optional JsonValue (nullopt if parsing fails)
         */
        static std::optional<JsonValue> parseFromFile(const std::string& filename);

        /**
         * @brief Convert JsonValue to string
         * @param value JsonValue to convert
         * @param pretty Whether to format with indentation
         * @return JSON string
         */
        static std::string stringify(const JsonValue& value, bool pretty = false);

        /**
         * @brief Write JsonValue to file
         * @param value JsonValue to write
         * @param filename Output file path
         * @param pretty Whether to format with indentation
         * @return true if successful
         */
        static bool writeToFile(const JsonValue& value, const std::string& filename, bool pretty = true);

        /**
         * @brief Validate JSON string
         * @param jsonStr JSON string to validate
         * @return true if valid JSON
         */
        static bool isValid(const std::string& jsonStr);

        /**
         * @brief Merge two JSON objects
         * @param base Base JSON object
         * @param overlay Overlay JSON object
         * @return Merged JSON object
         */
        static JsonValue merge(const JsonValue& base, const JsonValue& overlay);

        /**
         * @brief Get value at JSON path (e.g., "user.name" or "items[0].id")
         * @param root Root JSON value
         * @param path JSON path string
         * @return Optional JsonValue at path
         */
        static std::optional<JsonValue> getValueAtPath(const JsonValue& root, const std::string& path);

        /**
         * @brief Set value at JSON path
         * @param root Root JSON value (modified in place)
         * @param path JSON path string
         * @param value Value to set
         * @return true if successful
         */
        static bool setValueAtPath(JsonValue& root, const std::string& path, const JsonValue& value);

        /**
         * @brief Compare two JSON values for equality
         * @param a First JSON value
         * @param b Second JSON value
         * @return true if equal
         */
        static bool equals(const JsonValue& a, const JsonValue& b);

        /**
         * @brief Get JSON schema information
         * @param value JSON value to analyze
         * @return Schema description as JSON object
         */
        static JsonValue getSchema(const JsonValue& value);

        /**
         * @brief Flatten nested JSON object to dot notation
         * @param value JSON object to flatten
         * @return Flattened JSON object
         */
        static JsonValue flatten(const JsonValue& value);

        /**
         * @brief Unflatten dot notation JSON object to nested structure
         * @param value Flattened JSON object
         * @return Nested JSON object
         */
        static JsonValue unflatten(const JsonValue& value);

    private:
        /**
         * @brief Parse JSON value from string starting at position
         * @param str JSON string
         * @param pos Current position (updated)
         * @return Optional JsonValue
         */
        static std::optional<JsonValue> parseValue(const std::string& str, size_t& pos);

        /**
         * @brief Parse JSON object from string
         * @param str JSON string
         * @param pos Current position (updated)
         * @return Optional JsonObject
         */
        static std::optional<JsonObject> parseObject(const std::string& str, size_t& pos);

        /**
         * @brief Parse JSON array from string
         * @param str JSON string
         * @param pos Current position (updated)
         * @return Optional JsonArray
         */
        static std::optional<JsonArray> parseArray(const std::string& str, size_t& pos);

        /**
         * @brief Parse JSON string from string
         * @param str JSON string
         * @param pos Current position (updated)
         * @return Optional string
         */
        static std::optional<std::string> parseString(const std::string& str, size_t& pos);

        /**
         * @brief Parse JSON number from string
         * @param str JSON string
         * @param pos Current position (updated)
         * @return Optional double
         */
        static std::optional<double> parseNumber(const std::string& str, size_t& pos);

        /**
         * @brief Skip whitespace in string
         * @param str JSON string
         * @param pos Current position (updated)
         */
        static void skipWhitespace(const std::string& str, size_t& pos);

        /**
         * @brief Helper for flattening with prefix
         * @param value JSON value to flatten
         * @param prefix Current prefix
         * @param result Result object
         */
        static void flattenHelper(const JsonValue& value, const std::string& prefix, JsonObject& result);
    };

    // Convenience functions for ease of use
    
    /**
     * @brief Parse JSON string (convenience function)
     * @param jsonStr JSON string to parse
     * @return Optional JsonValue
     */
    std::optional<JsonValue> parseJson(const std::string& jsonStr);

    /**
     * @brief Convert to JSON string (convenience function)
     * @param value JsonValue to convert
     * @param pretty Whether to format with indentation
     * @return JSON string
     */
    std::string toJson(const JsonValue& value, bool pretty = false);

    /**
     * @brief Create JSON object (convenience function)
     * @return Empty JSON object
     */
    JsonValue createObject();

    /**
     * @brief Create JSON array (convenience function)
     * @return Empty JSON array
     */
    JsonValue createArray();
}
