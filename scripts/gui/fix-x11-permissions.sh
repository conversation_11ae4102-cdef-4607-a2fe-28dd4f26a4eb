#!/bin/bash

# <PERSON>ript to fix X11 permissions and connection issues in container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "Fixing X11 permissions and connection issues..."

# Check current user and environment
print_info "Current environment:"
print_info "  User: $(whoami)"
print_info "  UID: $(id -u)"
print_info "  GID: $(id -g)"
print_info "  DISPLAY: ${DISPLAY:-not set}"

# Check X11 socket directory
if [[ -d "/tmp/.X11-unix" ]]; then
    print_success "X11 socket directory exists"
    print_info "X11 sockets:"
    ls -la /tmp/.X11-unix/ | grep -E "^[^d]" || print_warning "No X11 sockets found"
else
    print_error "X11 socket directory not found"
    print_info "Creating X11 socket directory..."
    mkdir -p /tmp/.X11-unix
    chmod 1777 /tmp/.X11-unix
fi

# Fix X11 socket permissions
print_info "Fixing X11 socket permissions..."
if [[ -d "/tmp/.X11-unix" ]]; then
    # Make sure the directory is accessible
    chmod 1777 /tmp/.X11-unix
    
    # Fix socket permissions if they exist
    for socket in /tmp/.X11-unix/X*; do
        if [[ -S "$socket" ]]; then
            chmod 777 "$socket" 2>/dev/null || true
            print_info "Fixed permissions for $socket"
        fi
    done
    
    print_success "X11 socket permissions fixed"
else
    print_error "Could not access X11 socket directory"
fi

# Set up DISPLAY environment variable
if [[ -z "$DISPLAY" ]]; then
    print_warning "DISPLAY not set, trying common values..."
    
    # Try different DISPLAY values
    for display in ":0" ":1" "localhost:0" "host.docker.internal:0"; do
        export DISPLAY="$display"
        print_info "Testing DISPLAY=$display"
        
        if command -v xdpyinfo >/dev/null 2>&1; then
            if timeout 3s xdpyinfo >/dev/null 2>&1; then
                print_success "X11 connection successful with DISPLAY=$display"
                echo "export DISPLAY=$display" > /tmp/working_display.sh
                break
            fi
        fi
    done
else
    print_info "DISPLAY is set to: $DISPLAY"
fi

# Create or fix .Xauthority file
print_info "Setting up X11 authorization..."
XAUTH_FILE="${HOME}/.Xauthority"

if [[ ! -f "$XAUTH_FILE" ]]; then
    print_info "Creating .Xauthority file..."
    touch "$XAUTH_FILE"
    chmod 600 "$XAUTH_FILE"
fi

# Try to copy Xauthority from host if available
if [[ -f "/root/.Xauthority" && "$XAUTH_FILE" != "/root/.Xauthority" ]]; then
    cp /root/.Xauthority "$XAUTH_FILE" 2>/dev/null || true
fi

# Set XAUTHORITY environment variable
export XAUTHORITY="$XAUTH_FILE"

# Install X11 utilities if not present
print_info "Checking X11 utilities..."
X11_UTILS=("xdpyinfo" "xset" "xhost" "xclock")
MISSING_UTILS=()

for util in "${X11_UTILS[@]}"; do
    if ! command -v "$util" >/dev/null 2>&1; then
        MISSING_UTILS+=("$util")
    fi
done

if [[ ${#MISSING_UTILS[@]} -gt 0 ]]; then
    print_info "Installing missing X11 utilities..."
    apt-get update && apt-get install -y x11-utils x11-xserver-utils
fi

# Test X11 connection
print_info "Testing X11 connection..."
X11_WORKING=false

if command -v xdpyinfo >/dev/null 2>&1; then
    if timeout 5s xdpyinfo >/dev/null 2>&1; then
        print_success "X11 connection test successful"
        X11_WORKING=true
        
        # Get display info
        DISPLAY_INFO=$(xdpyinfo | head -5)
        print_info "Display information:"
        echo "$DISPLAY_INFO" | while read line; do
            print_info "  $line"
        done
    else
        print_warning "xdpyinfo failed"
    fi
fi

# Alternative test with xset
if [[ "$X11_WORKING" == false ]] && command -v xset >/dev/null 2>&1; then
    if timeout 5s xset q >/dev/null 2>&1; then
        print_success "X11 connection test successful (via xset)"
        X11_WORKING=true
    else
        print_warning "xset test failed"
    fi
fi

# Create comprehensive environment setup
print_info "Creating X11 environment configuration..."

cat > /tmp/x11_fixed_env.sh << EOF
#!/bin/bash
# Fixed X11 environment for container

# X11 Display
export DISPLAY=${DISPLAY:-:0}
export XAUTHORITY=${XAUTHORITY:-$HOME/.Xauthority}

# Qt X11 settings
export QT_X11_NO_MITSHM=1
export QT_QPA_PLATFORM=xcb

# OpenGL settings for container
export LIBGL_ALWAYS_INDIRECT=1
export LIBGL_ALWAYS_SOFTWARE=1
export QT_OPENGL=software

# Mesa configuration
export MESA_GL_VERSION_OVERRIDE=3.3
export MESA_GLSL_VERSION_OVERRIDE=330
export GALLIUM_DRIVER=llvmpipe

# WebEngine settings
export QTWEBENGINE_DISABLE_SANDBOX=1
export QTWEBENGINE_CHROMIUM_FLAGS="--no-sandbox --disable-dev-shm-usage --disable-gpu"

# Suppress warnings
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false;qt.qpa.gl.warning=false"

echo "X11 environment configured:"
echo "DISPLAY: \$DISPLAY"
echo "XAUTHORITY: \$XAUTHORITY"
echo "QT_QPA_PLATFORM: \$QT_QPA_PLATFORM"
EOF

chmod +x /tmp/x11_fixed_env.sh
print_success "X11 environment configuration created: /tmp/x11_fixed_env.sh"

# Create test script
cat > /tmp/test_x11_fixed.sh << 'EOF'
#!/bin/bash
echo "Testing fixed X11 environment..."

# Load environment
source /tmp/x11_fixed_env.sh

echo "Environment variables:"
echo "DISPLAY: $DISPLAY"
echo "XAUTHORITY: $XAUTHORITY"
echo "QT_QPA_PLATFORM: $QT_QPA_PLATFORM"

echo ""
echo "Testing X11 connection..."
if command -v xdpyinfo >/dev/null 2>&1; then
    if timeout 3s xdpyinfo >/dev/null 2>&1; then
        echo "✅ X11 connection successful"
        echo "Display info:"
        xdpyinfo | grep -E "(name of display|version number|vendor string)" | head -3
    else
        echo "❌ X11 connection failed"
    fi
else
    echo "❌ xdpyinfo not available"
fi

echo ""
echo "Testing with simple GUI app..."
if command -v xclock >/dev/null 2>&1; then
    echo "You can test GUI with: timeout 5s xclock"
else
    echo "xclock not available for testing"
fi
EOF

chmod +x /tmp/test_x11_fixed.sh
print_success "X11 test script created: /tmp/test_x11_fixed.sh"

# Final status
print_info ""
if [[ "$X11_WORKING" == true ]]; then
    print_success "X11 connection is working!"
    print_info "You can now run GUI applications"
else
    print_warning "X11 connection issues detected"
    print_info "This may be due to:"
    print_info "1. No X11 server running on host"
    print_info "2. X11 forwarding not properly configured"
    print_info "3. Container networking issues"
    print_info ""
    print_info "Solutions:"
    print_info "1. On Linux host: Make sure X11 is running and run 'xhost +local:docker'"
    print_info "2. On macOS: Install and start XQuartz"
    print_info "3. On Windows: Install and start VcXsrv or Xming"
fi

print_info ""
print_info "Next steps:"
print_info "1. Load environment: source /tmp/x11_fixed_env.sh"
print_info "2. Test X11: /tmp/test_x11_fixed.sh"
print_info "3. Run GUI app: ./scripts/run_gui_app.sh --skip-opengl-setup"
print_info ""
print_info "If X11 still doesn't work, you can run in headless mode:"
print_info "  ./scripts/run_client.sh --headless"
