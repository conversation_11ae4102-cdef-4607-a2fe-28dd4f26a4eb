#pragma once

#include <string>
#include <memory>
#include <future>
#include <functional>
#include <chrono>
#include "models/inference_request.hpp"
#include "models/inference_response.hpp"

namespace server::repositories {

/**
 * @brief Interface for AI model repository
 * Provides abstraction layer for AI model operations
 */
class IAIModelRepository {
public:
    virtual ~IAIModelRepository() = default;

    // Synchronous inference methods
    virtual models::ArcFaceResponse performArcFaceInference(const models::ArcFaceRequest& request) = 0;
    virtual models::YoloResponse performYoloInference(const models::YoloRequest& request) = 0;

    // Asynchronous inference methods
    virtual std::future<models::ArcFaceResponse> performArcFaceInferenceAsync(const models::ArcFaceRequest& request) = 0;
    virtual std::future<models::YoloResponse> performYoloInferenceAsync(const models::YoloRequest& request) = 0;

    // Model management
    virtual bool isModelAvailable(const std::string& model_name) const = 0;
    virtual std::string getModelVersion(const std::string& model_name) const = 0;
    virtual bool validateImageFile(const std::string& image_path) const = 0;
};

/**
 * @brief Implementation of AI model repository
 * Integrates with the existing models library (arcface, yolo)
 */
class AIModelRepository : public IAIModelRepository {
public:
    explicit AIModelRepository(const std::string& models_base_path = "./models");
    ~AIModelRepository() override = default;

    // Synchronous inference methods
    models::ArcFaceResponse performArcFaceInference(const models::ArcFaceRequest& request) override;
    models::YoloResponse performYoloInference(const models::YoloRequest& request) override;

    // Asynchronous inference methods
    std::future<models::ArcFaceResponse> performArcFaceInferenceAsync(const models::ArcFaceRequest& request) override;
    std::future<models::YoloResponse> performYoloInferenceAsync(const models::YoloRequest& request) override;

    // Model management
    bool isModelAvailable(const std::string& model_name) const override;
    std::string getModelVersion(const std::string& model_name) const override;
    bool validateImageFile(const std::string& image_path) const override;

private:
    std::string models_base_path_;

    // Helper methods
    models::ArcFaceResponse createMockArcFaceResponse(const models::ArcFaceRequest& request);
    models::YoloResponse createMockYoloResponse(const models::YoloRequest& request);
    std::chrono::milliseconds measureInferenceTime(const std::function<void()>& inference_func);
};

} // namespace server::repositories
