#include <gtest/gtest.h>
#include <utils/datetime_utils.hpp>
#include <thread>
#include <chrono>

class DateTimeUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up test fixtures if needed
    }

    void TearDown() override {
        // Clean up after tests
    }
};

TEST_F(DateTimeUtilsTest, CurrentTimeOperations) {
    auto now1 = utils::DateTimeUtils::now();
    auto now2 = utils::now(); // convenience function
    
    // Should be very close in time
    auto diff = utils::DateTimeUtils::diffMs(now2, now1);
    EXPECT_LT(std::abs(diff), 100); // Less than 100ms difference
    
    // Unix timestamp tests
    auto unixNow = utils::DateTimeUtils::nowUnix();
    auto unixNow2 = utils::nowUnix(); // convenience function
    EXPECT_LE(std::abs(unixNow2 - unixNow), 1); // Less than 1 second difference
    
    auto unixMsNow = utils::DateTimeUtils::nowUnixMs();
    EXPECT_GT(unixMsNow, unixNow * 1000); // Should be larger (includes milliseconds)
}

TEST_F(DateTimeUtilsTest, UnixTimestampConversion) {
    // Test known timestamp: 2023-01-01 00:00:00 UTC
    int64_t knownTimestamp = 1672531200; // 2023-01-01 00:00:00 UTC
    
    auto tp = utils::DateTimeUtils::fromUnix(knownTimestamp);
    auto convertedBack = utils::DateTimeUtils::toUnix(tp);
    
    EXPECT_EQ(convertedBack, knownTimestamp);
    
    // Test milliseconds
    int64_t knownTimestampMs = knownTimestamp * 1000 + 500; // Add 500ms
    auto tpMs = utils::DateTimeUtils::fromUnixMs(knownTimestampMs);
    auto convertedBackMs = utils::DateTimeUtils::toUnixMs(tpMs);
    
    EXPECT_EQ(convertedBackMs, knownTimestampMs);
}

TEST_F(DateTimeUtilsTest, FormatAndParse) {
    auto now = utils::DateTimeUtils::now();
    
    // Test default format
    std::string formatted = utils::DateTimeUtils::format(now);
    EXPECT_FALSE(formatted.empty());
    
    // Test custom format
    std::string customFormat = "%Y-%m-%d";
    std::string dateOnly = utils::DateTimeUtils::format(now, customFormat);
    EXPECT_EQ(dateOnly.length(), 10); // YYYY-MM-DD
    
    // Test formatNow convenience function
    std::string nowFormatted = utils::formatNow("%H:%M:%S");
    EXPECT_EQ(nowFormatted.length(), 8); // HH:MM:SS
    
    // Test parsing
    std::string testTime = "2023-12-25 10:30:45";
    auto parsed = utils::DateTimeUtils::parse(testTime);
    EXPECT_TRUE(parsed.has_value());
    
    if (parsed.has_value()) {
        std::string reformatted = utils::DateTimeUtils::format(parsed.value());
        EXPECT_EQ(reformatted, testTime);
    }
    
    // Test convenience parse function
    auto parsedConv = utils::parseTime(testTime);
    EXPECT_TRUE(parsedConv.has_value());
}

TEST_F(DateTimeUtilsTest, ISO8601Format) {
    auto now = utils::DateTimeUtils::now();
    
    // Test ISO 8601 formatting
    std::string iso = utils::DateTimeUtils::toISO8601(now);
    EXPECT_FALSE(iso.empty());
    EXPECT_NE(iso.find('T'), std::string::npos); // Should contain 'T'
    EXPECT_NE(iso.find('Z'), std::string::npos); // Should end with 'Z'
    
    // Test with milliseconds
    std::string isoMs = utils::DateTimeUtils::toISO8601(now, true);
    EXPECT_GT(isoMs.length(), iso.length()); // Should be longer with milliseconds
    
    // Test nowISO8601
    std::string nowIso = utils::DateTimeUtils::nowISO8601();
    EXPECT_FALSE(nowIso.empty());
    
    // Test parsing ISO 8601
    std::string testIso = "2023-12-25T10:30:45Z";
    auto parsedIso = utils::DateTimeUtils::parseISO8601(testIso);
    EXPECT_TRUE(parsedIso.has_value());
    
    if (parsedIso.has_value()) {
        std::string reformattedIso = utils::DateTimeUtils::toISO8601(parsedIso.value());
        EXPECT_EQ(reformattedIso, testIso);
    }
}

TEST_F(DateTimeUtilsTest, ArithmeticOperations) {
    auto baseTime = utils::DateTimeUtils::now();
    
    // Test addition
    auto oneHourLater = utils::DateTimeUtils::add(baseTime, std::chrono::hours(1));
    auto diffHours = utils::DateTimeUtils::diffSeconds(oneHourLater, baseTime);
    EXPECT_EQ(diffHours, 3600); // 1 hour = 3600 seconds
    
    // Test subtraction
    auto oneHourEarlier = utils::DateTimeUtils::subtract(baseTime, std::chrono::hours(1));
    auto diffHours2 = utils::DateTimeUtils::diffSeconds(baseTime, oneHourEarlier);
    EXPECT_EQ(diffHours2, 3600);
    
    // Test millisecond differences
    auto fiveSecondsLater = utils::DateTimeUtils::add(baseTime, std::chrono::seconds(5));
    auto diffMs = utils::DateTimeUtils::diffMs(fiveSecondsLater, baseTime);
    EXPECT_EQ(diffMs, 5000); // 5 seconds = 5000 milliseconds
}

TEST_F(DateTimeUtilsTest, ComparisonOperations) {
    auto now = utils::DateTimeUtils::now();
    auto later = utils::DateTimeUtils::add(now, std::chrono::minutes(1));
    auto earlier = utils::DateTimeUtils::subtract(now, std::chrono::minutes(1));
    
    EXPECT_TRUE(utils::DateTimeUtils::isBefore(earlier, now));
    EXPECT_TRUE(utils::DateTimeUtils::isBefore(now, later));
    EXPECT_FALSE(utils::DateTimeUtils::isBefore(later, now));
    
    EXPECT_TRUE(utils::DateTimeUtils::isAfter(later, now));
    EXPECT_TRUE(utils::DateTimeUtils::isAfter(now, earlier));
    EXPECT_FALSE(utils::DateTimeUtils::isAfter(earlier, now));
}

TEST_F(DateTimeUtilsTest, SleepOperations) {
    auto start = utils::DateTimeUtils::now();
    
    // Test sleep with duration
    utils::DateTimeUtils::sleep(std::chrono::milliseconds(100));
    auto afterSleep1 = utils::DateTimeUtils::now();
    auto elapsed1 = utils::DateTimeUtils::diffMs(afterSleep1, start);
    EXPECT_GE(elapsed1, 90); // At least 90ms (allowing for some variance)
    EXPECT_LE(elapsed1, 200); // But not more than 200ms
    
    // Test sleepMs
    auto start2 = utils::DateTimeUtils::now();
    utils::DateTimeUtils::sleepMs(50);
    auto afterSleep2 = utils::DateTimeUtils::now();
    auto elapsed2 = utils::DateTimeUtils::diffMs(afterSleep2, start2);
    EXPECT_GE(elapsed2, 40);
    EXPECT_LE(elapsed2, 100);
    
    // Test convenience sleepMs
    auto start3 = utils::DateTimeUtils::now();
    utils::sleepMs(30);
    auto afterSleep3 = utils::DateTimeUtils::now();
    auto elapsed3 = utils::DateTimeUtils::diffMs(afterSleep3, start3);
    EXPECT_GE(elapsed3, 20);
    EXPECT_LE(elapsed3, 80);
}

TEST_F(DateTimeUtilsTest, DayOperations) {
    // Create a specific time: 2023-12-25 15:30:45
    std::string testTimeStr = "2023-12-25 15:30:45";
    auto testTime = utils::DateTimeUtils::parse(testTimeStr);
    ASSERT_TRUE(testTime.has_value());
    
    auto tp = testTime.value();
    
    // Test start of day
    auto startDay = utils::DateTimeUtils::startOfDay(tp);
    std::string startDayStr = utils::DateTimeUtils::format(startDay);
    EXPECT_EQ(startDayStr, "2023-12-25 00:00:00");
    
    // Test end of day
    auto endDay = utils::DateTimeUtils::endOfDay(tp);
    std::string endDayStr = utils::DateTimeUtils::format(endDay, "%Y-%m-%d %H:%M:%S");
    EXPECT_EQ(endDayStr, "2023-12-25 23:59:59");
    
    // Test same day check
    auto sameDay = utils::DateTimeUtils::parse("2023-12-25 08:15:30");
    auto differentDay = utils::DateTimeUtils::parse("2023-12-26 15:30:45");
    
    ASSERT_TRUE(sameDay.has_value());
    ASSERT_TRUE(differentDay.has_value());
    
    EXPECT_TRUE(utils::DateTimeUtils::isSameDay(tp, sameDay.value()));
    EXPECT_FALSE(utils::DateTimeUtils::isSameDay(tp, differentDay.value()));
}

TEST_F(DateTimeUtilsTest, ElapsedTimeString) {
    auto now = utils::DateTimeUtils::now();
    
    // Test recent time (few seconds ago)
    auto fewSecondsAgo = utils::DateTimeUtils::subtract(now, std::chrono::seconds(5));
    std::string elapsed1 = utils::DateTimeUtils::getElapsedTime(fewSecondsAgo);
    EXPECT_NE(elapsed1.find("second"), std::string::npos);
    
    // Test minutes ago
    auto fewMinutesAgo = utils::DateTimeUtils::subtract(now, std::chrono::minutes(30));
    std::string elapsed2 = utils::DateTimeUtils::getElapsedTime(fewMinutesAgo);
    EXPECT_NE(elapsed2.find("minute"), std::string::npos);
    
    // Test hours ago
    auto fewHoursAgo = utils::DateTimeUtils::subtract(now, std::chrono::hours(3));
    std::string elapsed3 = utils::DateTimeUtils::getElapsedTime(fewHoursAgo);
    EXPECT_NE(elapsed3.find("hour"), std::string::npos);
    
    // Test days ago
    auto fewDaysAgo = utils::DateTimeUtils::subtract(now, std::chrono::hours(48));
    std::string elapsed4 = utils::DateTimeUtils::getElapsedTime(fewDaysAgo);
    EXPECT_NE(elapsed4.find("day"), std::string::npos);
}

TEST_F(DateTimeUtilsTest, ValidationAndErrorHandling) {
    // Test invalid format parsing
    auto invalidParse = utils::DateTimeUtils::parse("invalid-date");
    EXPECT_FALSE(invalidParse.has_value());
    
    // Test invalid ISO 8601 parsing
    auto invalidIso = utils::DateTimeUtils::parseISO8601("not-iso-format");
    EXPECT_FALSE(invalidIso.has_value());
    
    // Test format validation
    EXPECT_TRUE(utils::DateTimeUtils::isValidFormat("2023-12-25 10:30:45", "%Y-%m-%d %H:%M:%S"));
    EXPECT_FALSE(utils::DateTimeUtils::isValidFormat("invalid", "%Y-%m-%d %H:%M:%S"));
    EXPECT_FALSE(utils::DateTimeUtils::isValidFormat("2023-12-25", "%Y-%m-%d %H:%M:%S"));
}

TEST_F(DateTimeUtilsTest, EdgeCases) {
    // Test leap year
    auto leapYear = utils::DateTimeUtils::parse("2024-02-29 12:00:00");
    EXPECT_TRUE(leapYear.has_value());
    
    // Test end of year
    auto endOfYear = utils::DateTimeUtils::parse("2023-12-31 23:59:59");
    EXPECT_TRUE(endOfYear.has_value());
    
    // Test beginning of year
    auto beginOfYear = utils::DateTimeUtils::parse("2024-01-01 00:00:00");
    EXPECT_TRUE(beginOfYear.has_value());
    
    // Test zero timestamp
    auto epoch = utils::DateTimeUtils::fromUnix(0);
    auto epochUnix = utils::DateTimeUtils::toUnix(epoch);
    EXPECT_EQ(epochUnix, 0);
}
