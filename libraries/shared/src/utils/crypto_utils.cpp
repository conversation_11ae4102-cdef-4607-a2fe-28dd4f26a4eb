#include "utils/crypto_utils.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <random>
#include <algorithm>
#include <cstring>

#ifdef CRYPTO_USE_OPENSSL
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#include <openssl/md5.h>
#include <openssl/hmac.h>
#include <openssl/aes.h>
#include <openssl/kdf.h>
#include <openssl/crypto.h>
#include <openssl/opensslv.h>
#endif

namespace utils {

    // Define static member
    const std::string CryptoUtils::base64Chars =
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    // =============================================================================
    // Hash Functions
    // =============================================================================

    std::optional<std::string> CryptoUtils::sha256(const std::string& data) {
        return sha256(std::vector<uint8_t>(data.begin(), data.end()));
    }

    std::optional<std::string> CryptoUtils::sha256(const std::vector<uint8_t>& data) {
#ifdef CRYPTO_USE_OPENSSL
        EVP_MD_CTX* ctx = EVP_MD_CTX_new();
        if (!ctx) return std::nullopt;

        if (EVP_DigestInit_ex(ctx, EVP_sha256(), nullptr) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        if (EVP_DigestUpdate(ctx, data.data(), data.size()) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        unsigned char hash[EVP_MAX_MD_SIZE];
        unsigned int hashLen;
        if (EVP_DigestFinal_ex(ctx, hash, &hashLen) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        EVP_MD_CTX_free(ctx);
        return bytesToHex(std::vector<uint8_t>(hash, hash + hashLen));
#else
        // Fallback to std::hash (not cryptographically secure)
        std::hash<std::string> hasher;
        std::string dataStr(data.begin(), data.end());
        size_t hashValue = hasher(dataStr);

        std::ostringstream oss;
        oss << std::hex << hashValue;
        return oss.str();
#endif
    }

    std::optional<std::string> CryptoUtils::sha512(const std::string& data) {
#ifdef CRYPTO_USE_OPENSSL
        EVP_MD_CTX* ctx = EVP_MD_CTX_new();
        if (!ctx) return std::nullopt;

        if (EVP_DigestInit_ex(ctx, EVP_sha512(), nullptr) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        if (EVP_DigestUpdate(ctx, data.c_str(), data.length()) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        unsigned char hash[EVP_MAX_MD_SIZE];
        unsigned int hashLen;
        if (EVP_DigestFinal_ex(ctx, hash, &hashLen) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        EVP_MD_CTX_free(ctx);
        return bytesToHex(std::vector<uint8_t>(hash, hash + hashLen));
#else
        // Fallback implementation
        return sha256(data); // Use SHA256 as fallback
#endif
    }

    std::optional<std::string> CryptoUtils::md5(const std::string& data) {
#ifdef CRYPTO_USE_OPENSSL
        EVP_MD_CTX* ctx = EVP_MD_CTX_new();
        if (!ctx) return std::nullopt;

        if (EVP_DigestInit_ex(ctx, EVP_md5(), nullptr) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        if (EVP_DigestUpdate(ctx, data.c_str(), data.length()) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        unsigned char hash[EVP_MAX_MD_SIZE];
        unsigned int hashLen;
        if (EVP_DigestFinal_ex(ctx, hash, &hashLen) != 1) {
            EVP_MD_CTX_free(ctx);
            return std::nullopt;
        }

        EVP_MD_CTX_free(ctx);
        return bytesToHex(std::vector<uint8_t>(hash, hash + hashLen));
#else
        // MD5 not available without OpenSSL - suppress unused parameter warning
        (void)data;
        return std::nullopt;
#endif
    }

    // =============================================================================
    // HMAC Functions
    // =============================================================================

    std::optional<std::string> CryptoUtils::hmacSha256(const std::string& key, const std::string& data) {
#ifdef CRYPTO_USE_OPENSSL
        unsigned char hash[EVP_MAX_MD_SIZE];
        unsigned int hashLen;

        HMAC(EVP_sha256(), key.c_str(), static_cast<int>(key.length()),
             reinterpret_cast<const unsigned char*>(data.c_str()), data.length(),
             hash, &hashLen);

        return bytesToHex(std::vector<uint8_t>(hash, hash + hashLen));
#else
        // Simple HMAC implementation using XOR
        std::string keyPadded = key;
        if (keyPadded.length() > 64) {
            auto hashOpt = sha256(keyPadded);
            if (!hashOpt) return std::nullopt;
            keyPadded = *hashOpt;
        }
        keyPadded.resize(64, 0);

        std::string oKeyPad(64, 0x5c);
        std::string iKeyPad(64, 0x36);

        for (size_t i = 0; i < 64; ++i) {
            oKeyPad[i] ^= keyPadded[i];
            iKeyPad[i] ^= keyPadded[i];
        }

        auto innerHashOpt = sha256(iKeyPad + data);
        if (!innerHashOpt) return std::nullopt;

        return sha256(oKeyPad + *innerHashOpt);
#endif
    }

    std::optional<std::string> CryptoUtils::hmacSha512(const std::string& key, const std::string& data) {
#ifdef CRYPTO_USE_OPENSSL
        unsigned char hash[EVP_MAX_MD_SIZE];
        unsigned int hashLen;

        HMAC(EVP_sha512(), key.c_str(), static_cast<int>(key.length()),
             reinterpret_cast<const unsigned char*>(data.c_str()), data.length(),
             hash, &hashLen);

        return bytesToHex(std::vector<uint8_t>(hash, hash + hashLen));
#else
        // Fallback to HMAC-SHA256
        return hmacSha256(key, data);
#endif
    }

    // =============================================================================
    // Random Generation
    // =============================================================================

    std::optional<std::vector<uint8_t>> CryptoUtils::randomBytes(size_t length) {
#ifdef CRYPTO_USE_OPENSSL
        std::vector<uint8_t> buffer(length);
        if (RAND_bytes(buffer.data(), static_cast<int>(length)) != 1) {
            return std::nullopt;
        }
        return buffer;
#else
        // Fallback to std::random_device
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<uint8_t> dis(0, 255);

        std::vector<uint8_t> buffer(length);
        for (size_t i = 0; i < length; ++i) {
            buffer[i] = dis(gen);
        }
        return buffer;
#endif
    }

    std::optional<std::string> CryptoUtils::randomString(size_t length, const std::string& charset) {
        auto bytesOpt = randomBytes(length);
        if (!bytesOpt) return std::nullopt;

        std::string result;
        result.reserve(length);

        for (uint8_t byte : *bytesOpt) {
            result += charset[byte % charset.length()];
        }

        return result;
    }

    std::optional<std::string> CryptoUtils::randomHex(size_t length) {
        if (length % 2 != 0) {
            return std::nullopt; // Hex string length must be even
        }

        auto bytesOpt = randomBytes(length / 2);
        if (!bytesOpt) return std::nullopt;

        return bytesToHex(*bytesOpt);
    }

    std::optional<std::string> CryptoUtils::generateUuid() {
        auto bytesOpt = randomBytes(16);
        if (!bytesOpt) return std::nullopt;

        auto& bytes = *bytesOpt;

        // Set version (4) and variant bits
        bytes[6] = (bytes[6] & 0x0f) | 0x40; // Version 4
        bytes[8] = (bytes[8] & 0x3f) | 0x80; // Variant 10

        std::ostringstream oss;
        oss << std::hex << std::setfill('0');

        for (size_t i = 0; i < 16; ++i) {
            if (i == 4 || i == 6 || i == 8 || i == 10) {
                oss << '-';
            }
            oss << std::setw(2) << static_cast<int>(bytes[i]);
        }

        return oss.str();
    }

    // =============================================================================
    // Key Generation
    // =============================================================================

    std::optional<std::vector<uint8_t>> CryptoUtils::generateAes256Key() {
        return randomBytes(32); // 256 bits = 32 bytes
    }

    std::optional<std::vector<uint8_t>> CryptoUtils::generateSalt(size_t length) {
        return randomBytes(length);
    }

    // =============================================================================
    // Encoding/Decoding
    // =============================================================================

    std::string CryptoUtils::base64Encode(const std::vector<uint8_t>& data) {
        std::string result;
        size_t i = 0;

        while (i < data.size()) {
            uint32_t octet_a = i < data.size() ? data[i++] : 0;
            uint32_t octet_b = i < data.size() ? data[i++] : 0;
            uint32_t octet_c = i < data.size() ? data[i++] : 0;

            uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;

            result += base64Chars[(triple >> 3 * 6) & 0x3F];
            result += base64Chars[(triple >> 2 * 6) & 0x3F];
            result += base64Chars[(triple >> 1 * 6) & 0x3F];
            result += base64Chars[(triple >> 0 * 6) & 0x3F];
        }

        size_t padding = (3 - data.size() % 3) % 3;
        for (size_t j = 0; j < padding; ++j) {
            result[result.size() - 1 - j] = '=';
        }

        return result;
    }

    std::string CryptoUtils::base64Encode(const std::string& data) {
        return base64Encode(std::vector<uint8_t>(data.begin(), data.end()));
    }

    std::optional<std::vector<uint8_t>> CryptoUtils::base64Decode(const std::string& encoded) {
        std::vector<uint8_t> result;

        std::string input = encoded;
        // Remove padding
        while (!input.empty() && input.back() == '=') {
            input.pop_back();
        }

        for (size_t i = 0; i < input.length(); i += 4) {
            auto pos_a = base64Chars.find(input[i]);
            auto pos_b = i + 1 < input.length() ? base64Chars.find(input[i + 1]) : 0;
            auto pos_c = i + 2 < input.length() ? base64Chars.find(input[i + 2]) : 0;
            auto pos_d = i + 3 < input.length() ? base64Chars.find(input[i + 3]) : 0;

            if (pos_a == std::string::npos || pos_b == std::string::npos ||
                pos_c == std::string::npos || pos_d == std::string::npos) {
                return std::nullopt;
            }

            uint32_t sextet_a = static_cast<uint32_t>(pos_a);
            uint32_t sextet_b = static_cast<uint32_t>(pos_b);
            uint32_t sextet_c = static_cast<uint32_t>(pos_c);
            uint32_t sextet_d = static_cast<uint32_t>(pos_d);

            uint32_t triple = (sextet_a << 3 * 6) + (sextet_b << 2 * 6) + (sextet_c << 1 * 6) + (sextet_d << 0 * 6);

            if (i + 1 < input.length()) result.push_back((triple >> 2 * 8) & 0xFF);
            if (i + 2 < input.length()) result.push_back((triple >> 1 * 8) & 0xFF);
            if (i + 3 < input.length()) result.push_back((triple >> 0 * 8) & 0xFF);
        }

        return result;
    }

    std::optional<std::string> CryptoUtils::base64DecodeString(const std::string& encoded) {
        auto bytesOpt = base64Decode(encoded);
        if (!bytesOpt) return std::nullopt;

        return std::string(bytesOpt->begin(), bytesOpt->end());
    }



    // =============================================================================
    // Key Derivation Functions
    // =============================================================================

    std::optional<std::vector<uint8_t>> CryptoUtils::pbkdf2Sha256(
        const std::string& password,
        const std::vector<uint8_t>& salt,
        int iterations,
        int keyLength
    ) {
#ifdef CRYPTO_USE_OPENSSL
        std::vector<uint8_t> key(static_cast<size_t>(keyLength));

        if (PKCS5_PBKDF2_HMAC(password.c_str(), static_cast<int>(password.length()),
                              salt.data(), static_cast<int>(salt.size()),
                              iterations, EVP_sha256(),
                              keyLength, key.data()) != 1) {
            return std::nullopt;
        }

        return key;
#else
        // Simple PBKDF2 implementation
        std::vector<uint8_t> key;
        key.reserve(static_cast<size_t>(keyLength));

        int blocks = (keyLength + 31) / 32; // SHA256 output is 32 bytes

        for (int i = 1; i <= blocks; ++i) {
            std::vector<uint8_t> blockSalt = salt;
            blockSalt.push_back(static_cast<uint8_t>((i >> 24) & 0xFF));
            blockSalt.push_back(static_cast<uint8_t>((i >> 16) & 0xFF));
            blockSalt.push_back(static_cast<uint8_t>((i >> 8) & 0xFF));
            blockSalt.push_back(static_cast<uint8_t>(i & 0xFF));

            std::string saltStr(blockSalt.begin(), blockSalt.end());
            auto u = hmacSha256(password, saltStr);
            if (!u) return std::nullopt;

            auto uBytes = hexToBytes(*u);
            if (!uBytes) return std::nullopt;

            std::vector<uint8_t> result = *uBytes;

            for (int j = 1; j < iterations; ++j) {
                std::string uStr(uBytes->begin(), uBytes->end());
                u = hmacSha256(password, uStr);
                if (!u) return std::nullopt;

                uBytes = hexToBytes(*u);
                if (!uBytes) return std::nullopt;

                for (size_t k = 0; k < result.size() && k < uBytes->size(); ++k) {
                    result[k] ^= (*uBytes)[k];
                }
            }

            for (size_t k = 0; k < result.size() && key.size() < static_cast<size_t>(keyLength); ++k) {
                key.push_back(result[k]);
            }
        }

        key.resize(static_cast<size_t>(keyLength));
        return key;
#endif
    }

    // =============================================================================
    // Password Hashing
    // =============================================================================

    std::optional<std::string> CryptoUtils::hashPassword(
        const std::string& password,
        const std::vector<uint8_t>& salt,
        int iterations
    ) {
        std::vector<uint8_t> actualSalt = salt;
        if (actualSalt.empty()) {
            auto saltOpt = generateSalt(16);
            if (!saltOpt) return std::nullopt;
            actualSalt = *saltOpt;
        }

        auto keyOpt = pbkdf2Sha256(password, actualSalt, iterations, 32);
        if (!keyOpt) return std::nullopt;

        // Format: $pbkdf2$iterations$salt$hash
        std::ostringstream oss;
        oss << "$pbkdf2$" << iterations << "$"
            << base64Encode(actualSalt) << "$"
            << base64Encode(*keyOpt);

        return oss.str();
    }

    bool CryptoUtils::verifyPassword(const std::string& password, const std::string& hash) {
        // Parse hash format: $pbkdf2$iterations$salt$hash
        if (hash.substr(0, 8) != "$pbkdf2$") {
            return false;
        }

        size_t pos1 = hash.find('$', 8);
        if (pos1 == std::string::npos) return false;

        size_t pos2 = hash.find('$', pos1 + 1);
        if (pos2 == std::string::npos) return false;

        size_t pos3 = hash.find('$', pos2 + 1);
        if (pos3 == std::string::npos) return false;

        try {
            int iterations = std::stoi(hash.substr(8, pos1 - 8));
            std::string saltB64 = hash.substr(pos1 + 1, pos2 - pos1 - 1);
            std::string hashB64 = hash.substr(pos2 + 1, pos3 - pos2 - 1);

            auto saltOpt = base64Decode(saltB64);
            auto expectedHashOpt = base64Decode(hashB64);

            if (!saltOpt || !expectedHashOpt) return false;

            auto actualHashOpt = pbkdf2Sha256(password, *saltOpt, iterations, static_cast<int>(expectedHashOpt->size()));
            if (!actualHashOpt) return false;

            return secureCompare(*actualHashOpt, *expectedHashOpt);
        } catch (...) {
            return false;
        }
    }

    // =============================================================================
    // Utility Functions
    // =============================================================================

    bool CryptoUtils::isOpenSslAvailable() {
#ifdef CRYPTO_USE_OPENSSL
        return true;
#else
        return false;
#endif
    }

    std::string CryptoUtils::getVersion() {
#ifdef CRYPTO_USE_OPENSSL
        return std::string("OpenSSL ") + OPENSSL_VERSION_TEXT;
#else
        return "Standard Library Crypto (Limited)";
#endif
    }

    bool CryptoUtils::secureCompare(const std::vector<uint8_t>& a, const std::vector<uint8_t>& b) {
        if (a.size() != b.size()) {
            return false;
        }

        volatile uint8_t result = 0;
        for (size_t i = 0; i < a.size(); ++i) {
            result |= static_cast<uint8_t>(a[i] ^ b[i]);
        }

        return result == 0;
    }

    void CryptoUtils::secureClear(std::vector<uint8_t>& data) {
#ifdef CRYPTO_USE_OPENSSL
        OPENSSL_cleanse(data.data(), data.size());
#else
        volatile uint8_t* ptr = data.data();
        for (size_t i = 0; i < data.size(); ++i) {
            ptr[i] = 0;
        }
#endif
        data.clear();
    }

    void CryptoUtils::secureClear(std::string& data) {
#ifdef CRYPTO_USE_OPENSSL
        OPENSSL_cleanse(const_cast<char*>(data.data()), data.size());
#else
        volatile char* ptr = const_cast<char*>(data.data());
        for (size_t i = 0; i < data.size(); ++i) {
            ptr[i] = 0;
        }
#endif
        data.clear();
    }

    // =============================================================================
    // Private Helper Functions
    // =============================================================================

    std::string CryptoUtils::bytesToHex(const std::vector<uint8_t>& bytes) {
        std::ostringstream oss;
        oss << std::hex << std::setfill('0');
        for (uint8_t byte : bytes) {
            oss << std::setw(2) << static_cast<int>(byte);
        }
        return oss.str();
    }

    std::optional<std::vector<uint8_t>> CryptoUtils::hexToBytes(const std::string& hex) {
        if (hex.length() % 2 != 0) {
            return std::nullopt;
        }

        std::vector<uint8_t> bytes;
        bytes.reserve(hex.length() / 2);

        for (size_t i = 0; i < hex.length(); i += 2) {
            std::string byteString = hex.substr(i, 2);
            try {
                uint8_t byte = static_cast<uint8_t>(std::stoul(byteString, nullptr, 16));
                bytes.push_back(byte);
            } catch (...) {
                return std::nullopt;
            }
        }

        return bytes;
    }

    bool CryptoUtils::isBase64(unsigned char c) {
        return (isalnum(c) || (c == '+') || (c == '/'));
    }

    // =============================================================================
    // AES Encryption (Simplified implementation without OpenSSL)
    // =============================================================================

    std::optional<std::vector<uint8_t>> CryptoUtils::aes256GcmEncrypt(
        [[maybe_unused]] const std::string& plaintext,
        [[maybe_unused]] const std::vector<uint8_t>& key,
        [[maybe_unused]] const std::vector<uint8_t>& iv
    ) {
#ifdef CRYPTO_USE_OPENSSL
        if (key.size() != 32 || iv.size() != 12) {
            return std::nullopt;
        }

        EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
        if (!ctx) return std::nullopt;

        if (EVP_EncryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr, nullptr, nullptr) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }

        if (EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_IVLEN, static_cast<int>(iv.size()), nullptr) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }

        if (EVP_EncryptInit_ex(ctx, nullptr, nullptr, key.data(), iv.data()) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }

        std::vector<uint8_t> ciphertext(plaintext.length() + 16); // +16 for tag
        int len;
        int ciphertext_len;

        if (EVP_EncryptUpdate(ctx, ciphertext.data(), &len,
                             reinterpret_cast<const unsigned char*>(plaintext.c_str()),
                             static_cast<int>(plaintext.length())) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }
        ciphertext_len = len;

        if (EVP_EncryptFinal_ex(ctx, ciphertext.data() + len, &len) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }
        ciphertext_len += len;

        // Get the tag
        unsigned char tag[16];
        if (EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_GET_TAG, 16, tag) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }

        // Append tag to ciphertext
        for (int i = 0; i < 16; ++i) {
            ciphertext[static_cast<size_t>(ciphertext_len) + static_cast<size_t>(i)] = tag[i];
        }

        EVP_CIPHER_CTX_free(ctx);
        ciphertext.resize(static_cast<size_t>(ciphertext_len) + 16);
        return ciphertext;
#else
        // AES-GCM not available without OpenSSL
        return std::nullopt;
#endif
    }

    std::optional<std::string> CryptoUtils::aes256GcmDecrypt(
        [[maybe_unused]] const std::vector<uint8_t>& ciphertext,
        [[maybe_unused]] const std::vector<uint8_t>& key,
        [[maybe_unused]] const std::vector<uint8_t>& iv
    ) {
#ifdef CRYPTO_USE_OPENSSL
        if (key.size() != 32 || iv.size() != 12 || ciphertext.size() < 16) {
            return std::nullopt;
        }

        EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
        if (!ctx) return std::nullopt;

        if (EVP_DecryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr, nullptr, nullptr) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }

        if (EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_IVLEN, static_cast<int>(iv.size()), nullptr) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }

        if (EVP_DecryptInit_ex(ctx, nullptr, nullptr, key.data(), iv.data()) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }

        // Extract tag from end of ciphertext
        std::vector<uint8_t> tag(ciphertext.end() - 16, ciphertext.end());
        std::vector<uint8_t> actualCiphertext(ciphertext.begin(), ciphertext.end() - 16);

        std::vector<uint8_t> plaintext(actualCiphertext.size());
        int len;
        int plaintext_len;

        if (EVP_DecryptUpdate(ctx, plaintext.data(), &len,
                             actualCiphertext.data(), static_cast<int>(actualCiphertext.size())) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }
        plaintext_len = len;

        // Set expected tag value
        if (EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_TAG, 16, tag.data()) != 1) {
            EVP_CIPHER_CTX_free(ctx);
            return std::nullopt;
        }

        int ret = EVP_DecryptFinal_ex(ctx, plaintext.data() + len, &len);
        EVP_CIPHER_CTX_free(ctx);

        if (ret <= 0) {
            return std::nullopt; // Authentication failed
        }

        plaintext_len += len;
        plaintext.resize(static_cast<size_t>(plaintext_len));
        return std::string(plaintext.begin(), plaintext.end());
#else
        // AES-GCM not available without OpenSSL
        return std::nullopt;
#endif
    }

    std::optional<std::vector<uint8_t>> CryptoUtils::aes256CbcEncrypt(
        [[maybe_unused]] const std::string& plaintext,
        [[maybe_unused]] const std::vector<uint8_t>& key,
        [[maybe_unused]] const std::vector<uint8_t>& iv
    ) {
        // AES-CBC implementation would be complex without OpenSSL
        // For now, return nullopt for non-OpenSSL builds
#ifdef CRYPTO_USE_OPENSSL
        // Implementation would go here
        return std::nullopt;
#else
        return std::nullopt;
#endif
    }

    std::optional<std::string> CryptoUtils::aes256CbcDecrypt(
        [[maybe_unused]] const std::vector<uint8_t>& ciphertext,
        [[maybe_unused]] const std::vector<uint8_t>& key,
        [[maybe_unused]] const std::vector<uint8_t>& iv
    ) {
        // AES-CBC implementation would be complex without OpenSSL
        // For now, return nullopt for non-OpenSSL builds
#ifdef CRYPTO_USE_OPENSSL
        // Implementation would go here
        return std::nullopt;
#else
        return std::nullopt;
#endif
    }

    // =============================================================================
    // Convenience Functions
    // =============================================================================

    std::string sha256(const std::string& data) {
        auto result = CryptoUtils::sha256(data);
        return result.value_or("");
    }

    std::string hashPassword(const std::string& password) {
        auto result = CryptoUtils::hashPassword(password);
        return result.value_or("");
    }

    bool verifyPassword(const std::string& password, const std::string& hash) {
        return CryptoUtils::verifyPassword(password, hash);
    }

    std::string randomString(size_t length) {
        auto result = CryptoUtils::randomString(length);
        return result.value_or("");
    }

    std::string generateUuid() {
        auto result = CryptoUtils::generateUuid();
        return result.value_or("");
    }

    std::string base64Encode(const std::string& data) {
        return CryptoUtils::base64Encode(data);
    }

    std::string base64Decode(const std::string& encoded) {
        auto result = CryptoUtils::base64DecodeString(encoded);
        return result.value_or("");
    }

} // namespace utils
