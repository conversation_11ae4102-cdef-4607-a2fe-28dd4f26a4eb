#pragma once

#include "rtsp_types.hpp"
#include "rtsp_config.hpp"
#include <memory>
#include <atomic>
#include <mutex>
#include <thread>

// GStreamer forward declarations
#ifdef HAVE_GSTREAMER
// Include GStreamer headers to get proper type definitions
#include <gst/gst.h>
#include <gst/app/gstappsink.h>
#else
// Define dummy types when GStreamer is not available
typedef void GstElement;
typedef void GstBus;
typedef void GstMessage;
typedef void GstPipeline;
typedef void GstAppSink;
typedef void GstSample;
typedef void GstBuffer;
typedef void GstCaps;
typedef void GstPad;
typedef int GstFlowReturn;
typedef void* gpointer;
#endif

namespace aibox {
namespace rtsp {

// GStreamer pipeline statistics (atomic for internal use)
struct GStreamerStatisticsInternal {
    std::atomic<uint64_t> buffers_received{0};
    std::atomic<uint64_t> bytes_received{0};
    std::atomic<uint32_t> pipeline_errors{0};
    std::atomic<uint32_t> buffer_drops{0};
    std::atomic<uint32_t> format_changes{0};
    std::atomic<uint32_t> hardware_decode_count{0};
    std::atomic<uint32_t> software_decode_count{0};
    std::atomic<float> current_fps{0.0f};
    std::atomic<uint32_t> current_bitrate_kbps{0};

    void reset() {
        buffers_received = 0;
        bytes_received = 0;
        pipeline_errors = 0;
        buffer_drops = 0;
        format_changes = 0;
        hardware_decode_count = 0;
        software_decode_count = 0;
        current_fps = 0.0f;
        current_bitrate_kbps = 0;
    }
};

// GStreamer pipeline statistics (copyable snapshot)
struct GStreamerStatistics {
    uint64_t buffers_received = 0;
    uint64_t bytes_received = 0;
    uint32_t pipeline_errors = 0;
    uint32_t buffer_drops = 0;
    uint32_t format_changes = 0;
    uint32_t hardware_decode_count = 0;
    uint32_t software_decode_count = 0;
    float current_fps = 0.0f;
    uint32_t current_bitrate_kbps = 0;

    double getHardwareDecodeRate() const {
        uint64_t total = hardware_decode_count + software_decode_count;
        return total > 0 ? static_cast<double>(hardware_decode_count) / total : 0.0;
    }
};

// GStreamer pipeline configuration
struct GStreamerPipelineConfig {
    // Pipeline elements
    bool use_rtspsrc = true;
    bool use_mpp_decoder = true;
    bool use_rga_scaler = true;
    bool use_dmabuf = true;
    
    // Buffer configuration
    size_t max_size_buffers = 200;
    size_t max_size_bytes = 10 * 1024 * 1024;  // 10MB
    uint64_t max_size_time = 2000000000;  // 2 seconds in nanoseconds
    bool drop_on_latency = true;
    
    // RTP configuration
    uint32_t latency_ms = 100;
    bool do_retransmission = false;
    uint32_t retry_timeout = 5000;
    
    // Hardware acceleration
    std::string mpp_decoder_name = "mppvideodec";
    std::string rga_scaler_name = "rgaconvert";
    bool enable_zero_copy = true;
    
    // Debug options
    bool enable_debug = false;
    std::string debug_level = "WARNING";
    bool dump_pipeline_graph = false;
};

/**
 * @brief GStreamer RTSP Client optimized for RK3588 hardware acceleration
 * 
 * Provides RTSP stream reception using GStreamer with RockChip hardware
 * acceleration via MPP decoder and RGA scaler.
 */
class GStreamerRTSPClient {
public:
    // Constructor
    explicit GStreamerRTSPClient(const RTSPConnectionConfig& config);
    
    // Destructor
    ~GStreamerRTSPClient();
    
    // Non-copyable, movable
    GStreamerRTSPClient(const GStreamerRTSPClient&) = delete;
    GStreamerRTSPClient& operator=(const GStreamerRTSPClient&) = delete;
    GStreamerRTSPClient(GStreamerRTSPClient&&) = default;
    GStreamerRTSPClient& operator=(GStreamerRTSPClient&&) = default;
    
    // Lifecycle management
    bool initialize();
    bool start();
    void stop();
    void cleanup();
    bool isInitialized() const;
    bool isRunning() const;
    
    // Configuration management
    void updateConfig(const RTSPConnectionConfig& new_config);
    void updatePipelineConfig(const GStreamerPipelineConfig& pipeline_config);
    const RTSPConnectionConfig& getConfig() const;
    const GStreamerPipelineConfig& getPipelineConfig() const;
    
    // Connection management
    bool connect();
    void disconnect();
    bool isConnected() const;
    ConnectionState getState() const;
    
    // Hardware acceleration control
    bool enableMPPDecoder(bool enable);
    bool enableRGAScaler(bool enable);
    bool enableDMABuf(bool enable);
    HardwareAccelStatus getMPPStatus() const;
    HardwareAccelStatus getRGAStatus() const;
    bool isDMABufEnabled() const;
    
    // Data callbacks
    void setBufferCallback(std::function<void(const std::vector<uint8_t>&, Timestamp)> callback);
    void setNALUnitCallback(std::function<void(const NALUnit&)> callback);
    void setErrorCallback(StreamErrorCallback callback);
    void setStateChangeCallback(std::function<void(ConnectionState)> callback);
    
    // Statistics and monitoring
    GStreamerStatistics getStatistics() const;
    std::string getPipelineDescription() const;
    size_t getMemoryUsage() const;
    
    // Quality control
    void setLatency(uint32_t latency_ms);
    uint32_t getLatency() const;
    void enableAdaptiveQuality(bool enable);
    bool isAdaptiveQualityEnabled() const;
    
    // Thermal management
    void handleThermalThrottling(int temperature);
    void setPerformanceMode(bool high_performance);
    
    // Debug and diagnostics
    void dumpPipelineGraph(const std::string& filename) const;
    std::string getLastError() const;
    void enableDebugOutput(bool enable);
    
private:
    // Configuration
    RTSPConnectionConfig config_;
    GStreamerPipelineConfig pipeline_config_;
    mutable std::mutex config_mutex_;
    
    // GStreamer components
    GstPipeline* pipeline_;
    GstElement* rtspsrc_;
    GstElement* depayloader_;
    GstElement* parser_;
    GstElement* decoder_;
    GstElement* converter_;
    GstElement* appsink_;
    GstBus* bus_;
    
    // State management
    std::atomic<bool> initialized_;
    std::atomic<bool> running_;
    std::atomic<ConnectionState> state_;
    std::atomic<HardwareAccelStatus> mpp_status_;
    std::atomic<HardwareAccelStatus> rga_status_;
    std::atomic<bool> dmabuf_enabled_;
    
    // Threading
    std::unique_ptr<std::thread> bus_thread_;
    std::unique_ptr<std::thread> buffer_thread_;
    std::atomic<bool> should_stop_;
    
    // Statistics
    mutable GStreamerStatisticsInternal statistics_;
    mutable std::mutex stats_mutex_;
    
    // Error handling
    std::string last_error_;
    mutable std::mutex error_mutex_;
    
    // Callbacks
    std::function<void(const std::vector<uint8_t>&, Timestamp)> buffer_callback_;
    std::function<void(const NALUnit&)> nal_callback_;
    StreamErrorCallback error_callback_;
    std::function<void(ConnectionState)> state_callback_;
    std::mutex callback_mutex_;
    
    // Internal methods
    bool createPipeline();
    void destroyPipeline();
    bool configurePipeline();
    bool linkPipelineElements();
    void setupCallbacks();
    
    // Element creation
    GstElement* createRTSPSource();
    GstElement* createDepayloader(const std::string& codec);
    GstElement* createParser(const std::string& codec);
    GstElement* createDecoder(const std::string& codec);
    GstElement* createConverter();
    GstElement* createAppSink();
    
    // Hardware acceleration setup
    bool setupMPPDecoder();
    bool setupRGAConverter();
    bool setupDMABuf();
    void fallbackToSoftware();
    
    // Threading workers
    void busWorker();
    void bufferWorker();
    
    // Message handling
    void handleBusMessage(GstMessage* message);
    void handleErrorMessage(GstMessage* message);
    void handleWarningMessage(GstMessage* message);
    void handleInfoMessage(GstMessage* message);
    void handleStateChangeMessage(GstMessage* message);
    void handleEOSMessage(GstMessage* message);
    
    // Buffer processing and pad handling
    void handlePadAdded(GstElement* element, GstPad* pad);
    GstFlowReturn handleNewSample(GstAppSink* appsink);
    void processBuffer(GstSample* sample);
    void extractNALUnits(const std::vector<uint8_t>& buffer);
    
    // State management
    void updateState(ConnectionState new_state);
    void handleConnectionLost();
    void handleConnectionRestored();
    
    // Error handling
    void handlePipelineError(const std::string& error);
    void handleHardwareError(const std::string& component, const std::string& error);
    void setLastError(const std::string& error);
    
    // Statistics helpers
    void updateStatistics();
    void updateBufferStatistics(size_t buffer_size);
    void updatePerformanceStatistics();
    
    // Validation and capabilities
    bool validateConfig() const;
    bool checkGStreamerVersion() const;
    bool checkHardwareCapabilities() const;
    bool checkCodecSupport(VideoCodec codec) const;
    
    // RK3588 optimizations
    void optimizeForRK3588();
    void setCPUAffinity();
    void configureMemoryPools();
    
    // Utility methods
    std::string gstStateToString(int state) const;
    VideoCodec detectCodecFromCaps(GstCaps* caps) const;
    void logPipelineInfo() const;

    // Static callback functions for GStreamer
    static void onPadAdded(GstElement* element, GstPad* pad, gpointer user_data);
    static GstFlowReturn onNewSample(GstAppSink* appsink, gpointer user_data);
};

/**
 * @brief GStreamer initialization and cleanup helper
 * 
 * Manages global GStreamer initialization for the application.
 */
class GStreamerManager {
public:
    // Singleton access
    static GStreamerManager& getInstance();
    
    // Initialization
    bool initialize();
    void cleanup();
    bool isInitialized() const;
    
    // Version information
    std::string getVersion() const;
    std::vector<std::string> getAvailablePlugins() const;
    bool isPluginAvailable(const std::string& plugin_name) const;
    
    // Hardware capability detection
    bool isMPPDecoderAvailable() const;
    bool isRGAConverterAvailable() const;
    bool isDMABufSupported() const;
    std::vector<std::string> getSupportedCodecs() const;
    
    // Debug configuration
    void setDebugLevel(const std::string& level);
    void enableDebugOutput(bool enable);
    
private:
    GStreamerManager() = default;
    ~GStreamerManager() = default;
    
    std::atomic<bool> initialized_{false};
    mutable std::mutex mutex_;
    
    // Capability cache
    mutable std::vector<std::string> available_plugins_;
    mutable std::vector<std::string> supported_codecs_;
    mutable bool capabilities_cached_{false};
    
    void cacheCapabilities() const;
    bool checkPluginCapability(const std::string& plugin_name) const;
};

} // namespace rtsp
} // namespace aibox
