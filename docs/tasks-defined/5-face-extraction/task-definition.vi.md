# Định Nghĩa Tác Vụ Module Trích Xuất và Tiền Xử Lý Khuôn Mặt

## Tổng Quan
Module này chịu trách nhiệm trích xuất các vùng khuôn mặt từ khung hình video dựa trên kết quả phát hiện và chuẩn bị chúng cho nhận diện khuôn mặt. Nó sử dụng tăng tốc phần cứng RGA để cắt và tiền xử lý các khuôn mặt đã phát hiện để có hiệu suất nhận diện tối ưu.

## Yêu Cầu
- Trích xuất các vùng khuôn mặt từ khung hình gốc sử dụng bounding box phát hiện
- Tiền xử lý hình ảnh khuôn mặt cho nhận diện (thay đổi kích thướ<PERSON>, chuyển đổi màu)
- <PERSON><PERSON> trì các thao tác zero-copy sử dụng DMABUF khi có thể
- Xử lý nhiều phát hiện khuôn mặt từ nhiều luồng camera
- Chuẩn bị đầu vào tối ưu cho mô hình nhận diện khuôn mặt

## Phương Pháp Kỹ Thuật
1. **Trích Xuất Vùng Khuôn Mặt**:
   - Nhận kết quả phát hiện khuôn mặt (bounding box) và thông tin khung hình tương ứng
   - Truy cập khung hình gốc từ bộ giải mã MPP hoặc frame buffer
   - Triển khai logic cắt khuôn mặt chính xác với điều chỉnh margin nếu cần
   - Áp dụng alignment hoặc chuẩn hóa nếu có landmark từ mô hình phát hiện

2. **Xử Lý Tăng Tốc RGA**:
   - Cấu hình RGA cho các thao tác cắt dựa trên kết quả phát hiện
   - Triển khai thay đổi kích thước để khớp với kích thước đầu vào mô hình nhận diện khuôn mặt (thường là 112x112)
   - Thực hiện chuyển đổi không gian màu nếu mô hình nhận diện yêu cầu

3. **Chuẩn Bị Xử Lý Batch**:
   - Nhóm nhiều crop khuôn mặt cho xử lý batch trong mô hình nhận diện
   - Duy trì liên kết metadata giữa crop và phát hiện gốc
   - Triển khai quản lý bộ nhớ hiệu quả cho nhiều khuôn mặt

4. **Quản Lý DMABUF**:
   - Import DMABUF khung hình gốc để xử lý
   - Tạo DMABUF mới cho hình ảnh khuôn mặt đã xử lý
   - Đảm bảo đồng bộ hóa và đếm tham chiếu phù hợp

## Giao Diện
- **Đầu vào**: 
  - Kết quả phát hiện khuôn mặt (bounding box, điểm tin cậy)
  - File descriptor DMABUF trỏ đến khung hình video gốc
- **Đầu ra**: 
  - File descriptor DMABUF trỏ đến hình ảnh khuôn mặt đã cắt và tiền xử lý
  - Metadata liên kết khuôn mặt đã xử lý với phát hiện và khung hình gốc

## Phụ Thuộc
- librga (thư viện RGA của Rockchip)
- Thư viện chuẩn C++
- Thư viện DMABUF/DRM cho quản lý buffer

## Cân Nhắc Hiệu Suất
- Lập lịch thao tác RGA hiệu quả
- Giảm thiểu sử dụng băng thông bộ nhớ
- Tối ưu chuẩn bị xử lý batch
- Cân bằng giữa chất lượng hình ảnh khuôn mặt và tốc độ xử lý
