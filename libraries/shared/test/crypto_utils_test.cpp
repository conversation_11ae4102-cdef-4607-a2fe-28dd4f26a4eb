#include <gtest/gtest.h>
#include "utils/crypto_utils.hpp"
#include <string>
#include <vector>

using namespace utils;

// Simple test that only tests non-OpenSSL functions to avoid linking issues

class CryptoUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Setup test data
        testData = "Hello, <PERSON>!";
        testPassword = "mySecretPassword123";
        testKey = "This is a test key for encryption";
        testSalt = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
                   0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10};
    }

    std::string testData;
    std::string testPassword;
    std::string testKey;
    std::vector<uint8_t> testSalt;
};

// =============================================================================
// Hash Functions Tests
// =============================================================================

TEST_F(CryptoUtilsTest, SHA256_BasicTest) {
    auto result = CryptoUtils::sha256(testData);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());

    // Test consistency - same input should produce same output
    auto result2 = CryptoUtils::sha256(testData);
    ASSERT_TRUE(result2.has_value());
    EXPECT_EQ(*result, *result2);
}

TEST_F(CryptoUtilsTest, SHA256_EmptyString) {
    auto result = CryptoUtils::sha256("");
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());
}

TEST_F(CryptoUtilsTest, SHA256_ByteVector) {
    std::vector<uint8_t> data(testData.begin(), testData.end());
    auto result = CryptoUtils::sha256(data);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());
}

TEST_F(CryptoUtilsTest, SHA512_BasicTest) {
    auto result = CryptoUtils::sha512(testData);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());

    // SHA512 should produce longer hash than SHA256
    auto sha256Result = CryptoUtils::sha256(testData);
    ASSERT_TRUE(sha256Result.has_value());
    if (CryptoUtils::isOpenSslAvailable()) {
        EXPECT_GT(result->length(), sha256Result->length());
    }
}

TEST_F(CryptoUtilsTest, MD5_BasicTest) {
    auto result = CryptoUtils::md5(testData);
    if (CryptoUtils::isOpenSslAvailable()) {
        ASSERT_TRUE(result.has_value());
        EXPECT_FALSE(result->empty());
    } else {
        EXPECT_FALSE(result.has_value());
    }
}

// =============================================================================
// HMAC Functions Tests
// =============================================================================

TEST_F(CryptoUtilsTest, HMAC_SHA256_BasicTest) {
    auto result = CryptoUtils::hmacSha256(testKey, testData);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());

    // Test consistency
    auto result2 = CryptoUtils::hmacSha256(testKey, testData);
    ASSERT_TRUE(result2.has_value());
    EXPECT_EQ(*result, *result2);
}

TEST_F(CryptoUtilsTest, HMAC_SHA256_DifferentKeys) {
    auto result1 = CryptoUtils::hmacSha256("key1", testData);
    auto result2 = CryptoUtils::hmacSha256("key2", testData);

    ASSERT_TRUE(result1.has_value());
    ASSERT_TRUE(result2.has_value());
    EXPECT_NE(*result1, *result2);
}

TEST_F(CryptoUtilsTest, HMAC_SHA512_BasicTest) {
    auto result = CryptoUtils::hmacSha512(testKey, testData);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());
}

// =============================================================================
// Random Generation Tests
// =============================================================================

TEST_F(CryptoUtilsTest, RandomBytes_BasicTest) {
    auto result = CryptoUtils::randomBytes(16);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 16);

    // Two calls should produce different results
    auto result2 = CryptoUtils::randomBytes(16);
    ASSERT_TRUE(result2.has_value());
    EXPECT_NE(*result, *result2);
}

TEST_F(CryptoUtilsTest, RandomBytes_ZeroLength) {
    auto result = CryptoUtils::randomBytes(0);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 0);
}

TEST_F(CryptoUtilsTest, RandomString_BasicTest) {
    auto result = CryptoUtils::randomString(10);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->length(), 10);

    // Two calls should produce different results
    auto result2 = CryptoUtils::randomString(10);
    ASSERT_TRUE(result2.has_value());
    EXPECT_NE(*result, *result2);
}

TEST_F(CryptoUtilsTest, RandomString_CustomCharset) {
    std::string charset = "0123456789";
    auto result = CryptoUtils::randomString(10, charset);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->length(), 10);

    // Check all characters are from charset
    for (char c : *result) {
        EXPECT_NE(charset.find(c), std::string::npos);
    }
}

TEST_F(CryptoUtilsTest, RandomHex_BasicTest) {
    auto result = CryptoUtils::randomHex(16);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->length(), 16);

    // Check all characters are hex
    for (char c : *result) {
        EXPECT_TRUE(std::isxdigit(c));
    }
}

TEST_F(CryptoUtilsTest, RandomHex_OddLength) {
    auto result = CryptoUtils::randomHex(15); // Odd length should fail
    EXPECT_FALSE(result.has_value());
}

TEST_F(CryptoUtilsTest, GenerateUuid_BasicTest) {
    auto result = CryptoUtils::generateUuid();
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->length(), 36); // UUID format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx

    // Check format
    EXPECT_EQ((*result)[8], '-');
    EXPECT_EQ((*result)[13], '-');
    EXPECT_EQ((*result)[18], '-');
    EXPECT_EQ((*result)[23], '-');
    EXPECT_EQ((*result)[14], '4'); // Version 4

    // Two calls should produce different UUIDs
    auto result2 = CryptoUtils::generateUuid();
    ASSERT_TRUE(result2.has_value());
    EXPECT_NE(*result, *result2);
}

TEST_F(CryptoUtilsTest, GenerateSalt_BasicTest) {
    auto result = CryptoUtils::generateSalt(16);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 16);

    // Two calls should produce different salts
    auto result2 = CryptoUtils::generateSalt(16);
    ASSERT_TRUE(result2.has_value());
    EXPECT_NE(*result, *result2);
}

TEST_F(CryptoUtilsTest, GenerateAes256Key_BasicTest) {
    auto result = CryptoUtils::generateAes256Key();
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 32); // 256 bits = 32 bytes
}

// =============================================================================
// Base64 Encoding/Decoding Tests
// =============================================================================

TEST_F(CryptoUtilsTest, Base64_BasicTest) {
    std::string encoded = CryptoUtils::base64Encode(testData);
    EXPECT_FALSE(encoded.empty());

    auto decoded = CryptoUtils::base64DecodeString(encoded);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, testData);
}

TEST_F(CryptoUtilsTest, Base64_EmptyString) {
    std::string encoded = CryptoUtils::base64Encode("");
    EXPECT_FALSE(encoded.empty());

    auto decoded = CryptoUtils::base64DecodeString(encoded);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, "");
}

TEST_F(CryptoUtilsTest, Base64_ByteVector) {
    std::vector<uint8_t> data(testData.begin(), testData.end());
    std::string encoded = CryptoUtils::base64Encode(data);
    EXPECT_FALSE(encoded.empty());

    auto decoded = CryptoUtils::base64Decode(encoded);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, data);
}

TEST_F(CryptoUtilsTest, Base64_InvalidInput) {
    auto result = CryptoUtils::base64Decode("Invalid@Base64!");
    EXPECT_FALSE(result.has_value());
}

// =============================================================================
// Hex Encoding/Decoding Tests
// =============================================================================

TEST_F(CryptoUtilsTest, Hex_BasicTest) {
    std::vector<uint8_t> data = {0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF};
    std::string hex = CryptoUtils::bytesToHex(data);
    EXPECT_EQ(hex, "0123456789abcdef");

    auto decoded = CryptoUtils::hexToBytes(hex);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, data);
}

TEST_F(CryptoUtilsTest, Hex_EmptyData) {
    std::vector<uint8_t> data;
    std::string hex = CryptoUtils::bytesToHex(data);
    EXPECT_EQ(hex, "");

    auto decoded = CryptoUtils::hexToBytes(hex);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, data);
}

TEST_F(CryptoUtilsTest, Hex_InvalidInput) {
    auto result = CryptoUtils::hexToBytes("Invalid Hex!");
    EXPECT_FALSE(result.has_value());

    auto result2 = CryptoUtils::hexToBytes("123"); // Odd length
    EXPECT_FALSE(result2.has_value());
}

// =============================================================================
// Utility Functions Tests
// =============================================================================

TEST_F(CryptoUtilsTest, IsOpenSslAvailable_Test) {
    bool available = CryptoUtils::isOpenSslAvailable();
    // Just check it returns a boolean value
    EXPECT_TRUE(available == true || available == false);
}

TEST_F(CryptoUtilsTest, GetVersion_Test) {
    std::string version = CryptoUtils::getVersion();
    EXPECT_FALSE(version.empty());
}

TEST_F(CryptoUtilsTest, SecureCompare_BasicTest) {
    std::vector<uint8_t> data1 = {0x01, 0x02, 0x03, 0x04};
    std::vector<uint8_t> data2 = {0x01, 0x02, 0x03, 0x04};
    std::vector<uint8_t> data3 = {0x01, 0x02, 0x03, 0x05};

    EXPECT_TRUE(CryptoUtils::secureCompare(data1, data2));
    EXPECT_FALSE(CryptoUtils::secureCompare(data1, data3));
}

TEST_F(CryptoUtilsTest, SecureCompare_DifferentSizes) {
    std::vector<uint8_t> data1 = {0x01, 0x02, 0x03};
    std::vector<uint8_t> data2 = {0x01, 0x02, 0x03, 0x04};

    EXPECT_FALSE(CryptoUtils::secureCompare(data1, data2));
}

TEST_F(CryptoUtilsTest, SecureClear_Test) {
    std::vector<uint8_t> data = {0x01, 0x02, 0x03, 0x04};
    std::string str = "sensitive data";

    CryptoUtils::secureClear(data);
    CryptoUtils::secureClear(str);

    EXPECT_TRUE(data.empty());
    EXPECT_TRUE(str.empty());
}

// =============================================================================
// Key Derivation Tests
// =============================================================================

TEST_F(CryptoUtilsTest, PBKDF2_BasicTest) {
    auto result = CryptoUtils::pbkdf2Sha256(testPassword, testSalt, 1000, 32);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 32);

    // Test consistency
    auto result2 = CryptoUtils::pbkdf2Sha256(testPassword, testSalt, 1000, 32);
    ASSERT_TRUE(result2.has_value());
    EXPECT_EQ(*result, *result2);
}

TEST_F(CryptoUtilsTest, PBKDF2_DifferentPasswords) {
    auto result1 = CryptoUtils::pbkdf2Sha256("password1", testSalt, 1000, 32);
    auto result2 = CryptoUtils::pbkdf2Sha256("password2", testSalt, 1000, 32);

    ASSERT_TRUE(result1.has_value());
    ASSERT_TRUE(result2.has_value());
    EXPECT_NE(*result1, *result2);
}

TEST_F(CryptoUtilsTest, PBKDF2_DifferentSalts) {
    std::vector<uint8_t> salt2 = {0x10, 0x0F, 0x0E, 0x0D, 0x0C, 0x0B, 0x0A, 0x09,
                                  0x08, 0x07, 0x06, 0x05, 0x04, 0x03, 0x02, 0x01};

    auto result1 = CryptoUtils::pbkdf2Sha256(testPassword, testSalt, 1000, 32);
    auto result2 = CryptoUtils::pbkdf2Sha256(testPassword, salt2, 1000, 32);

    ASSERT_TRUE(result1.has_value());
    ASSERT_TRUE(result2.has_value());
    EXPECT_NE(*result1, *result2);
}

// =============================================================================
// Password Hashing Tests
// =============================================================================

TEST_F(CryptoUtilsTest, HashPassword_BasicTest) {
    auto result = CryptoUtils::hashPassword(testPassword);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());

    // Should start with $pbkdf2$
    EXPECT_TRUE(result->substr(0, 8) == "$pbkdf2$");
}

TEST_F(CryptoUtilsTest, HashPassword_WithSalt) {
    auto result = CryptoUtils::hashPassword(testPassword, testSalt, 1000);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());
    EXPECT_TRUE(result->substr(0, 8) == "$pbkdf2$");
}

TEST_F(CryptoUtilsTest, VerifyPassword_BasicTest) {
    auto hash = CryptoUtils::hashPassword(testPassword);
    ASSERT_TRUE(hash.has_value());

    EXPECT_TRUE(CryptoUtils::verifyPassword(testPassword, *hash));
    EXPECT_FALSE(CryptoUtils::verifyPassword("wrongpassword", *hash));
}

TEST_F(CryptoUtilsTest, VerifyPassword_WithSalt) {
    auto hash = CryptoUtils::hashPassword(testPassword, testSalt, 1000);
    ASSERT_TRUE(hash.has_value());

    EXPECT_TRUE(CryptoUtils::verifyPassword(testPassword, *hash));
    EXPECT_FALSE(CryptoUtils::verifyPassword("wrongpassword", *hash));
}

TEST_F(CryptoUtilsTest, VerifyPassword_InvalidFormat) {
    EXPECT_FALSE(CryptoUtils::verifyPassword(testPassword, "invalid_hash"));
    EXPECT_FALSE(CryptoUtils::verifyPassword(testPassword, "$invalid$format"));
}

// =============================================================================
// AES Encryption Tests
// =============================================================================

TEST_F(CryptoUtilsTest, AES256GCM_BasicTest) {
    if (!CryptoUtils::isOpenSslAvailable()) {
        GTEST_SKIP() << "OpenSSL not available, skipping AES tests";
    }

    auto key = CryptoUtils::generateAes256Key();
    auto iv = CryptoUtils::randomBytes(12); // GCM uses 12-byte IV

    ASSERT_TRUE(key.has_value());
    ASSERT_TRUE(iv.has_value());

    auto encrypted = CryptoUtils::aes256GcmEncrypt(testData, *key, *iv);
    ASSERT_TRUE(encrypted.has_value());
    EXPECT_FALSE(encrypted->empty());

    auto decrypted = CryptoUtils::aes256GcmDecrypt(*encrypted, *key, *iv);
    ASSERT_TRUE(decrypted.has_value());
    EXPECT_EQ(*decrypted, testData);
}

TEST_F(CryptoUtilsTest, AES256GCM_InvalidKeySize) {
    if (!CryptoUtils::isOpenSslAvailable()) {
        GTEST_SKIP() << "OpenSSL not available, skipping AES tests";
    }

    std::vector<uint8_t> invalidKey(16); // Wrong size (should be 32)
    auto iv = CryptoUtils::randomBytes(12);

    ASSERT_TRUE(iv.has_value());

    auto result = CryptoUtils::aes256GcmEncrypt(testData, invalidKey, *iv);
    EXPECT_FALSE(result.has_value());
}

TEST_F(CryptoUtilsTest, AES256GCM_InvalidIVSize) {
    if (!CryptoUtils::isOpenSslAvailable()) {
        GTEST_SKIP() << "OpenSSL not available, skipping AES tests";
    }

    auto key = CryptoUtils::generateAes256Key();
    std::vector<uint8_t> invalidIV(16); // Wrong size (should be 12)

    ASSERT_TRUE(key.has_value());

    auto result = CryptoUtils::aes256GcmEncrypt(testData, *key, invalidIV);
    EXPECT_FALSE(result.has_value());
}

// =============================================================================
// Convenience Functions Tests
// =============================================================================

TEST_F(CryptoUtilsTest, ConvenienceFunctions_Test) {
    // Test convenience functions
    std::string hash = sha256(testData);
    EXPECT_FALSE(hash.empty());

    std::string passwordHash = hashPassword(testPassword);
    EXPECT_FALSE(passwordHash.empty());

    EXPECT_TRUE(verifyPassword(testPassword, passwordHash));

    std::string randomStr = randomString(10);
    EXPECT_EQ(randomStr.length(), 10);

    std::string uuid = generateUuid();
    EXPECT_EQ(uuid.length(), 36);

    std::string encoded = base64Encode(testData);
    EXPECT_FALSE(encoded.empty());

    std::string decoded = base64Decode(encoded);
    EXPECT_EQ(decoded, testData);
}
