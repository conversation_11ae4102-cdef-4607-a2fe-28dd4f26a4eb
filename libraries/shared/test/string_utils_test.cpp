#include <gtest/gtest.h>
#include <utils/string_utils.hpp>

class StringUtilsTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Set up test fixtures if needed
    }

    void TearDown() override {
        // Clean up after tests
    }
};

TEST_F(StringUtilsTest, SplitOperations) {
    // Test split by character
    std::string str = "apple,banana,cherry";
    auto result = utils::StringUtils::split(str, ',');
    EXPECT_EQ(result.size(), 3);
    EXPECT_EQ(result[0], "apple");
    EXPECT_EQ(result[1], "banana");
    EXPECT_EQ(result[2], "cherry");

    // Test convenience function
    auto result2 = utils::split(str, ',');
    EXPECT_EQ(result2, result);

    // Test split by string
    std::string str2 = "apple::banana::cherry";
    auto result3 = utils::StringUtils::split(str2, "::");
    EXPECT_EQ(result3.size(), 3);
    EXPECT_EQ(result3[0], "apple");
    EXPECT_EQ(result3[1], "banana");
    EXPECT_EQ(result3[2], "cherry");

    // Test empty string
    auto result4 = utils::StringUtils::split("", ',');
    EXPECT_EQ(result4.size(), 1);
    EXPECT_EQ(result4[0], "");

    // Test no delimiter
    auto result5 = utils::StringUtils::split("apple", ',');
    EXPECT_EQ(result5.size(), 1);
    EXPECT_EQ(result5[0], "apple");
}

TEST_F(StringUtilsTest, JoinOperations) {
    std::vector<std::string> strings = {"apple", "banana", "cherry"};
    
    // Test join
    std::string result = utils::StringUtils::join(strings, ", ");
    EXPECT_EQ(result, "apple, banana, cherry");

    // Test convenience function
    std::string result2 = utils::join(strings, "-");
    EXPECT_EQ(result2, "apple-banana-cherry");

    // Test empty vector
    std::vector<std::string> empty;
    std::string result3 = utils::StringUtils::join(empty, ",");
    EXPECT_EQ(result3, "");

    // Test single element
    std::vector<std::string> single = {"apple"};
    std::string result4 = utils::StringUtils::join(single, ",");
    EXPECT_EQ(result4, "apple");
}

TEST_F(StringUtilsTest, TrimOperations) {
    // Test trim
    EXPECT_EQ(utils::StringUtils::trim("  hello  "), "hello");
    EXPECT_EQ(utils::StringUtils::trim("\t\nhello\r\n"), "hello");
    EXPECT_EQ(utils::StringUtils::trim("hello"), "hello");
    EXPECT_EQ(utils::StringUtils::trim("   "), "");

    // Test convenience function
    EXPECT_EQ(utils::trim("  hello  "), "hello");

    // Test ltrim
    EXPECT_EQ(utils::StringUtils::ltrim("  hello  "), "hello  ");
    EXPECT_EQ(utils::StringUtils::ltrim("hello"), "hello");

    // Test rtrim
    EXPECT_EQ(utils::StringUtils::rtrim("  hello  "), "  hello");
    EXPECT_EQ(utils::StringUtils::rtrim("hello"), "hello");

    // Test trim with custom characters
    EXPECT_EQ(utils::StringUtils::trim("...hello...", "."), "hello");
    EXPECT_EQ(utils::StringUtils::trim("abchelloabc", "abc"), "hello");
}

TEST_F(StringUtilsTest, CaseOperations) {
    // Test toLower
    EXPECT_EQ(utils::StringUtils::toLower("HELLO"), "hello");
    EXPECT_EQ(utils::StringUtils::toLower("Hello World"), "hello world");
    EXPECT_EQ(utils::StringUtils::toLower("123ABC"), "123abc");

    // Test convenience function
    EXPECT_EQ(utils::toLower("HELLO"), "hello");

    // Test toUpper
    EXPECT_EQ(utils::StringUtils::toUpper("hello"), "HELLO");
    EXPECT_EQ(utils::StringUtils::toUpper("Hello World"), "HELLO WORLD");
    EXPECT_EQ(utils::StringUtils::toUpper("123abc"), "123ABC");

    // Test convenience function
    EXPECT_EQ(utils::toUpper("hello"), "HELLO");

    // Test capitalize
    EXPECT_EQ(utils::StringUtils::capitalize("hello"), "Hello");
    EXPECT_EQ(utils::StringUtils::capitalize("HELLO"), "HELLO");
    EXPECT_EQ(utils::StringUtils::capitalize(""), "");

    // Test toTitleCase
    EXPECT_EQ(utils::StringUtils::toTitleCase("hello world"), "Hello World");
    EXPECT_EQ(utils::StringUtils::toTitleCase("HELLO WORLD"), "Hello World");
    EXPECT_EQ(utils::StringUtils::toTitleCase("hello-world test"), "Hello-World Test");
}

TEST_F(StringUtilsTest, SearchOperations) {
    std::string str = "Hello World";

    // Test startsWith
    EXPECT_TRUE(utils::StringUtils::startsWith(str, "Hello"));
    EXPECT_FALSE(utils::StringUtils::startsWith(str, "World"));
    EXPECT_TRUE(utils::StringUtils::startsWith(str, "hello", true)); // ignore case
    EXPECT_FALSE(utils::StringUtils::startsWith(str, "hello", false)); // case sensitive

    // Test endsWith
    EXPECT_TRUE(utils::StringUtils::endsWith(str, "World"));
    EXPECT_FALSE(utils::StringUtils::endsWith(str, "Hello"));
    EXPECT_TRUE(utils::StringUtils::endsWith(str, "world", true)); // ignore case
    EXPECT_FALSE(utils::StringUtils::endsWith(str, "world", false)); // case sensitive

    // Test contains
    EXPECT_TRUE(utils::StringUtils::contains(str, "llo"));
    EXPECT_FALSE(utils::StringUtils::contains(str, "xyz"));
    EXPECT_TRUE(utils::StringUtils::contains(str, "WORLD", true)); // ignore case
    EXPECT_FALSE(utils::StringUtils::contains(str, "WORLD", false)); // case sensitive
}

TEST_F(StringUtilsTest, ReplaceOperations) {
    std::string str = "Hello World Hello";

    // Test replace all
    EXPECT_EQ(utils::StringUtils::replace(str, "Hello", "Hi"), "Hi World Hi");
    EXPECT_EQ(utils::StringUtils::replace(str, "xyz", "abc"), str); // no match

    // Test replaceFirst
    EXPECT_EQ(utils::StringUtils::replaceFirst(str, "Hello", "Hi"), "Hi World Hello");
    EXPECT_EQ(utils::StringUtils::replaceFirst(str, "xyz", "abc"), str); // no match

    // Test replaceLast
    EXPECT_EQ(utils::StringUtils::replaceLast(str, "Hello", "Hi"), "Hello World Hi");
    EXPECT_EQ(utils::StringUtils::replaceLast(str, "xyz", "abc"), str); // no match

    // Test remove
    EXPECT_EQ(utils::StringUtils::remove(str, "Hello"), " World ");
    EXPECT_EQ(utils::StringUtils::remove(str, "xyz"), str); // no match
}

TEST_F(StringUtilsTest, PaddingOperations) {
    std::string str = "hello";

    // Test padLeft
    EXPECT_EQ(utils::StringUtils::padLeft(str, 10), "     hello");
    EXPECT_EQ(utils::StringUtils::padLeft(str, 10, '*'), "*****hello");
    EXPECT_EQ(utils::StringUtils::padLeft(str, 3), str); // no padding needed

    // Test padRight
    EXPECT_EQ(utils::StringUtils::padRight(str, 10), "hello     ");
    EXPECT_EQ(utils::StringUtils::padRight(str, 10, '*'), "hello*****");
    EXPECT_EQ(utils::StringUtils::padRight(str, 3), str); // no padding needed

    // Test padCenter
    EXPECT_EQ(utils::StringUtils::padCenter(str, 11), "   hello   ");
    EXPECT_EQ(utils::StringUtils::padCenter(str, 10), "  hello   "); // uneven padding
    EXPECT_EQ(utils::StringUtils::padCenter(str, 10, '*'), "**hello***");
    EXPECT_EQ(utils::StringUtils::padCenter(str, 3), str); // no padding needed
}

TEST_F(StringUtilsTest, UtilityOperations) {
    // Test reverse
    EXPECT_EQ(utils::StringUtils::reverse("hello"), "olleh");
    EXPECT_EQ(utils::StringUtils::reverse(""), "");
    EXPECT_EQ(utils::StringUtils::reverse("a"), "a");

    // Test repeat
    EXPECT_EQ(utils::StringUtils::repeat("abc", 3), "abcabcabc");
    EXPECT_EQ(utils::StringUtils::repeat("abc", 0), "");
    EXPECT_EQ(utils::StringUtils::repeat("", 5), "");

    // Test isBlank
    EXPECT_TRUE(utils::StringUtils::isBlank(""));
    EXPECT_TRUE(utils::StringUtils::isBlank("   "));
    EXPECT_TRUE(utils::StringUtils::isBlank("\t\n\r"));
    EXPECT_FALSE(utils::StringUtils::isBlank("hello"));
    EXPECT_FALSE(utils::StringUtils::isBlank("  hello  "));
}

TEST_F(StringUtilsTest, ValidationOperations) {
    // Test isNumeric
    EXPECT_TRUE(utils::StringUtils::isNumeric("123"));
    EXPECT_TRUE(utils::StringUtils::isNumeric("123.45"));
    EXPECT_TRUE(utils::StringUtils::isNumeric("-123.45"));
    EXPECT_TRUE(utils::StringUtils::isNumeric("1.23e10"));
    EXPECT_FALSE(utils::StringUtils::isNumeric("abc"));
    EXPECT_FALSE(utils::StringUtils::isNumeric("12.34.56"));
    EXPECT_FALSE(utils::StringUtils::isNumeric(""));

    // Test isInteger
    EXPECT_TRUE(utils::StringUtils::isInteger("123"));
    EXPECT_TRUE(utils::StringUtils::isInteger("-123"));
    EXPECT_FALSE(utils::StringUtils::isInteger("123.45"));
    EXPECT_FALSE(utils::StringUtils::isInteger("abc"));
    EXPECT_FALSE(utils::StringUtils::isInteger(""));

    // Test isAlpha
    EXPECT_TRUE(utils::StringUtils::isAlpha("hello"));
    EXPECT_TRUE(utils::StringUtils::isAlpha("HELLO"));
    EXPECT_FALSE(utils::StringUtils::isAlpha("hello123"));
    EXPECT_FALSE(utils::StringUtils::isAlpha("hello world"));
    EXPECT_FALSE(utils::StringUtils::isAlpha(""));

    // Test isAlphaNumeric
    EXPECT_TRUE(utils::StringUtils::isAlphaNumeric("hello123"));
    EXPECT_TRUE(utils::StringUtils::isAlphaNumeric("HELLO"));
    EXPECT_TRUE(utils::StringUtils::isAlphaNumeric("123"));
    EXPECT_FALSE(utils::StringUtils::isAlphaNumeric("hello world"));
    EXPECT_FALSE(utils::StringUtils::isAlphaNumeric("hello-123"));
    EXPECT_FALSE(utils::StringUtils::isAlphaNumeric(""));
}

TEST_F(StringUtilsTest, CountAndFindOperations) {
    std::string str = "Hello World Hello";

    // Test count
    EXPECT_EQ(utils::StringUtils::count(str, "Hello"), 2);
    EXPECT_EQ(utils::StringUtils::count(str, "hello", true), 2); // ignore case
    EXPECT_EQ(utils::StringUtils::count(str, "hello", false), 0); // case sensitive
    EXPECT_EQ(utils::StringUtils::count(str, "xyz"), 0);

    // Test findAll
    auto positions = utils::StringUtils::findAll(str, "Hello");
    EXPECT_EQ(positions.size(), 2);
    EXPECT_EQ(positions[0], 0);
    EXPECT_EQ(positions[1], 12);

    auto positions2 = utils::StringUtils::findAll(str, "hello", true);
    EXPECT_EQ(positions2.size(), 2);

    auto positions3 = utils::StringUtils::findAll(str, "xyz");
    EXPECT_EQ(positions3.size(), 0);
}

TEST_F(StringUtilsTest, ExtractionOperations) {
    std::string str = "Hello [World] Test";

    // Test extractBetween
    auto result = utils::StringUtils::extractBetween(str, "[", "]");
    EXPECT_TRUE(result.has_value());
    EXPECT_EQ(result.value(), "World");

    auto result2 = utils::StringUtils::extractBetween(str, "(", ")");
    EXPECT_FALSE(result2.has_value());

    auto result3 = utils::StringUtils::extractBetween("Hello World", "[", "]");
    EXPECT_FALSE(result3.has_value());

    // Test truncate
    EXPECT_EQ(utils::StringUtils::truncate("Hello World", 5), "He...");
    EXPECT_EQ(utils::StringUtils::truncate("Hello World", 15), "Hello World");
    EXPECT_EQ(utils::StringUtils::truncate("Hello World", 5, "***"), "He***");
    EXPECT_EQ(utils::StringUtils::truncate("Hello", 2, "..."), "..");
}

TEST_F(StringUtilsTest, ComparisonOperations) {
    // Test equalsIgnoreCase
    EXPECT_TRUE(utils::StringUtils::equalsIgnoreCase("Hello", "hello"));
    EXPECT_TRUE(utils::StringUtils::equalsIgnoreCase("HELLO", "hello"));
    EXPECT_FALSE(utils::StringUtils::equalsIgnoreCase("Hello", "world"));
    EXPECT_TRUE(utils::StringUtils::equalsIgnoreCase("", ""));
}

TEST_F(StringUtilsTest, RandomStringGeneration) {
    // Test random string generation
    std::string random1 = utils::StringUtils::random(10);
    std::string random2 = utils::StringUtils::random(10);
    
    EXPECT_EQ(random1.length(), 10);
    EXPECT_EQ(random2.length(), 10);
    EXPECT_NE(random1, random2); // Very unlikely to be the same

    // Test with custom charset
    std::string random3 = utils::StringUtils::random(5, "ABC");
    EXPECT_EQ(random3.length(), 5);
    for (char c : random3) {
        EXPECT_TRUE(c == 'A' || c == 'B' || c == 'C');
    }

    // Test edge cases
    EXPECT_EQ(utils::StringUtils::random(0).length(), 0);
    EXPECT_EQ(utils::StringUtils::random(5, "").length(), 0);
}

TEST_F(StringUtilsTest, EdgeCases) {
    // Test empty strings
    EXPECT_EQ(utils::StringUtils::trim(""), "");
    EXPECT_EQ(utils::StringUtils::toLower(""), "");
    EXPECT_EQ(utils::StringUtils::toUpper(""), "");
    EXPECT_EQ(utils::StringUtils::reverse(""), "");
    
    // Test single character
    EXPECT_EQ(utils::StringUtils::trim("a"), "a");
    EXPECT_EQ(utils::StringUtils::toLower("A"), "a");
    EXPECT_EQ(utils::StringUtils::toUpper("a"), "A");
    EXPECT_EQ(utils::StringUtils::reverse("a"), "a");
    
    // Test special characters
    std::string special = "!@#$%^&*()";
    EXPECT_EQ(utils::StringUtils::toLower(special), special);
    EXPECT_EQ(utils::StringUtils::toUpper(special), special);
    EXPECT_EQ(utils::StringUtils::reverse(special), ")(*&^%$#@!");
}
