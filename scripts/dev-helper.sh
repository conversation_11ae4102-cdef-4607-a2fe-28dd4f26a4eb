#!/bin/bash

# Development Helper Script (Container-side)
# This script provides common development tasks inside the container

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Check if we're in container
check_container() {
    if [ ! -f /.dockerenv ] && ! grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null; then
        log_error "This script should only be run inside the development container"
        log_info "Use './scripts/start-dev-flow.sh shell' to enter the container"
        exit 1
    fi
}

# Setup development environment
setup_dev_env() {
    log_step "Setting Up Development Environment"
    
    # Ensure we're in the right directory
    cd "$PROJECT_ROOT"
    
    # Setup GUI environment
    log_info "Setting up GUI environment..."
    if [ -f "./scripts/utils/setup-gui-env.sh" ]; then
        ./scripts/utils/setup-gui-env.sh setup
    else
        log_warning "GUI setup script not found"
    fi
    
    # Configure CMake if not done
    if [ ! -d "build" ] || [ ! -f "build/CMakeCache.txt" ]; then
        log_info "Configuring CMake build..."
        cmake -S . -B build -DCMAKE_BUILD_TYPE=Debug \
            -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
        log_success "CMake configured"
    else
        log_info "CMake already configured"
    fi
    
    # Install any additional dependencies
    if [ -f "./scripts/setup.sh" ]; then
        log_info "Running project setup..."
        ./scripts/setup.sh
    fi
    
    log_success "Development environment ready!"
}

# Build project
build_project() {
    log_step "Building Project"
    
    local build_type="${1:-Debug}"
    local jobs="${2:-$(nproc)}"
    
    cd "$PROJECT_ROOT"
    
    # Reconfigure if build type changed
    if [ -f "build/CMakeCache.txt" ]; then
        local current_type=$(grep CMAKE_BUILD_TYPE build/CMakeCache.txt | cut -d= -f2 || echo "")
        if [ "$current_type" != "$build_type" ]; then
            log_info "Build type changed from '$current_type' to '$build_type', reconfiguring..."
            rm -rf build
        fi
    fi
    
    # Configure if needed
    if [ ! -d "build" ] || [ ! -f "build/CMakeCache.txt" ]; then
        log_info "Configuring CMake for $build_type build..."
        cmake -S . -B build -DCMAKE_BUILD_TYPE="$build_type" \
            -DCMAKE_EXPORT_COMPILE_COMMANDS=ON
    fi
    
    # Build
    log_info "Building with $jobs parallel jobs..."
    cmake --build build -j"$jobs"
    
    log_success "Build completed successfully"
}

# Run tests
run_tests() {
    log_step "Running Tests"
    
    cd "$PROJECT_ROOT"
    
    if [ ! -d "build" ]; then
        log_error "Build directory not found. Run 'build' command first."
        return 1
    fi
    
    # Run CTest if available
    if [ -f "build/CTestTestfile.cmake" ]; then
        log_info "Running CTest..."
        cd build && ctest --output-on-failure
    elif [ -f "./scripts/test.sh" ]; then
        log_info "Running project test script..."
        ./scripts/test.sh
    else
        log_warning "No tests found"
    fi
}

# Format code
format_code() {
    log_step "Formatting Code"
    
    cd "$PROJECT_ROOT"
    
    if [ -f "./scripts/format.sh" ]; then
        ./scripts/format.sh
        log_success "Code formatted"
    else
        log_warning "Format script not found"
    fi
}

# Clean build
clean_build() {
    log_step "Cleaning Build"
    
    cd "$PROJECT_ROOT"
    
    if [ -d "build" ]; then
        rm -rf build
        log_success "Build directory cleaned"
    else
        log_info "Build directory doesn't exist"
    fi
    
    if [ -f "./scripts/clean.sh" ]; then
        ./scripts/clean.sh
    fi
}

# Run GUI application
run_gui_app() {
    log_step "Running GUI Application"
    
    cd "$PROJECT_ROOT"
    
    # Check GUI environment
    if ! xset q >/dev/null 2>&1; then
        log_warning "X11 not available, setting up GUI environment..."
        if [ -f "./scripts/utils/setup-gui-env.sh" ]; then
            ./scripts/utils/setup-gui-env.sh setup
        fi
    fi
    
    # Run GUI app script if available
    if [ -f "./scripts/dev/run-gui-app.sh" ]; then
        ./scripts/dev/run-gui-app.sh "$@"
    else
        log_error "GUI app script not found"
        log_info "Available executables in build/:"
        find build -type f -executable 2>/dev/null | head -10
    fi
}

# Debug application
debug_app() {
    log_step "Starting Debug Session"
    
    cd "$PROJECT_ROOT"
    
    local app_path="$1"
    local debugger="${2:-gdb}"
    
    if [ -z "$app_path" ]; then
        log_error "Please specify application path"
        log_info "Usage: debug <app_path> [gdb|lldb]"
        return 1
    fi
    
    if [ ! -f "$app_path" ]; then
        log_error "Application not found: $app_path"
        return 1
    fi
    
    log_info "Starting $debugger session for $app_path"
    
    case "$debugger" in
        gdb)
            gdb "$app_path"
            ;;
        lldb)
            lldb "$app_path"
            ;;
        *)
            log_error "Unknown debugger: $debugger"
            log_info "Supported debuggers: gdb, lldb"
            return 1
            ;;
    esac
}

# Show development status
show_dev_status() {
    log_step "Development Environment Status"
    
    cd "$PROJECT_ROOT"
    
    # Basic info
    echo "Working Directory: $(pwd)"
    echo "User: $(whoami)"
    echo "Container: $([ -f /.dockerenv ] && echo "Yes" || echo "No")"
    echo ""
    
    # Build status
    if [ -d "build" ]; then
        echo "✓ Build directory exists"
        if [ -f "build/CMakeCache.txt" ]; then
            local build_type=$(grep CMAKE_BUILD_TYPE build/CMakeCache.txt | cut -d= -f2 || echo "Unknown")
            echo "✓ CMake configured (Build type: $build_type)"
        else
            echo "⚠ CMake not configured"
        fi
        
        # Count built targets
        local exe_count=$(find build -type f -executable 2>/dev/null | wc -l)
        echo "  Built executables: $exe_count"
    else
        echo "⚠ Build directory missing"
    fi
    
    # GUI status
    echo ""
    if [ -f "./scripts/utils/setup-gui-env.sh" ]; then
        ./scripts/utils/setup-gui-env.sh status
    else
        echo "GUI Status: Unknown (setup script not found)"
    fi
    
    # Git status
    echo ""
    if git rev-parse --git-dir >/dev/null 2>&1; then
        echo "Git Status:"
        git status --porcelain | head -5
        if [ $(git status --porcelain | wc -l) -gt 5 ]; then
            echo "  ... and more"
        fi
    fi
}

# Show help
show_help() {
    cat << EOF
Development Helper Script (Container-side)
==========================================

This script provides common development tasks inside the container.

USAGE:
    $0 [COMMAND] [OPTIONS]

COMMANDS:
    setup           Setup development environment
    build [type]    Build project (Debug|Release, default: Debug)
    test            Run tests
    format          Format source code
    clean           Clean build directory
    gui [args]      Run GUI application
    debug <app>     Debug application with gdb/lldb
    status          Show development environment status
    help            Show this help

EXAMPLES:
    # Setup development environment
    $0 setup

    # Build project (Debug)
    $0 build

    # Build Release version
    $0 build Release

    # Run tests
    $0 test

    # Format code
    $0 format

    # Clean and rebuild
    $0 clean
    $0 build

    # Run GUI application
    $0 gui

    # Debug application
    $0 debug ./build/my-app

    # Check status
    $0 status

WORKFLOW:
    1. $0 setup     # First time setup
    2. $0 build     # Build project
    3. $0 test      # Run tests
    4. $0 gui       # Run GUI app
    
    # During development:
    # Edit code...
    $0 build        # Incremental build
    $0 test         # Test changes
    $0 gui          # Run updated app

NOTE: This script should only be run inside the development container.
      Use './scripts/start-dev-flow.sh shell' to enter the container.
EOF
}

# Main function
main() {
    check_container
    
    local command="${1:-help}"
    
    case "$command" in
        setup)
            setup_dev_env
            ;;
        build)
            build_project "${2:-Debug}" "${3:-$(nproc)}"
            ;;
        test)
            run_tests
            ;;
        format)
            format_code
            ;;
        clean)
            clean_build
            ;;
        gui)
            shift
            run_gui_app "$@"
            ;;
        debug)
            debug_app "$2" "$3"
            ;;
        status)
            show_dev_status
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
