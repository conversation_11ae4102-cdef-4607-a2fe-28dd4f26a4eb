# MPP Video Decoding Module - Detailed Implementation Plan

## AI Context & System Requirements

### Platform Context (RK3588 Optimized)

```
Platform: Orange Pi 5 Plus/Ultra with RK3588 SoC
- CPU: 4x Cortex-A76 @ 2.4GHz + 4x Cortex-A55 @ 1.8GHz  
- Memory: 4GB/8GB LPDDR4X-4224 (CRITICAL: Memory-constrained environment)
- Storage: 240GB SSD
- Hardware Accelerators: 
  * VPU (Video Processing Unit) - PRIMARY for MPP decoding
  * RGA (Raster Graphics Accelerator) - Integration target
  * NPU (6 TOPS) - Coordinate with face detection module
- OS: Ubuntu 22.04 LTS ARM64
- Cross-compilation: aarch64-linux-gnu toolchain required
- Thermal Management: 85°C max operating, throttle at 80°C
```

### Technology Stack Context (Hardware-First Approach)

```
MPP Decoding Technology Choices (Mandatory for RK3588):
- Video Decoding: RockChip MPP (Media Process Platform) - MANDATORY
- Memory Management: DMABUF zero-copy operations - CRITICAL
- Hardware Integration: VPU + RGA coordination
- Build System: CMake with RK3588-specific optimizations
- Threading: Hardware-aware worker pools (leverage 8-core architecture)
- Error Recovery: Hardware failure resilience required
- Performance: Hardware accelerator utilization >70% target

FORBIDDEN Technologies:
- Software-only video decoding (CPU usage too high)
- Memory copying operations (bandwidth limited)
- Generic ARM optimizations (RK3588-specific required)
```

### Resource Allocation Context (Critical Constraints)

```
MPP Decoding Resource Assignment:
- CPU Cores: Coordinate with existing allocations
  * Core 0-1 (A55): UI Client + System Services (AVOID)
  * Core 2-3 (A55): RTSP Input Module coordination
  * Core 4-7 (A76): Available for MPP worker threads
  
- Memory Allocation (STRICT LIMITS):
  * 4GB Config: MPP Decoder ≤800MB (includes DMABUF pools)
  * 8GB Config: MPP Decoder ≤1.5GB (includes DMABUF pools)
  * DMABUF Pool Sizing: Dynamic based on stream count
  
- Hardware Resource Coordination:
  * VPU: Primary user (MPP decoding)
  * RGA: Downstream integration (frame preprocessing)
  * NPU: Avoid contention with face detection module
  
- Performance Targets (Platform Limits):
  * 4GB: 8 concurrent 1080p streams max
  * 8GB: 16 concurrent 1080p streams max
  * Latency: <50ms decode time per frame
  * VPU Utilization: 70-85% optimal range
```

### Integration Context (AI Box System)

```
Module Dependencies & Interfaces:
- INPUT: RTSP Input Module (NAL units via thread-safe queues)
- OUTPUT: RGA Preprocessing Module (DMABUF file descriptors)
- COORDINATION: Face Detection/Recognition (NPU resource sharing)

Data Flow Pipeline:
RTSP → NAL Units → MPP Decode → DMABUF Frames → RGA Scale → Face AI

Resource Sharing Strategy:
- Hardware: VPU primary, RGA coordination, NPU avoidance
- Memory: DMABUF pools shared with downstream modules
- Timing: Frame synchronization across camera streams
- Error Handling: Cascade error recovery to upstream/downstream

Critical Integration Points:
1. NAL unit queue interface with RTSP module
2. DMABUF export/import with RGA module  
3. Hardware resource arbitration system-wide
4. Thermal management coordination
5. Memory pressure backpropagation
```

### Development Constraints Context

```
Mandatory Requirements:
- Hardware-in-the-Loop: All testing on actual Orange Pi 5 Plus/Ultra
- RK3588-First Development: All decisions prioritize RK3588 capabilities
- Zero-Copy Architecture: DMABUF mandatory, no frame copying
- Thermal Awareness: All algorithms must adapt to thermal throttling
- Resource Cooperation: Coordinate with other AI Box modules
- Hardware Validation: VPU, RGA, memory bandwidth testing required

Performance Constraints:
- Memory Bandwidth: Limited, zero-copy operations critical
- Thermal Budget: Shared across all modules, adaptive scaling required
- Power Efficiency: Embedded platform, optimize for sustained operation
- Hardware Failure: Graceful degradation, recovery mechanisms mandatory

Quality Gates:
- Orange Pi hardware validation for every major component
- Thermal cycling tests (idle → max load → thermal throttling)
- Memory leak detection under 72-hour stress testing  
- Integration testing with full AI Box pipeline
- Performance profiling under real-world camera loads
```

### Current Project Status Context

```
MPP Decoding Module Status:
- Phase: Implementation Planning (Phase 1 pending)
- Dependencies: RTSP Input Module (NAL unit output interface)
- Critical Path: Foundation → Core Implementation → RTSP Integration
- Hardware Access: Orange Pi 5 Plus/Ultra required for validation

Key Decisions Made:
1. RockChip MPP library as primary decoder (hardware acceleration)
2. DMABUF zero-copy memory architecture 
3. Worker thread pool design for 8-core RK3588
4. Hardware resource coordination framework
5. Thermal-aware adaptive performance scaling

Integration Requirements:
- RTSP Module: NAL unit queue interface design complete
- RGA Module: DMABUF file descriptor handoff protocol
- Face AI Modules: Hardware resource arbitration system
- System Monitor: Thermal and performance telemetry integration
```

### Risk Context (Critical for RK3588)

```
Critical Risks (Address Immediately):
- R2: DMABUF zero-copy implementation complexity (HIGH IMPACT)
- R1: MPP hardware driver compatibility issues
- R3: VPU resource contention with multiple streams

High Priority Risks:
- R4: Memory bandwidth limitations on 4GB variant
- R5: Thermal throttling under sustained load
- Hardware failure cascading to dependent modules

Mitigation Strategies (Mandatory):
- Early prototype testing on Orange Pi hardware
- DMABUF fallback to memory copy mode (performance degradation)
- Hardware resource scheduling with priority system
- Thermal monitoring with adaptive quality reduction
- Software fallback mechanisms for hardware failures

Testing Requirements:
- All components tested on target Orange Pi hardware
- Thermal cycling validation (cold boot → sustained load)
- Memory pressure testing at platform limits
- Hardware failure simulation and recovery validation
```

## Task Overview Summary

| Task ID                            | Task Name                      | Priority | Status    | Estimated Hours | Dependencies | Assignee      | Platform Focus      |
| ---------------------------------- | ------------------------------ | -------- | --------- | --------------- | ------------ | ------------- | ------------------- |
| **Phase 1: Foundation Setup**      |                                |          |           | **4-7 hours**   |              |               |                     |
| 1.1                                | Project Structure Creation     | Critical | ⏳ Pending | 2-3h            | RTSP Module  | Lead Dev      | RK3588 MPP System   |
| 1.2                                | CMake & MPP Dependencies Setup | Critical | ⏳ Pending | 2-4h            | 1.1          | DevOps        | RockChip + DMABUF   |
| **Phase 2: Core Implementation**   |                                |          |           | **45-65 hours** |              |               |                     |
| 2.1                                | MPP Context Manager            | Critical | ⏳ Pending | 8-12h           | 1.2          | Video Dev     | VPU Optimization    |
| 2.2                                | DMABUF Manager                 | Critical | ⏳ Pending | 10-14h          | 2.1          | System Dev    | Zero-Copy Pipeline  |
| 2.3                                | Decoder Worker Threads         | Critical | ⏳ Pending | 12-16h          | 2.1,2.2      | Performance   | Async Processing    |
| 2.4                                | Frame Output Manager           | High     | ⏳ Pending | 8-12h           | 2.3          | Pipeline Dev  | Format Management   |
| 2.5                                | Error Recovery System          | High     | ⏳ Pending | 7-11h           | Phase 2      | System Dev    | Hardware Resilience |
| **Phase 3: Integration & Testing** |                                |          |           | **18-26 hours** |              |               |                     |
| 3.1                                | RTSP Integration               | Critical | ⏳ Pending | 6-8h            | Phase 2      | Integration   | Pipeline Connection |
| 3.2                                | Performance Optimization       | High     | ⏳ Pending | 8-12h           | 3.1          | Perf Engineer | VPU + Memory Tuning |
| 3.3                                | Unit Testing Suite             | Critical | ⏳ Pending | 4-6h            | Phase 2      | QA + Devs     | Hardware Validation |
| **Phase 4: Advanced Features**     |                                |          |           | **16-24 hours** |              |               |                     |
| 4.1                                | Multi-Stream Coordination      | Medium   | ⏳ Pending | 8-12h           | Phase 3      | Architect     | Resource Scheduling |
| 4.2                                | Dynamic Quality Control        | Medium   | ⏳ Pending | 8-12h           | 4.1          | Video Dev     | Adaptive Decoding   |
| **Phase 5: Production Readiness**  |                                |          |           | **12-18 hours** |              |               |                     |
| 5.1                                | Stress Testing & Validation    | High     | ⏳ Pending | 8-12h           | Phase 4      | QA Engineer   | Hardware Limits     |
| 5.2                                | Documentation & Examples       | Medium   | ⏳ Pending | 4-6h            | Phase 4      | Tech Writer   | MPP Integration     |

### Status Legend

- ⏳ **Pending**: Not started
- 🔄 **In Progress**: Currently being worked on
- ✅ **Completed**: Task finished and tested
- ⚠️ **Blocked**: Waiting for dependencies or resources
- ❌ **Failed**: Task failed and needs rework

### Resource Allocation Summary

- **Total Estimated Time**: 95-140 hours (7-10 weeks)
- **Critical Path**: Tasks 1.1 → 1.2 → 2.1 → 2.2 → 2.3 → 3.1 → 4.1 → 5.1
- **RK3588 VPU Focus**: 90% of tasks include hardware-specific optimizations
- **Hardware Testing Required**: Tasks 2.1, 2.2, 2.3, 3.2, 4.1, 5.1

## Milestone Tracking

| Milestone                        | Target Date | Status    | Completion % | Key Deliverables                                     | Risk Level |
| -------------------------------- | ----------- | --------- | ------------ | ---------------------------------------------------- | ---------- |
| **M1: Foundation Complete**      | Week 1      | ⏳ Pending | 0%           | Build system, MPP integration, basic structure       | 🟢 Low      |
| **M2: Core Decoding Functional** | Week 4      | ⏳ Pending | 0%           | MPP contexts, DMABUF, basic H.264/H.265 decoding     | 🟠 High     |
| **M3: Pipeline Integration**     | Week 6      | ⏳ Pending | 0%           | RTSP→MPP pipeline, multi-stream support              | 🟡 Medium   |
| **M4: Advanced Features**        | Week 8      | ⏳ Pending | 0%           | Dynamic quality, resource coordination, optimization | 🟡 Medium   |
| **M5: Production Ready**         | Week 10     | ⏳ Pending | 0%           | Stress testing, validation, documentation            | 🟢 Low      |

### Milestone Success Criteria

#### M1: Foundation Complete

- [ ] CMake builds successfully with MPP dependencies
- [ ] RockChip MPP library linked and functional
- [ ] DMABUF support detected and configured
- [ ] Basic project structure created following patterns

#### M2: Core Decoding Functional

- [ ] Single H.264 stream decodes using MPP hardware acceleration
- [ ] DMABUF zero-copy pipeline working
- [ ] VPU utilization >60% for test streams
- [ ] Memory usage within platform limits (4GB/8GB variants)

#### M3: Pipeline Integration

- [ ] RTSP NAL units processed into decoded frames
- [ ] Multiple concurrent streams (8 for 4GB, 16 for 8GB)
- [ ] Integration with downstream RGA preprocessing
- [ ] Frame timing and synchronization working

#### M4: Advanced Features

- [ ] Dynamic quality adaptation based on system load
- [ ] Resource coordination between multiple streams
- [ ] Performance optimization targets met
- [ ] Error recovery mechanisms validated

#### M5: Production Ready

- [ ] 72-hour stress test passed on Orange Pi hardware
- [ ] All quality and performance targets achieved
- [ ] Complete documentation and integration guides
- [ ] RK3588 VPU compliance validation 100%

## Risk Assessment & Mitigation

| Risk ID | Risk Description                               | Probability | Impact | Risk Level | Mitigation Strategy                                      | Owner        |
| ------- | ---------------------------------------------- | ----------- | ------ | ---------- | -------------------------------------------------------- | ------------ |
| **R1**  | MPP hardware acceleration driver compatibility | Medium      | High   | 🟠 High     | Early hardware testing, software fallback implementation | Video Dev    |
| **R2**  | DMABUF zero-copy implementation complexity     | High        | High   | 🔴 Critical | Incremental implementation, copy fallback mode           | System Dev   |
| **R3**  | VPU resource contention with multiple streams  | Medium      | High   | 🟠 High     | Resource scheduling algorithm, dynamic load balancing    | Performance  |
| **R4**  | Memory bandwidth limitations on 4GB variant    | High        | Medium | 🟡 Medium   | Memory optimization, adaptive stream quality             | Architect    |
| **R5**  | Thermal throttling under sustained load        | Medium      | Medium | 🟡 Medium   | Thermal monitoring, adaptive performance scaling         | System Dev   |
| **R6**  | Decoder synchronization and timing issues      | Low         | High   | 🟡 Medium   | Robust timing implementation, comprehensive testing      | Pipeline Dev |
| **R7**  | Hardware decoder failure and recovery          | Low         | High   | 🟡 Medium   | Error detection, automatic recovery mechanisms           | System Dev   |
| **R8**  | Integration complexity with existing modules   | Medium      | Medium | 🟡 Medium   | Clear interface design, incremental integration testing  | Integration  |

### Risk Mitigation Actions

#### Critical Priority (Address Immediately)

- **R2**: Design DMABUF architecture with copy fallback from day one
- **R1**: Set up MPP hardware testing environment and validation suite
- **R3**: Design VPU resource allocation framework with priority system

#### High Priority (Address in Phase 1-2)

- **R4**: Implement memory pressure monitoring and adaptive algorithms
- **R5**: Develop thermal-aware performance scaling system
- **R6**: Create comprehensive timing and synchronization test suite

#### Medium Priority (Monitor and Address as Needed)

- **R7**: Design robust error detection and recovery mechanisms
- **R8**: Establish clear module interfaces and integration protocols

## Quick Reference

### Current Status Dashboard

```
📊 Overall Progress: 0% (0/12 tasks completed)
🎯 Current Phase: Phase 1 - Foundation Setup
⏰ Next Milestone: M1 - Foundation Complete (Week 1)
🔥 Critical Path: Task 1.1 (Project Structure Creation)
⚠️ Top Risk: DMABUF zero-copy implementation complexity
🏗️ Platform Focus: RK3588 VPU and hardware acceleration
```

### Key Contacts

- **Technical Lead**: [Name] - Overall architecture and RK3588 optimization
- **Video Developer**: [Name] - MPP integration and codec handling
- **System Developer**: [Name] - DMABUF and hardware integration
- **Performance Engineer**: [Name] - VPU optimization and resource management
- **QA Engineer**: [Name] - Hardware validation and stress testing

### Quick Commands

```bash
# Check task status
grep -E "⏳|🔄|✅|⚠️|❌" implementation-plan.md

# Update task status (example)
sed -i 's/| 2.1 | .* | ⏳ Pending |/| 2.1 | ... | 🔄 In Progress |/' implementation-plan.md

# View critical path
grep -A1 -B1 "Critical\|High" implementation-plan.md
```

## Executive Summary

This document provides a comprehensive implementation plan for the MPP Video Decoding Module optimized for Orange Pi 5 Plus/Ultra with RK3588 SoC. The module will leverage hardware video decoding capabilities through RockChip's Media Process Platform (MPP) to decode H.264/H.265 video streams with zero-copy DMABUF integration.

## Project Structure Analysis

### Current Architecture (RK3588 VPU Optimized)

- **Platform**: Orange Pi 5 Plus/Ultra with RK3588 SoC and VPU
- **Integration Point**: `libraries/mpp-decoder` (new module)
- **Hardware Acceleration**: RK3588 VPU, DMABUF, DRM allocator
- **Primary Dependencies**: RockChip MPP library, DRM/DRI, Linux DMABUF
- **Memory Architecture**: Zero-copy pipeline with DMABUF sharing
- **Threading Model**: Worker threads with hardware resource coordination

### Integration Points

- Input from RTSP module's NAL unit queues
- Output to RGA preprocessing module via DMABUF file descriptors
- Hardware resource coordination with other VPU users
- Integration with existing error handling and logging systems
- Configuration management following project patterns

## Detailed Task Breakdown

### Phase 1: Project Structure Setup

#### Task 1.1: Create Library Structure

**Priority**: Critical  
**Estimated Time**: 2-3 hours  
**Dependencies**: RTSP Module completion

**Files to Create:**

```
libraries/mpp-decoder/
├── CMakeLists.txt
├── include/
│   └── mpp_decoder/
│       ├── mpp_context_manager.hpp
│       ├── dmabuf_manager.hpp
│       ├── decoder_worker.hpp
│       ├── frame_output_manager.hpp
│       ├── error_recovery.hpp
│       ├── mpp_decoder.hpp
│       └── mpp_types.hpp
├── src/
│   ├── mpp_context_manager.cpp
│   ├── dmabuf_manager.cpp
│   ├── decoder_worker.cpp
│   ├── frame_output_manager.cpp
│   ├── error_recovery.cpp
│   └── mpp_decoder.cpp
└── tests/
    ├── CMakeLists.txt
    ├── test_mpp_context.cpp
    ├── test_dmabuf_manager.cpp
    ├── test_decoder_worker.cpp
    └── test_integration.cpp
```

**Configuration Requirements:**

- CMake integration with RockChip MPP library detection
- DMABUF and DRM library linking
- ARM64 cross-compilation support optimized for RK3588
- VPU hardware feature detection and configuration
- Memory management optimized for embedded platform constraints
- Integration with existing shared utilities and logging

#### Task 1.2: Update Root CMakeLists.txt and Dependencies

**Priority**: Critical  
**Estimated Time**: 2-4 hours  
**Dependencies**: Task 1.1

**Changes Required:**

- Add `add_subdirectory(libraries/mpp-decoder)` to root CMakeLists.txt
- Configure RockChip MPP library detection and linking
- Set up DMABUF and DRM dependencies
- Configure VPU hardware feature detection
- Ensure proper linking with RTSP input module
- Add RK3588-specific compiler optimizations
- Configure memory management for 4GB/8GB RAM variants

### Phase 2: Core Components Implementation

#### Task 2.1: MPP Context Manager Implementation

**Priority**: Critical  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 1.2

**Implementation Details:**

```cpp
// mpp_context_manager.hpp
struct MPPContextConfig {
    MppCodingType codec_type;  // MPP_VIDEO_CodingAVC, MPP_VIDEO_CodingHEVC
    uint32_t buffer_count;
    uint32_t buffer_size;
    bool enable_split_mode;
    bool enable_error_resilience;
    uint32_t max_width;
    uint32_t max_height;
};

class MPPContextManager {
public:
    struct DecoderContext {
        MppCtx ctx;
        MppApi *mpi;
        MppBufferGroup buffer_group;
        std::string camera_id;
        MppCodingType codec_type;
        std::atomic<bool> is_healthy;
        std::chrono::steady_clock::time_point last_activity;
    };
    
    std::shared_ptr<DecoderContext> createContext(
        const std::string& camera_id, 
        MppCodingType codec_type);
    
    void destroyContext(const std::string& camera_id);
    bool resetContext(const std::string& camera_id);
    std::shared_ptr<DecoderContext> getContext(const std::string& camera_id);
    
private:
    std::unordered_map<std::string, std::shared_ptr<DecoderContext>> contexts_;
    std::shared_mutex contexts_mutex_;
};
```

**Features:**

- Context pooling for multiple camera streams
- Codec-specific configuration (H.264/H.265)
- Hardware resource management and monitoring
- Error detection and context health tracking
- VPU utilization optimization
- Dynamic context creation and cleanup

#### Task 2.2: DMABUF Manager Implementation

**Priority**: Critical  
**Estimated Time**: 10-14 hours  
**Dependencies**: Task 2.1

**Core Functionality:**

```cpp
// dmabuf_manager.hpp
struct DMABUFBuffer {
    int fd;
    uint32_t size;
    uint32_t width;
    uint32_t height;
    uint32_t format;
    uint32_t stride;
    void* mapped_ptr;
    std::atomic<int> ref_count;
    std::chrono::steady_clock::time_point created_time;
};

class DMABUFManager {
public:
    std::shared_ptr<DMABUFBuffer> allocateBuffer(
        uint32_t width, uint32_t height, uint32_t format);
    
    std::shared_ptr<DMABUFBuffer> importBuffer(int fd);
    int exportBuffer(const std::shared_ptr<DMABUFBuffer>& buffer);
    
    void registerFrameBuffer(int fd, const MppFrameFormat& info);
    void releaseBuffer(const std::shared_ptr<DMABUFBuffer>& buffer);
    
    // Buffer pool management
    void createBufferPool(uint32_t width, uint32_t height, 
                         uint32_t format, size_t pool_size);
    std::shared_ptr<DMABUFBuffer> getFromPool(
        uint32_t width, uint32_t height, uint32_t format);
    
private:
    struct BufferPool {
        std::queue<std::shared_ptr<DMABUFBuffer>> available;
        std::vector<std::shared_ptr<DMABUFBuffer>> allocated;
        std::mutex mutex;
    };
    
    std::unordered_map<std::string, BufferPool> buffer_pools_;
    int drm_fd_;
};
```

**Key Features:**

- Zero-copy buffer management with DMABUF
- Reference counting for safe buffer sharing
- Buffer pools for different frame sizes and formats
- DRM allocator integration for optimal performance
- Memory pressure monitoring and adaptive allocation
- File descriptor lifecycle management

#### Task 2.3: Decoder Worker Threads Implementation

**Priority**: Critical  
**Estimated Time**: 12-16 hours  
**Dependencies**: Tasks 2.1, 2.2

**Core Functionality:**

```cpp
// decoder_worker.hpp
class DecoderWorker {
public:
    struct DecodeTask {
        std::string camera_id;
        std::vector<uint8_t> nal_data;
        uint64_t timestamp;
        uint32_t sequence_number;
        MppCodingType codec_type;
    };
    
    struct DecodedFrame {
        std::shared_ptr<DMABUFBuffer> buffer;
        uint64_t timestamp;
        uint32_t sequence_number;
        std::string camera_id;
        MppFrameFormat format;
        bool is_keyframe;
    };
    
    void start(size_t worker_count);
    void stop();
    
    void submitDecodeTask(const DecodeTask& task);
    bool getDecodedFrame(DecodedFrame& frame, std::chrono::milliseconds timeout);
    
private:
    void workerThreadFunction(size_t worker_id);
    bool processDecodeTask(const DecodeTask& task);
    void handleDecodeError(const std::string& camera_id, int error_code);
    
    std::vector<std::thread> worker_threads_;
    ThreadSafeQueue<DecodeTask> input_queue_;
    ThreadSafeQueue<DecodedFrame> output_queue_;
    std::atomic<bool> should_stop_;
    
    std::shared_ptr<MPPContextManager> context_manager_;
    std::shared_ptr<DMABUFManager> dmabuf_manager_;
};
```

**Threading Architecture:**

- Worker thread pool for parallel decoding
- Lock-free queues for high-performance task distribution
- Asynchronous decoding with MPP hardware acceleration
- Work stealing for optimal load balancing
- Error handling and recovery per worker thread

#### Task 2.4: Frame Output Manager Implementation

**Priority**: High  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 2.3

**Core Functionality:**

```cpp
// frame_output_manager.hpp
class FrameOutputManager {
public:
    struct FrameMetadata {
        std::string camera_id;
        uint64_t timestamp;
        uint32_t sequence_number;
        uint32_t width;
        uint32_t height;
        uint32_t format;
        bool is_keyframe;
        std::chrono::steady_clock::time_point decode_time;
    };
    
    struct OutputFrame {
        std::shared_ptr<DMABUFBuffer> buffer;
        FrameMetadata metadata;
    };
    
    void submitFrame(const DecodedFrame& frame);
    bool getFrame(const std::string& camera_id, OutputFrame& frame, 
                  std::chrono::milliseconds timeout);
    
    void enableSynchronization(const std::vector<std::string>& camera_ids);
    void configureSynchronizationWindow(std::chrono::milliseconds window);
    
private:
    struct CameraStream {
        ThreadSafeQueue<OutputFrame> frame_queue;
        std::chrono::steady_clock::time_point last_frame_time;
        uint64_t frame_count;
        std::atomic<bool> is_active;
    };
    
    std::unordered_map<std::string, CameraStream> camera_streams_;
    std::shared_mutex streams_mutex_;
    
    // Frame synchronization
    bool sync_enabled_;
    std::vector<std::string> sync_camera_ids_;
    std::chrono::milliseconds sync_window_;
    std::mutex sync_mutex_;
};
```

**Features:**

- Per-camera output queue management
- Frame metadata preservation and enrichment
- Multi-camera synchronization capabilities
- Backpressure handling and flow control
- Performance monitoring and statistics
- Format validation and conversion

#### Task 2.5: Error Recovery System Implementation

**Priority**: High  
**Estimated Time**: 7-11 hours  
**Dependencies**: Phase 2 core components

**Implementation Details:**

```cpp
// error_recovery.hpp
enum class MPPErrorType {
    HARDWARE_FAILURE,
    CORRUPTED_BITSTREAM,
    BUFFER_OVERFLOW,
    TIMEOUT,
    RESOURCE_EXHAUSTION,
    THERMAL_THROTTLING
};

class ErrorRecoveryManager {
public:
    enum class RecoveryAction {
        RESET_DECODER,
        RECREATE_CONTEXT,
        DROP_FRAMES,
        REDUCE_QUALITY,
        RESTART_WORKER,
        FATAL_ERROR
    };
    
    RecoveryAction handleError(const std::string& camera_id, 
                              MPPErrorType error_type,
                              const std::string& details);
    
    void monitorDecoderHealth();
    void implementThermalProtection();
    
private:
    struct ErrorStatistics {
        std::atomic<uint32_t> total_errors;
        std::atomic<uint32_t> hardware_errors;
        std::atomic<uint32_t> bitstream_errors;
        std::chrono::steady_clock::time_point last_error;
    };
    
    std::unordered_map<std::string, ErrorStatistics> camera_stats_;
    std::shared_mutex stats_mutex_;
};
```

### Phase 3: Integration and Testing

#### Task 3.1: RTSP Integration

**Priority**: Critical  
**Estimated Time**: 6-8 hours  
**Dependencies**: Phase 2 completion

**Integration Points:**

- Connect to RTSP module's NAL unit output queues
- Implement codec detection from SPS/PPS parameter sets
- Handle stream format changes and decoder reconfiguration
- Coordinate timing and synchronization between modules
- Implement backpressure handling from RTSP to MPP

#### Task 3.2: Performance Optimization

**Priority**: High  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 3.1

**Optimization Areas:**

- VPU utilization maximization through resource scheduling
- Memory bandwidth optimization with DMABUF sharing
- Threading model optimization for RK3588 architecture
- Cache-friendly data structures and memory access patterns
- Hardware-specific optimizations for H.264/H.265 decoding

#### Task 3.3: Unit Testing Suite

**Priority**: Critical  
**Estimated Time**: 4-6 hours  
**Dependencies**: Phase 2 completion

**Test Coverage:**

- MPP context creation and management
- DMABUF allocation and sharing
- Decoder worker functionality
- Error recovery mechanisms
- Performance benchmarks and stress tests

### Phase 4: Advanced Features

#### Task 4.1: Multi-Stream Coordination

**Priority**: Medium  
**Estimated Time**: 8-12 hours  
**Dependencies**: Phase 3 completion

**Features:**

- Dynamic resource allocation between streams
- Priority-based decoding scheduling
- Load balancing across VPU instances
- Quality adaptation based on system resources
- Thermal-aware performance scaling

#### Task 4.2: Dynamic Quality Control

**Priority**: Medium  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 4.1

**Implementation:**

- Real-time quality assessment and adaptation
- Bitrate and resolution scaling based on performance
- Frame dropping strategies for overload conditions
- Quality metrics collection and reporting
- Adaptive algorithms for optimal resource utilization

### Phase 5: Production Readiness

#### Task 5.1: Stress Testing & Validation

**Priority**: High  
**Estimated Time**: 8-12 hours  
**Dependencies**: Phase 4 completion

**Testing Requirements:**

- 72-hour continuous operation stress test
- Maximum concurrent stream testing
- Thermal cycling and performance validation
- Memory leak detection and resource monitoring
- Hardware failure simulation and recovery testing

#### Task 5.2: Documentation & Examples

**Priority**: Medium  
**Estimated Time**: 4-6 hours  
**Dependencies**: Phase 4 completion

**Documentation Requirements:**

- MPP integration and configuration guide
- DMABUF usage and best practices
- Performance tuning and optimization guide
- Error handling and troubleshooting guide
- Example applications and use cases

## Configuration Requirements

### Build Dependencies

```cmake
# RockChip MPP library
find_library(MPP_LIBRARY
    NAMES mpp
    PATHS /usr/lib/aarch64-linux-gnu
    REQUIRED)

# DMABUF and DRM libraries
find_package(PkgConfig REQUIRED)
pkg_check_modules(DRM REQUIRED libdrm)

# Threading support
find_package(Threads REQUIRED)

# Hardware detection
check_include_file("linux/dma-buf.h" HAVE_DMABUF)
```

### Runtime Configuration

```json
{
  "mpp_decoder": {
    "max_concurrent_contexts": 16,
    "worker_thread_count": 8,
    "buffer_pool_sizes": {
      "1920x1080": 32,
      "1280x720": 16,
      "640x480": 8
    },
    "dmabuf_config": {
      "enable_zero_copy": true,
      "buffer_alignment": 64,
      "max_buffer_count": 128
    },
    "performance": {
      "enable_thermal_protection": true,
      "max_vpu_utilization": 85,
      "frame_drop_threshold": 90
    }
  }
}
```

## Success Criteria

### Functional Requirements

- [ ] Hardware-accelerated H.264/H.265 decoding using RK3588 VPU
- [ ] Zero-copy DMABUF pipeline with downstream modules
- [ ] Support for 16+ concurrent streams on 8GB platform
- [ ] Robust error recovery and hardware failure handling
- [ ] Integration with RTSP input module

### Performance Requirements

- [ ] VPU utilization >70% for optimal throughput
- [ ] Decoding latency <50ms for 1080p streams
- [ ] Memory usage <4GB for 16 concurrent 1080p streams
- [ ] Support 16 streams @ 1080p30 or 32 streams @ 720p30
- [ ] 99.5% uptime with hardware error recovery

### Quality Requirements

- [ ] Comprehensive unit and integration test coverage (>95%)
- [ ] Hardware validation on Orange Pi 5 Plus/Ultra
- [ ] Stress testing under sustained load
- [ ] Documentation completeness for MPP integration
- [ ] Code review and RK3588 compliance validation

## Timeline Estimation

| Phase                          | Duration  | Dependencies |
| ------------------------------ | --------- | ------------ |
| Phase 1: Foundation Setup      | 1 week    | RTSP Module  |
| Phase 2: Core Implementation   | 3-4 weeks | Phase 1      |
| Phase 3: Integration & Testing | 2-3 weeks | Phase 2      |
| Phase 4: Advanced Features     | 2 weeks   | Phase 3      |
| Phase 5: Production Readiness  | 1-2 weeks | Phase 4      |

**Total Estimated Duration: 9-12 weeks**

## Next Steps

1. **Immediate Actions (Week 1)**
   - Set up project structure and MPP dependencies (Tasks 1.1, 1.2)
   - Begin MPP context manager implementation (Task 2.1)
   - Set up hardware testing environment

2. **Short-term Goals (Weeks 2-5)**
   - Complete core component implementation
   - Implement DMABUF zero-copy pipeline
   - Begin RTSP integration testing

3. **Medium-term Goals (Weeks 6-9)**
   - Complete integration with RTSP module
   - Implement advanced features and optimization
   - Comprehensive testing and validation

4. **Long-term Goals (Weeks 10-12)**
   - Production readiness validation
   - Documentation completion
   - Deployment and performance validation

This implementation plan provides a comprehensive roadmap for developing the MPP Video Decoding Module with hardware acceleration, zero-copy DMABUF integration, and robust error handling optimized for RK3588 platform capabilities. 