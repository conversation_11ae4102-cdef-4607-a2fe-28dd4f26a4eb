#pragma once

#include <httplib.h>
#include <memory>
#include <utils/json_utils.hpp>
#include "base_controller.hpp"
#include "handlers/inference_handler.hpp"

namespace server::controllers {

/**
 * @brief Controller for AI inference endpoints
 * Handles HTTP requests for ArcFace and YOLO inference
 */
class InferenceController : public BaseController {
public:
    InferenceController(std::shared_ptr<shared::ServerConfig> config,
                       std::shared_ptr<handlers::InferenceHandler> inference_handler);
    ~InferenceController() override = default;

    // HTTP endpoint handlers
    void handleArcFaceInference(const httplib::Request& req, httplib::Response& res);
    void handleYoloDetection(const httplib::Request& req, httplib::Response& res);

    // Async endpoint handlers (for future use)
    void handleArcFaceInferenceAsync(const httplib::Request& req, httplib::Response& res);
    void handleYoloDetectionAsync(const httplib::Request& req, httplib::Response& res);

    // Route registration helper
    void registerRoutes(httplib::Server& server);

private:
    std::shared_ptr<handlers::InferenceHandler> inference_handler_;

    // HTTP processing helpers
    utils::JsonValue parseRequestBody(const httplib::Request& req, httplib::Response& res);
    void sendJsonResponse(httplib::Response& res, const utils::JsonValue& json_response);
};

} // namespace server::controllers
