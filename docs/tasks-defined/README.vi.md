# Hệ Thống Phát Hiện và Nhận Diện <PERSON>huôn Mặt C-AIBox: <PERSON><PERSON><PERSON>c Tác <PERSON>ụ

<PERSON>hư mục này chứa các định nghĩa tác vụ chi tiết để triển khai hệ thống phát hiện và nhận diện khuôn mặt hiệu suất cao trên NPU RK3588 với đầu vào camera RTSP sử dụng C++.

## Tổng Quan Hệ Thống

Hệ thống được thiết kế như một pipeline với nhiều module chuyên biệt, mỗi module chịu trách nhiệm cho một phần cụ thể của chuỗi xử lý. Các module được thiết kế để hoạt động cùng nhau một cách hiệu quả trong khi tận dụng tối đa kiến trúc tính toán không đồng nhất của RK3588, bao gồm:

- VPU (Video Processing Unit) cho giải mã video tăng tốc phần cứng
- RGA (2D Raster Graphics Accelerator) cho tiền xử lý hình ảnh
- NPU (Neural Processing Unit) cho suy luận AI
- CPU cho logic ứng dụng và điều phối

## Kiến Trúc Pipeline

Hệ thống tuân theo kiến trúc pipeline modular với luồng dữ liệu như sau:

```
Camera RTSP → Module Đầu Vào RTSP → Module Giải Mã MPP → Module Tiền Xử Lý → 
Module Phát Hiện Khuôn Mặt → Module Trích Xuất Khuôn Mặt → Module Nhận Diện Khuôn Mặt → Logic Ứng Dụng
```

Mỗi module được thiết kế để tối đa hóa tăng tốc phần cứng và triển khai các thao tác zero-copy khi có thể sử dụng DMABUF.

## Cấu Trúc Tác Vụ

Việc triển khai được chia thành 7 tác vụ chính, mỗi tác vụ có thư mục riêng:

1. **Module Đầu Vào RTSP**: Xử lý kết nối đến nhiều luồng camera RTSP
2. **Module Giải Mã MPP**: Giải mã video H.264/H.265 sử dụng tăng tốc phần cứng
3. **Module Tiền Xử Lý**: Chuẩn bị khung hình video cho đầu vào mạng neural sử dụng RGA
4. **Module Phát Hiện Khuôn Mặt**: Chạy các mô hình phát hiện khuôn mặt trên NPU
5. **Module Trích Xuất Khuôn Mặt**: Trích xuất và tiền xử lý vùng khuôn mặt cho nhận diện
6. **Module Nhận Diện Khuôn Mặt**: Tạo embedding khuôn mặt sử dụng NPU
7. **Module Logic Ứng Dụng**: Xử lý khớp khuôn mặt, theo dõi và logic nghiệp vụ

Mỗi thư mục tác vụ chứa một tài liệu định nghĩa tác vụ chi tiết bao gồm:
- Tổng quan và yêu cầu
- Phương pháp kỹ thuật
- Giao diện (đầu vào và đầu ra)
- Phụ thuộc
- Cân nhắc về hiệu suất

## Chiến Lược Triển Khai

Để đạt hiệu suất tối ưu, việc triển khai nên tập trung vào:

1. **Luồng Dữ Liệu Zero-Copy**: Sử dụng DMABUF trong toàn bộ pipeline để giảm thiểu sử dụng băng thông bộ nhớ
2. **Sử Dụng NPU Đa Lõi**: Phân phối khối lượng công việc một cách hợp lý trên cả ba lõi NPU
3. **Xử Lý Batch**: Xử lý nhiều đầu vào đồng thời khi có thể
4. **Thao Tác Bất Đồng Bộ**: Sử dụng các thao tác không chặn để tối đa hóa thông lượng
5. **Quản Lý Thread**: Triển khai thread pool và work queue hiệu quả

## Lựa Chọn Mô Hình

Hệ thống sử dụng các mô hình được tối ưu hóa và lượng tử hóa để đạt hiệu suất tối ưu:
- **Phát Hiện Khuôn Mặt**: RetinaFace (biến thể mobile, lượng tử hóa INT8)
- **Nhận Diện Khuôn Mặt**: ArcFace/FaceNet (lượng tử hóa INT8)

## Quản Lý Tài Nguyên

Cần chú ý đặc biệt đến:
- Giới hạn bộ nhớ địa chỉ 4GB của NPU
- Phân bổ lõi hiệu quả giữa các tác vụ phát hiện và nhận diện
- Cân bằng giữa độ chính xác và hiệu suất thông qua lượng tử hóa mô hình phù hợp
- Giám sát và điều chỉnh thích ứng pipeline dưới tải nặng

## Các Bước Tiếp Theo

Sau khi xem xét các định nghĩa tác vụ này, việc triển khai nên tiến hành theo thứ tự của pipeline, với trọng tâm ban đầu là thiết lập framework cơ bản và luồng dữ liệu, sau đó là các tối ưu hóa về hiệu suất và độ chính xác.
