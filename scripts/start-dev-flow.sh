#!/bin/bash

# Development Environment Flow Script
# This script orchestrates the complete development environment setup
# Clearly distinguishes between HOST and CONTAINER operations

set -e

# Script metadata
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
CONTAINER_NAME="c-aibox-dev"
IMAGE_NAME="c-aibox-dev"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_host() {
    echo -e "${CYAN}[HOST]${NC} $1"
}

log_container() {
    echo -e "${GREEN}[CONTAINER]${NC} $1"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${BLUE}=== $1 ===${NC}"
}

# Check if running on host or in container
is_container() {
    if [ -f /.dockerenv ] || grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Host system checks
check_host_prerequisites() {
    log_step "Checking Host Prerequisites"
    
    # Check Docker
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker is not installed"
        return 1
    fi
    log_host "✓ Docker is available"
    
    # Check Docker daemon
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker daemon is not running"
        return 1
    fi
    log_host "✓ Docker daemon is running"
    
    # Check user permissions
    if ! docker ps >/dev/null 2>&1; then
        log_warning "User may not be in docker group. You might need sudo."
    else
        log_host "✓ Docker permissions OK"
    fi
    
    # Check X11
    if [ -n "$DISPLAY" ]; then
        if xset q >/dev/null 2>&1; then
            log_host "✓ X11 server is available on $DISPLAY"
        else
            log_warning "DISPLAY is set but X11 server is not responding"
        fi
    else
        log_warning "DISPLAY not set - GUI applications may not work"
    fi
    
    # Check DRI devices
    if [ -d "/dev/dri" ]; then
        log_host "✓ DRI devices available:"
        ls -la /dev/dri/ | sed 's/^/    /'
        
        # Check/create render node
        if [ ! -c "/dev/dri/renderD128" ]; then
            log_warning "Render node missing, attempting to create..."
            if [ -f "$SCRIPT_DIR/utils/create-render-node.sh" ]; then
                "$SCRIPT_DIR/utils/create-render-node.sh"
            fi
        fi
    else
        log_warning "DRI devices not found - GPU acceleration may not work"
    fi
}

# Build or update container
build_container() {
    log_step "Building Development Container"
    
    local dockerfile_path="$PROJECT_ROOT/.devcontainer/Dockerfile"
    
    if [ ! -f "$dockerfile_path" ]; then
        log_error "Dockerfile not found at $dockerfile_path"
        return 1
    fi
    
    log_host "Building container image: $IMAGE_NAME"
    log_host "Using Dockerfile: $dockerfile_path"
    
    # Build with progress output
    docker build \
        -f "$dockerfile_path" \
        -t "$IMAGE_NAME" \
        "$PROJECT_ROOT" \
        --progress=plain
    
    log_success "Container image built successfully"
}

# Start development container
start_container() {
    log_step "Starting Development Container"
    
    # Stop existing container if running
    if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
        log_host "Stopping existing container..."
        docker stop "$CONTAINER_NAME" >/dev/null
    fi
    
    # Remove existing container if exists
    if docker ps -aq -f name="$CONTAINER_NAME" | grep -q .; then
        log_host "Removing existing container..."
        docker rm "$CONTAINER_NAME" >/dev/null
    fi
    
    # Prepare run arguments
    local run_args=(
        --name "$CONTAINER_NAME"
        --rm
        -it
        --cap-add=SYS_PTRACE
        --security-opt=seccomp=unconfined
        --security-opt=apparmor=unconfined
        --net=host
        --ipc=host
        -v "$PROJECT_ROOT:/app"
        -w /app
    )
    
    # Add X11 forwarding if available
    if [ -n "$DISPLAY" ]; then
        run_args+=(
            -e "DISPLAY=$DISPLAY"
            -v "/tmp/.X11-unix:/tmp/.X11-unix:rw"
        )
    fi
    
    # Add DRI devices if available
    if [ -d "/dev/dri" ]; then
        for device in /dev/dri/*; do
            if [ -c "$device" ]; then
                run_args+=("--device=$device:$device")
            fi
        done
    fi
    
    log_host "Starting container with name: $CONTAINER_NAME"
    log_host "Mounting project directory: $PROJECT_ROOT -> /app"
    
    # Start container
    docker run "${run_args[@]}" "$IMAGE_NAME" bash
}

# Container setup (runs inside container)
setup_container_environment() {
    log_step "Setting Up Container Environment"
    
    # Verify we're in container
    if ! is_container; then
        log_error "This function should only run inside container"
        return 1
    fi
    
    log_container "Working directory: $(pwd)"
    log_container "User: $(whoami)"
    
    # Setup GUI environment
    if [ -f "./scripts/utils/setup-gui-env.sh" ]; then
        log_container "Setting up GUI environment..."
        ./scripts/utils/setup-gui-env.sh setup
    fi
    
    # Run initial CMake configuration
    if [ ! -d "build" ]; then
        log_container "Configuring CMake build..."
        cmake -S . -B build -DCMAKE_BUILD_TYPE=Debug
    else
        log_container "Build directory already exists"
    fi
    
    log_success "Container environment setup complete"
}

# Show development environment status
show_status() {
    log_step "Development Environment Status"
    
    if is_container; then
        log_container "Running inside container"
        log_container "Working directory: $(pwd)"
        log_container "User: $(whoami)"
        
        # Show GUI status
        if [ -f "./scripts/utils/setup-gui-env.sh" ]; then
            ./scripts/utils/setup-gui-env.sh status
        fi
        
        # Show build status
        if [ -d "build" ]; then
            log_container "✓ Build directory exists"
            if [ -f "build/CMakeCache.txt" ]; then
                log_container "✓ CMake configured"
            else
                log_container "⚠ CMake not configured"
            fi
        else
            log_container "⚠ Build directory missing"
        fi
    else
        log_host "Running on host system"
        
        # Check container status
        if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
            log_host "✓ Development container is running"
        else
            log_host "⚠ Development container is not running"
        fi
        
        # Check image status
        if docker images -q "$IMAGE_NAME" | grep -q .; then
            log_host "✓ Development image exists"
        else
            log_host "⚠ Development image not found"
        fi
        
        check_host_prerequisites
    fi
}

# Show help
show_help() {
    cat << EOF
Development Environment Flow Script
==================================

This script manages the complete development environment setup for the c-aibox project.

USAGE:
    $0 [COMMAND]

COMMANDS:
    start       Start the complete development flow (default)
    build       Build/rebuild the development container
    shell       Start a shell in the development container
    status      Show development environment status
    setup       Setup container environment (run inside container)
    stop        Stop the development container
    clean       Clean up containers and images
    help        Show this help

EXAMPLES:
    # [HOST] Start complete development environment
    $0 start

    # [HOST] Just build the container
    $0 build

    # [HOST] Get a shell in the container
    $0 shell

    # [CONTAINER] Setup environment inside container
    $0 setup

    # [HOST] Check status
    $0 status

FLOW OVERVIEW:
    1. [HOST] Check prerequisites (Docker, X11, GPU)
    2. [HOST] Build development container
    3. [HOST] Start container with proper mounts and devices
    4. [CONTAINER] Setup GUI environment
    5. [CONTAINER] Configure CMake build
    6. [CONTAINER] Ready for development!

For detailed documentation, see: scripts/DEV_FLOW.md
EOF
}

# Main function
main() {
    local command="${1:-start}"
    
    case "$command" in
        start)
            if is_container; then
                setup_container_environment
            else
                check_host_prerequisites
                build_container
                start_container
            fi
            ;;
        build)
            if is_container; then
                log_error "Cannot build container from inside container"
                exit 1
            fi
            check_host_prerequisites
            build_container
            ;;
        shell)
            if is_container; then
                log_error "Already inside container"
                exit 1
            fi
            start_container
            ;;
        setup)
            if ! is_container; then
                log_error "Setup command should only be run inside container"
                exit 1
            fi
            setup_container_environment
            ;;
        status)
            show_status
            ;;
        stop)
            if is_container; then
                log_error "Cannot stop container from inside container"
                exit 1
            fi
            if docker ps -q -f name="$CONTAINER_NAME" | grep -q .; then
                log_host "Stopping development container..."
                docker stop "$CONTAINER_NAME"
                log_success "Container stopped"
            else
                log_warning "Container is not running"
            fi
            ;;
        clean)
            if is_container; then
                log_error "Cannot clean from inside container"
                exit 1
            fi
            log_host "Cleaning up containers and images..."
            docker ps -aq -f name="$CONTAINER_NAME" | xargs -r docker rm -f
            docker images -q "$IMAGE_NAME" | xargs -r docker rmi -f
            log_success "Cleanup complete"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
