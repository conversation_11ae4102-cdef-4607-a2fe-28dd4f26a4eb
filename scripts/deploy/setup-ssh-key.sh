#!/bin/bash

# SSH Key Setup Script for Orange Pi
# Sets up passwordless SSH authentication

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
SSH_KEY_NAME="orangepi_key"
SSH_KEY_PATH="./.ssh/$SSH_KEY_NAME"
FORCE_REGENERATE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --port)
            ORANGE_PI_PORT="$2"
            shift 2
            ;;
        --key-name)
            SSH_KEY_NAME="$2"
            SSH_KEY_PATH="./.ssh/$SSH_KEY_NAME"
            shift 2
            ;;
        --force)
            FORCE_REGENERATE=true
            shift
            ;;
        --help)
            cat << EOF
SSH Key Setup Script for Orange Pi

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP         Orange Pi IP address

OPTIONS:
    --user USER     SSH username (default: orangepi)
    --port PORT     SSH port (default: 22)
    --key-name NAME SSH key name (default: orangepi_key)
    --force         Force regenerate SSH key if exists
    --help          Show this help

DESCRIPTION:
    Sets up passwordless SSH authentication to Orange Pi.
    Generates SSH key pair and copies public key to Orange Pi.

EXAMPLES:
    $0 --ip ***************                    # Basic setup
    $0 --ip *************** --force            # Regenerate key
    $0 --ip *************** --key-name my_key  # Custom key name

EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    log_info "Usage: $0 --ip IP_ADDRESS"
    exit 1
fi

# Create SSH directory if it doesn't exist
create_ssh_directory() {
    log_step "Setting Up SSH Directory"
    
    if [[ ! -d "$HOME/.ssh" ]]; then
        log_info "Creating SSH directory..."
        mkdir -p "$HOME/.ssh"
        chmod 700 "$HOME/.ssh"
        log_success "SSH directory created"
    else
        log_info "SSH directory already exists"
    fi
}

# Generate SSH key pair
generate_ssh_key() {
    log_step "Generating SSH Key Pair"
    
    if [[ -f "$SSH_KEY_PATH" && "$FORCE_REGENERATE" != "true" ]]; then
        log_info "SSH key already exists: $SSH_KEY_PATH"
        log_info "Use --force to regenerate"
        return
    fi
    
    if [[ -f "$SSH_KEY_PATH" && "$FORCE_REGENERATE" == "true" ]]; then
        log_warning "Removing existing SSH key..."
        rm -f "$SSH_KEY_PATH" "$SSH_KEY_PATH.pub"
    fi
    
    log_info "Generating new SSH key pair..."
    ssh-keygen -t ed25519 -f "$SSH_KEY_PATH" -N "" -C "c-aibox-orangepi-$(date +%Y%m%d)"
    
    chmod 600 "$SSH_KEY_PATH"
    chmod 644 "$SSH_KEY_PATH.pub"
    
    log_success "SSH key pair generated: $SSH_KEY_PATH"
}

# Test initial SSH connection
test_initial_connection() {
    log_step "Testing Initial SSH Connection"
    
    log_info "Testing connection to $ORANGE_PI_USER@$ORANGE_PI_IP:$ORANGE_PI_PORT"
    log_warning "You will need to enter the password once to set up the key"
    
    if ssh -o ConnectTimeout=10 -p "$ORANGE_PI_PORT" "$ORANGE_PI_USER@$ORANGE_PI_IP" "echo 'SSH connection successful'" 2>/dev/null; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed"
        log_info "Please check:"
        log_info "  - Orange Pi IP address: $ORANGE_PI_IP"
        log_info "  - SSH service is running on Orange Pi"
        log_info "  - Username and password are correct"
        log_info "  - Network connectivity"
        exit 1
    fi
}

# Copy public key to Orange Pi
copy_public_key() {
    log_step "Copying Public Key to Orange Pi"
    
    if [[ ! -f "$SSH_KEY_PATH.pub" ]]; then
        log_error "Public key not found: $SSH_KEY_PATH.pub"
        exit 1
    fi
    
    log_info "Copying public key to Orange Pi..."
    log_info "You may need to enter the password one more time"
    
    # Use ssh-copy-id if available, otherwise manual copy
    if command -v ssh-copy-id >/dev/null 2>&1; then
        ssh-copy-id -i "$SSH_KEY_PATH.pub" -p "$ORANGE_PI_PORT" "$ORANGE_PI_USER@$ORANGE_PI_IP"
    else
        # Manual copy method
        cat "$SSH_KEY_PATH.pub" | ssh -p "$ORANGE_PI_PORT" "$ORANGE_PI_USER@$ORANGE_PI_IP" \
            "mkdir -p ~/.ssh && chmod 700 ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 600 ~/.ssh/authorized_keys"
    fi
    
    log_success "Public key copied to Orange Pi"
}

# Test passwordless connection
test_passwordless_connection() {
    log_step "Testing Passwordless SSH Connection"
    
    log_info "Testing passwordless connection..."
    
    if ssh -i "$SSH_KEY_PATH" -o ConnectTimeout=10 -p "$ORANGE_PI_PORT" "$ORANGE_PI_USER@$ORANGE_PI_IP" "echo 'Passwordless SSH working!'" 2>/dev/null; then
        log_success "Passwordless SSH connection established!"
    else
        log_error "Passwordless SSH connection failed"
        log_info "You may need to check SSH server configuration on Orange Pi"
        exit 1
    fi
    
    # Get system info to verify connection
    log_info "Orange Pi system information:"
    ssh -i "$SSH_KEY_PATH" -p "$ORANGE_PI_PORT" "$ORANGE_PI_USER@$ORANGE_PI_IP" \
        "uname -a && cat /etc/os-release | grep PRETTY_NAME"
}

# Update SSH config
update_ssh_config() {
    log_step "Updating SSH Configuration"
    
    local ssh_config="$HOME/.ssh/config"
    local host_entry="orangepi-$ORANGE_PI_IP"
    
    # Remove existing entry if it exists
    if [[ -f "$ssh_config" ]]; then
        sed -i "/^Host $host_entry$/,/^$/d" "$ssh_config" 2>/dev/null || true
    fi
    
    # Add new entry
    log_info "Adding SSH config entry..."
    cat >> "$ssh_config" << EOF

Host $host_entry
    HostName $ORANGE_PI_IP
    User $ORANGE_PI_USER
    Port $ORANGE_PI_PORT
    IdentityFile $SSH_KEY_PATH
    IdentitiesOnly yes
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null

EOF
    
    chmod 600 "$ssh_config"
    
    log_success "SSH config updated"
    log_info "You can now connect with: ssh $host_entry"
}

# Update deployment scripts
update_deployment_scripts() {
    log_step "Updating Deployment Scripts"
    
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local project_root="$(cd "$script_dir/../.." && pwd)"
    
    # Create environment file with SSH key path
    local env_file="$project_root/.orangepi_ssh"
    cat > "$env_file" << EOF
# Orange Pi SSH Configuration
# Generated by setup-ssh-key.sh on $(date)

ORANGE_PI_IP="$ORANGE_PI_IP"
ORANGE_PI_USER="$ORANGE_PI_USER"
ORANGE_PI_PORT="$ORANGE_PI_PORT"
ORANGE_PI_SSH_KEY="$SSH_KEY_PATH"
ORANGE_PI_SSH_HOST="orangepi-$ORANGE_PI_IP"
EOF
    
    log_success "SSH configuration saved to $env_file"
    log_info "Deployment scripts will automatically use this configuration"
}

# Show usage instructions
show_usage_instructions() {
    log_step "Usage Instructions"
    
    echo ""
    log_success "SSH key authentication is now set up!"
    echo ""
    log_info "Quick connection test:"
    echo "  ssh -i $SSH_KEY_PATH $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo ""
    log_info "Or use the SSH config alias:"
    echo "  ssh orangepi-$ORANGE_PI_IP"
    echo ""
    log_info "Deploy with SSH key:"
    echo "  ./scripts/deploy/deploy-to-orangepi.sh --ip $ORANGE_PI_IP --ssh-key $SSH_KEY_PATH"
    echo ""
    log_info "Test with SSH key:"
    echo "  ./scripts/deploy/test-on-orangepi.sh --ip $ORANGE_PI_IP --ssh-key $SSH_KEY_PATH"
    echo ""
    log_info "The deployment scripts will automatically detect and use the SSH key"
    echo "from the .orangepi_ssh configuration file."
    echo ""
    log_success "No more password prompts needed!"
}

# Main execution
main() {
    log_step "SSH Key Setup for Orange Pi"
    
    create_ssh_directory
    generate_ssh_key
    test_initial_connection
    copy_public_key
    test_passwordless_connection
    update_ssh_config
    update_deployment_scripts
    show_usage_instructions
}

# Run main function
main "$@"
