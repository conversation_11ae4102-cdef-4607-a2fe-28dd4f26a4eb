#!/bin/bash

# Script to fix X11 authorization issues in container
# This helps resolve "Authorization required" errors

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "Fixing X11 authorization for container GUI..."

# Check if we're in a container
if [[ ! -f /.dockerenv ]]; then
    print_warning "Not running in a container - this script may not be necessary"
fi

# Check DISPLAY variable
if [[ -z "$DISPLAY" ]]; then
    print_error "DISPLAY environment variable is not set"
    exit 1
fi

print_info "Current DISPLAY: $DISPLAY"

# Method 1: Try to disable access control (for development containers)
print_info "Attempting to disable X11 access control..."
if command -v xhost >/dev/null 2>&1; then
    # This would work if we had access to the host X server
    print_info "xhost command available"
else
    print_info "Installing xhost..."
    apt-get update && apt-get install -y x11-xserver-utils
fi

# Method 2: Create a minimal .Xauthority file
print_info "Setting up X11 authorization..."

# Create .Xauthority file if it doesn't exist
if [[ ! -f "$HOME/.Xauthority" ]]; then
    print_info "Creating .Xauthority file..."
    touch "$HOME/.Xauthority"
    chmod 600 "$HOME/.Xauthority"
fi

# Method 3: Try to extract auth from host if available
if [[ -f "/tmp/.X11-unix/X0" ]]; then
    print_success "X11 socket found at /tmp/.X11-unix/X0"
else
    print_warning "X11 socket not found - GUI may not work"
fi

# Method 4: Set up environment for Qt
print_info "Setting up Qt environment for container..."
cat > /tmp/x11_qt_env.sh << 'EOF'
# X11 and Qt Environment for Container
export DISPLAY=${DISPLAY:-:0}
export QT_X11_NO_MITSHM=1
export QT_QPA_PLATFORM=xcb
export QTWEBENGINE_DISABLE_SANDBOX=1
export LIBGL_ALWAYS_INDIRECT=1
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false"

# Additional Qt settings for container compatibility
export QT_AUTO_SCREEN_SCALE_FACTOR=0
export QT_SCALE_FACTOR=1
export QT_FONT_DPI=96

# X11 settings
export XAUTHORITY=$HOME/.Xauthority

# Debug settings
export QT_DEBUG_PLUGINS=0
export QT_QPA_PLATFORM_PLUGIN_PATH=/usr/lib/x86_64-linux-gnu/qt5/plugins/platforms
EOF

print_info "Environment setup saved to /tmp/x11_qt_env.sh"

# Method 5: Test X11 connection with different approaches
print_info "Testing X11 connection..."

# Source the environment
source /tmp/x11_qt_env.sh

# Test 1: Basic X11 test
if command -v xdpyinfo >/dev/null 2>&1; then
    if xdpyinfo >/dev/null 2>&1; then
        print_success "X11 connection working with xdpyinfo"
    else
        print_warning "xdpyinfo failed - trying alternative methods"
    fi
else
    print_info "Installing xdpyinfo for testing..."
    apt-get update && apt-get install -y x11-utils
fi

# Test 2: Try with different DISPLAY formats
for display_format in ":0" "localhost:0" "127.0.0.1:0"; do
    print_info "Testing DISPLAY=$display_format"
    export DISPLAY="$display_format"
    if timeout 2s xdpyinfo >/dev/null 2>&1; then
        print_success "X11 working with DISPLAY=$display_format"
        echo "export DISPLAY=$display_format" >> /tmp/x11_qt_env.sh
        break
    else
        print_warning "DISPLAY=$display_format failed"
    fi
done

# Method 6: Create a test script for GUI applications
cat > /tmp/test_x11_gui.sh << 'EOF'
#!/bin/bash
echo "Testing X11 GUI capability..."
source /tmp/x11_qt_env.sh

echo "Environment variables:"
echo "DISPLAY: $DISPLAY"
echo "QT_QPA_PLATFORM: $QT_QPA_PLATFORM"
echo "XAUTHORITY: $XAUTHORITY"

echo "Testing X11 connection..."
if command -v xdpyinfo >/dev/null 2>&1; then
    if timeout 3s xdpyinfo >/dev/null 2>&1; then
        echo "✅ X11 connection successful"
        
        # Test with a simple Qt application if available
        if command -v qmake >/dev/null 2>&1; then
            echo "Qt development tools available"
        fi
        
        # Test with xclock
        if command -v xclock >/dev/null 2>&1; then
            echo "Testing with xclock (will run for 3 seconds)..."
            timeout 3s xclock 2>/dev/null || echo "xclock test completed"
        fi
    else
        echo "❌ X11 connection failed"
        echo "GUI applications will run in headless mode"
    fi
else
    echo "❌ xdpyinfo not available"
fi
EOF

chmod +x /tmp/test_x11_gui.sh

print_success "X11 authorization setup completed!"
print_info ""
print_info "Next steps:"
print_info "1. Source the environment: source /tmp/x11_qt_env.sh"
print_info "2. Test X11 GUI: /tmp/test_x11_gui.sh"
print_info "3. Run client application:"
print_info "   - GUI mode: ./scripts/run_client.sh --gui"
print_info "   - Headless mode: ./scripts/run_client.sh --headless"
print_info ""
print_info "If GUI still doesn't work, the application will automatically"
print_info "fall back to headless mode for testing purposes."
