#!/bin/bash

# Code formatting script for c-aibox-v1 project
# Usage: ./scripts/format.sh [check|fix]

set -e  # Exit on any error

# Default values
ACTION="fix"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [ACTION]"
    echo ""
    echo "ACTION:"
    echo "  fix      Format code in-place (default)"
    echo "  check    Check if code is properly formatted (CI mode)"
    echo "  help     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0           # Format all code files"
    echo "  $0 check     # Check formatting without modifying files"
}

# Parse command line arguments
case "${1:-}" in
    fix)
        ACTION="fix"
        ;;
    check)
        ACTION="check"
        ;;
    help|--help|-h)
        show_usage
        exit 0
        ;;
    "")
        ACTION="fix"
        ;;
    *)
        print_error "Unknown action: $1"
        show_usage
        exit 1
        ;;
esac

# Change to project root directory
cd "$PROJECT_ROOT"

print_info "Code formatting for c-aibox-v1 project"
print_info "Action: $ACTION"
print_info "Project root: $PROJECT_ROOT"

# Check if clang-format is available
if ! command -v clang-format >/dev/null 2>&1; then
    print_error "clang-format not found. Please install clang-format."
    print_info "On Ubuntu/Debian: sudo apt install clang-format"
    print_info "On macOS: brew install clang-format"
    print_info "On Windows: Install LLVM tools"
    exit 1
fi

# Check if .clang-format exists
if [ ! -f ".clang-format" ]; then
    print_warning ".clang-format file not found. Using default formatting style."
fi

# Find all C++ source files
print_info "Finding C++ source files..."
CPP_FILES=$(find . -type f \( -name "*.cpp" -o -name "*.hpp" -o -name "*.h" -o -name "*.cc" -o -name "*.cxx" \) \
    -not -path "./build*" \
    -not -path "./third_party/*" \
    -not -path "./.git/*" \
    -not -path "./cmake-build-*")

if [ -z "$CPP_FILES" ]; then
    print_warning "No C++ source files found."
    exit 0
fi

print_info "Found $(echo "$CPP_FILES" | wc -l) C++ files to process"

# Process files based on action
if [ "$ACTION" = "fix" ]; then
    print_info "Formatting files..."
    echo "$CPP_FILES" | xargs clang-format -i
    print_success "Code formatting completed!"
    
    # Show git diff if in a git repository
    if git rev-parse --git-dir > /dev/null 2>&1; then
        if ! git diff --quiet; then
            print_info "Files modified by formatting:"
            git diff --name-only
            print_info "Use 'git diff' to see the changes"
        else
            print_success "No formatting changes needed"
        fi
    fi
    
elif [ "$ACTION" = "check" ]; then
    print_info "Checking code formatting..."
    
    # Create temporary directory for formatted files
    TEMP_DIR=$(mktemp -d)
    trap "rm -rf $TEMP_DIR" EXIT
    
    # Copy files and format them
    echo "$CPP_FILES" | while read -r file; do
        if [ -f "$file" ]; then
            # Create directory structure in temp
            mkdir -p "$TEMP_DIR/$(dirname "$file")"
            cp "$file" "$TEMP_DIR/$file"
            clang-format -i "$TEMP_DIR/$file"
        fi
    done
    
    # Compare original and formatted files
    DIFF_COUNT=0
    echo "$CPP_FILES" | while read -r file; do
        if [ -f "$file" ] && [ -f "$TEMP_DIR/$file" ]; then
            if ! diff -q "$file" "$TEMP_DIR/$file" >/dev/null; then
                echo "Formatting issue in: $file"
                DIFF_COUNT=$((DIFF_COUNT + 1))
            fi
        fi
    done
    
    # Check if any files need formatting
    if [ $DIFF_COUNT -eq 0 ]; then
        print_success "All files are properly formatted!"
        exit 0
    else
        print_error "$DIFF_COUNT files need formatting"
        print_info "Run '$0 fix' to format the files"
        exit 1
    fi
fi
