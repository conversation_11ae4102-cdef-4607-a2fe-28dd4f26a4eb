#include <gtest/gtest.h>
#include "rtsp/gstreamer_rtsp_client.hpp"
#include "rtsp/connection_manager.hpp"
#include <chrono>
#include <thread>

using namespace aibox::rtsp;

class GStreamerClientTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create a basic RTSP configuration for testing
        config_.stream_id = "test_stream";
        config_.rtsp_url = "rtsp://test.example.com/stream";
        config_.username = "test_user";
        config_.password = "test_pass";
        config_.timeout_ms = 5000;
        config_.retry_count = 3;
        config_.retry_delay_ms = 1000;
        config_.transport = TransportProtocol::TCP;
        config_.use_mpp_decoder = true;
        config_.use_rga_scaler = true;
        config_.use_dmabuf_zerocopy = true;
    }

    RTSPConnectionConfig config_;
};

TEST_F(GStreamerClientTest, ManagerInitialization) {
    // Test GStreamerManager singleton
    auto& manager = GStreamerManager::getInstance();
    
    // Initialize should work (even without actual GStreamer installed)
    bool initialized = manager.initialize();
    (void)initialized;  // Suppress unused variable warning
    
    // Get version info
    std::string version = manager.getVersion();
    EXPECT_FALSE(version.empty());
    
    std::cout << "GStreamer version: " << version << std::endl;
    
    // Check plugin availability (will be false in test environment)
    bool mpp_available = manager.isMPPDecoderAvailable();
    bool rga_available = manager.isRGAConverterAvailable();
    bool dmabuf_supported = manager.isDMABufSupported();
    
    std::cout << "MPP Decoder available: " << (mpp_available ? "Yes" : "No") << std::endl;
    std::cout << "RGA Converter available: " << (rga_available ? "Yes" : "No") << std::endl;
    std::cout << "DMABUF supported: " << (dmabuf_supported ? "Yes" : "No") << std::endl;
    
    // Cleanup
    manager.cleanup();
}

TEST_F(GStreamerClientTest, ClientCreation) {
    try {
        // Create GStreamer RTSP client
        GStreamerRTSPClient client(config_);
        
        // Test basic properties
        EXPECT_EQ(client.getConfig().rtsp_url, config_.rtsp_url);
        EXPECT_EQ(client.getState(), ConnectionState::DISCONNECTED);
        EXPECT_FALSE(client.isInitialized());
        EXPECT_FALSE(client.isRunning());
        EXPECT_FALSE(client.isConnected());
        
        // Test hardware acceleration status
        EXPECT_EQ(client.getMPPStatus(), HardwareAccelStatus::DISABLED);
        EXPECT_EQ(client.getRGAStatus(), HardwareAccelStatus::DISABLED);
        EXPECT_FALSE(client.isDMABufEnabled());
        
        std::cout << "GStreamer client created successfully" << std::endl;
        
    } catch (const std::exception& e) {
        // This is expected if GStreamer is not available
        std::cout << "Expected exception (GStreamer not available): " << e.what() << std::endl;
        EXPECT_TRUE(true);  // Test passes if exception is thrown
    }
}

TEST_F(GStreamerClientTest, ConnectionManagerIntegration) {
    try {
        // Create connection manager (which creates GStreamer client internally)
        ConnectionManager manager(config_);
        
        // Test basic properties
        EXPECT_EQ(manager.getConfig().rtsp_url, config_.rtsp_url);
        EXPECT_EQ(manager.getState(), ConnectionState::DISCONNECTED);
        EXPECT_FALSE(manager.isConnected());
        
        // Test statistics
        auto stats = manager.getStatistics();
        EXPECT_EQ(stats.packets_received, 0);
        EXPECT_EQ(stats.bytes_received, 0);
        
        // Test health
        auto health = manager.getHealth();
        EXPECT_EQ(health.state, ConnectionState::DISCONNECTED);
        EXPECT_EQ(health.reconnect_count, 0);
        
        std::cout << "Connection manager created successfully" << std::endl;
        
        // Test connection attempt (will fail with test URL)
        auto result = manager.connect();
        if (!result) {
            std::cout << "Expected connection failure: " << result.error_message << std::endl;
            EXPECT_TRUE(true);  // Expected to fail with test URL
        }
        
    } catch (const std::exception& e) {
        // This is expected if GStreamer is not available
        std::cout << "Expected exception (GStreamer not available): " << e.what() << std::endl;
        EXPECT_TRUE(true);  // Test passes if exception is thrown
    }
}

TEST_F(GStreamerClientTest, ConfigurationUpdate) {
    try {
        GStreamerRTSPClient client(config_);
        
        // Test configuration update
        RTSPConnectionConfig new_config = config_;
        new_config.rtsp_url = "rtsp://new.example.com/stream";
        new_config.timeout_ms = 10000;
        
        client.updateConfig(new_config);
        
        EXPECT_EQ(client.getConfig().rtsp_url, new_config.rtsp_url);
        EXPECT_EQ(client.getConfig().timeout_ms, new_config.timeout_ms);
        
        // Test pipeline configuration
        GStreamerPipelineConfig pipeline_config;
        pipeline_config.latency_ms = 50;
        pipeline_config.use_mpp_decoder = false;
        
        client.updatePipelineConfig(pipeline_config);
        
        EXPECT_EQ(client.getPipelineConfig().latency_ms, 50);
        EXPECT_FALSE(client.getPipelineConfig().use_mpp_decoder);
        
        std::cout << "Configuration update test passed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Expected exception (GStreamer not available): " << e.what() << std::endl;
        EXPECT_TRUE(true);
    }
}

TEST_F(GStreamerClientTest, CallbackSetup) {
    try {
        GStreamerRTSPClient client(config_);
        
        bool buffer_callback_called = false;
        bool nal_callback_called = false;
        bool error_callback_called = false;
        bool state_callback_called = false;
        
        // Set up callbacks
        client.setBufferCallback([&](const std::vector<uint8_t>& buffer, Timestamp timestamp) {
            (void)buffer; (void)timestamp;  // Suppress unused parameter warnings
            buffer_callback_called = true;
        });

        client.setNALUnitCallback([&](const NALUnit& nal_unit) {
            (void)nal_unit;  // Suppress unused parameter warning
            nal_callback_called = true;
        });

        client.setErrorCallback([&](const StreamId& stream_id, ErrorCategory category, const std::string& message) {
            (void)stream_id; (void)category; (void)message;  // Suppress unused parameter warnings
            error_callback_called = true;
        });

        client.setStateChangeCallback([&](ConnectionState state) {
            (void)state;  // Suppress unused parameter warning
            state_callback_called = true;
        });
        
        std::cout << "Callbacks set up successfully" << std::endl;
        
        // Callbacks won't be called without actual connection, but setup should work
        EXPECT_TRUE(true);
        
    } catch (const std::exception& e) {
        std::cout << "Expected exception (GStreamer not available): " << e.what() << std::endl;
        EXPECT_TRUE(true);
    }
}

TEST_F(GStreamerClientTest, HardwareAccelerationControl) {
    try {
        GStreamerRTSPClient client(config_);
        
        // Test hardware acceleration control
        bool mpp_enabled = client.enableMPPDecoder(true);
        bool rga_enabled = client.enableRGAScaler(true);
        bool dmabuf_enabled = client.enableDMABuf(true);
        
        // These will likely fail in test environment, but should not crash
        std::cout << "MPP Decoder enable result: " << (mpp_enabled ? "Success" : "Failed") << std::endl;
        std::cout << "RGA Scaler enable result: " << (rga_enabled ? "Success" : "Failed") << std::endl;
        std::cout << "DMABUF enable result: " << (dmabuf_enabled ? "Success" : "Failed") << std::endl;
        
        // Test thermal throttling
        client.handleThermalThrottling(85);  // Should trigger throttling
        client.handleThermalThrottling(70);  // Should restore
        
        // Test performance mode
        client.setPerformanceMode(true);   // High performance
        client.setPerformanceMode(false);  // Balanced
        
        EXPECT_TRUE(true);  // Test passes if no crashes
        
    } catch (const std::exception& e) {
        std::cout << "Expected exception (GStreamer not available): " << e.what() << std::endl;
        EXPECT_TRUE(true);
    }
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
