# Định dạng cho code C++ dự án này
BasedOnStyle: Google
Language:        Cpp

# Định dạng chung
IndentWidth:             4       # Sử dụng 4 spaces cho mỗi tab
TabWidth:                4
UseTab:                  Never

# Định dạng ngoặc
BreakBeforeBraces:       Allman   # Xuống dòng cho mỗi dấu '{'
BraceWrapping:
  AfterClass:            true
  AfterControlStatement: true
  AfterEnum:             true
  AfterFunction:         true
  AfterNamespace:        true
  AfterObjCDeclaration:  true
  AfterStruct:           true
  AfterUnion:            true
  BeforeCatch:           true
  BeforeElse:            true
  IndentBraces:          false

# Định dạng dòng
ColumnLimit:             120
AllowShortFunctionsOnASingleLine: Empty

# Căn chỉnh danh sách tham số/hàm
AlignConsecutiveDeclarations:     true
AlignConsecutiveAssignments:      true
AlignOperands:                    true
AlignAfterOpenBracket:            Align

# Dấu cách & căn chỉnh
SpaceAfterCStyleCast:             true
SpaceBeforeParens:                ControlStatements
SpacesInParentheses:              false
SpacesInSquareBrackets:           false
SpacesInAngles:                   false
SpaceInEmptyParentheses:          false

# Include & thứ tự
IncludeBlocks:                    Regroup
IncludeCategories:
  - Regex:           '^<.*\.h>'
    Priority:        1
    SortPriority:    0
    CaseSensitive:   false
  - Regex:           '^<.*>'
    Priority:        2
    SortPriority:    1
    CaseSensitive:   false
  - Regex:           '^".*"'
    Priority:        3
    SortPriority:    2
    CaseSensitive:   false
SortIncludes:                    true

# Tên hàm & biến
DerivePointerAlignment:           false
PointerAlignment:                 Left   # int* a, không phải int *a

# C++11, 14, 17, 20 hỗ trợ tốt
Standard:                Latest

# Khác
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine:        false
AllowShortCaseLabelsOnASingleLine:   false

# Trailing comma cho danh sách initializer dài
Cpp11BracedListStyle:               true
Cpp03BracedListStyle:               true

# Tối ưu cho teamwork
ReflowComments:             true
SortUsingDeclarations:      true

# Comment
CommentPragmas: '^ IWYU pragma:'

# Format khi lưu file
InsertTrailingCommas:       Wrapped
AlwaysBreakTemplateDeclarations: Yes

# Giới hạn số dòng trắng
MaxEmptyLinesToKeep:        2

# Format docstring (cho Doxygen)
IndentWrappedFunctionNames: false

# Nâng cao: Nếu dùng clang-format >=15, có thể bổ sung thêm các field mới.
