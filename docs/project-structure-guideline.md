# Project Structure Guideline

## 1. Purpose
This document provides guidelines for organizing the directory structure of a C++ monorepo project, aiming to:

*   Standardize the way projects are built, developed, maintained, and extended.
*   Facilitate teamwork, CI/CD, testing, packaging, and release processes.

## 2. Directory Structure Overview

```plaintext
c-aibox-v1/
├── apps/
├── libraries/
├── third_party/
├── tests/
├── cmake/
├── scripts/
├── docs/
├── .github/
├── CMakeLists.txt
├── README.md
└── .gitignore
```

**Meaning of Main Directories**

| Directory      | Meaning                                                                 |
| :------------- | :---------------------------------------------------------------------- |
| `apps/`        | Contains main applications (CLI, services, GUI, etc.)                   |
| `libraries/`        | Shared libraries used by multiple applications                          |
| `third_party/` | External libraries/software not managed by a package manager            |
| `tests/`       | Test source code (unit/integration tests)                               |
| `cmake/`       | CMake configuration scripts/macros/toolchains                           |
| `scripts/`     | Automation scripts (build, lint, doc generation, deploy, etc.)          |
| `docs/`        | Project documentation, architecture, design, API, etc.                  |
| `.github/`     | CI/CD configuration, issue templates, etc., for GitHub                |
| `CMakeLists.txt`| Main build configuration file (root level)                             |
| `README.md`    | Project introduction, installation/running instructions                 |
| `.gitignore`   | List of files/directories to be ignored by Git                          |

## 3. Detailed Breakdown of Components

### 3.1. `apps/`
Each application resides in its own sub-folder.

Minimum structure:
```plaintext
apps/app_name/
├── src/
├── include/
├── CMakeLists.txt
└── main.cpp
```
*   `main.cpp` is the entry point.
*   `CMakeLists.txt` declares the executable and links necessary libraries.

### 3.2. `libraries/`
Each shared library is in its own folder.

Minimum structure:
```plaintext
libraries/lib_name/
├── src/
├── include/
└── CMakeLists.txt
```
*   Only public headers should be placed in `include/`.
*   It's recommended to clearly separate public API (`include`) from implementation (`src`).
*   Header-only libraries can be used for simple libraries.

### 3.3. `third_party/`
Manages source code or binary files of external libraries (if not using Conan/vcpkg).
Prefer using git submodules or CMake's `FetchContent`.

### 3.4. `tests/`
Each module has its own test folder.
Recommended to use GoogleTest, Catch2, etc.

Example:
```plaintext
tests/
├── app1/
├── core/
└── utils/
```

### 3.5. `cmake/`
Macros, functions, presets, or toolchain files.
Avoid placing complex build logic in the root `CMakeLists.txt`.

### 3.6. `scripts/`
Shell/Python scripts for building, linting, formatting, deploying, etc.
Use clear naming conventions: `build_all.sh`, `run_lint.py`, `gen_docs.sh`, etc.

### 3.7. `docs/`
Markdown documents, architecture diagrams, database models, guidelines, etc.

### 3.8. `.github/`
GitHub Actions workflows, issue/pull request templates.

## 4. CMake Conventions
*   The root `CMakeLists.txt` should only call `add_subdirectory` for components.
*   Each module/app/lib has its own `CMakeLists.txt` file.
*   Use `target_include_directories` to declare include paths for libraries.
*   Clearly define targets: STATIC/SHARED LIB, EXECUTABLE, TEST.

## 5. General Principles
*   Avoid unnecessary deep nesting of directories.
*   All shared code should be moved into libraries within `libraries/`.
*   Do not write "overlapping" source code between apps and libraries.
*   Any structural changes must be updated in this guideline and communicated to the team.
*   Ensure modules are easy to test, easy to extend, and can be built/tested independently.

## 6. Basic `CMakeLists.txt` Examples

**Root `CMakeLists.txt`:**
```cmake
cmake_minimum_required(VERSION 3.18)
project(your_project)
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

add_subdirectory(libraries/core)
add_subdirectory(apps/app1)
add_subdirectory(tests)
```

**`libraries/core/CMakeLists.txt`:**
```cmake
add_library(core
    src/core.cpp
    include/core/core.hpp
)
target_include_directories(core PUBLIC include)
```

**`apps/app1/CMakeLists.txt`:**
```cmake
add_executable(app1
    src/main.cpp
)
target_link_libraries(app1 PRIVATE core)
```

## 7. References
*   Google C++ Style Guide
*   ClickHouse Project Structure
*   CMake Documentation

## 8. Frequently Asked Questions (FAQ)

| Issue                       | Solution                                                                       |
| :-------------------------- | :----------------------------------------------------------------------------- |
| Adding a new app/lib        | Add a folder, add `CMakeLists.txt`, declare `add_subdirectory` in the root `CMakeLists.txt`. |
| Adding an external library  | Use Conan/vcpkg or add to `third_party/` and configure CMake.                  |
| Running tests               | Build tests in `tests/`, declare test targets for CI/CD.                       |
