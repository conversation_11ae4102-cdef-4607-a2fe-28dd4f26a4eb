Orange Pi 5 Plus Test Report
===========================
Date: Tue Jun  3 03:07:35 UTC 2025
Orange Pi: orangepi@***************
Deployment Directory: /home/<USER>/c-aibox

System Information:
Linux orangepi5plus 5.10.160-rockchip-rk3588 #1.0.8 SMP Fri Nov 10 17:58:32 CST 2023 aarch64 GNU/Linux
PRETTY_NAME="Orange Pi 1.0.8 Bookworm"
               total        used        free      shared  buff/cache   available
Mem:           7.8Gi       766Mi       5.6Gi        22Mi       1.5Gi       7.0Gi
Swap:          3.9Gi          0B       3.9Gi

Test Results:
- Basic Tests: See output above
- Performance Tests: SKIPPED
- Hardware Tests: SKIPPED
- Stress Tests: SKIPPED

Application Status:
total 528
drwxr-xr-x  3 <USER> <GROUP>   4096 May  6 18:06 .
drwx------ 20 <USER> <GROUP>   4096 May  6 18:06 ..
drwxrwxr-x  2 <USER> <GROUP>   4096 May  6 18:06 deploy
-rw-r--r--  1 orange<PERSON> orangepi      0 May  6 18:06 .env
-rwxr-xr-x  1 <USER> <GROUP> 527560 May  6 18:06 server

Dependencies:
./server: /lib/aarch64-linux-gnu/libc.so.6: version `GLIBC_2.38' not found (required by ./server)
./server: /lib/aarch64-linux-gnu/libstdc++.so.6: version `GLIBCXX_3.4.31' not found (required by ./server)
	linux-vdso.so.1 (0x0000007fb5063000)
	libstdc++.so.6 => /lib/aarch64-linux-gnu/libstdc++.so.6 (0x0000007fb4d50000)
	libm.so.6 => /lib/aarch64-linux-gnu/libm.so.6 (0x0000007fb4cb0000)
	libgcc_s.so.1 => /lib/aarch64-linux-gnu/libgcc_s.so.1 (0x0000007fb4c70000)
	libc.so.6 => /lib/aarch64-linux-gnu/libc.so.6 (0x0000007fb4ac0000)
	/lib/ld-linux-aarch64.so.1 (0x0000007fb5026000)

