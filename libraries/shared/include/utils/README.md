# Utils Library

This directory contains utility classes and functions for the C-AIBOX project.

## Available Utilities

### Environment Utils (env_utils.hpp)

Environment utilities for loading and managing environment variables from `.env` files and system environment.

### DateTime Utils (datetime_utils.hpp)

Comprehensive date and time utilities for parsing, formatting, and manipulating date/time values.

### HTTP Utils (http_utils.hpp)

HTTP client utilities for making HTTP requests, similar to axios library. Supports GET, POST, PUT, DELETE, PATCH, HEAD, and OPTIONS methods with comprehensive configuration options.

---

# Environment Utils (env_utils.hpp)

Environment utilities for loading and managing environment variables from `.env` files and system environment.

## Features

- ✅ Load environment variables from `.env` files
- ✅ Load environment variables from string content
- ✅ Get environment variables with default values
- ✅ Set and unset environment variables
- ✅ Check if environment variables exist
- ✅ Support for quoted values (single and double quotes)
- ✅ Support for comments and empty lines in `.env` files
- ✅ Cross-platform support (Windows, Linux, macOS)
- ✅ Thread-safe operations
- ✅ Convenience functions for ease of use

## Quick Start

### 1. Include the header

```cpp
#include <utils/env_utils.hpp>
```

### 2. Load environment variables from file

```cpp
// Load from .env file in current directory
if (utils::EnvUtils::loadEnvFromFile()) {
    std::cout << "Environment loaded successfully" << std::endl;
}

// Load from specific file
if (utils::EnvUtils::loadEnvFromFile("config/.env")) {
    std::cout << "Config loaded successfully" << std::endl;
}
```

### 3. Get environment variables

```cpp
// Get with default value
std::string dbHost = utils::EnvUtils::getEnv("DB_HOST", "localhost");
std::string apiKey = utils::EnvUtils::getEnv("API_KEY", "");

// Get as optional
auto port = utils::EnvUtils::getEnvOptional("PORT");
if (port.has_value()) {
    std::cout << "Port: " << port.value() << std::endl;
}
```

### 4. Set environment variables

```cpp
// Set environment variable
utils::EnvUtils::setEnv("APP_NAME", "C-AIBOX");

// Set with overwrite control
utils::EnvUtils::setEnv("DEBUG", "true", false); // Don't overwrite if exists
```

## .env File Format

The `.env` file format follows these rules:

```bash
# Comments start with #
# Empty lines are ignored

# Simple key-value pairs
DATABASE_URL=postgresql://localhost:5432/mydb
API_KEY=abc123def456
DEBUG=true
PORT=8080

# Quoted values (removes quotes)
HOST="localhost"
SECRET='my-secret-key'
MESSAGE="Hello, World!"

# Empty values
EMPTY_VALUE=

# Values with spaces (must be quoted)
APP_NAME="My Application"
DESCRIPTION='This is a long description'

# Special characters in quotes
SPECIAL="value with !@#$%^&*() characters"
URL="https://api.example.com/v1/endpoint?key=value"
```

## API Reference

### Class: `utils::EnvUtils`

#### Static Methods

##### `loadEnvFromFile(filepath)`
Load environment variables from a `.env` file.

```cpp
static bool loadEnvFromFile(const std::string& filepath = ".env");
```

**Parameters:**
- `filepath`: Path to the `.env` file (default: ".env")

**Returns:** `true` if file was loaded successfully, `false` otherwise

##### `loadEnvFromString(content, silent)`
Load environment variables from string content.

```cpp
static bool loadEnvFromString(const std::string& content, bool silent = false);
```

**Parameters:**
- `content`: String content in `.env` format
- `silent`: Whether to suppress warning messages (default: false)

**Returns:** `true` if content was parsed successfully, `false` otherwise

##### `getEnv(key, defaultValue)`
Get environment variable value with default.

```cpp
static std::string getEnv(const std::string& key, const std::string& defaultValue = "");
```

**Parameters:**
- `key`: Environment variable name
- `defaultValue`: Default value if variable doesn't exist

**Returns:** Environment variable value or default value

##### `getEnvOptional(key)`
Get environment variable as optional.

```cpp
static std::optional<std::string> getEnvOptional(const std::string& key);
```

**Parameters:**
- `key`: Environment variable name

**Returns:** Optional containing the value if exists, nullopt otherwise

##### `setEnv(key, value, overwrite)`
Set environment variable.

```cpp
static bool setEnv(const std::string& key, const std::string& value, bool overwrite = true);
```

**Parameters:**
- `key`: Environment variable name
- `value`: Environment variable value
- `overwrite`: Whether to overwrite existing variable (default: true)

**Returns:** `true` if set successfully, `false` otherwise

##### `hasEnv(key)`
Check if environment variable exists.

```cpp
static bool hasEnv(const std::string& key);
```

**Parameters:**
- `key`: Environment variable name

**Returns:** `true` if variable exists, `false` otherwise

##### `unsetEnv(key)`
Remove environment variable.

```cpp
static bool unsetEnv(const std::string& key);
```

**Parameters:**
- `key`: Environment variable name

**Returns:** `true` if removed successfully, `false` otherwise

##### `getAllEnv()`
Get all loaded environment variables.

```cpp
static std::unordered_map<std::string, std::string> getAllEnv();
```

**Returns:** Map of all environment variables

##### `clearLoadedEnv()`
Clear all loaded environment variables from memory.

```cpp
static void clearLoadedEnv();
```

### Convenience Functions

For easier usage, convenience functions are provided in the `utils` namespace:

```cpp
// Load environment from file
bool utils::loadEnv(const std::string& filepath = ".env");

// Get environment variable
std::string utils::getEnv(const std::string& key, const std::string& defaultValue = "");

// Set environment variable
bool utils::setEnv(const std::string& key, const std::string& value);

// Check if environment variable exists
bool utils::hasEnv(const std::string& key);
```

## Examples

### Basic Usage

```cpp
#include <utils/env_utils.hpp>
#include <iostream>

int main() {
    // Load .env file
    utils::loadEnv();

    // Get configuration
    std::string dbUrl = utils::getEnv("DATABASE_URL", "sqlite://memory");
    std::string port = utils::getEnv("PORT", "8080");
    bool debug = utils::getEnv("DEBUG") == "true";

    std::cout << "Database: " << dbUrl << std::endl;
    std::cout << "Port: " << port << std::endl;
    std::cout << "Debug: " << (debug ? "enabled" : "disabled") << std::endl;

    return 0;
}
```

### Advanced Usage

```cpp
#include <utils/env_utils.hpp>
#include <iostream>

int main() {
    // Load from specific file
    if (!utils::EnvUtils::loadEnvFromFile("config/production.env")) {
        std::cerr << "Failed to load production config" << std::endl;
        return 1;
    }

    // Check required variables
    if (!utils::EnvUtils::hasEnv("API_KEY")) {
        std::cerr << "API_KEY is required" << std::endl;
        return 1;
    }

    // Get optional configuration
    auto timeout = utils::EnvUtils::getEnvOptional("TIMEOUT");
    int timeoutValue = timeout.has_value() ? std::stoi(timeout.value()) : 30;

    // Set runtime configuration
    utils::EnvUtils::setEnv("RUNTIME_ID", "12345");

    // Print all loaded variables
    auto allVars = utils::EnvUtils::getAllEnv();
    for (const auto& [key, value] : allVars) {
        std::cout << key << "=" << value << std::endl;
    }

    return 0;
}
```

## Testing

The env_utils comes with comprehensive unit tests. Run them with:

```bash
# Build and run tests
./scripts/build.sh
./scripts/test.sh

# Or run specific test
./build/bin/test_shared --gtest_filter="EnvUtilsTest.*"
```

## Thread Safety

The `EnvUtils` class uses static storage and is thread-safe for read operations. However, for write operations (setting/unsetting variables), external synchronization may be required in multi-threaded environments.

## Platform Support

- ✅ Linux
- ✅ macOS
- ✅ Windows (MSVC, MinGW)

## Dependencies

- C++17 or later (for `std::optional`)
- Standard library only (no external dependencies)

---

# HTTP Utils (http_utils.hpp)

HTTP client utilities for making HTTP requests, similar to axios library. Provides a comprehensive set of features for HTTP communication.

## Features

- ✅ Support for all major HTTP methods (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS)
- ✅ Request/Response structures with detailed information
- ✅ Custom headers and authentication (Basic, Bearer)
- ✅ JSON request/response handling
- ✅ File upload and download capabilities
- ✅ URL encoding/decoding utilities
- ✅ Query string building
- ✅ URL parsing
- ✅ Timeout configuration
- ✅ SSL/TLS support with verification options
- ✅ Proxy support
- ✅ Redirect handling
- ✅ Response time measurement
- ✅ Error handling and status code checking
- ✅ Thread-safe operations
- ✅ Convenience functions for ease of use

## Quick Start

### 1. Include the header

```cpp
#include <utils/http_utils.hpp>
```

### 2. Check availability

```cpp
if (!utils::HttpUtils::isAvailable()) {
    std::cout << "HTTP utils not available - CURL not compiled" << std::endl;
    return 1;
}
```

### 3. Make a simple GET request

```cpp
auto response = utils::HttpUtils::get("https://api.example.com/users");
if (response.isSuccess()) {
    std::cout << "Response: " << response.body << std::endl;
} else {
    std::cout << "Error: " << response.errorMessage << std::endl;
}
```

### 4. POST request with JSON data

```cpp
utils::HttpConfig config;
config.setContentTypeJson();
config.setBearerAuth("your-token");

std::string jsonData = R"({"name": "John", "age": 30})";
auto response = utils::HttpUtils::post("https://api.example.com/users", jsonData, config);
```

## API Reference

### Core Functions

#### HTTP Methods

```cpp
static HttpResponse get(const std::string& url, const HttpConfig& config = {});
static HttpResponse post(const std::string& url, const std::string& body = "", const HttpConfig& config = {});
static HttpResponse put(const std::string& url, const std::string& body = "", const HttpConfig& config = {});
static HttpResponse del(const std::string& url, const HttpConfig& config = {});
static HttpResponse patch(const std::string& url, const std::string& body = "", const HttpConfig& config = {});
static HttpResponse head(const std::string& url, const HttpConfig& config = {});
```

#### Generic Request

```cpp
static HttpResponse request(HttpMethod method, const std::string& url, const HttpConfig& config = {});
```

#### File Operations

```cpp
static bool downloadFile(const std::string& url, const std::string& filepath, const HttpConfig& config = {});
static HttpResponse uploadFile(const std::string& url, const std::string& filepath,
                             const std::string& fieldName = "file", const HttpConfig& config = {});
```

### Configuration

#### HttpConfig Structure

```cpp
struct HttpConfig {
    std::unordered_map<std::string, std::string> headers;
    std::string body;
    int timeoutMs = 30000;
    bool followRedirects = true;
    int maxRedirects = 5;
    bool verifySSL = true;
    std::string userAgent = "C-AIBOX-HttpUtils/1.0";
    std::string proxy;
    std::string proxyAuth;
    std::string basicAuth;
    std::string bearerToken;

    // Helper methods
    void setContentTypeJson();
    void setContentTypeForm();
    void setContentTypeMultipart();
    void setBearerAuth(const std::string& token);
    void setBasicAuth(const std::string& username, const std::string& password);
};
```

#### HttpResponse Structure

```cpp
struct HttpResponse {
    int statusCode = 0;
    std::string statusText;
    std::unordered_map<std::string, std::string> headers;
    std::string body;
    bool success = false;
    std::string errorMessage;
    double responseTime = 0.0;

    // Helper methods
    bool isSuccess() const;        // 200-299
    bool isClientError() const;    // 400-499
    bool isServerError() const;    // 500-599
};
```

### Utility Functions

```cpp
static std::string urlEncode(const std::string& str);
static std::string urlDecode(const std::string& str);
static std::string buildQueryString(const std::unordered_map<std::string, std::string>& params);
static bool parseUrl(const std::string& url, std::string& scheme, std::string& host,
                   int& port, std::string& path, std::string& query);
```

## Convenience Functions

For ease of use, the following convenience functions are available:

```cpp
utils::HttpResponse utils::httpGet(const std::string& url, const HttpConfig& config = {});
utils::HttpResponse utils::httpPost(const std::string& url, const std::string& body = "", const HttpConfig& config = {});
utils::HttpResponse utils::httpPut(const std::string& url, const std::string& body = "", const HttpConfig& config = {});
utils::HttpResponse utils::httpDelete(const std::string& url, const HttpConfig& config = {});
bool utils::downloadFile(const std::string& url, const std::string& filepath, const HttpConfig& config = {});
```

## Examples

### Basic Usage

```cpp
#include <utils/http_utils.hpp>
#include <iostream>

int main() {
    // Simple GET request
    auto response = utils::HttpUtils::get("https://httpbin.org/get");
    std::cout << "Status: " << response.statusCode << std::endl;
    std::cout << "Body: " << response.body << std::endl;

    return 0;
}
```

### POST with JSON Data

```cpp
#include <utils/http_utils.hpp>
#include <utils/json_utils.hpp>
#include <iostream>

int main() {
    // Create JSON data
    utils::JsonValue data = utils::createObject();
    data["name"] = utils::JsonValue("John Doe");
    data["email"] = utils::JsonValue("<EMAIL>");
    data["age"] = utils::JsonValue(30);

    // Configure request
    utils::HttpConfig config;
    config.setContentTypeJson();
    config.setBearerAuth("your-api-token");

    // Make POST request
    std::string jsonBody = utils::JsonUtils::stringify(data);
    auto response = utils::HttpUtils::post("https://api.example.com/users", jsonBody, config);

    if (response.isSuccess()) {
        std::cout << "User created successfully!" << std::endl;
        std::cout << "Response: " << response.body << std::endl;
    } else {
        std::cout << "Error: " << response.statusCode << " - " << response.errorMessage << std::endl;
    }

    return 0;
}
```

### File Download

```cpp
#include <utils/http_utils.hpp>
#include <iostream>

int main() {
    utils::HttpConfig config;
    config.timeoutMs = 60000; // 60 seconds timeout

    bool success = utils::HttpUtils::downloadFile(
        "https://example.com/large-file.zip",
        "/tmp/downloaded-file.zip",
        config
    );

    if (success) {
        std::cout << "File downloaded successfully!" << std::endl;
    } else {
        std::cout << "Download failed!" << std::endl;
    }

    return 0;
}
```

### Custom Headers and Authentication

```cpp
#include <utils/http_utils.hpp>
#include <iostream>

int main() {
    utils::HttpConfig config;
    config.headers["X-API-Key"] = "your-api-key";
    config.headers["X-Client-Version"] = "1.0.0";
    config.setBasicAuth("username", "password");
    config.userAgent = "MyApp/1.0";

    auto response = utils::HttpUtils::get("https://api.example.com/protected", config);

    std::cout << "Status: " << response.statusCode << std::endl;
    std::cout << "Response Time: " << response.responseTime << "s" << std::endl;

    // Check response headers
    for (const auto& header : response.headers) {
        std::cout << header.first << ": " << header.second << std::endl;
    }

    return 0;
}
```

### Error Handling and Status Checks

```cpp
#include <utils/http_utils.hpp>
#include <iostream>

int main() {
    auto response = utils::HttpUtils::get("https://httpbin.org/status/404");

    std::cout << "Status Code: " << response.statusCode << std::endl;
    std::cout << "Status Text: " << response.statusText << std::endl;

    if (response.isSuccess()) {
        std::cout << "Request successful!" << std::endl;
    } else if (response.isClientError()) {
        std::cout << "Client error (4xx)" << std::endl;
    } else if (response.isServerError()) {
        std::cout << "Server error (5xx)" << std::endl;
    } else if (!response.success) {
        std::cout << "Network error: " << response.errorMessage << std::endl;
    }

    return 0;
}
```

### URL Utilities

```cpp
#include <utils/http_utils.hpp>
#include <iostream>

int main() {
    // URL encoding/decoding
    std::string original = "Hello World! @#$%";
    std::string encoded = utils::HttpUtils::urlEncode(original);
    std::string decoded = utils::HttpUtils::urlDecode(encoded);

    std::cout << "Original: " << original << std::endl;
    std::cout << "Encoded: " << encoded << std::endl;
    std::cout << "Decoded: " << decoded << std::endl;

    // Query string building
    std::unordered_map<std::string, std::string> params = {
        {"page", "1"},
        {"limit", "10"},
        {"search", "hello world"}
    };
    std::string queryString = utils::HttpUtils::buildQueryString(params);
    std::cout << "Query String: " << queryString << std::endl;

    // URL parsing
    std::string url = "https://api.example.com:8080/v1/users?page=1";
    std::string scheme, host, path, query;
    int port;

    if (utils::HttpUtils::parseUrl(url, scheme, host, port, path, query)) {
        std::cout << "Scheme: " << scheme << std::endl;
        std::cout << "Host: " << host << std::endl;
        std::cout << "Port: " << port << std::endl;
        std::cout << "Path: " << path << std::endl;
        std::cout << "Query: " << query << std::endl;
    }

    return 0;
}
```

## Testing

The http_utils comes with comprehensive unit tests. Run them with:

```bash
# Build and run tests
./scripts/build.sh
./scripts/test.sh

# Or run specific test
./build/bin/shared_http_utils_test
```

## Thread Safety

The `HttpUtils` class is thread-safe for concurrent requests. Each request creates its own CURL handle and doesn't share state between requests.

## Platform Support

- ✅ Linux
- ✅ macOS
- ✅ Windows (MSVC, MinGW)

## Dependencies

- C++17 or later (for `std::optional`)
- libcurl (automatically detected and linked)
- Standard library
```

---

# DateTime Utils (datetime_utils.hpp)

Comprehensive date and time utilities for parsing, formatting, and manipulating date/time values using modern C++ chrono library.

## Features

- ✅ Current time operations (now, Unix timestamps)
- ✅ Time formatting with custom formats
- ✅ Time parsing from strings
- ✅ ISO 8601 support (parsing and formatting)
- ✅ Unix timestamp conversions (seconds and milliseconds)
- ✅ Arithmetic operations (add, subtract, difference)
- ✅ Comparison operations (before, after)
- ✅ Sleep operations with various durations
- ✅ Day operations (start/end of day, same day check)
- ✅ Elapsed time in human readable format
- ✅ Format validation
- ✅ Cross-platform support (Windows, Linux, macOS)
- ✅ Thread-safe operations
- ✅ Convenience functions for ease of use

## Quick Start

### 1. Include the header

```cpp
#include <utils/datetime_utils.hpp>
```

### 2. Get current time

```cpp
// Get current time
auto now = utils::DateTimeUtils::now();
auto unixNow = utils::DateTimeUtils::nowUnix();
auto unixMsNow = utils::DateTimeUtils::nowUnixMs();

// Convenience functions
auto nowConv = utils::now();
auto unixConv = utils::nowUnix();
```

### 3. Format time

```cpp
auto now = utils::DateTimeUtils::now();

// Default format
std::string formatted = utils::DateTimeUtils::format(now);
// Output: "2023-12-25 10:30:45"

// Custom format
std::string custom = utils::DateTimeUtils::format(now, "%Y-%m-%d");
// Output: "2023-12-25"

// ISO 8601
std::string iso = utils::DateTimeUtils::toISO8601(now);
// Output: "2023-12-25T10:30:45Z"

// Convenience function
std::string nowStr = utils::formatNow("%H:%M:%S");
```

### 4. Parse time

```cpp
// Parse from string
auto parsed = utils::DateTimeUtils::parse("2023-12-25 10:30:45");
if (parsed.has_value()) {
    std::cout << "Parsed successfully" << std::endl;
}

// Parse ISO 8601
auto parsedIso = utils::DateTimeUtils::parseISO8601("2023-12-25T10:30:45Z");

// Convenience function
auto parsedConv = utils::parseTime("2023-12-25 10:30:45");
```

## API Reference

### Core Functions

#### `now()`
Get current system time.

```cpp
static TimePoint now();
```

**Returns:** Current system time as `std::chrono::system_clock::time_point`

#### `nowUnix()` / `nowUnixMs()`
Get current time as Unix timestamp.

```cpp
static int64_t nowUnix();        // seconds since epoch
static int64_t nowUnixMs();      // milliseconds since epoch
```

**Returns:** Unix timestamp as `int64_t`

#### `format(tp, format)`
Format time point to string.

```cpp
static std::string format(const TimePoint& tp, const std::string& format = "%Y-%m-%d %H:%M:%S");
```

**Parameters:**
- `tp`: Time point to format
- `format`: Format string (strftime format)

**Returns:** Formatted string

#### `parse(timeStr, format)`
Parse string to time point.

```cpp
static std::optional<TimePoint> parse(const std::string& timeStr, const std::string& format = "%Y-%m-%d %H:%M:%S");
```

**Parameters:**
- `timeStr`: Time string to parse
- `format`: Format string (strptime format)

**Returns:** Optional time point (nullopt if parsing fails)

#### `toISO8601(tp, includeMs)` / `parseISO8601(isoStr)`
ISO 8601 formatting and parsing.

```cpp
static std::string toISO8601(const TimePoint& tp, bool includeMs = false);
static std::optional<TimePoint> parseISO8601(const std::string& isoStr);
```

### Unix Timestamp Conversions

```cpp
static TimePoint fromUnix(int64_t timestamp);      // from seconds
static TimePoint fromUnixMs(int64_t timestamp);    // from milliseconds
static int64_t toUnix(const TimePoint& tp);        // to seconds
static int64_t toUnixMs(const TimePoint& tp);      // to milliseconds
```

### Arithmetic Operations

```cpp
static TimePoint add(const TimePoint& tp, const Duration& duration);
static TimePoint subtract(const TimePoint& tp, const Duration& duration);
static Duration diff(const TimePoint& tp1, const TimePoint& tp2);
static int64_t diffSeconds(const TimePoint& tp1, const TimePoint& tp2);
static int64_t diffMs(const TimePoint& tp1, const TimePoint& tp2);
```

### Comparison Operations

```cpp
static bool isBefore(const TimePoint& tp1, const TimePoint& tp2);
static bool isAfter(const TimePoint& tp1, const TimePoint& tp2);
```

### Sleep Operations

```cpp
static void sleep(const Duration& duration);
static void sleepMs(int64_t ms);
static void sleepSeconds(int64_t seconds);
```

### Day Operations

```cpp
static TimePoint startOfDay(const TimePoint& tp);
static TimePoint endOfDay(const TimePoint& tp);
static bool isSameDay(const TimePoint& tp1, const TimePoint& tp2);
```

### Utility Functions

```cpp
static std::string getElapsedTime(const TimePoint& tp);
static bool isValidFormat(const std::string& timeStr, const std::string& format);
```

## Convenience Functions

For ease of use, the following convenience functions are available:

```cpp
// Get current time
utils::TimePoint utils::now();
int64_t utils::nowUnix();

// Format current time
std::string utils::formatNow(const std::string& format = "%Y-%m-%d %H:%M:%S");

// Parse time string
std::optional<utils::TimePoint> utils::parseTime(const std::string& timeStr, const std::string& format = "%Y-%m-%d %H:%M:%S");

// Sleep
void utils::sleepMs(int64_t ms);
```

## Examples

### Basic Usage

```cpp
#include <utils/datetime_utils.hpp>
#include <iostream>

int main() {
    // Get current time
    auto now = utils::DateTimeUtils::now();
    std::cout << "Current time: " << utils::DateTimeUtils::format(now) << std::endl;

    // Unix timestamp
    auto unixTime = utils::DateTimeUtils::nowUnix();
    std::cout << "Unix timestamp: " << unixTime << std::endl;

    // ISO 8601
    std::cout << "ISO 8601: " << utils::DateTimeUtils::toISO8601(now) << std::endl;

    return 0;
}
```

### Time Arithmetic

```cpp
#include <utils/datetime_utils.hpp>
#include <iostream>

int main() {
    auto now = utils::DateTimeUtils::now();

    // Add time
    auto oneHourLater = utils::DateTimeUtils::add(now, std::chrono::hours(1));
    auto fiveMinutesEarlier = utils::DateTimeUtils::subtract(now, std::chrono::minutes(5));

    // Calculate differences
    auto diffSec = utils::DateTimeUtils::diffSeconds(oneHourLater, now);
    std::cout << "Difference: " << diffSec << " seconds" << std::endl;

    // Comparisons
    if (utils::DateTimeUtils::isAfter(oneHourLater, now)) {
        std::cout << "One hour later is after now" << std::endl;
    }

    return 0;
}
```

### Parsing and Validation

```cpp
#include <utils/datetime_utils.hpp>
#include <iostream>

int main() {
    // Parse different formats
    auto parsed1 = utils::DateTimeUtils::parse("2023-12-25 10:30:45");
    auto parsed2 = utils::DateTimeUtils::parseISO8601("2023-12-25T10:30:45Z");

    // Validate formats
    bool valid = utils::DateTimeUtils::isValidFormat("2023-12-25", "%Y-%m-%d");
    std::cout << "Valid format: " << (valid ? "Yes" : "No") << std::endl;

    // Get elapsed time
    if (parsed1.has_value()) {
        std::string elapsed = utils::DateTimeUtils::getElapsedTime(parsed1.value());
        std::cout << "Elapsed: " << elapsed << std::endl;
    }

    return 0;
}
```

## Testing

The datetime_utils comes with comprehensive unit tests. Run them with:

```bash
# Build and run tests
./scripts/build.sh
./scripts/test.sh

# Or run specific test
./build/bin/test_shared --gtest_filter="DateTimeUtilsTest.*"
```

## Thread Safety

The `DateTimeUtils` class is thread-safe for all operations as it uses only static functions and doesn't maintain any shared state.

## Platform Support

- ✅ Linux
- ✅ macOS
- ✅ Windows (MSVC, MinGW)

## Dependencies

- C++20 or later (for `std::chrono` improvements)
- Standard library only (no external dependencies)
- `<regex>` for ISO 8601 parsing
- `<thread>` for sleep operations
