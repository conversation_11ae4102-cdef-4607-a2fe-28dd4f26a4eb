# EditorConfig is awesome: https://EditorConfig.org

# Top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# C++ source files
[*.{cpp,cxx,cc,c++}]
indent_style = space
indent_size = 4
max_line_length = 120

# C++ header files
[*.{hpp,hxx,h++,h}]
indent_style = space
indent_size = 4
max_line_length = 120

# C source files
[*.{c}]
indent_style = space
indent_size = 4
max_line_length = 120

# CMake files
[{CMakeLists.txt,*.cmake}]
indent_style = space
indent_size = 4
max_line_length = 120

# Shell scripts
[*.{sh,bash}]
indent_style = space
indent_size = 4
max_line_length = 120

# Python scripts (if any)
[*.py]
indent_style = space
indent_size = 4
max_line_length = 88

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2
max_line_length = 120

# JSON files
[*.json]
indent_style = space
indent_size = 2
max_line_length = 120

# XML files
[*.xml]
indent_style = space
indent_size = 2
max_line_length = 120

# Markdown files
[*.md]
indent_style = space
indent_size = 2
max_line_length = 120
trim_trailing_whitespace = false

# Environment files
[.env*]
indent_style = space
indent_size = 2
max_line_length = 120

# Configuration files
[*.{ini,cfg,conf,config}]
indent_style = space
indent_size = 2
max_line_length = 120

# Docker files
[{Dockerfile,*.dockerfile}]
indent_style = space
indent_size = 2
max_line_length = 120

# Git files
[.git*]
indent_style = space
indent_size = 2
max_line_length = 120

# Makefiles (use tabs)
[{Makefile,*.mk}]
indent_style = tab
indent_size = 4

# Windows batch files
[*.{bat,cmd}]
end_of_line = crlf
indent_style = space
indent_size = 2

# PowerShell files
[*.ps1]
indent_style = space
indent_size = 4
max_line_length = 120

# License and legal files
[{LICENSE,COPYING,COPYRIGHT,NOTICE}]
max_line_length = 80

# Package manager files
[{package.json,package-lock.json,yarn.lock}]
indent_style = space
indent_size = 2

# Conan files
[{conanfile.txt,conanfile.py,conandata.yml}]
indent_style = space
indent_size = 4

# vcpkg files
[{vcpkg.json,vcpkg-configuration.json}]
indent_style = space
indent_size = 2