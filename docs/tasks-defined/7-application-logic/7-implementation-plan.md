# Application Logic Module - Detailed Implementation Plan

## AI Context & System Requirements

### Platform Context (Application Integration Focused)

```
Platform: Orange Pi 5 Plus/Ultra with RK3588 SoC
- CPU: 4x Cortex-A76 @ 2.4GHz + 4x Cortex-A55 @ 1.8GHz  
- Memory: 4GB/8GB LPDDR4X-4224 (CRITICAL: Memory-constrained environment)
- Storage: 240GB SSD for face database and application data
- Hardware Accelerators: 
  * CPU NEON (SIMD) - PRIMARY for similarity computation
  * NPU/RGA - Coordinate with AI pipeline modules
- OS: Ubuntu 22.04 LTS ARM64
- Cross-compilation: aarch64-linux-gnu toolchain required
- Database: SQLite/LevelDB for face identity storage
```

### Technology Stack Context (CPU-Optimized Application Layer)

```
Application Logic Technology Choices (Optimized for RK3588):
- Core Processing: CPU with NEON SIMD acceleration - MANDATORY
- Database: SQLite for structured data, custom indexing for embeddings
- Vector Operations: ARM64 NEON optimizations for similarity computation
- Threading: Async processing with worker pools for CPU utilization
- Build System: CMake with ARM64-specific optimizations
- Math Libraries: Eigen3 or custom SIMD for vector operations
- Event System: Async event processing with priority queues

FORBIDDEN Technologies:
- Heavy database engines (PostgreSQL, MySQL) - memory overhead
- Non-optimized linear algebra libraries
- Synchronous blocking operations in critical paths
- Memory-intensive caching beyond platform limits
```

### Resource Allocation Context (Application Layer Constraints)

```
Application Logic Resource Assignment:
- CPU Cores: Application-focused allocation
  * Core 0-1 (A55): UI Client + System Services (coordinate)
  * Core 2-3 (A55): RTSP coordination (avoid)
  * Core 4-5 (A76): AI pipeline coordination (NPU/RGA)
  * Core 6-7 (A76): PRIMARY for application logic processing
  
- Memory Allocation (STRICT LIMITS):
  * 4GB Config: Application Logic ≤800MB (database + processing)
  * 8GB Config: Application Logic ≤1.2GB (database + processing)
  * Face Database: Scalable from 1K to 10K+ identities
  
- Storage Requirements:
  * Face Database: 10MB per 1K identities (128-dim embeddings)
  * Application Data: Logs, configurations, tracking history
  * Temporary Processing: Minimal memory footprint
  
- Performance Targets:
  * 4GB: Support 1K-5K face identities with real-time matching
  * 8GB: Support 10K+ face identities with optimized search
  * Latency: <5ms face matching per detection
  * Throughput: 200+ face matches per second
```

### Integration Context (AI Box Application Layer)

```
Module Dependencies & Interfaces:
- INPUT: Face Recognition Module (embeddings, quality scores)
- INPUT: Face Detection Module (bounding boxes, confidence)
- OUTPUT: Client Applications (REST API, WebSocket, events)
- COORDINATION: System monitoring and resource management

Data Flow Pipeline:
Recognition Results → Face Matching → Identity Tracking → Event Generation → Application Output

Application Pipeline:
Face Embeddings → Database Search → Identity Resolution → Track Management → Business Logic

Resource Coordination Strategy:
- Hardware: CPU-based processing coordinated with AI pipeline
- Memory: Face database and tracking state management
- Storage: Persistent identity database and event logging
- Integration: Real-time event generation and external API
- Business Logic: Configurable rules and alert processing

Critical Integration Points:
1. Face recognition embedding import for identity matching
2. Real-time face tracking across video frames
3. Multi-camera identity coordination and reconciliation
4. Event generation and external system notification
5. Performance monitoring and adaptive processing
```

### Development Constraints Context

```
Mandatory Requirements:
- Hardware-in-the-Loop: All testing on actual Orange Pi 5 Plus/Ultra
- CPU-First Development: Optimize for ARM64 CPU with NEON acceleration
- Real-time Processing: <5ms latency for face matching operations
- Scalable Database: Support 1K-10K+ face identities efficiently
- Multi-Camera Support: Cross-camera identity tracking and coordination

Performance Constraints:
- Memory Usage: Strict limits based on platform configuration
- CPU Utilization: Optimize for sustained application processing
- Database Performance: Sub-millisecond embedding search at scale
- Event Processing: Real-time event generation without blocking
- Integration Latency: Minimal overhead for external system communication

Quality Gates:
- Orange Pi hardware validation for all application components
- Face matching accuracy validation (>99% precision/recall)
- Performance stress testing under sustained load
- Database scalability testing with large identity sets
- Multi-camera integration and cross-camera tracking validation
```

### Current Project Status Context

```
Application Logic Module Status:
- Phase: Implementation Planning (Phase 1 pending)
- Dependencies: Face Recognition Module (embedding input)
- Critical Path: Database → Matching → Tracking → Event Processing
- Hardware Access: Orange Pi 5 Plus/Ultra required for validation

Key Decisions Made:
1. SQLite + custom indexing for face database management
2. ARM64 NEON SIMD for optimized similarity computation
3. Async worker pool architecture for scalable processing
4. Event-driven architecture for real-time application logic
5. RESTful API design for external system integration

Integration Requirements:
- Recognition Module: Face embedding input with quality metrics
- Detection Module: Bounding box and tracking coordinate integration
- Client Systems: REST API and WebSocket event streaming
- Database: Persistent face identity storage with efficient search
- Monitoring: Performance telemetry and system health reporting
```

### Risk Context (Critical for Application Logic)

```
Critical Risks (Address Immediately):
- R1: Database performance degradation with large identity sets
- R2: Face matching accuracy issues affecting application reliability
- R3: Memory constraints limiting database size and performance

High Priority Risks:
- R4: CPU utilization bottlenecks under sustained processing load
- R5: Identity tracking consistency across multi-camera scenarios
- R6: Event processing delays affecting real-time application requirements

Mitigation Strategies (Mandatory):
- Early database performance testing with target identity scales
- Face matching accuracy validation with diverse datasets
- Memory optimization and efficient data structure design
- CPU performance profiling and NEON optimization validation
- Multi-camera tracking algorithm validation and stress testing

Testing Requirements:
- All components tested on target Orange Pi hardware
- Database performance testing with 1K-10K identity scales
- Face matching accuracy validation with benchmark datasets
- Multi-camera coordination testing with real camera setups
- Event processing latency validation under sustained load
```

## Task Overview Summary

| Task ID                            | Task Name                  | Priority | Status    | Estimated Hours | Dependencies | Assignee         | Platform Focus         |
| ---------------------------------- | -------------------------- | -------- | --------- | --------------- | ------------ | ---------------- | ---------------------- |
| **Phase 1: Foundation Setup**      |                            |          |           | **4-6 hours**   |              |                  |                        |
| 1.1                                | Project Structure Creation | Critical | ⏳ Pending | 2-3h            | Recognition  | Lead Dev         | Application Layer      |
| 1.2                                | CMake & Dependencies Setup | Critical | ⏳ Pending | 2-3h            | 1.1          | DevOps           | ARM64 + Database       |
| **Phase 2: Core Implementation**   |                            |          |           | **36-50 hours** |              |                  |                        |
| 2.1                                | Face Database Manager      | Critical | ⏳ Pending | 10-14h          | 1.2          | Backend Dev      | Database + Indexing    |
| 2.2                                | Face Matcher               | Critical | ⏳ Pending | 8-12h           | 2.1          | Backend Dev      | NEON + Similarity      |
| 2.3                                | Identity Tracker           | Critical | ⏳ Pending | 10-14h          | 2.2          | CV Engineer      | Multi-Frame Tracking   |
| 2.4                                | Event Processor            | High     | ⏳ Pending | 8-10h           | 2.3          | Backend Dev      | Business Logic         |
| **Phase 3: Integration & Testing** |                            |          |           | **16-22 hours** |              |                  |                        |
| 3.1                                | Recognition Integration    | Critical | ⏳ Pending | 6-8h            | Phase 2      | Integration      | Pipeline Connection    |
| 3.2                                | Multi-Camera Coordination  | High     | ⏳ Pending | 6-8h            | 3.1          | System Architect | Cross-Camera Logic     |
| 3.3                                | Unit Testing Suite         | High     | ⏳ Pending | 4-6h            | Phase 2      | QA + Devs        | Application Validation |
| **Phase 4: Application Features**  |                            |          |           | **14-20 hours** |              |                  |                        |
| 4.1                                | System Coordinator         | Medium   | ⏳ Pending | 6-8h            | Phase 3      | System Architect | Resource Management    |
| 4.2                                | External API Interface     | Medium   | ⏳ Pending | 8-12h           | 4.1          | API Developer    | REST + WebSocket       |
| **Phase 5: Production Readiness**  |                            |          |           | **10-16 hours** |              |                  |                        |
| 5.1                                | Performance Optimization   | High     | ⏳ Pending | 6-10h           | Phase 4      | Performance      | Database + CPU Tuning  |
| 5.2                                | Documentation & Examples   | Medium   | ⏳ Pending | 4-6h            | Phase 4      | Tech Writer      | API + Configuration    |

### Status Legend

- ⏳ **Pending**: Not started
- 🔄 **In Progress**: Currently being worked on
- ✅ **Completed**: Task finished and tested
- ⚠️ **Blocked**: Waiting for dependencies or resources
- ❌ **Failed**: Task failed and needs rework

### Resource Allocation Summary

- **Total Estimated Time**: 80-114 hours (6-8 weeks)
- **Critical Path**: Tasks 1.1 → 1.2 → 2.1 → 2.2 → 2.3 → 3.1 → 3.2 → 5.1
- **ARM64 CPU Focus**: 85% of tasks include CPU-specific optimizations
- **Hardware Testing Required**: Tasks 2.1, 2.2, 2.3, 3.2, 4.1, 4.2, 5.1

## Milestone Tracking

| Milestone                    | Target Date | Status    | Completion % | Key Deliverables                                        | Risk Level |
| ---------------------------- | ----------- | --------- | ------------ | ------------------------------------------------------- | ---------- |
| **M1: Foundation Complete**  | Week 1      | ⏳ Pending | 0%           | Build system, database setup, basic structure           | 🟢 Low      |
| **M2: Core Logic Engine**    | Week 3      | ⏳ Pending | 0%           | Face database, matching, tracking, event processing     | 🔴 Critical |
| **M3: Pipeline Integration** | Week 5      | ⏳ Pending | 0%           | Recognition integration, multi-camera coordination      | 🟡 Medium   |
| **M4: Application Features** | Week 6      | ⏳ Pending | 0%           | System coordination, external API interface             | 🟡 Medium   |
| **M5: Production Ready**     | Week 8      | ⏳ Pending | 0%           | Performance optimization, documentation, stress testing | 🟢 Low      |

### Milestone Success Criteria

#### M1: Foundation Complete

- [ ] CMake builds successfully with database dependencies
- [ ] SQLite integration functional with basic schema
- [ ] ARM64 cross-compilation working with optimizations
- [ ] Basic project structure following patterns

#### M2: Core Logic Engine

- [ ] Face database supporting 1K+ identities efficiently
- [ ] Face matching with >99% accuracy on test datasets
- [ ] Multi-frame identity tracking working smoothly
- [ ] Event processing generating application events

#### M3: Pipeline Integration

- [ ] Recognition embeddings processed into identity matches
- [ ] Multi-camera coordination with cross-camera tracking
- [ ] Real-time performance meeting latency requirements
- [ ] Integration testing with full AI pipeline

#### M4: Application Features

- [ ] System coordination managing resources efficiently
- [ ] External API providing real-time application data
- [ ] WebSocket events streaming to client applications
- [ ] Configuration management for application parameters

#### M5: Production Ready

- [ ] Database performance optimized for 10K+ identities
- [ ] CPU utilization optimized with NEON acceleration
- [ ] 72-hour stress test with sustained application load
- [ ] Complete documentation and deployment guides

## Risk Assessment & Mitigation

| Risk ID | Risk Description                                         | Probability | Impact | Risk Level | Mitigation Strategy                                      | Owner         |
| ------- | -------------------------------------------------------- | ----------- | ------ | ---------- | -------------------------------------------------------- | ------------- |
| **R1**  | Database performance degradation (large identity sets)   | High        | High   | 🔴 Critical | Custom indexing, performance testing, optimization       | Backend Dev   |
| **R2**  | Face matching accuracy affecting application reliability | High        | High   | 🔴 Critical | Accuracy validation, threshold tuning, quality filtering | Backend Dev   |
| **R3**  | Memory constraints limiting database and performance     | Medium      | High   | 🟠 High     | Memory optimization, efficient data structures           | Performance   |
| **R4**  | CPU utilization bottlenecks under sustained load         | High        | Medium | 🟡 Medium   | NEON optimization, worker pool tuning, profiling         | Performance   |
| **R5**  | Identity tracking consistency (multi-camera scenarios)   | Medium      | High   | 🟠 High     | Robust tracking algorithms, cross-camera validation      | CV Engineer   |
| **R6**  | Event processing delays affecting real-time requirements | Low         | High   | 🟡 Medium   | Async processing, priority queues, performance tuning    | Backend Dev   |
| **R7**  | Integration complexity with AI pipeline modules          | Medium      | Medium | 🟡 Medium   | Clear interfaces, incremental integration testing        | Integration   |
| **R8**  | External API performance under concurrent load           | Low         | Medium | 🟡 Medium   | Load testing, connection pooling, rate limiting          | API Developer |

### Risk Mitigation Actions

#### Critical Priority (Address Immediately)

- **R1**: Design custom face database indexing with performance benchmarking
- **R2**: Establish face matching accuracy validation with diverse test datasets
- **R3**: Implement memory optimization strategy with efficient data structures

#### High Priority (Address in Phase 1-2)

- **R4**: Optimize CPU utilization with ARM64 NEON SIMD acceleration
- **R5**: Develop robust identity tracking with multi-camera coordination
- **R6**: Design async event processing with priority-based queuing

#### Medium Priority (Monitor and Address as Needed)

- **R7**: Establish clear integration interfaces with AI pipeline modules
- **R8**: Implement external API performance optimization and load testing

## Quick Reference

### Current Status Dashboard

```
📊 Overall Progress: 0% (0/12 tasks completed)
🎯 Current Phase: Phase 1 - Foundation Setup
⏰ Next Milestone: M1 - Foundation Complete (Week 1)
🔥 Critical Path: Task 1.1 (Project Structure Creation)
⚠️ Top Risk: Database performance with large identity sets
🏗️ Platform Focus: ARM64 CPU and application logic optimization
```

### Key Contacts

- **Technical Lead**: [Name] - Overall architecture and system integration
- **Backend Developer**: [Name] - Database, matching algorithms, event processing
- **System Architect**: [Name] - Multi-camera coordination, system design
- **Performance Engineer**: [Name] - CPU optimization, NEON acceleration
- **API Developer**: [Name] - External interfaces, WebSocket implementation
- **Integration Engineer**: [Name] - AI pipeline coordination and testing 

### Quick Commands

```bash
# Check task status
grep -E "⏳|🔄|✅|⚠️|❌" implementation-plan.md

# Update task status (example)
sed -i 's/| 2.1 | .* | ⏳ Pending |/| 2.1 | ... | 🔄 In Progress |/' implementation-plan.md

# View critical path
grep -A1 -B1 "Critical\|High" implementation-plan.md
```

## Executive Summary

This document provides a comprehensive implementation plan for the Application Logic Module optimized for Orange Pi 5 Plus/Ultra with RK3588 SoC. The module serves as the high-level application layer that processes face recognition results, manages identity databases, tracks faces across frames, and provides business logic for the complete AI Box system.

## Project Structure Analysis

### Current Architecture (Application Layer Focus)

- **Platform**: Orange Pi 5 Plus/Ultra with RK3588 SoC (CPU-optimized)
- **Integration Point**: `libraries/application-logic` (new module)
- **Primary Focus**: ARM64 CPU with NEON SIMD, SQLite database, async processing
- **Dependencies**: SQLite, Eigen3/custom SIMD, threading libraries
- **Memory Architecture**: Efficient face database with custom indexing
- **API Interface**: REST API and WebSocket for external integration

### Integration Points

- Input from Face Recognition module (embeddings and metadata)
- Input from Face Detection module (bounding boxes for tracking)
- Output to client applications via REST API and WebSocket events
- Integration with system monitoring and configuration management
- Coordination with AI pipeline modules for resource optimization

## Detailed Task Breakdown

### Phase 1: Project Structure Setup

#### Task 1.1: Create Library Structure

**Priority**: Critical  
**Estimated Time**: 2-3 hours  
**Dependencies**: Face Recognition Module output

**Files to Create:**

```
libraries/application-logic/
├── CMakeLists.txt
├── include/
│   └── application_logic/
│       ├── face_database_manager.hpp
│       ├── face_matcher.hpp
│       ├── identity_tracker.hpp
│       ├── event_processor.hpp
│       ├── system_coordinator.hpp
│       ├── application_logic.hpp
│       └── logic_types.hpp
├── src/
│   ├── face_database_manager.cpp
│   ├── face_matcher.cpp
│   ├── identity_tracker.cpp
│   ├── event_processor.cpp
│   ├── system_coordinator.cpp
│   └── application_logic.cpp
├── api/
│   ├── rest_api.hpp
│   ├── rest_api.cpp
│   ├── websocket_server.hpp
│   └── websocket_server.cpp
├── database/
│   ├── schema.sql
│   └── migrations/
└── tests/
    ├── CMakeLists.txt
    ├── test_database.cpp
    ├── test_matcher.cpp
    ├── test_tracker.cpp
    └── test_integration.cpp
```

#### Task 1.2: Update Root CMakeLists.txt and Dependencies

**Priority**: Critical  
**Estimated Time**: 2-3 hours  
**Dependencies**: Task 1.1

**Changes Required:**

- Add `add_subdirectory(libraries/application-logic)` to root CMakeLists.txt
- Configure SQLite3 library detection and linking
- Set up Eigen3 or custom SIMD library for vector operations
- Configure threading and async processing libraries
- Add ARM64-specific compiler optimizations (-mfpu=neon)
- Set up REST API and WebSocket dependencies

### Phase 2: Core Components Implementation

#### Task 2.1: Face Database Manager Implementation

**Priority**: Critical  
**Estimated Time**: 10-14 hours  
**Dependencies**: Task 1.2

**Implementation Details:**

```cpp
// face_database_manager.hpp
struct FaceIdentity {
    uint32_t identity_id;
    std::string name;
    std::string metadata;
    std::vector<float> embedding;
    float quality_score;
    std::chrono::steady_clock::time_point created_at;
    std::chrono::steady_clock::time_point updated_at;
    uint32_t observation_count;
};

struct SearchResult {
    FaceIdentity identity;
    float similarity_score;
    float confidence;
};

class FaceDatabaseManager {
public:
    bool initialize(const std::string& db_path);
    void shutdown();
    
    // Identity management
    uint32_t addIdentity(const FaceIdentity& identity);
    bool updateIdentity(uint32_t identity_id, const FaceIdentity& updated);
    bool removeIdentity(uint32_t identity_id);
    FaceIdentity* getIdentity(uint32_t identity_id);
    
    // Search operations
    std::vector<SearchResult> searchSimilar(const std::vector<float>& embedding, 
                                           float threshold, 
                                           uint32_t max_results = 10);
    
    std::vector<SearchResult> searchBatch(const std::vector<std::vector<float>>& embeddings,
                                         float threshold,
                                         uint32_t max_results = 10);
    
    // Database management
    bool createIndex();
    bool optimizeDatabase();
    DatabaseStats getStatistics();
    
private:
    sqlite3* db_;
    std::shared_mutex db_mutex_;
    
    // Custom embedding index for fast similarity search
    struct EmbeddingIndex {
        std::vector<std::vector<float>> embeddings;
        std::vector<uint32_t> identity_ids;
        std::vector<float> norms;  // Pre-computed L2 norms
        bool is_built;
    };
    EmbeddingIndex embedding_index_;
    std::mutex index_mutex_;
    
    struct DatabaseStats {
        uint32_t total_identities;
        uint32_t total_embeddings;
        size_t database_size_bytes;
        std::chrono::steady_clock::time_point last_optimization;
    };
    
    bool createTables();
    bool loadEmbeddingIndex();
    bool rebuildIndex();
    void performLinearSearch(const std::vector<float>& embedding,
                           float threshold,
                           std::vector<SearchResult>& results);
    void performIndexedSearch(const std::vector<float>& embedding,
                            float threshold, 
                            std::vector<SearchResult>& results);
};
```

**Features:**

- SQLite database with custom embedding indexing
- Batch search operations for multiple embeddings
- ARM64-optimized similarity computation
- Automatic index rebuilding and optimization
- Thread-safe operations with read-write locks
- Database statistics and performance monitoring

#### Task 2.2: Face Matcher Implementation

**Priority**: Critical  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 2.1

**Core Functionality:**

```cpp
// face_matcher.hpp
struct MatchingConfig {
    float similarity_threshold = 0.6f;
    uint32_t max_candidates = 10;
    bool enable_batch_processing = true;
    bool enable_neon_optimization = true;
    std::string similarity_metric = "cosine";  // "cosine", "euclidean"
};

struct MatchingTask {
    std::vector<float> embedding;
    uint32_t face_id;
    std::string camera_id;
    uint64_t timestamp;
    float quality_score;
};

struct MatchingResult {
    uint32_t face_id;
    std::string camera_id;
    uint64_t timestamp;
    
    // Match information
    bool has_match;
    uint32_t matched_identity_id;
    std::string matched_name;
    float similarity_score;
    float confidence_score;
    
    // Candidates (for debugging/analysis)
    std::vector<SearchResult> candidates;
};

class FaceMatcher {
public:
    bool initialize(const MatchingConfig& config);
    void setDatabase(std::shared_ptr<FaceDatabaseManager> database);
    
    // Synchronous matching
    MatchingResult matchFace(const MatchingTask& task);
    std::vector<MatchingResult> matchBatch(const std::vector<MatchingTask>& tasks);
    
    // Asynchronous matching
    void startMatchingWorkers(size_t worker_count);
    void stopMatchingWorkers();
    void submitMatchingTask(const MatchingTask& task);
    bool getMatchingResult(MatchingResult& result, std::chrono::milliseconds timeout);
    
    // Configuration and optimization
    void updateConfig(const MatchingConfig& config);
    MatchingStats getMatchingStats();
    
private:
    MatchingConfig config_;
    std::shared_ptr<FaceDatabaseManager> database_;
    
    // Worker thread management
    std::vector<std::thread> worker_threads_;
    ThreadSafeQueue<MatchingTask> input_queue_;
    ThreadSafeQueue<MatchingResult> output_queue_;
    std::atomic<bool> should_stop_;
    
    struct MatchingStats {
        uint64_t total_matches_processed;
        uint64_t successful_matches;
        uint64_t unknown_faces;
        float average_processing_time_ms;
        float average_similarity_score;
    };
    MatchingStats stats_;
    std::mutex stats_mutex_;
    
    void workerFunction();
    
    // Similarity computation (NEON-optimized)
    float computeCosineSimilarity(const std::vector<float>& embedding1,
                                 const std::vector<float>& embedding2);
    
    float computeEuclideanDistance(const std::vector<float>& embedding1,
                                  const std::vector<float>& embedding2);
    
    // ARM64 NEON optimized versions
    float computeCosineSimilarityNEON(const float* embedding1,
                                     const float* embedding2,
                                     size_t dimension);
    
    float computeDotProductNEON(const float* vec1, const float* vec2, size_t len);
    float computeL2NormNEON(const float* vec, size_t len);
    
    // Confidence computation
    float computeMatchConfidence(const std::vector<SearchResult>& candidates,
                               float best_similarity);
    
    void updateStatistics(const MatchingResult& result, float processing_time);
};
```

**Features:**

- ARM64 NEON SIMD-optimized similarity computation
- Multi-threaded async matching with worker pools
- Configurable similarity thresholds and metrics
- Batch processing for multiple face embeddings
- Confidence scoring based on candidate distribution
- Performance statistics and monitoring

#### Task 2.3: Identity Tracker Implementation

**Priority**: Critical  
**Estimated Time**: 10-14 hours  
**Dependencies**: Task 2.2

**Core Functionality:**

```cpp
// identity_tracker.hpp
struct TrackingConfig {
    uint32_t max_track_age = 30;          // frames
    float iou_threshold = 0.3f;           // for bbox association
    float identity_confidence_threshold = 0.7f;
    uint32_t identity_stability_frames = 5;
    bool enable_multi_camera = false;
    std::string tracking_algorithm = "kalman";  // "kalman", "simple"
};

struct FaceTrack {
    uint32_t track_id;
    std::string camera_id;
    
    // Spatial information
    cv::Rect2f current_bbox;
    cv::Point2f velocity;
    uint32_t age;
    uint32_t hits;
    uint32_t time_since_update;
    
    // Identity information
    uint32_t assigned_identity_id;
    std::string assigned_name;
    float identity_confidence;
    std::deque<MatchingResult> recognition_history;
    
    // State tracking
    bool is_confirmed;
    bool is_deleted;
    std::chrono::steady_clock::time_point created_at;
    std::chrono::steady_clock::time_point last_updated;
};

struct TrackingInput {
    std::string camera_id;
    uint64_t frame_id;
    uint64_t timestamp;
    std::vector<cv::Rect2f> detections;
    std::vector<MatchingResult> recognition_results;
};

struct TrackingOutput {
    std::string camera_id;
    uint64_t frame_id;
    uint64_t timestamp;
    std::vector<FaceTrack> active_tracks;
    std::vector<uint32_t> new_track_ids;
    std::vector<uint32_t> lost_track_ids;
};

class IdentityTracker {
public:
    bool initialize(const TrackingConfig& config);
    
    // Single camera tracking
    TrackingOutput updateTracks(const TrackingInput& input);
    
    // Multi-camera coordination
    void enableMultiCamera(bool enable);
    std::vector<FaceTrack> getGlobalTracks();
    void reconcileCrossCameraTracks();
    
    // Track management
    FaceTrack* getTrack(uint32_t track_id);
    std::vector<FaceTrack> getActiveTracksByCamera(const std::string& camera_id);
    void removeTrack(uint32_t track_id);
    
    // Identity assignment
    void updateTrackIdentity(uint32_t track_id, const MatchingResult& recognition);
    void consolidateTrackIdentity(uint32_t track_id);
    
    // Statistics and monitoring
    TrackingStats getTrackingStats();
    
private:
    TrackingConfig config_;
    std::unordered_map<uint32_t, FaceTrack> tracks_;
    std::unordered_map<std::string, std::vector<uint32_t>> camera_tracks_;
    std::atomic<uint32_t> next_track_id_;
    std::mutex tracks_mutex_;
    
    struct TrackingStats {
        uint32_t total_tracks_created;
        uint32_t active_tracks;
        uint32_t confirmed_tracks;
        float average_track_length;
        float identity_accuracy;
    };
    TrackingStats stats_;
    
    // Kalman filter for track prediction
    struct KalmanTracker {
        cv::KalmanFilter kf;
        cv::Mat state;
        cv::Mat measurement;
        bool is_initialized;
    };
    std::unordered_map<uint32_t, KalmanTracker> kalman_trackers_;
    
    // Association algorithms
    std::vector<std::pair<uint32_t, uint32_t>> associateDetectionsToTracks(
        const std::vector<cv::Rect2f>& detections,
        const std::vector<uint32_t>& track_ids);
    
    float computeIOU(const cv::Rect2f& bbox1, const cv::Rect2f& bbox2);
    
    // Track management
    void initializeNewTrack(const cv::Rect2f& detection, 
                           const std::string& camera_id,
                           uint64_t frame_id);
    
    void updateExistingTrack(uint32_t track_id, 
                           const cv::Rect2f& detection,
                           uint64_t frame_id);
    
    void predictTrackStates();
    void removeStaleTracksFor(const std::string& camera_id);
    
    // Identity consolidation
    void performIdentityVoting(FaceTrack& track);
    float computeIdentityConfidence(const std::deque<MatchingResult>& history);
    
    // Multi-camera coordination
    void performCrossCameraMatching();
    float computeCrossCameraSimilarity(const FaceTrack& track1, const FaceTrack& track2);
};
```

**Features:**

- Kalman filter-based track prediction and state estimation
- Multi-camera identity tracking and reconciliation
- Identity confidence computation with temporal voting
- IOU-based detection-to-track association
- Automatic track lifecycle management
- Cross-camera track matching and identity consistency

#### Task 2.4: Event Processor Implementation

**Priority**: High  
**Estimated Time**: 8-10 hours  
**Dependencies**: Task 2.3

**Core Functionality:**

```cpp
// event_processor.hpp
enum class EventType {
    FACE_DETECTED,
    IDENTITY_RECOGNIZED,
    IDENTITY_CHANGED,
    NEW_PERSON_DETECTED,
    PERSON_ENTERED,
    PERSON_EXITED,
    TRACK_LOST,
    SYSTEM_ALERT
};

struct ApplicationEvent {
    EventType type;
    uint64_t event_id;
    std::string camera_id;
    uint64_t timestamp;
    
    // Event-specific data
    uint32_t track_id;
    uint32_t identity_id;
    std::string identity_name;
    cv::Rect2f bbox;
    float confidence;
    std::string metadata;
    
    // Priority and routing
    uint32_t priority;
    std::vector<std::string> target_channels;
};

struct EventConfig {
    bool enable_event_processing = true;
    uint32_t max_event_queue_size = 1000;
    std::chrono::milliseconds event_batch_timeout{100};
    
    // Event filtering
    float min_confidence_for_recognition = 0.7f;
    uint32_t min_track_age_for_events = 3;
    
    // Business logic rules
    bool enable_entry_exit_detection = true;
    bool enable_unknown_person_alerts = true;
    std::chrono::seconds person_persistence_timeout{30};
};

class EventProcessor {
public:
    bool initialize(const EventConfig& config);
    void shutdown();
    
    // Event generation
    void processTrackingResults(const TrackingOutput& tracking_output);
    void processMatchingResults(const std::vector<MatchingResult>& matching_results);
    
    // Event handling
    void startEventWorkers(size_t worker_count);
    void stopEventWorkers();
    void registerEventHandler(EventType type, 
                            std::function<void(const ApplicationEvent&)> handler);
    
    // Event retrieval
    std::vector<ApplicationEvent> getEvents(EventType type,
                                          std::chrono::milliseconds timeout);
    
    std::vector<ApplicationEvent> getAllEvents(std::chrono::milliseconds timeout);
    
    // Configuration and monitoring
    void updateConfig(const EventConfig& config);
    EventProcessingStats getEventStats();
    
private:
    EventConfig config_;
    std::atomic<uint64_t> next_event_id_;
    
    // Event processing
    ThreadSafeQueue<ApplicationEvent> event_queue_;
    std::vector<std::thread> worker_threads_;
    std::atomic<bool> should_stop_;
    
    // Event handlers
    std::unordered_map<EventType, std::vector<std::function<void(const ApplicationEvent&)>>> 
        event_handlers_;
    std::mutex handlers_mutex_;
    
    // Business logic state
    struct PersonState {
        uint32_t identity_id;
        std::string camera_id;
        bool is_present;
        std::chrono::steady_clock::time_point last_seen;
        uint32_t track_id;
    };
    std::unordered_map<uint32_t, PersonState> person_states_;
    std::mutex states_mutex_;
    
    struct EventProcessingStats {
        uint64_t total_events_generated;
        uint64_t events_by_type[8];  // EventType count
        float average_processing_time_ms;
        uint32_t current_queue_size;
    };
    EventProcessingStats stats_;
    std::mutex stats_mutex_;
    
    void workerFunction();
    
    // Event generation logic
    void generateTrackEvents(const FaceTrack& track, const TrackingOutput& output);
    void generateIdentityEvents(const MatchingResult& result);
    void generateEntryExitEvents(const TrackingOutput& output);
    
    // Business logic
    void updatePersonStates(const TrackingOutput& output);
    void processPersonPersistence();
    bool shouldGenerateEvent(const ApplicationEvent& event);
    
    // Event routing
    void dispatchEvent(const ApplicationEvent& event);
    void updateEventStatistics(const ApplicationEvent& event, float processing_time);
};
```

**Features:**

- Comprehensive event generation for all face tracking states
- Configurable business logic rules and filtering
- Multi-threaded event processing with priority queuing
- Person state management with entry/exit detection
- Event handler registration system for extensibility
- Performance monitoring and event statistics

### Phase 3: Integration and Testing

#### Task 3.1: Recognition Integration

**Priority**: Critical  
**Estimated Time**: 6-8 hours  
**Dependencies**: Phase 2 completion

**Integration Points:**

- Connect to Face Recognition module's embedding output
- Coordinate with Face Detection module for tracking bounding boxes
- Implement real-time data flow from AI pipeline to application logic
- Handle backpressure and flow control between modules
- Ensure consistent metadata preservation through the pipeline

#### Task 3.2: Multi-Camera Coordination

**Priority**: High  
**Estimated Time**: 6-8 hours  
**Dependencies**: Task 3.1

**Features:**

- Cross-camera identity reconciliation algorithms
- Global track management across multiple camera views
- Spatial-temporal consistency for multi-camera tracking
- Event correlation and deduplication across cameras
- Resource allocation and load balancing for multi-camera scenarios

#### Task 3.3: Unit Testing Suite

**Priority**: High  
**Estimated Time**: 4-6 hours  
**Dependencies**: Phase 2 completion

**Test Coverage:**

- Database operations and indexing performance
- Face matching accuracy and performance
- Identity tracking consistency across frames
- Event generation and processing correctness
- Multi-camera coordination functionality

### Phase 4: Application Features

#### Task 4.1: System Coordinator

**Priority**: Medium  
**Estimated Time**: 6-8 hours  
**Dependencies**: Phase 3 completion

**Features:**

- Resource monitoring and adaptive allocation
- Performance optimization based on system load
- Configuration management and runtime parameter adjustment
- Health monitoring and diagnostic reporting
- Integration with system-wide resource coordination

#### Task 4.2: External API Interface

**Priority**: Medium  
**Estimated Time**: 8-12 hours  
**Dependencies**: Task 4.1

**Implementation:**

- RESTful API for external system integration
- WebSocket server for real-time event streaming
- Authentication and authorization mechanisms
- Rate limiting and connection management
- API documentation and client SDK examples

### Phase 5: Production Readiness

#### Task 5.1: Performance Optimization

**Priority**: High  
**Estimated Time**: 6-10 hours  
**Dependencies**: Phase 4 completion

**Optimization Areas:**

- Database query optimization and indexing tuning
- ARM64 NEON SIMD acceleration for similarity computation
- Memory usage optimization and garbage collection tuning
- CPU utilization optimization for sustained processing
- Event processing latency reduction and throughput improvement

#### Task 5.2: Documentation & Examples

**Priority**: Medium  
**Estimated Time**: 4-6 hours  
**Dependencies**: Phase 4 completion

**Documentation Requirements:**

- Application logic configuration and tuning guide
- Face database management and optimization guide
- Multi-camera deployment and coordination guide
- External API integration documentation and examples
- Performance optimization and troubleshooting guide

## Configuration Requirements

### Build Dependencies

```cmake
# SQLite3 database library
find_package(PkgConfig REQUIRED)
pkg_check_modules(SQLITE3 REQUIRED sqlite3)

# Eigen3 for optimized vector operations
find_package(Eigen3 REQUIRED)

# OpenCV for bounding box operations
find_package(OpenCV REQUIRED COMPONENTS core imgproc)

# Threading support
find_package(Threads REQUIRED)

# REST API and WebSocket libraries
find_package(nlohmann_json REQUIRED)
find_package(websocketpp REQUIRED)

# ARM64 NEON support
check_cxx_compiler_flag("-mfpu=neon" COMPILER_SUPPORTS_NEON)
if(COMPILER_SUPPORTS_NEON)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -mfpu=neon")
endif()
```

### Runtime Configuration

```json
{
  "application_logic": {
    "database": {
      "path": "/var/lib/aibox/face_database.db",
      "enable_indexing": true,
      "index_rebuild_threshold": 1000,
      "max_identities": 10000,
      "optimization_interval_hours": 24
    },
    "matching": {
      "similarity_threshold": 0.6,
      "similarity_metric": "cosine",
      "max_candidates": 10,
      "enable_batch_processing": true,
      "enable_neon_optimization": true,
      "worker_thread_count": 2
    },
    "tracking": {
      "max_track_age_frames": 30,
      "iou_threshold": 0.3,
      "identity_confidence_threshold": 0.7,
      "identity_stability_frames": 5,
      "enable_multi_camera": false,
      "tracking_algorithm": "kalman"
    },
    "events": {
      "enable_event_processing": true,
      "max_event_queue_size": 1000,
      "event_batch_timeout_ms": 100,
      "min_confidence_for_recognition": 0.7,
      "min_track_age_for_events": 3,
      "enable_entry_exit_detection": true,
      "enable_unknown_person_alerts": true,
      "person_persistence_timeout_seconds": 30
    },
    "api": {
      "enable_rest_api": true,
      "rest_port": 8080,
      "enable_websocket": true,
      "websocket_port": 8081,
      "max_connections": 100,
      "enable_authentication": false,
      "rate_limit_per_minute": 1000
    },
    "performance": {
      "database_cache_size_mb": 64,
      "max_memory_usage_mb": 800,
      "enable_performance_monitoring": true,
      "cpu_utilization_target": 0.8
    }
  }
}
```

## Success Criteria

### Functional Requirements

- [ ] Face database supporting 10K+ identities with sub-millisecond search
- [ ] Real-time face matching with >99% accuracy on known identities
- [ ] Multi-frame identity tracking with temporal consistency
- [ ] Event-driven business logic with configurable rules
- [ ] RESTful API and WebSocket integration for external systems

### Performance Requirements

- [ ] Database search latency <1ms for similarity queries
- [ ] Face matching processing <5ms per detection
- [ ] Memory usage <1.2GB for 10K identities (8GB config)
- [ ] CPU utilization optimized with ARM64 NEON acceleration
- [ ] Support 200+ face matches per second sustained processing

### Quality Requirements

- [ ] Comprehensive unit and integration test coverage (>90%)
- [ ] Hardware validation on Orange Pi 5 Plus/Ultra
- [ ] Multi-camera coordination accuracy validation
- [ ] API performance testing under concurrent load
- [ ] Database scalability testing with large identity sets

## Timeline Estimation

| Phase                          | Duration | Dependencies |
| ------------------------------ | -------- | ------------ |
| Phase 1: Foundation Setup      | 1 week   | Recognition  |
| Phase 2: Core Implementation   | 3 weeks  | Phase 1      |
| Phase 3: Integration & Testing | 2 weeks  | Phase 2      |
| Phase 4: Application Features  | 2 weeks  | Phase 3      |
| Phase 5: Production Readiness  | 1 week   | Phase 4      |

**Total Estimated Duration: 9 weeks**

## Next Steps

1. **Immediate Actions (Week 1)**
   - Set up project structure and database dependencies (Tasks 1.1, 1.2)
   - Begin face database manager implementation (Task 2.1)
   - Set up hardware testing environment with face recognition integration

2. **Short-term Goals (Weeks 2-4)**
   - Complete core application logic implementation
   - Implement face matching with NEON optimization
   - Develop identity tracking and event processing

3. **Medium-term Goals (Weeks 5-7)**
   - Complete integration with face recognition pipeline
   - Implement multi-camera coordination features
   - Develop external API interfaces

4. **Long-term Goals (Weeks 8-9)**
   - Performance optimization and stress testing
   - Documentation completion
   - Production deployment validation

This implementation plan provides a comprehensive roadmap for developing the Application Logic Module with ARM64 CPU optimization, efficient face database management, and real-time processing capabilities tailored for the complete AI Box system integration. 