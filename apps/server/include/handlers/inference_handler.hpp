#pragma once

#include <memory>
#include <utils/json_utils.hpp>
#include "base_handler.hpp"
#include "services/inference_service.hpp"

namespace server::handlers {

/**
 * @brief Handler for AI inference operations
 * Processes inference requests and transforms responses to JSON
 */
class InferenceHandler : public BaseHandler {
public:
    InferenceHandler(std::shared_ptr<shared::ServerConfig> config,
                    std::shared_ptr<services::IInferenceService> inference_service);
    ~InferenceHandler() override = default;

    // Main processing methods
    utils::JsonValue handleArcFaceInference(const utils::JsonValue& request_json);
    utils::JsonValue handleYoloDetection(const utils::JsonValue& request_json);
    
    // Future async methods
    utils::JsonValue handleArcFaceInferenceAsync(const utils::JsonValue& request_json);
    utils::JsonValue handleYoloDetectionAsync(const utils::JsonValue& request_json);

private:
    std::shared_ptr<services::IInferenceService> inference_service_;

    // Request parsing
    std::optional<models::ArcFaceRequest> parseArcFaceRequest(const utils::JsonValue& json);
    std::optional<models::YoloRequest> parseYoloRequest(const utils::JsonValue& json);
    
    // Response transformation
    utils::JsonValue transformArcFaceResponse(const models::ArcFaceResponse& response);
    utils::JsonValue transformYoloResponse(const models::YoloResponse& response);
    
    // Helper transformations
    utils::JsonValue transformBoundingBox(const models::BoundingBox& bbox);
    utils::JsonValue transformFaceDetection(const models::FaceDetection& face);
    utils::JsonValue transformFaceEmbedding(const models::FaceEmbedding& embedding);
    utils::JsonValue transformObjectDetection(const models::ObjectDetection& detection);
    utils::JsonValue transformFloatArray(const std::vector<float>& arr);
    
    // Validation
    bool validateInferenceRequest(const utils::JsonValue& json);
    bool validateImagePath(const std::string& image_path);
};

} // namespace server::handlers
