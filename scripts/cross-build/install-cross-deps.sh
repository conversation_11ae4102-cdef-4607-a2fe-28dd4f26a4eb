#!/bin/bash

# Cross-Compilation Dependencies Installation Script
# Installs cross-compilation toolchains and dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
TARGET_ARCH="aarch64"
INSTALL_SYSROOT=false
SYSROOT_PATH=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --arch)
            TARGET_ARCH="$2"
            shift 2
            ;;
        --sysroot)
            INSTALL_SYSROOT=true
            SYSROOT_PATH="$2"
            shift 2
            ;;
        --help)
            cat << EOF
Cross-Compilation Dependencies Installation Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --arch ARCH     Target architecture (default: aarch64)
                    Supported: aarch64, armhf, riscv64
    --sysroot PATH  Install and setup sysroot at specified path
    --help          Show this help

EXAMPLES:
    $0                                    # Install aarch64 cross-compiler
    $0 --arch armhf                       # Install ARM hard-float cross-compiler
    $0 --arch aarch64 --sysroot /opt/orangepi-sysroot  # Install with sysroot

SUPPORTED ARCHITECTURES:
    aarch64         ARM64 (Orange Pi 5 Plus, Raspberry Pi 4/5 64-bit)
    armhf           ARM hard-float (Raspberry Pi 3/4 32-bit)
    riscv64         RISC-V 64-bit (VisionFive 2, etc.)
EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Detect host platform
detect_host_platform() {
    local os=$(uname -s)
    local arch=$(uname -m)
    
    case "$os" in
        Linux)
            HOST_PLATFORM="linux"
            ;;
        Darwin)
            HOST_PLATFORM="macos"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            HOST_PLATFORM="windows"
            ;;
        *)
            log_error "Unsupported host platform: $os"
            exit 1
            ;;
    esac
    
    log_info "Host platform: $HOST_PLATFORM $arch"
}

# Check if running with appropriate privileges
check_privileges() {
    if [[ "$HOST_PLATFORM" == "linux" ]]; then
        if [[ $EUID -eq 0 ]]; then
            log_info "Running as root"
        else
            log_warning "Not running as root. Some operations may require sudo."
        fi
    fi
}

# Install cross-compilation toolchain
install_cross_toolchain() {
    log_step "Installing Cross-Compilation Toolchain for $TARGET_ARCH"
    
    case "$HOST_PLATFORM" in
        linux)
            install_linux_toolchain
            ;;
        macos)
            install_macos_toolchain
            ;;
        windows)
            install_windows_toolchain
            ;;
        *)
            log_error "Unsupported platform: $HOST_PLATFORM"
            exit 1
            ;;
    esac
}

# Install toolchain on Linux
install_linux_toolchain() {
    log_info "Installing toolchain for Linux host..."
    
    # Update package lists
    if command -v apt-get >/dev/null 2>&1; then
        log_info "Updating package lists..."
        apt-get update
        
        case "$TARGET_ARCH" in
            aarch64)
                log_info "Installing ARM64 cross-compilation toolchain..."
                apt-get install -y \
                    gcc-aarch64-linux-gnu \
                    g++-aarch64-linux-gnu \
                    libc6-dev-arm64-cross
                ;;
            armhf)
                log_info "Installing ARM hard-float cross-compilation toolchain..."
                apt-get install -y \
                    gcc-arm-linux-gnueabihf \
                    g++-arm-linux-gnueabihf \
                    libc6-dev-armhf-cross
                ;;
            riscv64)
                log_info "Installing RISC-V 64-bit cross-compilation toolchain..."
                apt-get install -y \
                    gcc-riscv64-linux-gnu \
                    g++-riscv64-linux-gnu \
                    libc6-dev-riscv64-cross
                ;;
            *)
                log_error "Unsupported target architecture: $TARGET_ARCH"
                exit 1
                ;;
        esac
    elif command -v yum >/dev/null 2>&1; then
        log_info "Installing toolchain using yum..."
        # Add RHEL/CentOS/Fedora support
        case "$TARGET_ARCH" in
            aarch64)
                yum install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu
                ;;
            *)
                log_error "Target architecture $TARGET_ARCH not supported on this distribution"
                exit 1
                ;;
        esac
    else
        log_error "No supported package manager found (apt-get, yum)"
        exit 1
    fi
}

# Install toolchain on macOS
install_macos_toolchain() {
    log_info "Installing toolchain for macOS host..."
    
    if ! command -v brew >/dev/null 2>&1; then
        log_error "Homebrew not found. Please install Homebrew first."
        exit 1
    fi
    
    case "$TARGET_ARCH" in
        aarch64)
            log_info "Installing ARM64 cross-compilation toolchain..."
            brew install aarch64-elf-gcc
            ;;
        *)
            log_error "Target architecture $TARGET_ARCH not supported on macOS"
            exit 1
            ;;
    esac
}

# Install toolchain on Windows
install_windows_toolchain() {
    log_info "Installing toolchain for Windows host..."
    log_warning "Windows cross-compilation setup requires WSL2 or MSYS2"
    log_info "Please use WSL2 with Ubuntu and run this script inside WSL2"
}

# Setup sysroot
setup_sysroot() {
    if [[ "$INSTALL_SYSROOT" != "true" ]]; then
        return
    fi
    
    log_step "Setting up Sysroot"
    
    if [[ -z "$SYSROOT_PATH" ]]; then
        log_error "Sysroot path not specified"
        exit 1
    fi
    
    log_info "Creating sysroot directory: $SYSROOT_PATH"
    mkdir -p "$SYSROOT_PATH"
    
    # Create basic sysroot structure
    mkdir -p "$SYSROOT_PATH"/{usr/{lib,include},lib,etc}
    
    case "$TARGET_ARCH" in
        aarch64)
            mkdir -p "$SYSROOT_PATH/usr/lib/aarch64-linux-gnu"
            mkdir -p "$SYSROOT_PATH/lib/aarch64-linux-gnu"
            ;;
        armhf)
            mkdir -p "$SYSROOT_PATH/usr/lib/arm-linux-gnueabihf"
            mkdir -p "$SYSROOT_PATH/lib/arm-linux-gnueabihf"
            ;;
        riscv64)
            mkdir -p "$SYSROOT_PATH/usr/lib/riscv64-linux-gnu"
            mkdir -p "$SYSROOT_PATH/lib/riscv64-linux-gnu"
            ;;
    esac
    
    log_success "Sysroot structure created at $SYSROOT_PATH"
    log_info "To use this sysroot, set environment variable:"
    log_info "  export ${TARGET_ARCH^^}_SYSROOT=$SYSROOT_PATH"
}

# Verify installation
verify_installation() {
    log_step "Verifying Cross-Compilation Installation"
    
    case "$TARGET_ARCH" in
        aarch64)
            CROSS_GCC="aarch64-linux-gnu-gcc"
            CROSS_GXX="aarch64-linux-gnu-g++"
            ;;
        armhf)
            CROSS_GCC="arm-linux-gnueabihf-gcc"
            CROSS_GXX="arm-linux-gnueabihf-g++"
            ;;
        riscv64)
            CROSS_GCC="riscv64-linux-gnu-gcc"
            CROSS_GXX="riscv64-linux-gnu-g++"
            ;;
    esac
    
    if command -v "$CROSS_GCC" >/dev/null 2>&1; then
        local gcc_version=$($CROSS_GCC --version | head -n1)
        log_success "Cross-compiler found: $gcc_version"
    else
        log_error "Cross-compiler not found: $CROSS_GCC"
        exit 1
    fi
    
    if command -v "$CROSS_GXX" >/dev/null 2>&1; then
        local gxx_version=$($CROSS_GXX --version | head -n1)
        log_success "Cross-compiler found: $gxx_version"
    else
        log_error "Cross-compiler not found: $CROSS_GXX"
        exit 1
    fi
}

# Show installation summary
show_summary() {
    log_step "Installation Summary"
    
    echo "Target Architecture: $TARGET_ARCH"
    echo "Host Platform: $HOST_PLATFORM"
    echo "Sysroot: $(if [[ "$INSTALL_SYSROOT" == "true" ]]; then echo "$SYSROOT_PATH"; else echo "Not installed"; fi)"
    echo ""
    
    log_success "Cross-compilation dependencies installed successfully!"
    log_info "You can now use the cross-compilation build scripts:"
    
    case "$TARGET_ARCH" in
        aarch64)
            log_info "  ./scripts/cross-build/build-orangepi.sh"
            ;;
        armhf)
            log_info "  ./scripts/cross-build/build-raspberrypi.sh"
            ;;
        riscv64)
            log_info "  ./scripts/cross-build/build-visionfive.sh"
            ;;
    esac
}

# Main execution
main() {
    log_step "Cross-Compilation Dependencies Installation"
    
    detect_host_platform
    check_privileges
    install_cross_toolchain
    setup_sysroot
    verify_installation
    show_summary
}

# Run main function
main "$@"
