#include "utils/regex_utils.hpp"
#include <algorithm>
#include <sstream>
#include <stdexcept>

namespace utils {

    // =============================================================================
    // RegexBuilder Implementation
    // =============================================================================

    RegexBuilder::RegexBuilder() : pattern_("") {}

    RegexBuilder::RegexBuilder(const RegexBuilder& other) : pattern_(other.pattern_) {}

    RegexBuilder& RegexBuilder::operator=(const RegexBuilder& other) {
        if (this != &other) {
            pattern_ = other.pattern_;
        }
        return *this;
    }

    RegexBuilder& RegexBuilder::literal(const std::string& text) {
        pattern_ += escape(text);
        return *this;
    }

    RegexBuilder& RegexBuilder::raw(const std::string& pattern) {
        pattern_ += pattern;
        return *this;
    }

    RegexBuilder& RegexBuilder::anyChar() {
        pattern_ += ".";
        return *this;
    }

    RegexBuilder& RegexBuilder::digit() {
        pattern_ += "\\d";
        return *this;
    }

    RegexBuilder& RegexBuilder::word() {
        pattern_ += "\\w";
        return *this;
    }

    RegexBuilder& RegexBuilder::whitespace() {
        pattern_ += "\\s";
        return *this;
    }

    RegexBuilder& RegexBuilder::charClass(const std::string& chars) {
        pattern_ += "[" + chars + "]";
        return *this;
    }

    RegexBuilder& RegexBuilder::notCharClass(const std::string& chars) {
        pattern_ += "[^" + chars + "]";
        return *this;
    }

    RegexBuilder& RegexBuilder::range(char from, char to) {
        pattern_ += "[" + std::string(1, from) + "-" + std::string(1, to) + "]";
        return *this;
    }

    RegexBuilder& RegexBuilder::zeroOrMore() {
        pattern_ += "*";
        return *this;
    }

    RegexBuilder& RegexBuilder::oneOrMore() {
        pattern_ += "+";
        return *this;
    }

    RegexBuilder& RegexBuilder::zeroOrOne() {
        pattern_ += "?";
        return *this;
    }

    RegexBuilder& RegexBuilder::exactly(int count) {
        pattern_ += "{" + std::to_string(count) + "}";
        return *this;
    }

    RegexBuilder& RegexBuilder::between(int min, int max) {
        pattern_ += "{" + std::to_string(min) + "," + std::to_string(max) + "}";
        return *this;
    }

    RegexBuilder& RegexBuilder::atLeast(int min) {
        pattern_ += "{" + std::to_string(min) + ",}";
        return *this;
    }

    RegexBuilder& RegexBuilder::startOfString() {
        pattern_ += "^";
        return *this;
    }

    RegexBuilder& RegexBuilder::endOfString() {
        pattern_ += "$";
        return *this;
    }

    RegexBuilder& RegexBuilder::wordBoundary() {
        pattern_ += "\\b";
        return *this;
    }

    RegexBuilder& RegexBuilder::notWordBoundary() {
        pattern_ += "\\B";
        return *this;
    }

    RegexBuilder& RegexBuilder::startGroup() {
        pattern_ += "(";
        return *this;
    }

    RegexBuilder& RegexBuilder::startNonCapturingGroup() {
        pattern_ += "(?:";
        return *this;
    }

    RegexBuilder& RegexBuilder::endGroup() {
        pattern_ += ")";
        return *this;
    }

    RegexBuilder& RegexBuilder::or_() {
        pattern_ += "|";
        return *this;
    }

    RegexBuilder& RegexBuilder::email() {
        pattern_ += R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})";
        return *this;
    }

    RegexBuilder& RegexBuilder::url() {
        pattern_ += R"(https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?)";
        return *this;
    }

    RegexBuilder& RegexBuilder::phoneNumber() {
        pattern_ += R"(\+?[1-9]\d{6,14})";
        return *this;
    }

    RegexBuilder& RegexBuilder::ipAddress() {
        pattern_ += R"((?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))";
        return *this;
    }

    RegexBuilder& RegexBuilder::date() {
        pattern_ += R"(\d{4}-\d{2}-\d{2})";
        return *this;
    }

    RegexBuilder& RegexBuilder::time() {
        pattern_ += R"(\d{2}:\d{2}:\d{2})";
        return *this;
    }

    std::string RegexBuilder::build() const {
        return pattern_;
    }

    std::regex RegexBuilder::buildRegex(std::regex_constants::syntax_option_type flags) const {
        return std::regex(pattern_, flags);
    }

    RegexBuilder& RegexBuilder::reset() {
        pattern_.clear();
        return *this;
    }

    std::string RegexBuilder::escape(const std::string& text) const {
        std::string result;
        result.reserve(text.length() * 2); // Reserve space for potential escaping

        for (char c : text) {
            switch (c) {
                case '^': case '$': case '\\': case '.': case '*': case '+':
                case '?': case '(': case ')': case '[': case ']': case '{':
                case '}': case '|':
                    result += '\\';
                    result += c;
                    break;
                default:
                    result += c;
                    break;
            }
        }

        return result;
    }

    // =============================================================================
    // RegexUtils Implementation
    // =============================================================================

    bool RegexUtils::matches(const std::string& text, const std::string& pattern,
                            std::regex_constants::syntax_option_type flags) {
        try {
            std::regex regex(pattern, flags);
            return std::regex_match(text, regex);
        } catch (const std::regex_error&) {
            return false;
        }
    }

    bool RegexUtils::matches(const std::string& text, const std::regex& regex) {
        try {
            return std::regex_match(text, regex);
        } catch (const std::regex_error&) {
            return false;
        }
    }

    std::optional<std::smatch> RegexUtils::findFirst(const std::string& text, const std::string& pattern,
                                                    std::regex_constants::syntax_option_type flags) {
        try {
            std::regex regex(pattern, flags);
            std::smatch match;
            if (std::regex_search(text, match, regex)) {
                return match;
            }
        } catch (const std::regex_error&) {
            // Pattern error
        }
        return std::nullopt;
    }

    std::vector<std::smatch> RegexUtils::findAll(const std::string& text, const std::string& pattern,
                                                std::regex_constants::syntax_option_type flags) {
        std::vector<std::smatch> matches;
        try {
            std::regex regex(pattern, flags);
            std::sregex_iterator iter(text.begin(), text.end(), regex);
            std::sregex_iterator end;

            for (; iter != end; ++iter) {
                matches.push_back(*iter);
            }
        } catch (const std::regex_error&) {
            // Pattern error
        }
        return matches;
    }

    std::string RegexUtils::replaceFirst(const std::string& text, const std::string& pattern,
                                        const std::string& replacement,
                                        std::regex_constants::syntax_option_type flags) {
        try {
            std::regex regex(pattern, flags);
            return std::regex_replace(text, regex, replacement,
                                    std::regex_constants::format_first_only);
        } catch (const std::regex_error&) {
            return text; // Return original text on error
        }
    }

    std::string RegexUtils::replaceAll(const std::string& text, const std::string& pattern,
                                      const std::string& replacement,
                                      std::regex_constants::syntax_option_type flags) {
        try {
            std::regex regex(pattern, flags);
            return std::regex_replace(text, regex, replacement);
        } catch (const std::regex_error&) {
            return text; // Return original text on error
        }
    }

    std::vector<std::string> RegexUtils::split(const std::string& text, const std::string& pattern,
                                              std::regex_constants::syntax_option_type flags) {
        std::vector<std::string> result;
        try {
            std::regex regex(pattern, flags);
            std::sregex_token_iterator iter(text.begin(), text.end(), regex, -1);
            std::sregex_token_iterator end;

            for (; iter != end; ++iter) {
                result.push_back(iter->str());
            }
        } catch (const std::regex_error&) {
            result.push_back(text); // Return original text as single element on error
        }
        return result;
    }

    std::vector<std::string> RegexUtils::extractGroups(const std::string& text, const std::string& pattern,
                                                       std::regex_constants::syntax_option_type flags) {
        std::vector<std::string> groups;
        try {
            std::regex regex(pattern, flags);
            std::smatch match;
            if (std::regex_search(text, match, regex)) {
                for (size_t i = 1; i < match.size(); ++i) { // Skip full match (index 0)
                    groups.push_back(match[i].str());
                }
            }
        } catch (const std::regex_error&) {
            // Pattern error
        }
        return groups;
    }

    std::map<std::string, std::string> RegexUtils::extractNamedGroups(const std::string& text, const std::string& pattern,
                                                                     const std::vector<std::string>& groupNames,
                                                                     std::regex_constants::syntax_option_type flags) {
        std::map<std::string, std::string> namedGroups;
        try {
            std::regex regex(pattern, flags);
            std::smatch match;
            if (std::regex_search(text, match, regex)) {
                for (size_t i = 1; i < match.size() && i - 1 < groupNames.size(); ++i) {
                    namedGroups[groupNames[i - 1]] = match[i].str();
                }
            }
        } catch (const std::regex_error&) {
            // Pattern error
        }
        return namedGroups;
    }

    // =============================================================================
    // Validation Functions
    // =============================================================================

    bool RegexUtils::isValidEmail(const std::string& email) {
        const std::string emailPattern = R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})";
        return matches(email, emailPattern);
    }

    bool RegexUtils::isValidUrl(const std::string& url) {
        const std::string urlPattern = R"(https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?)";
        return matches(url, urlPattern);
    }

    bool RegexUtils::isValidPhoneNumber(const std::string& phone) {
        const std::string phonePattern = R"(^\+?[1-9]\d{6,14}$)";
        return matches(phone, phonePattern);
    }

    bool RegexUtils::isValidIpAddress(const std::string& ip) {
        const std::string ipPattern = R"(^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)";
        return matches(ip, ipPattern);
    }

    bool RegexUtils::isValidDate(const std::string& date) {
        const std::string datePattern = R"(^\d{4}-\d{2}-\d{2}$)";
        if (!matches(date, datePattern)) {
            return false;
        }

        // Additional validation for date ranges
        auto groups = extractGroups(date, R"(^(\d{4})-(\d{2})-(\d{2})$)");
        if (groups.size() == 3) {
            int year = std::stoi(groups[0]);
            int month = std::stoi(groups[1]);
            int day = std::stoi(groups[2]);

            if (year < 1900 || year > 2100) return false;
            if (month < 1 || month > 12) return false;
            if (day < 1 || day > 31) return false;

            // Basic month-day validation
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) return false;
            if (month == 2 && day > 29) return false;
        }

        return true;
    }

    bool RegexUtils::isValidTime(const std::string& time) {
        const std::string timePattern = R"(^\d{2}:\d{2}:\d{2}$)";
        if (!matches(time, timePattern)) {
            return false;
        }

        // Additional validation for time ranges
        auto groups = extractGroups(time, R"(^(\d{2}):(\d{2}):(\d{2})$)");
        if (groups.size() == 3) {
            int hour = std::stoi(groups[0]);
            int minute = std::stoi(groups[1]);
            int second = std::stoi(groups[2]);

            if (hour < 0 || hour > 23) return false;
            if (minute < 0 || minute > 59) return false;
            if (second < 0 || second > 59) return false;
        }

        return true;
    }

    // =============================================================================
    // Utility Functions
    // =============================================================================

    std::string RegexUtils::escape(const std::string& text) {
        std::string result;
        result.reserve(text.length() * 2);

        for (char c : text) {
            switch (c) {
                case '^': case '$': case '\\': case '.': case '*': case '+':
                case '?': case '(': case ')': case '[': case ']': case '{':
                case '}': case '|':
                    result += '\\';
                    result += c;
                    break;
                default:
                    result += c;
                    break;
            }
        }

        return result;
    }

    bool RegexUtils::isValidPattern(const std::string& pattern,
                                   std::regex_constants::syntax_option_type flags) {
        try {
            std::regex regex(pattern, flags);
            return true;
        } catch (const std::regex_error&) {
            return false;
        }
    }

    std::string RegexUtils::getPatternError(const std::string& pattern) {
        try {
            std::regex regex(pattern);
            return "Pattern is valid";
        } catch (const std::regex_error& e) {
            std::string errorMsg = "Regex error: ";
            switch (e.code()) {
                case std::regex_constants::error_collate:
                    errorMsg += "Invalid collating element";
                    break;
                case std::regex_constants::error_ctype:
                    errorMsg += "Invalid character class";
                    break;
                case std::regex_constants::error_escape:
                    errorMsg += "Invalid escape sequence";
                    break;
                case std::regex_constants::error_backref:
                    errorMsg += "Invalid back reference";
                    break;
                case std::regex_constants::error_brack:
                    errorMsg += "Mismatched brackets";
                    break;
                case std::regex_constants::error_paren:
                    errorMsg += "Mismatched parentheses";
                    break;
                case std::regex_constants::error_brace:
                    errorMsg += "Mismatched braces";
                    break;
                case std::regex_constants::error_badbrace:
                    errorMsg += "Invalid range in braces";
                    break;
                case std::regex_constants::error_range:
                    errorMsg += "Invalid character range";
                    break;
                case std::regex_constants::error_space:
                    errorMsg += "Insufficient memory";
                    break;
                case std::regex_constants::error_badrepeat:
                    errorMsg += "Invalid repeat";
                    break;
                case std::regex_constants::error_complexity:
                    errorMsg += "Expression too complex";
                    break;
                case std::regex_constants::error_stack:
                    errorMsg += "Insufficient stack space";
                    break;
                default:
                    errorMsg += "Unknown error";
                    break;
            }
            return errorMsg;
        }
    }

    // =============================================================================
    // Convenience Functions
    // =============================================================================

    bool regexMatch(const std::string& text, const std::string& pattern) {
        return RegexUtils::matches(text, pattern);
    }

    std::string regexReplace(const std::string& text, const std::string& pattern, const std::string& replacement) {
        return RegexUtils::replaceAll(text, pattern, replacement);
    }

    std::vector<std::string> regexSplit(const std::string& text, const std::string& pattern) {
        return RegexUtils::split(text, pattern);
    }

    RegexBuilder regex() {
        return RegexBuilder();
    }

} // namespace utils
