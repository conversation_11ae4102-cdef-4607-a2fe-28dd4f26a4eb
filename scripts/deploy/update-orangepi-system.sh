#!/bin/bash

# Update Orange Pi System for c-aibox compatibility
# Updates system libraries to support newer GLIBC requirements

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
SSH_KEY=""
UPDATE_SYSTEM=true
INSTALL_BACKPORTS=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --port)
            ORANGE_PI_PORT="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --no-update)
            UPDATE_SYSTEM=false
            shift
            ;;
        --no-backports)
            INSTALL_BACKPORTS=false
            shift
            ;;
        --help)
            cat << EOF
Update Orange Pi System for c-aibox compatibility

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP         Orange Pi IP address

OPTIONS:
    --user USER     SSH username (default: orangepi)
    --port PORT     SSH port (default: 22)
    --ssh-key PATH  SSH private key file
    --no-update     Skip system update
    --no-backports  Skip backports installation
    --help          Show this help

DESCRIPTION:
    Updates Orange Pi system libraries to support c-aibox applications.
    This includes updating GLIBC and other system libraries.

EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    log_info "Usage: $0 --ip IP_ADDRESS"
    exit 1
fi

# SSH command builder
build_ssh_cmd() {
    local ssh_cmd="ssh"
    
    if [[ -n "$SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY"
    fi
    
    ssh_cmd="$ssh_cmd -p $ORANGE_PI_PORT $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "$ssh_cmd"
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Testing connection to $ORANGE_PI_USER@$ORANGE_PI_IP:$ORANGE_PI_PORT"
    
    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed"
        exit 1
    fi
}

# Check current system
check_current_system() {
    log_step "Checking Current System"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Current system information:"
    $ssh_cmd "cat /etc/os-release | grep PRETTY_NAME"
    $ssh_cmd "ldd --version | head -n1"
    
    log_info "Current GLIBC version:"
    $ssh_cmd "strings /lib/aarch64-linux-gnu/libc.so.6 | grep GLIBC_ | tail -5"
}

# Update system packages
update_system() {
    if [[ "$UPDATE_SYSTEM" != "true" ]]; then
        return
    fi
    
    log_step "Updating System Packages"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Updating package lists..."
    $ssh_cmd "sudo apt-get update"
    
    log_info "Upgrading system packages..."
    $ssh_cmd "sudo apt-get upgrade -y"
    
    log_success "System update completed"
}

# Add backports repository
add_backports() {
    if [[ "$INSTALL_BACKPORTS" != "true" ]]; then
        return
    fi
    
    log_step "Adding Backports Repository"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Adding Debian backports repository..."
    $ssh_cmd "echo 'deb http://deb.debian.org/debian bookworm-backports main' | sudo tee /etc/apt/sources.list.d/backports.list"
    
    log_info "Updating package lists with backports..."
    $ssh_cmd "sudo apt-get update"
    
    log_success "Backports repository added"
}

# Install newer libraries
install_newer_libraries() {
    log_step "Installing Newer Libraries"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Installing development libraries..."
    $ssh_cmd "sudo apt-get install -y build-essential"
    
    log_info "Installing runtime libraries..."
    $ssh_cmd "sudo apt-get install -y libc6-dev libstdc++6"
    
    # Try to install newer versions from backports
    if [[ "$INSTALL_BACKPORTS" == "true" ]]; then
        log_info "Attempting to install newer libraries from backports..."
        $ssh_cmd "sudo apt-get install -y -t bookworm-backports libc6 libc6-dev libstdc++6 || true"
    fi
    
    log_success "Library installation completed"
}

# Install c-aibox dependencies
install_dependencies() {
    log_step "Installing c-aibox Dependencies"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Installing GStreamer libraries..."
    $ssh_cmd "sudo apt-get install -y \
        libgstreamer1.0-0 \
        libgstreamer-plugins-base1.0-0 \
        libgstreamer-plugins-good1.0-0 \
        libgstreamer-plugins-bad1.0-0 \
        gstreamer1.0-plugins-ugly \
        gstreamer1.0-libav \
        gstreamer1.0-tools"
    
    log_info "Installing networking libraries..."
    $ssh_cmd "sudo apt-get install -y \
        libcurl4-openssl-dev \
        libssl-dev \
        libcrypto++-dev"
    
    log_info "Installing multimedia libraries..."
    $ssh_cmd "sudo apt-get install -y \
        libavcodec-dev \
        libavformat-dev \
        libavutil-dev \
        libswscale-dev"
    
    log_success "Dependencies installation completed"
}

# Check final system state
check_final_system() {
    log_step "Checking Final System State"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Updated system information:"
    $ssh_cmd "ldd --version | head -n1"
    
    log_info "Available GLIBC versions:"
    $ssh_cmd "strings /lib/aarch64-linux-gnu/libc.so.6 | grep GLIBC_ | tail -10"
    
    log_info "Available GLIBCXX versions:"
    $ssh_cmd "strings /usr/lib/aarch64-linux-gnu/libstdc++.so.6 | grep GLIBCXX | tail -10"
    
    log_info "System resources after update:"
    $ssh_cmd "free -h && df -h /"
}

# Test with simple executable
test_simple_executable() {
    log_step "Testing Simple Executable"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Create a simple test program
    log_info "Creating simple test program..."
    cat > /tmp/test_orangepi.cpp << 'EOF'
#include <iostream>
#include <string>
#include <chrono>

int main() {
    std::cout << "Hello from Orange Pi!" << std::endl;
    std::cout << "System test successful!" << std::endl;
    
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::cout << "Current time: " << std::ctime(&time_t);
    
    return 0;
}
EOF
    
    # Compile with cross-compiler
    log_info "Cross-compiling test program..."
    aarch64-linux-gnu-g++ -static-libgcc -static-libstdc++ -o /tmp/test_orangepi /tmp/test_orangepi.cpp
    
    # Transfer to Orange Pi
    log_info "Transferring test program..."
    scp -P $ORANGE_PI_PORT /tmp/test_orangepi $ORANGE_PI_USER@$ORANGE_PI_IP:/tmp/
    
    # Test execution
    log_info "Testing execution on Orange Pi..."
    if $ssh_cmd "/tmp/test_orangepi"; then
        log_success "Simple executable test passed!"
    else
        log_error "Simple executable test failed"
        return 1
    fi
    
    # Clean up
    $ssh_cmd "rm -f /tmp/test_orangepi"
    rm -f /tmp/test_orangepi /tmp/test_orangepi.cpp
}

# Show summary
show_summary() {
    log_step "Update Summary"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    echo "Orange Pi: $ORANGE_PI_USER@$ORANGE_PI_IP:$ORANGE_PI_PORT"
    echo "System Update: $(if [[ "$UPDATE_SYSTEM" == "true" ]]; then echo "Completed"; else echo "Skipped"; fi)"
    echo "Backports: $(if [[ "$INSTALL_BACKPORTS" == "true" ]]; then echo "Installed"; else echo "Skipped"; fi)"
    echo ""
    
    log_success "Orange Pi system update completed!"
    log_info "The system is now ready for c-aibox deployment"
    log_info "Next steps:"
    log_info "  1. Deploy c-aibox: ./scripts/deploy/deploy-to-orangepi.sh --ip $ORANGE_PI_IP"
    log_info "  2. Test deployment: ./scripts/deploy/test-on-orangepi.sh --ip $ORANGE_PI_IP"
}

# Main execution
main() {
    log_step "Orange Pi System Update for c-aibox"
    
    test_ssh_connection
    check_current_system
    update_system
    add_backports
    install_newer_libraries
    install_dependencies
    check_final_system
    test_simple_executable
    show_summary
}

# Run main function
main "$@"
