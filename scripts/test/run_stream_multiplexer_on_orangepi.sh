#!/bin/bash

# Deploy and Run Stream Multiplexer Test on Real Orange Pi Device
# This script deploys the test to Orange Pi and executes it remotely

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# Load SSH configuration
load_ssh_config() {
    log_step "Loading SSH Configuration"
    
    if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
        source "$PROJECT_ROOT/.orangepi_ssh"
        log_success "SSH configuration loaded from .orangepi_ssh"
        log_info "Orange Pi IP: $ORANGE_PI_IP"
        log_info "SSH Key: $ORANGE_PI_SSH_KEY"
    else
        log_error "SSH configuration file not found: $PROJECT_ROOT/.orangepi_ssh"
        exit 1
    fi
    
    # Verify SSH key exists
    if [[ ! -f "$PROJECT_ROOT/$ORANGE_PI_SSH_KEY" ]]; then
        log_error "SSH key not found: $PROJECT_ROOT/$ORANGE_PI_SSH_KEY"
        exit 1
    fi
}

# Build SSH and SCP commands
build_ssh_cmd() {
    echo "ssh -i $PROJECT_ROOT/$ORANGE_PI_SSH_KEY -p $ORANGE_PI_PORT $ORANGE_PI_USER@$ORANGE_PI_IP"
}

build_scp_cmd() {
    echo "scp -i $PROJECT_ROOT/$ORANGE_PI_SSH_KEY -P $ORANGE_PI_PORT"
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established to $ORANGE_PI_IP"
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
        log_info "Please check:"
        log_info "  1. Orange Pi is powered on and connected to network"
        log_info "  2. IP address is correct: $ORANGE_PI_IP"
        log_info "  3. SSH key is valid: $ORANGE_PI_SSH_KEY"
        exit 1
    fi
}

# Deploy test script to Orange Pi
deploy_test_script() {
    log_step "Deploying Stream Multiplexer Test Script"
    
    local scp_cmd=$(build_scp_cmd)
    local ssh_cmd=$(build_ssh_cmd)
    local test_script="$SCRIPT_DIR/test_stream_multiplexer_orangepi.sh"
    
    # Create remote test directory
    log_info "Creating remote test directory..."
    $ssh_cmd "mkdir -p /home/<USER>/tests"
    
    # Copy test script
    log_info "Copying test script to Orange Pi..."
    $scp_cmd "$test_script" "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/tests/"
    
    # Make script executable
    log_info "Making script executable..."
    $ssh_cmd "chmod +x /home/<USER>/tests/test_stream_multiplexer_orangepi.sh"
    
    log_success "Test script deployed successfully"
}

# Check Orange Pi system requirements
check_system_requirements() {
    log_step "Checking Orange Pi System Requirements"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Check if running on Orange Pi
    log_info "Verifying Orange Pi platform..."
    if $ssh_cmd "cat /proc/device-tree/model 2>/dev/null | grep -i 'orange pi'" >/dev/null 2>&1; then
        local model=$($ssh_cmd "cat /proc/device-tree/model 2>/dev/null | tr -d '\0'")
        log_success "Confirmed Orange Pi platform: $model"
    else
        log_warning "Could not verify Orange Pi platform"
    fi
    
    # Check available memory
    log_info "Checking available memory..."
    local mem_info=$($ssh_cmd "free -h | grep Mem")
    log_info "Memory: $mem_info"
    
    # Check CPU cores
    log_info "Checking CPU cores..."
    local cpu_cores=$($ssh_cmd "nproc")
    log_info "CPU cores: $cpu_cores"
    
    # Check for required tools
    log_info "Checking for required tools..."
    if $ssh_cmd "which g++ >/dev/null 2>&1"; then
        log_success "g++ compiler found"
    else
        log_warning "g++ compiler not found - installing..."
        $ssh_cmd "sudo apt-get update && sudo apt-get install -y build-essential"
    fi
}

# Test RTSP camera connectivity
test_rtsp_connectivity() {
    log_step "Testing RTSP Camera Connectivity"
    
    local ssh_cmd=$(build_ssh_cmd)
    local rtsp_url="rtsp://admin:CMC2024!@***************:554/streaming/channels/01"
    
    log_info "Testing RTSP camera at: $rtsp_url"
    
    # Test with ffprobe if available
    if $ssh_cmd "which ffprobe >/dev/null 2>&1"; then
        log_info "Testing with ffprobe..."
        if $ssh_cmd "timeout 10 ffprobe -v quiet -print_format json -show_streams '$rtsp_url'" >/dev/null 2>&1; then
            log_success "RTSP camera is accessible"
            return 0
        else
            log_warning "RTSP camera test with ffprobe failed"
        fi
    fi
    
    # Test basic network connectivity to camera IP
    log_info "Testing network connectivity to camera..."
    if $ssh_cmd "ping -c 3 ***************" >/dev/null 2>&1; then
        log_success "Camera IP is reachable"
    else
        log_warning "Camera IP is not reachable"
    fi
    
    return 1
}

# Run the Stream Multiplexer test
run_stream_multiplexer_test() {
    log_step "Running Stream Multiplexer Test on Orange Pi"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    log_info "Executing test script on Orange Pi..."
    log_info "This may take several minutes..."
    
    # Run the test and capture output
    if $ssh_cmd "cd /home/<USER>/tests && ./test_stream_multiplexer_orangepi.sh" 2>&1; then
        log_success "Stream Multiplexer test completed successfully!"
    else
        log_error "Stream Multiplexer test failed"
        return 1
    fi
}

# Collect test results
collect_test_results() {
    log_step "Collecting Test Results"
    
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    local timestamp=$(date +%Y%m%d-%H%M%S)
    local results_dir="$PROJECT_ROOT/test_results_$timestamp"
    
    mkdir -p "$results_dir"
    
    log_info "Downloading test results..."
    
    # Download test logs
    if $ssh_cmd "test -f /tmp/stream_multiplexer_test/test_results.log"; then
        $scp_cmd "$ORANGE_PI_USER@$ORANGE_PI_IP:/tmp/stream_multiplexer_test/test_results.log" "$results_dir/"
        log_success "Downloaded test_results.log"
    fi
    
    # Download resource monitoring logs
    if $ssh_cmd "test -f /tmp/stream_multiplexer_test/resource_monitor.log"; then
        $scp_cmd "$ORANGE_PI_USER@$ORANGE_PI_IP:/tmp/stream_multiplexer_test/resource_monitor.log" "$results_dir/"
        log_success "Downloaded resource_monitor.log"
    fi
    
    # Download test report
    if $ssh_cmd "test -f /tmp/stream_multiplexer_test/test_report.md"; then
        $scp_cmd "$ORANGE_PI_USER@$ORANGE_PI_IP:/tmp/stream_multiplexer_test/test_report.md" "$results_dir/"
        log_success "Downloaded test_report.md"
    fi
    
    log_success "Test results collected in: $results_dir"
    
    # Display summary
    if [[ -f "$results_dir/test_results.log" ]]; then
        log_info "Test Results Summary:"
        echo "----------------------------------------"
        cat "$results_dir/test_results.log"
        echo "----------------------------------------"
    fi
}

# Main execution
main() {
    echo -e "${BLUE}"
    echo "======================================================="
    echo "  Stream Multiplexer Test on Real Orange Pi Device"
    echo "======================================================="
    echo -e "${NC}"
    
    load_ssh_config
    test_ssh_connection
    check_system_requirements
    test_rtsp_connectivity
    deploy_test_script
    
    if run_stream_multiplexer_test; then
        collect_test_results
        log_success "Stream Multiplexer testing completed successfully!"
    else
        log_error "Stream Multiplexer testing failed"
        collect_test_results
        exit 1
    fi
}

# Run main function
main "$@"
