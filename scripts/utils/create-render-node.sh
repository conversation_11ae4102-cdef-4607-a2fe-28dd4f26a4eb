#!/bin/bash

# Script to create missing DRI render node for GPU acceleration
# This is needed for some systems where the render node is not automatically created

RENDER_NODE="/dev/dri/renderD128"

if [ ! -e "$RENDER_NODE" ]; then
    echo "Creating missing render node: $RENDER_NODE"
    sudo mknod "$RENDER_NODE" c 226 128
    sudo chown root:video "$RENDER_NODE"
    sudo chmod 666 "$RENDER_NODE"
    echo "Render node created successfully"
else
    echo "Render node already exists: $RENDER_NODE"
fi

# List all DRI devices
echo "Available DRI devices:"
ls -la /dev/dri/
