#include "handlers/inference_handler.hpp"
#include "shared/config.hpp"
#include <iostream>

namespace server::handlers {

InferenceHandler::InferenceHandler(std::shared_ptr<shared::ServerConfig> config,
                                 std::shared_ptr<services::IInferenceService> inference_service)
    : BaseHandler(config), inference_service_(inference_service) {
    logInfo("InferenceHandler initialized", "InferenceHandler");
}

// Main processing methods
utils::JsonValue InferenceHandler::handleArcFaceInference(const utils::JsonValue& request_json) {
    logInfo("Processing ArcFace inference request", "InferenceHandler");
    
    try {
        // Validate request
        if (!validateInferenceRequest(request_json)) {
            return createErrorResponse("Invalid request format", shared::AppConstants::ERROR_INVALID_REQUEST);
        }
        
        // Parse request
        auto request_opt = parseArcFaceRequest(request_json);
        if (!request_opt.has_value()) {
            return createErrorResponse("Failed to parse ArcFace request", shared::AppConstants::ERROR_INVALID_REQUEST);
        }
        
        auto request = request_opt.value();
        
        // Validate image path
        if (!validateImagePath(request.image_path)) {
            return createErrorResponse("Invalid or missing image path", shared::AppConstants::ERROR_FILE_NOT_FOUND);
        }
        
        // Process inference
        auto response = inference_service_->processArcFaceInference(request);
        
        // Transform response
        return transformArcFaceResponse(response);
        
    } catch (const std::exception& e) {
        logError("ArcFace inference exception: " + std::string(e.what()), "InferenceHandler");
        return createErrorResponse("Internal server error", shared::AppConstants::ERROR_INTERNAL, 500);
    }
}

utils::JsonValue InferenceHandler::handleYoloDetection(const utils::JsonValue& request_json) {
    logInfo("Processing YOLO detection request", "InferenceHandler");
    
    try {
        // Validate request
        if (!validateInferenceRequest(request_json)) {
            return createErrorResponse("Invalid request format", shared::AppConstants::ERROR_INVALID_REQUEST);
        }
        
        // Parse request
        auto request_opt = parseYoloRequest(request_json);
        if (!request_opt.has_value()) {
            return createErrorResponse("Failed to parse YOLO request", shared::AppConstants::ERROR_INVALID_REQUEST);
        }
        
        auto request = request_opt.value();
        
        // Validate image path
        if (!validateImagePath(request.image_path)) {
            return createErrorResponse("Invalid or missing image path", shared::AppConstants::ERROR_FILE_NOT_FOUND);
        }
        
        // Process inference
        auto response = inference_service_->processYoloInference(request);
        
        // Transform response
        return transformYoloResponse(response);
        
    } catch (const std::exception& e) {
        logError("YOLO detection exception: " + std::string(e.what()), "InferenceHandler");
        return createErrorResponse("Internal server error", shared::AppConstants::ERROR_INTERNAL, 500);
    }
}

utils::JsonValue InferenceHandler::handleArcFaceInferenceAsync(const utils::JsonValue& /*request_json*/) {
    return createErrorResponse("Async inference not yet implemented", "NOT_IMPLEMENTED", 501);
}

utils::JsonValue InferenceHandler::handleYoloDetectionAsync(const utils::JsonValue& /*request_json*/) {
    return createErrorResponse("Async inference not yet implemented", "NOT_IMPLEMENTED", 501);
}

// Request parsing
std::optional<models::ArcFaceRequest> InferenceHandler::parseArcFaceRequest(const utils::JsonValue& json) {
    models::ArcFaceRequest request;
    
    // Extract required fields
    request.image_path = extractStringField(json, "image_path");
    if (request.image_path.empty()) {
        return std::nullopt;
    }
    
    // Extract optional fields
    auto model_version = extractStringField(json, "model_version");
    if (!model_version.empty()) {
        request.model_version = model_version;
    }
    
    auto timeout_ms = extractIntField(json, "timeout_ms");
    if (timeout_ms.has_value()) {
        request.timeout_ms = timeout_ms.value();
    }
    
    auto confidence_threshold = extractFloatField(json, "confidence_threshold");
    if (confidence_threshold.has_value()) {
        request.confidence_threshold = confidence_threshold.value();
    }
    
    auto extract_features = extractBoolField(json, "extract_features");
    if (extract_features.has_value()) {
        request.extract_features = extract_features.value();
    }
    
    return request.isValid() ? std::make_optional(request) : std::nullopt;
}

std::optional<models::YoloRequest> InferenceHandler::parseYoloRequest(const utils::JsonValue& json) {
    models::YoloRequest request;
    
    // Extract required fields
    request.image_path = extractStringField(json, "image_path");
    if (request.image_path.empty()) {
        return std::nullopt;
    }
    
    // Extract optional fields
    auto model_version = extractStringField(json, "model_version");
    if (!model_version.empty()) {
        request.model_version = model_version;
    }
    
    auto timeout_ms = extractIntField(json, "timeout_ms");
    if (timeout_ms.has_value()) {
        request.timeout_ms = timeout_ms.value();
    }
    
    auto confidence_threshold = extractFloatField(json, "confidence_threshold");
    if (confidence_threshold.has_value()) {
        request.confidence_threshold = confidence_threshold.value();
    }
    
    auto nms_threshold = extractFloatField(json, "nms_threshold");
    if (nms_threshold.has_value()) {
        request.nms_threshold = nms_threshold.value();
    }
    
    // TODO: Extract target_classes array when needed
    
    return request.isValid() ? std::make_optional(request) : std::nullopt;
}

// Response transformation
utils::JsonValue InferenceHandler::transformArcFaceResponse(const models::ArcFaceResponse& response) {
    utils::JsonObject result;
    
    result["success"] = utils::JsonValue(response.success);
    result["message"] = utils::JsonValue(response.message);
    result["processing_time_ms"] = createDuration(response.processing_time);
    result["model_version"] = utils::JsonValue(response.model_version);
    result["total_faces_detected"] = utils::JsonValue(response.total_faces_detected);
    
    if (response.error_code.has_value()) {
        result["error_code"] = utils::JsonValue(response.error_code.value());
    }
    
    // Transform faces array
    if (!response.faces.empty()) {
        utils::JsonArray faces_array;
        for (const auto& face : response.faces) {
            faces_array.push_back(transformFaceDetection(face));
        }
        result["faces"] = utils::JsonValue(faces_array);
    }
    
    // Transform embeddings array
    if (!response.embeddings.empty()) {
        utils::JsonArray embeddings_array;
        for (const auto& embedding : response.embeddings) {
            embeddings_array.push_back(transformFaceEmbedding(embedding));
        }
        result["embeddings"] = utils::JsonValue(embeddings_array);
    }
    
    return utils::JsonValue(result);
}

utils::JsonValue InferenceHandler::transformYoloResponse(const models::YoloResponse& response) {
    utils::JsonObject result;
    
    result["success"] = utils::JsonValue(response.success);
    result["message"] = utils::JsonValue(response.message);
    result["processing_time_ms"] = createDuration(response.processing_time);
    result["model_version"] = utils::JsonValue(response.model_version);
    result["total_objects_detected"] = utils::JsonValue(response.total_objects_detected);
    
    if (response.error_code.has_value()) {
        result["error_code"] = utils::JsonValue(response.error_code.value());
    }
    
    // Transform detections array
    if (!response.detections.empty()) {
        utils::JsonArray detections_array;
        for (const auto& detection : response.detections) {
            detections_array.push_back(transformObjectDetection(detection));
        }
        result["detections"] = utils::JsonValue(detections_array);
    }
    
    // Transform detected classes array
    if (!response.detected_classes.empty()) {
        utils::JsonArray classes_array;
        for (const auto& class_name : response.detected_classes) {
            classes_array.push_back(utils::JsonValue(class_name));
        }
        result["detected_classes"] = utils::JsonValue(classes_array);
    }
    
    return utils::JsonValue(result);
}

// Helper transformations
utils::JsonValue InferenceHandler::transformBoundingBox(const models::BoundingBox& bbox) {
    utils::JsonObject result;
    result["x"] = utils::JsonValue(static_cast<double>(bbox.x));
    result["y"] = utils::JsonValue(static_cast<double>(bbox.y));
    result["width"] = utils::JsonValue(static_cast<double>(bbox.width));
    result["height"] = utils::JsonValue(static_cast<double>(bbox.height));
    result["confidence"] = utils::JsonValue(static_cast<double>(bbox.confidence));
    return utils::JsonValue(result);
}

utils::JsonValue InferenceHandler::transformFaceDetection(const models::FaceDetection& face) {
    utils::JsonObject result;
    result["bbox"] = transformBoundingBox(face.bbox);
    result["quality_score"] = utils::JsonValue(static_cast<double>(face.quality_score));
    
    if (!face.landmarks.empty()) {
        result["landmarks"] = transformFloatArray(face.landmarks);
    }
    
    return utils::JsonValue(result);
}

utils::JsonValue InferenceHandler::transformFaceEmbedding(const models::FaceEmbedding& embedding) {
    utils::JsonObject result;
    result["confidence"] = utils::JsonValue(static_cast<double>(embedding.confidence));
    
    if (!embedding.face_id.empty()) {
        result["face_id"] = utils::JsonValue(embedding.face_id);
    }
    
    if (!embedding.features.empty()) {
        result["features"] = transformFloatArray(embedding.features);
    }
    
    return utils::JsonValue(result);
}

utils::JsonValue InferenceHandler::transformObjectDetection(const models::ObjectDetection& detection) {
    utils::JsonObject result;
    result["bbox"] = transformBoundingBox(detection.bbox);
    result["class_name"] = utils::JsonValue(detection.class_name);
    result["class_id"] = utils::JsonValue(detection.class_id);
    return utils::JsonValue(result);
}

utils::JsonValue InferenceHandler::transformFloatArray(const std::vector<float>& arr) {
    utils::JsonArray result;
    for (float value : arr) {
        result.push_back(utils::JsonValue(static_cast<double>(value)));
    }
    return utils::JsonValue(result);
}

// Validation
bool InferenceHandler::validateInferenceRequest(const utils::JsonValue& json) {
    return validateJsonRequest(json, {"image_path"});
}

bool InferenceHandler::validateImagePath(const std::string& image_path) {
    return !image_path.empty();
}

} // namespace server::handlers
