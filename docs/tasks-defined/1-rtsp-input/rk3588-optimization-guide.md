# RK3588 Optimization Guide for RTSP Input Module

## Hardware Platform Overview

### Orange Pi 5 Plus/Ultra Specifications
- **SoC**: Rockchip RK3588 (8nm process)
- **CPU**: 4x Cortex-A76 (2.4GHz) + 4x Cortex-A55 (1.8GHz)
- **GPU**: Mali-G610 MP4
- **NPU**: 6 TOPS AI accelerator
- **VPU**: 8K@60fps H.265/VP9 decoder, 8K@30fps H.264 encoder
- **Memory**: 4GB/8GB LPDDR4X-4224
- **Storage**: 240GB SSD
- **Network**: Gigabit Ethernet + WiFi 6

### Hardware Acceleration Units
1. **MPP (Media Process Platform)**: Hardware video decoder/encoder
2. **RGA (Raster Graphics Accelerator)**: 2D graphics acceleration
3. **NPU**: Neural network processing (optional for preprocessing)
4. **VOP (Video Output Processor)**: Display processing

## GStreamer Pipeline Architecture

### Optimized RTSP Pipeline for RK3588

```bash
# Basic RTSP to H.264 NAL units pipeline
gst-launch-1.0 rtspsrc location=rtsp://camera_ip:554/stream ! \
    rtph264depay ! \
    h264parse ! \
    mppvideodec ! \
    rgaconvert ! \
    appsink name=sink

# Multi-stream pipeline with hardware acceleration
gst-launch-1.0 \
    rtspsrc location=rtsp://cam1/stream name=src1 ! \
    rtph264depay ! h264parse ! mppvideodec ! rgaconvert ! queue ! mux.sink_0 \
    rtspsrc location=rtsp://cam2/stream name=src2 ! \
    rtph264depay ! h264parse ! mppvideodec ! rgaconvert ! queue ! mux.sink_1 \
    videomixer name=mux ! appsink
```

### GStreamer Element Configuration

```cpp
// Optimized GStreamer elements for RK3588
class GStreamerRTSPClient {
private:
    struct PipelineElements {
        GstElement* pipeline;
        GstElement* rtspsrc;
        GstElement* rtph264depay;
        GstElement* h264parse;
        GstElement* mppvideodec;    // Hardware decoder
        GstElement* rgaconvert;     // Hardware scaler/converter
        GstElement* appsink;
    };
    
public:
    bool createOptimizedPipeline(const RTSPConfig& config) {
        // Create pipeline with RK3588 optimizations
        pipeline_ = gst_pipeline_new("rtsp-pipeline");
        
        // RTSP source with optimized settings
        rtspsrc_ = gst_element_factory_make("rtspsrc", "source");
        g_object_set(rtspsrc_,
            "location", config.rtsp_url.c_str(),
            "protocols", GST_RTSP_LOWER_TRANS_TCP,  // Prefer TCP
            "retry", 3,
            "timeout", 5000000,  // 5 seconds in microseconds
            "tcp-timeout", 10000000,  // 10 seconds
            "buffer-mode", 4,  // Low latency mode
            NULL);
        
        // Hardware H.264 decoder
        mppvideodec_ = gst_element_factory_make("mppvideodec", "decoder");
        g_object_set(mppvideodec_,
            "fast-mode", TRUE,
            "split-parse", TRUE,
            NULL);
        
        // RGA converter for format conversion
        rgaconvert_ = gst_element_factory_make("rgaconvert", "converter");
        g_object_set(rgaconvert_,
            "output-io-mode", 4,  // DMABUF mode
            NULL);
        
        // Application sink with zero-copy
        appsink_ = gst_element_factory_make("appsink", "sink");
        g_object_set(appsink_,
            "emit-signals", TRUE,
            "sync", FALSE,
            "async", FALSE,
            "max-buffers", 3,  // Low latency
            "drop", TRUE,
            NULL);
        
        return linkElements();
    }
};
```

## Memory Optimization for Orange Pi

### Memory Configuration by RAM Size

```cpp
// Memory limits based on available RAM
struct MemoryConfig {
    size_t max_streams;
    size_t buffer_pool_size;
    size_t queue_size_per_stream;
    size_t max_memory_mb;
};

MemoryConfig getOptimalMemoryConfig() {
    size_t total_ram = getTotalRAM();
    
    if (total_ram >= 8 * 1024 * 1024 * 1024) {  // 8GB
        return {
            .max_streams = 16,
            .buffer_pool_size = 64,
            .queue_size_per_stream = 100,
            .max_memory_mb = 6144  // Leave 2GB for system
        };
    } else if (total_ram >= 4 * 1024 * 1024 * 1024) {  // 4GB
        return {
            .max_streams = 8,
            .buffer_pool_size = 32,
            .queue_size_per_stream = 60,
            .max_memory_mb = 3072  // Leave 1GB for system
        };
    } else {
        return {
            .max_streams = 4,
            .buffer_pool_size = 16,
            .queue_size_per_stream = 40,
            .max_memory_mb = 1536
        };
    }
}
```

### DMABUF Integration for Zero-Copy

```cpp
class DMABufManager {
public:
    struct DMABuffer {
        int fd;
        void* mapped_addr;
        size_t size;
        uint32_t fourcc;
        uint32_t width;
        uint32_t height;
    };
    
    std::unique_ptr<DMABuffer> allocateBuffer(size_t size) {
        // Allocate DMA buffer using RK3588 DRM
        int fd = allocateDRMBuffer(size);
        if (fd < 0) return nullptr;
        
        void* addr = mmap(nullptr, size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
        if (addr == MAP_FAILED) {
            close(fd);
            return nullptr;
        }
        
        return std::make_unique<DMABuffer>(DMABuffer{fd, addr, size, 0, 0, 0});
    }
    
private:
    int drm_fd_;
    std::vector<std::unique_ptr<DMABuffer>> buffer_pool_;
};
```

## CPU Affinity and Threading Strategy

### System-Wide Thread Distribution for AI Box

```cpp
class AIBoxSystemThreadManager {
public:
    enum class CoreType {
        LITTLE_CORE,  // Cortex-A55 (cores 0-3)
        BIG_CORE      // Cortex-A76 (cores 4-7)
    };

    void setupSystemWideAffinity() {
        // Core 0-1: UI Client and System Services
        setThreadAffinity(ui_main_thread_, {0});
        setThreadAffinity(ui_render_threads_, {0, 1});
        setThreadAffinity(system_service_threads_, {1});

        // Core 2-3: RTSP Input Module (limited allocation)
        setThreadAffinity(rtsp_network_threads_, {2});
        setThreadAffinity(rtsp_processing_threads_, {3});

        // Core 4-5: Face Detection (NPU coordination)
        setThreadAffinity(face_detection_threads_, {4, 5});

        // Core 6-7: Face Recognition & Identification
        setThreadAffinity(face_recognition_threads_, {6, 7});

        // CPU Governor Settings for balanced system performance
        setCPUGovernor({0, 1}, "ondemand");      // UI responsiveness
        setCPUGovernor({2, 3}, "schedutil");     // RTSP balanced performance
        setCPUGovernor({4, 5, 6, 7}, "performance"); // AI processing maximum
    }
    
private:
    void setThreadAffinity(const std::vector<std::thread>& threads, 
                          const std::vector<int>& cores) {
        cpu_set_t cpuset;
        CPU_ZERO(&cpuset);
        for (int core : cores) {
            CPU_SET(core, &cpuset);
        }
        
        for (const auto& thread : threads) {
            pthread_setaffinity_np(thread.native_handle(), 
                                  sizeof(cpu_set_t), &cpuset);
        }
    }
};
```

## Performance Tuning Parameters

### GStreamer Environment Variables

```bash
# RK3588 specific GStreamer optimizations
export GST_DEBUG=2
export GST_PLUGIN_PATH=/usr/lib/aarch64-linux-gnu/gstreamer-1.0
export GST_PLUGIN_SCANNER=/usr/libexec/gstreamer-1.0/gst-plugin-scanner

# MPP decoder optimizations
export MPP_DEC_FAST_MODE=1
export MPP_DEC_SPLIT_PARSE=1
export MPP_DEC_OUTPUT_FORMAT=NV12

# RGA optimizations
export RGA_OUTPUT_IO_MODE=4  # DMABUF mode
export RGA_SCALE_MODE=0      # Bilinear scaling

# Memory optimizations
export GST_BUFFER_POOL_SIZE=8
export GST_QUEUE_MAX_SIZE_BUFFERS=3
export GST_QUEUE_MAX_SIZE_TIME=1000000000  # 1 second
```

### System-level Optimizations

```bash
# /etc/sysctl.d/99-rk3588-rtsp.conf
# Network buffer optimizations for ARM64
net.core.rmem_default = 131072
net.core.rmem_max = 8388608
net.core.wmem_default = 131072
net.core.wmem_max = 8388608

# UDP specific optimizations
net.core.netdev_max_backlog = 2000
net.ipv4.udp_rmem_min = 4096
net.ipv4.udp_wmem_min = 4096

# Memory management for embedded system
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# CMA (Contiguous Memory Allocator) for DMA buffers
# Add to /boot/cmdline.txt or equivalent
# cma=256M@512M
```

## Power Management and Thermal Considerations

### Dynamic Frequency Scaling

```cpp
class RK3588PowerManager {
public:
    void setPerformanceMode(bool high_performance) {
        if (high_performance) {
            // Set big cores to maximum frequency
            setCPUFrequency({4, 5, 6, 7}, 2400000);  // 2.4GHz
            setCPUFrequency({0, 1, 2, 3}, 1800000);  // 1.8GHz
            
            // Set GPU to high performance
            setGPUGovernor("performance");
            
            // Increase memory frequency
            setDDRFrequency(4224);  // 4224MHz
        } else {
            // Balanced mode for power saving
            setCPUFrequency({4, 5, 6, 7}, 1800000);  // 1.8GHz
            setCPUFrequency({0, 1, 2, 3}, 1416000);  // 1.416GHz
            
            setGPUGovernor("simple_ondemand");
            setDDRFrequency(3200);  // 3200MHz
        }
    }
    
    void monitorThermalThrottling() {
        // Monitor SoC temperature
        int temp = readSoCTemperature();
        if (temp > 80) {  // 80°C threshold
            // Reduce performance to prevent overheating
            setPerformanceMode(false);
            reduceStreamCount();
        }
    }
};
```

## Benchmarking and Performance Metrics

### System-Wide Performance Targets

```cpp
struct AIBoxPerformanceTargets {
    // For 4GB Orange Pi 5 Plus (Conservative)
    struct Config4GB {
        // RTSP Module allocation (25% of system resources)
        static constexpr int rtsp_max_streams = 6;
        static constexpr int rtsp_cpu_usage_percent = 25;
        static constexpr int rtsp_memory_usage_mb = 1200;

        // System-wide targets
        static constexpr int total_cpu_usage_percent = 75;
        static constexpr int total_memory_usage_mb = 3800;
        static constexpr int face_processing_fps = 5;  // faces/second
        static constexpr int end_to_end_latency_ms = 200;
    };

    // For 8GB Orange Pi 5 Ultra (Optimal)
    struct Config8GB {
        // RTSP Module allocation (30% of system resources)
        static constexpr int rtsp_max_streams = 12;
        static constexpr int rtsp_cpu_usage_percent = 30;
        static constexpr int rtsp_memory_usage_mb = 2500;

        // System-wide targets
        static constexpr int total_cpu_usage_percent = 80;
        static constexpr int total_memory_usage_mb = 7500;
        static constexpr int face_processing_fps = 10; // faces/second
        static constexpr int end_to_end_latency_ms = 150;
    };
};
```

### Performance Monitoring

```cpp
class RK3588PerformanceMonitor {
public:
    struct SystemMetrics {
        float cpu_usage_percent;
        float memory_usage_mb;
        float gpu_usage_percent;
        int soc_temperature_celsius;
        float network_throughput_mbps;
        int active_streams;
        float average_latency_ms;
    };
    
    SystemMetrics collectMetrics() {
        return {
            .cpu_usage_percent = getCPUUsage(),
            .memory_usage_mb = getMemoryUsage(),
            .gpu_usage_percent = getGPUUsage(),
            .soc_temperature_celsius = getSoCTemperature(),
            .network_throughput_mbps = getNetworkThroughput(),
            .active_streams = getActiveStreamCount(),
            .average_latency_ms = getAverageLatency()
        };
    }
    
    bool checkPerformanceThresholds(const SystemMetrics& metrics) {
        return metrics.cpu_usage_percent < 80.0f &&
               metrics.memory_usage_mb < getMaxMemoryMB() &&
               metrics.soc_temperature_celsius < 85 &&
               metrics.average_latency_ms < 200.0f;
    }
};
```

This optimization guide provides comprehensive configuration and tuning specifically for the RK3588 platform, maximizing the use of hardware acceleration while respecting the memory and thermal constraints of the Orange Pi 5 Plus/Ultra.
