#!/bin/bash

# Script to setup X11 authorization for container GUI applications
# This script handles X11 authentication issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "Setting up X11 authorization for container..."

# Method 1: Try to disable X11 access control (for development)
print_info "Attempting to disable X11 access control..."
if command -v xhost >/dev/null 2>&1; then
    # Try different xhost commands
    xhost +local: 2>/dev/null || true
    xhost +localhost 2>/dev/null || true
    xhost +127.0.0.1 2>/dev/null || true
    xhost +local:root 2>/dev/null || true
    xhost + 2>/dev/null || true  # Disable access control completely (development only)
    print_success "X11 access control configured"
else
    print_warning "xhost not available"
fi

# Method 2: Create a working .Xauthority file
print_info "Setting up .Xauthority file..."
XAUTH_FILE="$HOME/.Xauthority"

# Remove existing file if it's corrupted
if [[ -f "$XAUTH_FILE" ]]; then
    rm -f "$XAUTH_FILE" 2>/dev/null || true
fi

# Create new .Xauthority file
touch "$XAUTH_FILE"
chmod 600 "$XAUTH_FILE"

# Try to add authorization entries
if command -v xauth >/dev/null 2>&1; then
    # Add authorization for different display formats
    for display in ":0" ":1" "localhost:0" "127.0.0.1:0"; do
        xauth add "$display" . "$(mcookie)" 2>/dev/null || true
    done
    print_success ".Xauthority file configured"
else
    print_warning "xauth not available"
fi

# Method 3: Create environment with fallback options
print_info "Creating X11 environment with fallback options..."

cat > /tmp/x11_auth_env.sh << 'EOF'
#!/bin/bash
# X11 environment with authorization handling

# Try different DISPLAY values
for display in ":0" ":1" "localhost:0" "127.0.0.1:0" "host.docker.internal:0"; do
    export DISPLAY="$display"
    
    # Test if this display works
    if command -v xdpyinfo >/dev/null 2>&1; then
        if timeout 2s xdpyinfo >/dev/null 2>&1; then
            echo "Working DISPLAY found: $display"
            break
        fi
    fi
done

# Set XAUTHORITY
export XAUTHORITY="$HOME/.Xauthority"

# Qt platform settings with fallback
export QT_QPA_PLATFORM_PLUGIN_PATH="/usr/lib/x86_64-linux-gnu/qt5/plugins/platforms"

# Try XCB first, fallback to offscreen
if timeout 2s xdpyinfo >/dev/null 2>&1; then
    export QT_QPA_PLATFORM="xcb"
    echo "Using XCB platform"
else
    export QT_QPA_PLATFORM="offscreen"
    echo "Falling back to offscreen platform"
fi

# OpenGL settings
export QT_OPENGL="software"
export LIBGL_ALWAYS_SOFTWARE=1
export LIBGL_ALWAYS_INDIRECT=1

# Mesa settings
export MESA_GL_VERSION_OVERRIDE=3.3
export GALLIUM_DRIVER=llvmpipe

# WebEngine settings
export QTWEBENGINE_DISABLE_SANDBOX=1
export QTWEBENGINE_CHROMIUM_FLAGS="--no-sandbox --disable-dev-shm-usage --disable-gpu"

# Suppress warnings
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false;qt.qpa.gl.warning=false"

echo "X11 authorization environment configured"
echo "DISPLAY: $DISPLAY"
echo "QT_QPA_PLATFORM: $QT_QPA_PLATFORM"
EOF

chmod +x /tmp/x11_auth_env.sh
print_success "X11 authorization environment created: /tmp/x11_auth_env.sh"

# Method 4: Create a smart Qt application launcher
cat > /tmp/run_qt_app_smart.sh << 'EOF'
#!/bin/bash
# Smart Qt application launcher with fallback

APP_PATH="$1"
if [[ -z "$APP_PATH" ]]; then
    echo "Usage: $0 <path_to_qt_app> [args...]"
    exit 1
fi

shift  # Remove app path from arguments

echo "Smart Qt Application Launcher"
echo "============================="

# Load environment
source /tmp/x11_auth_env.sh

echo "Attempting to run: $APP_PATH"
echo "Arguments: $@"
echo ""

# Method 1: Try with XCB platform
echo "Trying XCB platform..."
export QT_QPA_PLATFORM="xcb"
if timeout 5s "$APP_PATH" "$@" 2>/dev/null; then
    echo "Success with XCB platform"
    exit 0
fi

# Method 2: Try with offscreen platform
echo "XCB failed, trying offscreen platform..."
export QT_QPA_PLATFORM="offscreen"
if timeout 5s "$APP_PATH" "$@" 2>/dev/null; then
    echo "Success with offscreen platform"
    exit 0
fi

# Method 3: Try with minimal platform
echo "Offscreen failed, trying minimal platform..."
export QT_QPA_PLATFORM="minimal"
if timeout 5s "$APP_PATH" "$@" 2>/dev/null; then
    echo "Success with minimal platform"
    exit 0
fi

# Method 4: Last resort - run with full output
echo "All platforms failed, running with full output for debugging..."
export QT_QPA_PLATFORM="xcb"
export QT_DEBUG_PLUGINS=1
"$APP_PATH" "$@"
EOF

chmod +x /tmp/run_qt_app_smart.sh
print_success "Smart Qt launcher created: /tmp/run_qt_app_smart.sh"

# Test the setup
print_info "Testing X11 authorization setup..."

# Test 1: Basic X11 connection
if command -v xdpyinfo >/dev/null 2>&1; then
    if timeout 3s xdpyinfo >/dev/null 2>&1; then
        print_success "X11 connection test passed"
    else
        print_warning "X11 connection test failed"
    fi
fi

# Test 2: Qt platform detection
source /tmp/x11_auth_env.sh
print_info "Qt platform will be: $QT_QPA_PLATFORM"

print_info ""
print_success "X11 authorization setup completed!"
print_info ""
print_info "Usage options:"
print_info "1. Load environment: source /tmp/x11_auth_env.sh"
print_info "2. Run Qt app directly: ./your_qt_app"
print_info "3. Use smart launcher: /tmp/run_qt_app_smart.sh ./your_qt_app"
print_info ""
print_info "The setup will automatically:"
print_info "- Try to find working DISPLAY"
print_info "- Configure X11 authorization"
print_info "- Fallback to offscreen mode if needed"
print_info "- Handle Qt platform selection"
