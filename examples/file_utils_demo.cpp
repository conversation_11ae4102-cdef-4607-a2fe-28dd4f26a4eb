#include <iostream>
#include <utils/file_utils.hpp>

int main() {
    std::cout << "=== C-AIBOX File Utils Demo ===" << std::endl;

    // Create demo directory
    std::string demoDir = "file_utils_demo";
    utils::FileUtils::createDirectory(demoDir);

    // 1. Basic File Operations
    std::cout << "\n1. Basic File Operations:" << std::endl;
    
    std::string testFile = utils::FileUtils::joinPath(demoDir, "test.txt");
    std::string content = "Hello, File Utils!\nThis is a test file.\nLine 3 with special chars: àáâãäå";
    
    // Write file
    bool writeSuccess = utils::FileUtils::writeFile(testFile, content);
    std::cout << "Write file: " << (writeSuccess ? "Success" : "Failed") << std::endl;
    
    // Check if file exists
    std::cout << "File exists: " << (utils::FileUtils::exists(testFile) ? "Yes" : "No") << std::endl;
    std::cout << "Is file: " << (utils::FileUtils::isFile(testFile) ? "Yes" : "No") << std::endl;
    std::cout << "Is directory: " << (utils::FileUtils::isDirectory(testFile) ? "Yes" : "No") << std::endl;
    
    // Read file
    auto readContent = utils::FileUtils::readFile(testFile);
    if (readContent.has_value()) {
        std::cout << "Read file success. Content length: " << readContent->length() << std::endl;
        std::cout << "Content matches: " << (readContent.value() == content ? "Yes" : "No") << std::endl;
    }
    
    // Test convenience functions
    std::string convFile = utils::FileUtils::joinPath(demoDir, "convenience.txt");
    utils::writeFile(convFile, "Convenience function test");
    auto convRead = utils::readFile(convFile);
    std::cout << "Convenience functions work: " << (convRead.has_value() ? "Yes" : "No") << std::endl;

    // 2. File Properties
    std::cout << "\n2. File Properties:" << std::endl;
    
    auto fileSize = utils::FileUtils::getFileSize(testFile);
    if (fileSize.has_value()) {
        std::cout << "File size: " << fileSize.value() << " bytes" << std::endl;
    }
    
    auto modTime = utils::FileUtils::getModificationTime(testFile);
    if (modTime.has_value()) {
        std::cout << "File has modification time: Yes" << std::endl;
    }
    
    std::cout << "File is readable: " << (utils::FileUtils::isReadable(testFile) ? "Yes" : "No") << std::endl;
    std::cout << "File is writable: " << (utils::FileUtils::isWritable(testFile) ? "Yes" : "No") << std::endl;
    std::cout << "File is executable: " << (utils::FileUtils::isExecutable(testFile) ? "Yes" : "No") << std::endl;

    // 3. Line-based Operations
    std::cout << "\n3. Line-based Operations:" << std::endl;
    
    std::string linesFile = utils::FileUtils::joinPath(demoDir, "lines.txt");
    std::vector<std::string> lines = {
        "First line",
        "Second line with numbers: 123",
        "Third line with symbols: !@#$%",
        "",  // Empty line
        "Last line"
    };
    
    bool linesWritten = utils::FileUtils::writeLines(linesFile, lines);
    std::cout << "Write lines: " << (linesWritten ? "Success" : "Failed") << std::endl;
    
    auto readLines = utils::FileUtils::readLines(linesFile);
    if (readLines.has_value()) {
        std::cout << "Read " << readLines->size() << " lines" << std::endl;
        for (size_t i = 0; i < readLines->size(); ++i) {
            std::cout << "  Line " << (i + 1) << ": \"" << (*readLines)[i] << "\"" << std::endl;
        }
    }

    // 4. Binary Operations
    std::cout << "\n4. Binary Operations:" << std::endl;
    
    std::string binaryFile = utils::FileUtils::joinPath(demoDir, "binary.dat");
    std::vector<uint8_t> binaryData = {0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE, 0xFD, 0xFC};
    
    bool binaryWritten = utils::FileUtils::writeBinary(binaryFile, binaryData);
    std::cout << "Write binary: " << (binaryWritten ? "Success" : "Failed") << std::endl;
    
    auto readBinary = utils::FileUtils::readBinary(binaryFile);
    if (readBinary.has_value()) {
        std::cout << "Read " << readBinary->size() << " bytes" << std::endl;
        std::cout << "Binary data matches: " << (readBinary.value() == binaryData ? "Yes" : "No") << std::endl;
        
        std::cout << "Binary content: ";
        for (uint8_t byte : readBinary.value()) {
            printf("0x%02X ", byte);
        }
        std::cout << std::endl;
    }

    // 5. File Copy, Move, Delete
    std::cout << "\n5. File Copy, Move, Delete:" << std::endl;
    
    std::string sourceFile = utils::FileUtils::joinPath(demoDir, "source.txt");
    std::string copyFile = utils::FileUtils::joinPath(demoDir, "copy.txt");
    std::string moveFile = utils::FileUtils::joinPath(demoDir, "moved.txt");
    
    utils::FileUtils::writeFile(sourceFile, "Content to copy and move");
    
    // Copy
    bool copied = utils::FileUtils::copyFile(sourceFile, copyFile);
    std::cout << "Copy file: " << (copied ? "Success" : "Failed") << std::endl;
    std::cout << "Copy exists: " << (utils::FileUtils::exists(copyFile) ? "Yes" : "No") << std::endl;
    
    // Move
    bool moved = utils::FileUtils::moveFile(copyFile, moveFile);
    std::cout << "Move file: " << (moved ? "Success" : "Failed") << std::endl;
    std::cout << "Original copy exists: " << (utils::FileUtils::exists(copyFile) ? "Yes" : "No") << std::endl;
    std::cout << "Moved file exists: " << (utils::FileUtils::exists(moveFile) ? "Yes" : "No") << std::endl;
    
    // Delete
    bool deleted = utils::FileUtils::deleteFile(sourceFile);
    std::cout << "Delete file: " << (deleted ? "Success" : "Failed") << std::endl;
    std::cout << "Source file exists: " << (utils::FileUtils::exists(sourceFile) ? "Yes" : "No") << std::endl;

    // 6. Directory Operations
    std::cout << "\n6. Directory Operations:" << std::endl;
    
    std::string subDir = utils::FileUtils::joinPath(demoDir, "subdir");
    std::string nestedDir = utils::FileUtils::joinPath(subDir, "nested");
    
    bool dirCreated = utils::FileUtils::createDirectory(nestedDir);
    std::cout << "Create nested directory: " << (dirCreated ? "Success" : "Failed") << std::endl;
    std::cout << "Nested dir exists: " << (utils::FileUtils::exists(nestedDir) ? "Yes" : "No") << std::endl;
    std::cout << "Nested dir is directory: " << (utils::FileUtils::isDirectory(nestedDir) ? "Yes" : "No") << std::endl;
    
    // Create some files in directories
    utils::FileUtils::writeFile(utils::FileUtils::joinPath(subDir, "file1.txt"), "File 1 content");
    utils::FileUtils::writeFile(utils::FileUtils::joinPath(subDir, "file2.log"), "File 2 content");
    utils::FileUtils::writeFile(utils::FileUtils::joinPath(nestedDir, "nested_file.txt"), "Nested file content");
    
    // List files
    auto files = utils::FileUtils::listFiles(subDir, false);
    if (files.has_value()) {
        std::cout << "Files in subdir (non-recursive): " << files->size() << std::endl;
        for (const auto& file : files.value()) {
            std::cout << "  - " << utils::FileUtils::getFilename(file) << std::endl;
        }
    }
    
    auto filesRecursive = utils::FileUtils::listFiles(subDir, true);
    if (filesRecursive.has_value()) {
        std::cout << "Files in subdir (recursive): " << filesRecursive->size() << std::endl;
    }
    
    // List directories
    auto dirs = utils::FileUtils::listDirectories(subDir, false);
    if (dirs.has_value()) {
        std::cout << "Subdirectories: " << dirs->size() << std::endl;
    }

    // 7. Path Operations
    std::cout << "\n7. Path Operations:" << std::endl;
    
    std::string samplePath = "/home/<USER>/documents/report.pdf";
    std::cout << "Sample path: " << samplePath << std::endl;
    std::cout << "Filename: " << utils::FileUtils::getFilename(samplePath) << std::endl;
    std::cout << "Basename: " << utils::FileUtils::getBasename(samplePath) << std::endl;
    std::cout << "Extension: " << utils::FileUtils::getExtension(samplePath) << std::endl;
    std::cout << "Directory: " << utils::FileUtils::getDirectory(samplePath) << std::endl;
    
    // Path joining
    std::vector<std::string> pathComponents = {"home", "user", "documents", "file.txt"};
    std::string joinedPath = utils::FileUtils::joinPath(pathComponents);
    std::cout << "Joined path: " << joinedPath << std::endl;
    
    std::string joined2 = utils::FileUtils::joinPath("path1", "path2");
    std::cout << "Joined two paths: " << joined2 << std::endl;
    
    // Convenience function
    std::string convJoined = utils::joinPath("conv1", "conv2");
    std::cout << "Convenience join: " << convJoined << std::endl;

    // 8. Working Directory
    std::cout << "\n8. Working Directory:" << std::endl;
    
    std::string currentDir = utils::FileUtils::getCurrentDirectory();
    std::cout << "Current directory: " << currentDir << std::endl;
    
    std::string absolutePath = utils::FileUtils::getAbsolutePath(demoDir);
    std::cout << "Absolute path of demo dir: " << absolutePath << std::endl;

    // 9. Temporary Files and Directories
    std::cout << "\n9. Temporary Files and Directories:" << std::endl;
    
    std::string tempDir = utils::FileUtils::getTempDirectory();
    std::cout << "System temp directory: " << tempDir << std::endl;
    
    auto tempFile = utils::FileUtils::createTempFile("demo_", ".tmp");
    if (tempFile.has_value()) {
        std::cout << "Created temp file: " << tempFile.value() << std::endl;
        utils::FileUtils::writeFile(tempFile.value(), "Temporary content");
        
        auto tempContent = utils::FileUtils::readFile(tempFile.value());
        if (tempContent.has_value()) {
            std::cout << "Temp file content: " << tempContent.value() << std::endl;
        }
        
        // Clean up
        utils::FileUtils::deleteFile(tempFile.value());
        std::cout << "Temp file cleaned up" << std::endl;
    }
    
    auto tempDirPath = utils::FileUtils::createTempDirectory("demo_dir_");
    if (tempDirPath.has_value()) {
        std::cout << "Created temp directory: " << tempDirPath.value() << std::endl;
        
        // Use temp directory
        std::string tempTestFile = utils::FileUtils::joinPath(tempDirPath.value(), "test.txt");
        utils::FileUtils::writeFile(tempTestFile, "Test in temp dir");
        
        // Clean up
        utils::FileUtils::deleteDirectory(tempDirPath.value());
        std::cout << "Temp directory cleaned up" << std::endl;
    }

    // 10. File Search and Patterns
    std::cout << "\n10. File Search and Patterns:" << std::endl;
    
    // Create files with different extensions
    utils::FileUtils::writeFile(utils::FileUtils::joinPath(demoDir, "document1.txt"), "Text document 1");
    utils::FileUtils::writeFile(utils::FileUtils::joinPath(demoDir, "document2.txt"), "Text document 2");
    utils::FileUtils::writeFile(utils::FileUtils::joinPath(demoDir, "config.json"), "{}");
    utils::FileUtils::writeFile(utils::FileUtils::joinPath(demoDir, "data.csv"), "col1,col2");
    utils::FileUtils::writeFile(utils::FileUtils::joinPath(demoDir, "script.py"), "print('hello')");
    
    auto txtFiles = utils::FileUtils::findFiles(demoDir, "*.txt", false);
    if (txtFiles.has_value()) {
        std::cout << "Found " << txtFiles->size() << " .txt files:" << std::endl;
        for (const auto& file : txtFiles.value()) {
            std::cout << "  - " << utils::FileUtils::getFilename(file) << std::endl;
        }
    }
    
    auto allFiles = utils::FileUtils::findFiles(demoDir, "*", false);
    if (allFiles.has_value()) {
        std::cout << "Found " << allFiles->size() << " files total" << std::endl;
    }

    // 11. Directory Size
    std::cout << "\n11. Directory Size:" << std::endl;
    
    auto dirSize = utils::FileUtils::getDirectorySize(demoDir);
    if (dirSize.has_value()) {
        std::cout << "Demo directory size: " << dirSize.value() << " bytes" << std::endl;
    }

    // 12. File Comparison and Hashing
    std::cout << "\n12. File Comparison and Hashing:" << std::endl;
    
    std::string file1 = utils::FileUtils::joinPath(demoDir, "compare1.txt");
    std::string file2 = utils::FileUtils::joinPath(demoDir, "compare2.txt");
    std::string file3 = utils::FileUtils::joinPath(demoDir, "compare3.txt");
    
    std::string sameContent = "Same content for comparison";
    utils::FileUtils::writeFile(file1, sameContent);
    utils::FileUtils::writeFile(file2, sameContent);
    utils::FileUtils::writeFile(file3, "Different content");
    
    auto comparison1 = utils::FileUtils::compareFiles(file1, file2);
    auto comparison2 = utils::FileUtils::compareFiles(file1, file3);
    
    if (comparison1.has_value()) {
        std::cout << "File1 vs File2 (same content): " << (comparison1.value() ? "Equal" : "Different") << std::endl;
    }
    if (comparison2.has_value()) {
        std::cout << "File1 vs File3 (different content): " << (comparison2.value() ? "Equal" : "Different") << std::endl;
    }
    
    auto hash1 = utils::FileUtils::getFileHash(file1);
    auto hash2 = utils::FileUtils::getFileHash(file2);
    auto hash3 = utils::FileUtils::getFileHash(file3);
    
    if (hash1.has_value() && hash2.has_value() && hash3.has_value()) {
        std::cout << "Hash1: " << hash1.value() << std::endl;
        std::cout << "Hash2: " << hash2.value() << std::endl;
        std::cout << "Hash3: " << hash3.value() << std::endl;
        std::cout << "Hash1 == Hash2: " << (hash1.value() == hash2.value() ? "Yes" : "No") << std::endl;
        std::cout << "Hash1 == Hash3: " << (hash1.value() == hash3.value() ? "Yes" : "No") << std::endl;
    }

    // 13. File Append Operations
    std::cout << "\n13. File Append Operations:" << std::endl;
    
    std::string appendFile = utils::FileUtils::joinPath(demoDir, "append_test.txt");
    
    utils::FileUtils::writeFile(appendFile, "Initial content\n");
    utils::FileUtils::writeFile(appendFile, "Appended line 1\n", true);
    utils::FileUtils::writeFile(appendFile, "Appended line 2\n", true);
    
    auto appendContent = utils::FileUtils::readFile(appendFile);
    if (appendContent.has_value()) {
        std::cout << "Append test file content:" << std::endl;
        std::cout << appendContent.value() << std::endl;
    }

    // 14. Error Handling
    std::cout << "\n14. Error Handling:" << std::endl;
    
    std::string nonExistentFile = "this_file_does_not_exist.txt";
    std::cout << "Non-existent file exists: " << (utils::FileUtils::exists(nonExistentFile) ? "Yes" : "No") << std::endl;
    
    auto failedRead = utils::FileUtils::readFile(nonExistentFile);
    std::cout << "Read non-existent file: " << (failedRead.has_value() ? "Success" : "Failed") << std::endl;
    
    auto failedSize = utils::FileUtils::getFileSize(nonExistentFile);
    std::cout << "Get size of non-existent file: " << (failedSize.has_value() ? "Success" : "Failed") << std::endl;

    // Clean up demo directory
    std::cout << "\n15. Cleanup:" << std::endl;
    bool cleanupSuccess = utils::FileUtils::deleteDirectory(demoDir);
    std::cout << "Cleanup demo directory: " << (cleanupSuccess ? "Success" : "Failed") << std::endl;

    std::cout << "\n=== Demo completed ===" << std::endl;
    return 0;
}
