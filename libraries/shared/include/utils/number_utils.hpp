#pragma once
#include <string>
#include <optional>
#include <vector>
#include <cmath>
#include <limits>
#include <type_traits>

namespace utils {
    /**
     * @brief Number utilities for parsing, formatting, and manipulating numeric values
     */
    class NumberUtils {
    public:
        /**
         * @brief Parse string to integer
         * @param str String to parse
         * @return Optional integer (nullopt if parsing fails)
         */
        static std::optional<int> parseInt(const std::string& str);

        /**
         * @brief Parse string to long
         * @param str String to parse
         * @return Optional long (nullopt if parsing fails)
         */
        static std::optional<long> parseLong(const std::string& str);

        /**
         * @brief Parse string to long long
         * @param str String to parse
         * @return Optional long long (nullopt if parsing fails)
         */
        static std::optional<long long> parseLongLong(const std::string& str);

        /**
         * @brief Parse string to float
         * @param str String to parse
         * @return Optional float (nullopt if parsing fails)
         */
        static std::optional<float> parseFloat(const std::string& str);

        /**
         * @brief Parse string to double
         * @param str String to parse
         * @return Optional double (nullopt if parsing fails)
         */
        static std::optional<double> parseDouble(const std::string& str);

        /**
         * @brief Parse string to unsigned integer
         * @param str String to parse
         * @return Optional unsigned int (nullopt if parsing fails)
         */
        static std::optional<unsigned int> parseUInt(const std::string& str);

        /**
         * @brief Parse string to unsigned long
         * @param str String to parse
         * @return Optional unsigned long (nullopt if parsing fails)
         */
        static std::optional<unsigned long> parseULong(const std::string& str);

        /**
         * @brief Parse string to unsigned long long
         * @param str String to parse
         * @return Optional unsigned long long (nullopt if parsing fails)
         */
        static std::optional<unsigned long long> parseULongLong(const std::string& str);

        /**
         * @brief Format integer to string with specified base
         * @param value Integer value
         * @param base Number base (2-36, default: 10)
         * @return Formatted string
         */
        static std::string toString(long long value, int base = 10);

        /**
         * @brief Format unsigned integer to string with specified base
         * @param value Unsigned integer value
         * @param base Number base (2-36, default: 10)
         * @return Formatted string
         */
        static std::string toString(unsigned long long value, int base = 10);

        /**
         * @brief Format floating point to string with specified precision
         * @param value Floating point value
         * @param precision Number of decimal places (default: 6)
         * @param fixed Use fixed-point notation (default: false)
         * @return Formatted string
         */
        static std::string toString(double value, int precision = 6, bool fixed = false);

        /**
         * @brief Format number with thousands separator
         * @param value Number value
         * @param separator Thousands separator (default: ",")
         * @return Formatted string with separators
         */
        static std::string formatWithSeparator(long long value, const std::string& separator = ",");

        /**
         * @brief Format number with thousands separator
         * @param value Number value
         * @param separator Thousands separator (default: ",")
         * @param precision Decimal precision (default: 2)
         * @return Formatted string with separators
         */
        static std::string formatWithSeparator(double value, const std::string& separator = ",", int precision = 2);

        /**
         * @brief Check if two floating point numbers are equal within tolerance
         * @param a First number
         * @param b Second number
         * @param tolerance Tolerance value (default: 1e-9)
         * @return true if numbers are approximately equal
         */
        static bool isEqual(double a, double b, double tolerance = 1e-9);

        /**
         * @brief Check if floating point number is zero within tolerance
         * @param value Number to check
         * @param tolerance Tolerance value (default: 1e-9)
         * @return true if number is approximately zero
         */
        static bool isZero(double value, double tolerance = 1e-9);

        /**
         * @brief Check if number is positive
         * @param value Number to check
         * @return true if number is positive
         */
        template<typename T>
        static bool isPositive(T value) {
            return value > 0;
        }

        /**
         * @brief Check if number is negative
         * @param value Number to check
         * @return true if number is negative
         */
        template<typename T>
        static bool isNegative(T value) {
            return value < 0;
        }

        /**
         * @brief Check if number is even
         * @param value Integer value to check
         * @return true if number is even
         */
        template<typename T>
        static bool isEven(T value) {
            static_assert(std::is_integral_v<T>, "isEven requires integral type");
            return value % 2 == 0;
        }

        /**
         * @brief Check if number is odd
         * @param value Integer value to check
         * @return true if number is odd
         */
        template<typename T>
        static bool isOdd(T value) {
            static_assert(std::is_integral_v<T>, "isOdd requires integral type");
            return value % 2 != 0;
        }

        /**
         * @brief Check if number is prime
         * @param value Integer value to check
         * @return true if number is prime
         */
        static bool isPrime(long long value);

        /**
         * @brief Check if number is perfect square
         * @param value Integer value to check
         * @return true if number is perfect square
         */
        static bool isPerfectSquare(long long value);

        /**
         * @brief Clamp value between min and max
         * @param value Value to clamp
         * @param min Minimum value
         * @param max Maximum value
         * @return Clamped value
         */
        template<typename T>
        static T clamp(T value, T min, T max) {
            return std::max(min, std::min(value, max));
        }

        /**
         * @brief Get absolute value
         * @param value Input value
         * @return Absolute value
         */
        template<typename T>
        static T abs(T value) {
            return std::abs(value);
        }

        /**
         * @brief Get minimum of two values
         * @param a First value
         * @param b Second value
         * @return Minimum value
         */
        template<typename T>
        static T min(T a, T b) {
            return std::min(a, b);
        }

        /**
         * @brief Get maximum of two values
         * @param a First value
         * @param b Second value
         * @return Maximum value
         */
        template<typename T>
        static T max(T a, T b) {
            return std::max(a, b);
        }

        /**
         * @brief Round to specified decimal places
         * @param value Value to round
         * @param decimals Number of decimal places
         * @return Rounded value
         */
        static double round(double value, int decimals = 0);

        /**
         * @brief Ceiling to specified decimal places
         * @param value Value to ceil
         * @param decimals Number of decimal places
         * @return Ceiled value
         */
        static double ceil(double value, int decimals = 0);

        /**
         * @brief Floor to specified decimal places
         * @param value Value to floor
         * @param decimals Number of decimal places
         * @return Floored value
         */
        static double floor(double value, int decimals = 0);

        /**
         * @brief Calculate percentage
         * @param part Part value
         * @param total Total value
         * @return Percentage (0-100)
         */
        static double percentage(double part, double total);

        /**
         * @brief Calculate percentage of value
         * @param value Base value
         * @param percent Percentage (0-100)
         * @return Calculated value
         */
        static double percentageOf(double value, double percent);

        /**
         * @brief Generate random integer in range [min, max]
         * @param min Minimum value (inclusive)
         * @param max Maximum value (inclusive)
         * @return Random integer
         */
        static int randomInt(int min, int max);

        /**
         * @brief Generate random double in range [min, max)
         * @param min Minimum value (inclusive)
         * @param max Maximum value (exclusive)
         * @return Random double
         */
        static double randomDouble(double min = 0.0, double max = 1.0);

        /**
         * @brief Calculate greatest common divisor
         * @param a First number
         * @param b Second number
         * @return GCD of a and b
         */
        static long long gcd(long long a, long long b);

        /**
         * @brief Calculate least common multiple
         * @param a First number
         * @param b Second number
         * @return LCM of a and b
         */
        static long long lcm(long long a, long long b);

        /**
         * @brief Calculate factorial
         * @param n Input number
         * @return Factorial of n
         */
        static long long factorial(int n);

        /**
         * @brief Calculate power (base^exponent)
         * @param base Base value
         * @param exponent Exponent value
         * @return Result of base^exponent
         */
        static double power(double base, double exponent);

        /**
         * @brief Calculate square root
         * @param value Input value
         * @return Square root of value
         */
        static double sqrt(double value);

        /**
         * @brief Calculate logarithm (base 10)
         * @param value Input value
         * @return Logarithm base 10 of value
         */
        static double log10(double value);

        /**
         * @brief Calculate natural logarithm
         * @param value Input value
         * @return Natural logarithm of value
         */
        static double ln(double value);

        /**
         * @brief Convert degrees to radians
         * @param degrees Angle in degrees
         * @return Angle in radians
         */
        static double toRadians(double degrees);

        /**
         * @brief Convert radians to degrees
         * @param radians Angle in radians
         * @return Angle in degrees
         */
        static double toDegrees(double radians);

        /**
         * @brief Check if value is within range [min, max]
         * @param value Value to check
         * @param min Minimum value (inclusive)
         * @param max Maximum value (inclusive)
         * @return true if value is in range
         */
        template<typename T>
        static bool inRange(T value, T min, T max) {
            return value >= min && value <= max;
        }

        /**
         * @brief Linear interpolation between two values
         * @param a Start value
         * @param b End value
         * @param t Interpolation factor (0.0 to 1.0)
         * @return Interpolated value
         */
        static double lerp(double a, double b, double t);

        /**
         * @brief Map value from one range to another
         * @param value Input value
         * @param fromMin Source range minimum
         * @param fromMax Source range maximum
         * @param toMin Target range minimum
         * @param toMax Target range maximum
         * @return Mapped value
         */
        static double map(double value, double fromMin, double fromMax, double toMin, double toMax);

    private:
        /**
         * @brief Helper function to check if string is valid number
         * @param str String to check
         * @return true if string represents a valid number
         */
        static bool isValidNumber(const std::string& str);
    };

    // Convenience functions for ease of use
    
    /**
     * @brief Parse string to integer (convenience function)
     * @param str String to parse
     * @return Optional integer
     */
    std::optional<int> parseInt(const std::string& str);

    /**
     * @brief Parse string to double (convenience function)
     * @param str String to parse
     * @return Optional double
     */
    std::optional<double> parseDouble(const std::string& str);

    /**
     * @brief Format number to string (convenience function)
     * @param value Number value
     * @return Formatted string
     */
    std::string toString(double value);

    /**
     * @brief Check if numbers are equal (convenience function)
     * @param a First number
     * @param b Second number
     * @return true if approximately equal
     */
    bool isEqual(double a, double b);

    /**
     * @brief Generate random integer (convenience function)
     * @param min Minimum value
     * @param max Maximum value
     * @return Random integer
     */
    int randomInt(int min, int max);
}
