#include <iostream>
#include <utils/datetime_utils.hpp>

int main() {
    std::cout << "=== C-AIBOX DateTime Utils Demo ===" << std::endl;

    // 1. Current time operations
    std::cout << "\n1. Current Time Operations:" << std::endl;
    auto now = utils::DateTimeUtils::now();
    auto nowConv = utils::now(); // convenience function
    
    std::cout << "Current time (formatted): " << utils::DateTimeUtils::format(now) << std::endl;
    std::cout << "Current time (convenience): " << utils::formatNow() << std::endl;
    std::cout << "Current Unix timestamp: " << utils::DateTimeUtils::nowUnix() << std::endl;
    std::cout << "Current Unix timestamp (ms): " << utils::DateTimeUtils::nowUnixMs() << std::endl;
    std::cout << "Current Unix (convenience): " << utils::nowUnix() << std::endl;

    // 2. Different formatting options
    std::cout << "\n2. Different Formatting Options:" << std::endl;
    std::cout << "ISO 8601: " << utils::DateTimeUtils::toISO8601(now) << std::endl;
    std::cout << "ISO 8601 with ms: " << utils::DateTimeUtils::toISO8601(now, true) << std::endl;
    std::cout << "ISO 8601 now: " << utils::DateTimeUtils::nowISO8601() << std::endl;
    std::cout << "Date only: " << utils::DateTimeUtils::format(now, "%Y-%m-%d") << std::endl;
    std::cout << "Time only: " << utils::DateTimeUtils::format(now, "%H:%M:%S") << std::endl;
    std::cout << "Custom format: " << utils::DateTimeUtils::format(now, "%A, %B %d, %Y at %I:%M %p") << std::endl;

    // 3. Unix timestamp conversions
    std::cout << "\n3. Unix Timestamp Conversions:" << std::endl;
    int64_t unixTime = 1672531200; // 2023-01-01 00:00:00 UTC
    auto timePoint = utils::DateTimeUtils::fromUnix(unixTime);
    std::cout << "Unix " << unixTime << " -> " << utils::DateTimeUtils::format(timePoint) << std::endl;
    
    int64_t unixTimeMs = 1672531200500; // 2023-01-01 00:00:00.500 UTC
    auto timePointMs = utils::DateTimeUtils::fromUnixMs(unixTimeMs);
    std::cout << "Unix ms " << unixTimeMs << " -> " << utils::DateTimeUtils::toISO8601(timePointMs, true) << std::endl;

    // 4. Parsing operations
    std::cout << "\n4. Parsing Operations:" << std::endl;
    std::string testTime = "2023-12-25 10:30:45";
    auto parsed = utils::DateTimeUtils::parse(testTime);
    if (parsed.has_value()) {
        std::cout << "Parsed '" << testTime << "' -> Unix: " << utils::DateTimeUtils::toUnix(parsed.value()) << std::endl;
        std::cout << "Reformatted: " << utils::DateTimeUtils::format(parsed.value()) << std::endl;
    }

    // Parse ISO 8601
    std::string isoTime = "2023-12-25T10:30:45Z";
    auto parsedIso = utils::DateTimeUtils::parseISO8601(isoTime);
    if (parsedIso.has_value()) {
        std::cout << "Parsed ISO '" << isoTime << "' -> " << utils::DateTimeUtils::format(parsedIso.value()) << std::endl;
    }

    // Convenience parse
    auto parsedConv = utils::parseTime("2023-06-15 14:20:30");
    if (parsedConv.has_value()) {
        std::cout << "Convenience parse -> " << utils::DateTimeUtils::format(parsedConv.value()) << std::endl;
    }

    // 5. Arithmetic operations
    std::cout << "\n5. Arithmetic Operations:" << std::endl;
    auto baseTime = utils::DateTimeUtils::now();
    auto oneHourLater = utils::DateTimeUtils::add(baseTime, std::chrono::hours(1));
    auto oneHourEarlier = utils::DateTimeUtils::subtract(baseTime, std::chrono::hours(1));
    
    std::cout << "Base time: " << utils::DateTimeUtils::format(baseTime) << std::endl;
    std::cout << "One hour later: " << utils::DateTimeUtils::format(oneHourLater) << std::endl;
    std::cout << "One hour earlier: " << utils::DateTimeUtils::format(oneHourEarlier) << std::endl;
    
    auto diffSec = utils::DateTimeUtils::diffSeconds(oneHourLater, baseTime);
    auto diffMs = utils::DateTimeUtils::diffMs(oneHourLater, baseTime);
    std::cout << "Difference: " << diffSec << " seconds, " << diffMs << " milliseconds" << std::endl;

    // 6. Comparison operations
    std::cout << "\n6. Comparison Operations:" << std::endl;
    std::cout << "Is one hour later after base time? " << (utils::DateTimeUtils::isAfter(oneHourLater, baseTime) ? "Yes" : "No") << std::endl;
    std::cout << "Is one hour earlier before base time? " << (utils::DateTimeUtils::isBefore(oneHourEarlier, baseTime) ? "Yes" : "No") << std::endl;

    // 7. Day operations
    std::cout << "\n7. Day Operations:" << std::endl;
    auto testDay = utils::DateTimeUtils::parse("2023-12-25 15:30:45");
    if (testDay.has_value()) {
        auto startDay = utils::DateTimeUtils::startOfDay(testDay.value());
        auto endDay = utils::DateTimeUtils::endOfDay(testDay.value());
        
        std::cout << "Test time: " << utils::DateTimeUtils::format(testDay.value()) << std::endl;
        std::cout << "Start of day: " << utils::DateTimeUtils::format(startDay) << std::endl;
        std::cout << "End of day: " << utils::DateTimeUtils::format(endDay) << std::endl;
        
        auto sameDay = utils::DateTimeUtils::parse("2023-12-25 08:15:30");
        auto differentDay = utils::DateTimeUtils::parse("2023-12-26 15:30:45");
        
        if (sameDay.has_value() && differentDay.has_value()) {
            std::cout << "Same day check (2023-12-25 15:30:45 vs 2023-12-25 08:15:30): " 
                      << (utils::DateTimeUtils::isSameDay(testDay.value(), sameDay.value()) ? "Yes" : "No") << std::endl;
            std::cout << "Same day check (2023-12-25 15:30:45 vs 2023-12-26 15:30:45): " 
                      << (utils::DateTimeUtils::isSameDay(testDay.value(), differentDay.value()) ? "Yes" : "No") << std::endl;
        }
    }

    // 8. Elapsed time
    std::cout << "\n8. Elapsed Time:" << std::endl;
    auto fiveMinutesAgo = utils::DateTimeUtils::subtract(now, std::chrono::minutes(5));
    auto twoHoursAgo = utils::DateTimeUtils::subtract(now, std::chrono::hours(2));
    auto threeDaysAgo = utils::DateTimeUtils::subtract(now, std::chrono::hours(72));
    
    std::cout << "5 minutes ago: " << utils::DateTimeUtils::getElapsedTime(fiveMinutesAgo) << std::endl;
    std::cout << "2 hours ago: " << utils::DateTimeUtils::getElapsedTime(twoHoursAgo) << std::endl;
    std::cout << "3 days ago: " << utils::DateTimeUtils::getElapsedTime(threeDaysAgo) << std::endl;

    // 9. Validation
    std::cout << "\n9. Format Validation:" << std::endl;
    std::cout << "Valid format '2023-12-25 10:30:45' with '%Y-%m-%d %H:%M:%S': " 
              << (utils::DateTimeUtils::isValidFormat("2023-12-25 10:30:45", "%Y-%m-%d %H:%M:%S") ? "Yes" : "No") << std::endl;
    std::cout << "Valid format '2023-12-25' with '%Y-%m-%d %H:%M:%S': " 
              << (utils::DateTimeUtils::isValidFormat("2023-12-25", "%Y-%m-%d %H:%M:%S") ? "Yes" : "No") << std::endl;
    std::cout << "Valid format '2023-12-25' with '%Y-%m-%d': " 
              << (utils::DateTimeUtils::isValidFormat("2023-12-25", "%Y-%m-%d") ? "Yes" : "No") << std::endl;
    std::cout << "Valid format 'invalid-date' with '%Y-%m-%d': " 
              << (utils::DateTimeUtils::isValidFormat("invalid-date", "%Y-%m-%d") ? "Yes" : "No") << std::endl;

    // 10. Sleep demonstration (short sleep)
    std::cout << "\n10. Sleep Demonstration:" << std::endl;
    std::cout << "Sleeping for 100ms..." << std::endl;
    auto beforeSleep = utils::DateTimeUtils::now();
    utils::DateTimeUtils::sleepMs(100);
    auto afterSleep = utils::DateTimeUtils::now();
    auto sleepDuration = utils::DateTimeUtils::diffMs(afterSleep, beforeSleep);
    std::cout << "Actual sleep duration: " << sleepDuration << "ms" << std::endl;

    // Convenience sleep
    std::cout << "Convenience sleep for 50ms..." << std::endl;
    auto beforeSleep2 = utils::DateTimeUtils::now();
    utils::sleepMs(50);
    auto afterSleep2 = utils::DateTimeUtils::now();
    auto sleepDuration2 = utils::DateTimeUtils::diffMs(afterSleep2, beforeSleep2);
    std::cout << "Actual sleep duration: " << sleepDuration2 << "ms" << std::endl;

    std::cout << "\n=== Demo completed ===" << std::endl;
    return 0;
}
