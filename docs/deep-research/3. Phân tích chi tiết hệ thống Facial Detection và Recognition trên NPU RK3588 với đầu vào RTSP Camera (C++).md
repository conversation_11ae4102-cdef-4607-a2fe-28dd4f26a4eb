# **Phân tích chi tiết hệ thống Facial Detection và Recognition trên NPU RK3588 với đầu vào RTSP Camera (C++)**

## **1\. Giới thiệu**

Bài toán phát hiện và nhận dạng khuôn mặt từ nhiều luồng camera RTSP đòi hỏi một hệ thống có khả năng xử lý song song cao và tận dụng tối đa tài nguyên phần cứng, đặc biệt là NPU (Neural Processing Unit) trên các SoC như Rockchip RK3588. Mục tiêu của phân tích này là đưa ra một kiến trúc và logic triển khai chi tiết bằng C++ để đạt được hiệu suất tối đa về số lượng camera có thể xử lý đồng thời mà vẫn đảm bảo độ chính xác cho cả hai tác vụ.

## **2\. <PERSON><PERSON><PERSON> quan kiến trúc hệ thống**

<PERSON><PERSON> thống sẽ được thiết kế theo kiến trúc module hóa, đa luồng để xử lý song song nhiều tác vụ và nhiều luồng camera.

```
graph TD  
    subgraph Camera Inputs  
        RTSP\_Cam1\[Camera 1 (RTSP)\]  
        RTSP\_Cam2\[Camera 2 (RTSP)\]  
        RTSP\_CamN\[Camera N (RTSP)\]  
    end

    subgraph Processing Pipeline (cho mỗi luồng camera)  
        A\[Stream Receiver & Decoder\] \--\>|Frame| B(Frame Buffer/Queue)  
        B \--\>|Frame| C{Frame Dispatcher}  
        C \--\>|Frame (đã tiền xử lý)| D\[Face Detection (NPU)\]  
        D \--\>|Bounding Boxes, Frame| E(Face ROI Extractor & Preprocessor)  
        E \--\>|Face ROI (đã tiền xử lý)| F\[Face Feature Extraction (NPU)\]  
        F \--\>|Face Embedding| G\[Face Recognition (Matcher)\]  
        G \--\>|Identity| H(Result Aggregator & Output)  
    end

    subgraph Shared Resources  
        NPU\_Manager\[NPU Resource Manager\]  
        RGA\_Accelerator\[RGA Accelerator (cho tiền/hậu xử lý)\]  
        FaceDB\[Face Embeddings Database\]  
    end

    RTSP\_Cam1 \--\> A  
    RTSP\_Cam2 \--\> A  
    RTSP\_CamN \--\> A

    D \-.-\> NPU\_Manager  
    F \-.-\> NPU\_Manager  
    E \-.-\> RGA\_Accelerator  
    C \-.-\> RGA\_Accelerator  
    G \--\> FaceDB

    H \--\> Output\_Interface\[Application/API/Notification\]

    style A fill:\#f9f,stroke:\#333,stroke-width:2px  
    style B fill:\#f9f,stroke:\#333,stroke-width:2px  
    style C fill:\#f9f,stroke:\#333,stroke-width:2px  
    style D fill:\#ccf,stroke:\#333,stroke-width:2px,labelStyle:'font-weight:bold'  
    style E fill:\#f9f,stroke:\#333,stroke-width:2px  
    style F fill:\#ccf,stroke:\#333,stroke-width:2px,labelStyle:'font-weight:bold'  
    style G fill:\#ccf,stroke:\#333,stroke-width:2px  
    style H fill:\#f9f,stroke:\#333,stroke-width:2px  
    style NPU\_Manager fill:\#lightgrey,stroke:\#333,stroke-width:2px  
    style RGA\_Accelerator fill:\#lightgrey,stroke:\#333,stroke-width:2px  
    style FaceDB fill:\#lightgrey,stroke:\#333,stroke-width:2px
```

**Các thành phần chính:**

1. **Stream Receiver & Decoder:** Nhận luồng RTSP từ camera, giải mã video (sử dụng RKMPP \- Rockchip Media Process Platform để tăng tốc phần cứng).  
2. **Frame Buffer/Queue:** Lưu trữ các frame đã giải mã, đóng vai trò bộ đệm giữa các giai đoạn xử lý.  
3. **Frame Dispatcher:** Lấy frame từ buffer, thực hiện tiền xử lý cơ bản (resize, chuyển đổi không gian màu nếu cần thiết, có thể dùng RGA \- Rockchip 2D Graphic Acceleration Engine).  
4. **Face Detection (NPU):** Sử dụng NPU để phát hiện khuôn mặt trong frame. Trả về tọa độ bounding box của các khuôn mặt.  
5. **Face ROI Extractor & Preprocessor:** Từ bounding box, cắt (crop) vùng chứa khuôn mặt (Region of Interest \- ROI), căn chỉnh (align) và tiền xử lý (resize, normalization) cho phù hợp đầu vào của mô hình trích xuất đặc trưng. Có thể dùng RGA.  
6. **Face Feature Extraction (NPU):** Sử dụng NPU để trích xuất vector đặc trưng (embedding) từ ROI khuôn mặt.  
7. **Face Recognition (Matcher):** So sánh vector đặc trưng vừa trích xuất với cơ sở dữ liệu các vector đặc trưng đã biết để nhận dạng. Thường chạy trên CPU hoặc có thể có các thư viện hỗ trợ NPU cho các phép toán vector.  
8. **Result Aggregator & Output:** Tổng hợp kết quả, có thể bao gồm tracking, lưu log, gửi thông báo hoặc cung cấp API cho ứng dụng khác.  
9. **NPU Resource Manager:** Quản lý việc truy cập và sử dụng NPU, đảm bảo các tác vụ NPU được thực thi hiệu quả (ví dụ: batching, ưu tiên).  
10. **RGA Accelerator:** Cung cấp giao diện để sử dụng RGA cho các tác vụ tăng tốc đồ họa 2D.  
11. **Face Embeddings Database:** Lưu trữ vector đặc trưng của những người đã đăng ký.

## **3\. Pipeline xử lý chi tiết và Technical Stacks**

Mỗi luồng camera sẽ được xử lý bởi một pipeline riêng biệt, các pipeline này hoạt động song song.

### **3.1. Giai đoạn 1: Thu nhận và Giải mã luồng RTSP (Stream Receiver & Decoder)**

* **Mục tiêu:** Kết nối tới camera RTSP, nhận luồng video và giải mã thành các frame raw (ví dụ: YUV, BGR).  
* **Technical Stacks:**  
  * **Thư viện:**  
    * **GStreamer (khuyến nghị):** Framework mạnh mẽ cho xử lý media, hỗ trợ plugin rkmpp (ví dụ: rkmppdec) để giải mã video bằng phần cứng trên RK3588. Điều này cực kỳ quan trọng để giảm tải CPU.  
    * **FFmpeg (libavcodec, libavformat):** Một lựa chọn khác, cũng có thể hỗ trợ giải mã phần cứng thông qua VAAPI/VDPAU nếu được cấu hình đúng với driver của Rockchip.  
  * **Ngôn ngữ:** C++  
* **Logic triển khai:**  
  1. Mỗi luồng camera sẽ có một thread hoặc một đối tượng GStreamer pipeline riêng.  
  2. Sử dụng rtspsrc element của GStreamer để kết nối.  
  3. Kết nối rtspsrc với rkmppdec (hoặc decoder tương ứng) để giải mã.  
  4. Sử dụng appsink element của GStreamer để lấy frame đã giải mã ra dưới dạng buffer (ví dụ: cv::Mat nếu tích hợp OpenCV).  
  5. Quản lý kết nối lại (reconnection) nếu luồng bị mất.  
* **Tối ưu hóa:**  
  * **Giải mã phần cứng:** Bắt buộc sử dụng rkmppdec hoặc tương đương.  
  * **Buffer management:** Thiết kế buffer đầu ra của appsink một cách cẩn thận để tránh copy dữ liệu không cần thiết.

### **3.2. Giai đoạn 2: Tiền xử lý ảnh (Image Pre-processing for Detection)**

* **Mục tiêu:** Chuẩn bị frame cho mô hình phát hiện khuôn mặt (thay đổi kích thước, chuyển đổi không gian màu, chuẩn hóa).  
* **Technical Stacks:**  
  * **Thư viện:**  
    * **Rockchip RGA (khuyến nghị):** Sử dụng thư viện librga để thực hiện các tác vụ như resize, CSC (Color Space Conversion), rotation với tốc độ cao bằng phần cứng RGA, giảm tải CPU.  
    * **OpenCV:** Nếu RGA không đáp ứng được một số tác vụ phức tạp, hoặc để quản lý cv::Mat. Tuy nhiên, các phép toán nặng nên ưu tiên RGA.  
  * **Ngôn ngữ:** C++  
* **Logic triển khai:**  
  1. Lấy frame từ Giai đoạn 1\.  
  2. **Resize:** Đưa frame về kích thước đầu vào của mô hình phát hiện khuôn mặt (ví dụ: 640x640). Sử dụng imresize của RGA.  
  3. **Color Space Conversion:** Chuyển đổi không gian màu nếu cần (ví dụ: BGR sang RGB). Sử dụng imcvtcolor của RGA.  
  4. **Normalization:** Chuẩn hóa giá trị pixel (ví dụ: chia cho 255.0, trừ mean, chia stddev). Có thể thực hiện bằng RGA nếu hỗ trợ, hoặc CPU/NEON. Một số mô hình NPU có thể tích hợp bước này.  
* **Tối ưu hóa:**  
  * **Tận dụng RGA:** Ưu tiên RGA cho tất cả các tác vụ có thể.  
  * **Zero-copy:** Nếu có thể, sử dụng bộ nhớ đệm vật lý liên tục (physical contiguous memory) được chia sẻ giữa RKMPP, RGA và NPU để tránh sao chép dữ liệu. DRM (Direct Rendering Manager) buffers có thể hữu ích.

### **3.3. Giai đoạn 3: Phát hiện khuôn mặt (Facial Detection) với NPU**

* **Mục tiêu:** Xác định vị trí các khuôn mặt trong frame.  
* **Technical Stacks:**  
  * **Thư viện:**  
    * **RKNN Toolkit / RKNN Runtime API:** Bộ công cụ và API chính thức của Rockchip để chạy mô hình deep learning trên NPU.  
    * **Mô hình AI:**  
      * **YOLOv5-Face, YOLOv7-Face, YOLOv8-Face:** Phổ biến, cân bằng giữa tốc độ và độ chính xác.  
      * **RetinaFace (phiên bản MobileNet/ResNet backbone nhẹ):** Độ chính xác cao, đặc biệt với khuôn mặt nhỏ.  
      * **MTCNN (Multi-task Cascaded Convolutional Networks):** Cổ điển hơn, nhưng vẫn hiệu quả.  
      * Cần chọn mô hình đã được lượng tử hóa (quantized) và tối ưu cho RK3588 NPU (định dạng .rknn).  
  * **Ngôn ngữ:** C++  
* **Logic triển khai:**  
  1. Khởi tạo RKNN runtime, load mô hình .rknn cho face detection.  
  2. Chuẩn bị dữ liệu đầu vào (frame đã tiền xử lý) theo đúng định dạng yêu cầu của mô hình (layout, type).  
  3. Thực hiện inference: rknn\_inputs\_set, rknn\_run, rknn\_outputs\_get.  
  4. Hậu xử lý (post-processing) kết quả output từ NPU:  
     * Giải mã bounding box, scores, landmarks (nếu có).  
     * Áp dụng Non-Maximum Suppression (NMS) để loại bỏ các box trùng lặp. NMS có thể chạy trên CPU hoặc tìm cách tối ưu bằng NEON. Một số NPU có thể hỗ trợ NMS tùy chỉnh.  
* **Tối ưu hóa:**  
  * **Batch Processing:** Nếu có thể, gom nhiều frame từ các luồng khác nhau (hoặc các frame liên tiếp từ một luồng nếu độ trễ chấp nhận được) thành một batch để đưa vào NPU một lần, tăng thông lượng.  
  * **Asynchronous Inference:** Sử dụng rknn\_run\_async (nếu API hỗ trợ và phù hợp với luồng xử lý) để NPU và CPU có thể hoạt động song song.  
  * **Model Quantization:** Sử dụng mô hình đã được lượng tử hóa (INT8) để tăng tốc độ và giảm yêu cầu bộ nhớ.  
  * **NPU Core Affinity:** RK3588 có 3 NPU core. Cân nhắc việc phân chia tác vụ hoặc pipeline cho các core cụ thể nếu cần thiết và API cho phép.

### **3.4. Giai đoạn 4: Trích xuất ROI và Tiền xử lý cho Nhận dạng**

* **Mục tiêu:** Từ các bounding box phát hiện được, cắt ra các vùng ảnh chứa khuôn mặt (ROI), căn chỉnh và tiền xử lý chúng cho mô hình trích xuất đặc trưng.  
* **Technical Stacks:**  
  * **Thư viện:**  
    * **OpenCV:** Hữu ích cho việc cắt ảnh (cropping), affine transform (cho alignment dựa trên landmarks nếu có).  
    * **Rockchip RGA:** Có thể dùng RGA để crop và resize ROI, CSC.  
  * **Ngôn ngữ:** C++  
* **Logic triển khai:**  
  1. Lặp qua các bounding box có độ tin cậy cao từ Giai đoạn 3\.  
  2. **Crop ROI:** Cắt vùng ảnh khuôn mặt từ frame gốc (frame chưa resize cho detection, hoặc frame đã resize nhưng cần tính toán lại tọa độ).  
  3. **(Tùy chọn) Face Alignment:** Nếu mô hình detection trả về landmarks (ví dụ: vị trí mắt, mũi, miệng), sử dụng chúng để thực hiện phép biến đổi affine (affine transformation) nhằm căn chỉnh khuôn mặt (ví dụ: xoay và scale sao cho mắt nằm trên một đường ngang). Điều này cải thiện độ chính xác của nhận dạng.  
  4. **Resize ROI:** Đưa ROI về kích thước đầu vào của mô hình trích xuất đặc trưng (ví dụ: 112x112 hoặc 160x160).  
  5. **Color Space Conversion & Normalization:** Tương tự như Giai đoạn 2, nhưng cho ROI và theo yêu cầu của mô hình trích xuất đặc trưng.  
* **Tối ưu hóa:**  
  * **RGA for Cropping/Resizing:** Giảm tải CPU.  
  * **Xử lý song song các ROI:** Nếu một frame có nhiều khuôn mặt, có thể xử lý các ROI song song.

### **3.5. Giai đoạn 5: Trích xuất Đặc trưng Khuôn mặt (Face Feature Extraction) với NPU**

* **Mục tiêu:** Biến đổi mỗi ROI khuôn mặt thành một vector đặc trưng (embedding) có số chiều cố định, đại diện cho nhận dạng của khuôn mặt đó.  
* **Technical Stacks:**  
  * **Thư viện:**  
    * **RKNN Toolkit / RKNN Runtime API:** Để chạy mô hình trích xuất đặc trưng.  
    * **Mô hình AI:**  
      * **ArcFace, CosFace, SphereFace, FaceNet (InsightFace implementation):** Các mô hình SOTA (State-of-the-art) cho face recognition.  
      * Cần chọn mô hình đã được lượng tử hóa và tối ưu cho RK3588 NPU (định dạng .rknn).  
  * **Ngôn ngữ:** C++  
* **Logic triển khai:**  
  1. Khởi tạo RKNN runtime, load mô hình .rknn cho feature extraction.  
  2. Với mỗi ROI đã tiền xử lý, chuẩn bị dữ liệu đầu vào.  
  3. Thực hiện inference: rknn\_inputs\_set, rknn\_run, rknn\_outputs\_get.  
  4. Output là một vector embedding (ví dụ: 128-D, 256-D, 512-D).  
* **Tối ưu hóa:**  
  * **Batch Processing:** Rất quan trọng. Gom nhiều ROI khuôn mặt (từ cùng một frame hoặc nhiều frame/camera) thành một batch để NPU xử lý một lần.  
  * **Asynchronous Inference.**  
  * **Model Quantization (INT8).**

### **3.6. Giai đoạn 6: Nhận dạng Khuôn mặt (Face Recognition \- Matching)**

* **Mục tiêu:** So sánh vector đặc trưng vừa trích xuất với cơ sở dữ liệu các vector đã biết để tìm ra danh tính.  
* **Technical Stacks:**  
  * **Thư viện:**  
    * **Faiss (Facebook AI Similarity Search):** Thư viện hiệu quả cao cho tìm kiếm tương tự trong không gian vector lớn. Có thể chạy trên CPU.  
    * **Annoy (Approximate Nearest Neighbors Oh Yeah):** Một thư viện khác cho tìm kiếm lân cận xấp xỉ.  
    * **Thực thi tùy chỉnh:** Tính toán cosine similarity hoặc Euclidean distance trực tiếp trên CPU. Có thể tối ưu bằng NEON intrinsics.  
  * **Cơ sở dữ liệu (Database):**  
    * **SQLite, PostgreSQL:** Lưu trữ metadata của người dùng và có thể lưu embedding dưới dạng BLOB hoặc array (nếu DB hỗ trợ).  
    * **Vector Database chuyên dụng (ví dụ: Milvus, Weaviate):** Nếu số lượng embedding rất lớn, nhưng có thể overkill cho các ứng dụng embedded.  
  * **Ngôn ngữ:** C++  
* **Logic triển khai:**  
  1. Load các embeddings đã biết từ database vào bộ nhớ (hoặc sử dụng index của Faiss/Annoy).  
  2. Với mỗi embedding mới từ Giai đoạn 5:  
     * Tính toán cosine similarity (hoặc L2 distance) với tất cả các embeddings trong database.  
     * Tìm embedding trong database có độ tương tự cao nhất.  
     * Nếu độ tương tự vượt một ngưỡng (threshold) xác định, gán danh tính tương ứng. Ngược lại, là "unknown".  
* **Tối ưu hóa:**  
  * **Indexing (Faiss/Annoy):** Giúp tăng tốc tìm kiếm đáng kể so với brute-force.  
  * **Threshold Tuning:** Ngưỡng nhận dạng cần được tinh chỉnh cẩn thận để cân bằng giữa False Acceptance Rate (FAR) và False Rejection Rate (FRR).  
  * **CPU NEON Optimization:** Nếu tự implement phép so sánh, sử dụng NEON để tăng tốc các phép toán vector.

### **3.7. Giai đoạn 7: Hậu xử lý và Thông báo (Post-processing and Notification)**

* **Mục tiêu:** Tổng hợp kết quả, thực hiện tracking, lưu trữ và thông báo.  
* **Technical Stacks:**  
  * **Thư viện:** OpenCV (cho vẽ bounding box, tên), thư viện logging, thư viện network (cho API).  
  * **Ngôn ngữ:** C++  
* **Logic triển khai:**  
  1. **Tracking (Tùy chọn):** Nếu cần theo dõi một người qua nhiều frame, có thể implement một thuật toán tracking đơn giản (ví dụ: dựa trên IOU của bounding box hoặc kết hợp với re-identification nếu embedding thay đổi nhẹ).  
  2. **Hiển thị kết quả:** Vẽ bounding box, tên người được nhận dạng (hoặc "unknown") lên frame.  
  3. **Lưu trữ sự kiện:** Ghi log các sự kiện phát hiện/nhận dạng (thời gian, camera ID, người được nhận dạng, ảnh crop).  
  4. **Thông báo/API:** Gửi thông báo (MQTT, HTTP request) hoặc cung cấp API để các hệ thống khác truy vấn kết quả.

## **4\. Tối ưu hóa hiệu năng toàn hệ thống**

* **Tận dụng tối đa Hardware Accelerators:**  
  * **NPU:** Cho detection và feature extraction. Sử dụng batching, INT8 quantization.  
  * **RKMPP:** Cho video decoding.  
  * **RGA:** Cho pre/post-processing (resize, CSC, crop, rotation).  
* **Xử lý song song và bất đồng bộ:**  
  * **Multithreading:** Mỗi luồng camera có thể chạy trên một thread riêng. Các giai đoạn trong pipeline (decoder, detector, recognizer) cũng có thể là các worker threads giao tiếp qua message queues.  
  * **Message Queues (ví dụ: std::queue với std::mutex và std::condition\_variable, hoặc thư viện như ZeroMQ):** Để truyền dữ liệu (frames, ROIs, embeddings) giữa các giai đoạn/threads một cách an toàn và không blocking.  
  * **Asynchronous NPU calls:** Sử dụng rknn\_run\_async nếu có thể.  
* **Quản lý bộ nhớ hiệu quả:**  
  * **Zero-Copy:** Cố gắng sử dụng các buffer vật lý được chia sẻ giữa các bộ phận phần cứng (MPP, RGA, NPU) thông qua DRM/DMA-BUF để tránh sao chép dữ liệu tốn kém giữa CPU và các accelerator. Điều này đòi hỏi kiến thức sâu về driver và memory management của Linux trên RK3588.  
  * **Object Pooling:** Tái sử dụng các đối tượng buffer (ví dụ: cv::Mat, RKNN input/output tensors) thay vì cấp phát và giải phóng liên tục.  
* **Chọn lựa mô hình AI:**  
  * Cân bằng giữa độ chính xác và tốc độ/tài nguyên yêu cầu. Các mô hình nhẹ hơn (ví dụ: backbone MobileNet) sẽ cho phép xử lý nhiều luồng hơn.  
  * Luôn ưu tiên các mô hình đã được tối ưu và lượng tử hóa cho RKNN.  
* **Dynamic Frame Rate/Resolution Control:**  
  * Có thể giảm frame rate xử lý hoặc độ phân giải đầu vào cho một số camera nếu tải hệ thống quá cao, hoặc chỉ xử lý ở frame rate cao khi có chuyển động hoặc sự kiện quan trọng.  
* **NPU Task Scheduling và Batching:**  
  * Xây dựng một NPU task scheduler để quản lý việc gửi các yêu cầu inference (detection, feature extraction) từ nhiều pipeline camera khác nhau tới NPU.  
  * Ưu tiên gom các tác vụ cùng loại (ví dụ, nhiều yêu cầu detection) thành một batch lớn để tối đa hóa hiệu suất NPU. RK3588 có 3 NPU cores, scheduler có thể phân phối batch cho các core này.  
* **CPU Affinity:** Gán các thread xử lý nặng (ví dụ: thread quản lý NPU, thread xử lý chính của pipeline) cho các CPU core cụ thể để tránh context switching không cần thiết và tận dụng cache tốt hơn. RK3588 có CPU đa nhân (4xA76 \+ 4xA55).

## **5\. Sơ đồ tuần tự (Sequence Diagram) \- Luồng xử lý cho một frame**

Sơ đồ này mô tả chi tiết các bước và tương tác giữa các thành phần khi xử lý một frame từ một camera.

```
@startuml  
\!theme vibrant  
title Sequence Diagram: Facial Detection & Recognition Pipeline (Single Frame)

actor Camera as Cam  
participant "StreamReceiver\\n(GStreamer/FFmpeg)" as Receiver  
participant "FrameQueue" as FQueue  
participant "FramePreprocessor\\n(RGA/OpenCV)" as Preproc  
participant "FaceDetector\\n(RKNN NPU)" as Detector  
participant "NPU\_Manager" as NPUMgr  
participant "FaceROI\_Extractor\\n(RGA/OpenCV)" as ROIExtractor  
participant "FeatureExtractor\\n(RKNN NPU)" as FeatExtractor  
participant "EmbeddingDB\\n(Faiss/Custom)" as DB  
participant "FaceMatcher\\n(CPU)" as Matcher  
participant "ResultHandler" as Handler  
participant "Application" as App

Cam \-\> Receiver: RTSP Stream  
activate Receiver  
    Receiver \-\> Receiver: Decode Frame (RKMPP)  
    Receiver \-\> FQueue: Push DecodedFrame  
    deactivate Receiver  
activate FQueue

FQueue \-\> Preproc: Get Frame  
deactivate FQueue  
activate Preproc  
    Preproc \-\> Preproc: Resize, CSC, Normalize (RGA)  
    Preproc \-\> Detector: Request Face Detection(PreprocessedFrame)  
    deactivate Preproc  
    activate Detector  
        Detector \-\> NPUMgr: Submit Detection Task(PreprocessedFrame)  
        activate NPUMgr  
            NPUMgr \-\> NPUMgr: Schedule & Run NPU Inference  
            NPUMgr \--\> Detector: DetectionRawOutput  
        deactivate NPUMgr  
        Detector \-\> Detector: Post-process (NMS)  
        Detector \--\> Preproc: BoundingBoxes  
    deactivate Detector  
    activate Preproc

    alt Faces Detected  
        Preproc \-\> ROIExtractor: ProcessFrame(OriginalFrame, BoundingBoxes)  
        deactivate Preproc  
        activate ROIExtractor  
            loop for each BoundingBox  
                ROIExtractor \-\> ROIExtractor: Crop Face ROI (RGA/OpenCV)  
                ROIExtractor \-\> ROIExtractor: Align & Preprocess ROI (RGA/OpenCV)  
                ROIExtractor \-\> FeatExtractor: Request FeatureExtraction(ProcessedROI)  
                activate FeatExtractor  
                    FeatExtractor \-\> NPUMgr: Submit Extraction Task(ProcessedROI)  
                    activate NPUMgr  
                        NPUMgr \-\> NPUMgr: Schedule & Run NPU Inference (Batch if possible)  
                        NPUMgr \--\> FeatExtractor: EmbeddingVector  
                    deactivate NPUMgr  
                    FeatExtractor \--\> ROIExtractor: EmbeddingVector  
                deactivate FeatExtractor  
            end  
            ROIExtractor \--\> Matcher: Submit EmbeddingsForRecognition(List\<EmbeddingVector\>)  
        deactivate ROIExtractor  
        activate Matcher  
            Matcher \-\> DB: QuerySimilarEmbeddings(List\<EmbeddingVector\>)  
            activate DB  
                DB \--\> Matcher: SimilarIDs, Scores  
            deactivate DB  
            Matcher \-\> Matcher: Apply Threshold, Determine Identity  
            Matcher \--\> Handler: RecognitionResults(Identities, BBoxes, Frame)  
        deactivate Matcher  
        activate Handler  
            Handler \-\> Handler: Aggregate, Log Event  
            Handler \-\> App: Notify/DisplayResults  
            deactivate Handler  
    else No Faces Detected  
        Preproc \--\> Handler: NoFacesDetectedEvent(Frame)  
        deactivate Preproc  
        activate Handler  
            Handler \-\> App: Notify/DisplayNoDetection  
        deactivate Handler  
    end  
@enduml
```

**Diễn giải sơ đồ tuần tự:**

1. **Camera (Cam)** gửi luồng RTSP.  
2. **StreamReceiver** nhận và giải mã frame (sử dụng RKMPP), sau đó đẩy vào **FrameQueue**.  
3. **FramePreprocessor** lấy frame từ queue, tiền xử lý (resize, CSC, normalize bằng RGA) rồi gửi yêu cầu phát hiện khuôn mặt tới **FaceDetector**.  
4. **FaceDetector** gửi tác vụ inference tới **NPU\_Manager**.  
5. **NPU\_Manager** điều phối và thực thi trên NPU, trả kết quả thô.  
6. **FaceDetector** hậu xử lý (NMS) và trả về các bounding box.  
7. **Nếu có khuôn mặt được phát hiện:**  
   * **FramePreprocessor** (hoặc một module điều phối) chuyển frame gốc và các bounding box cho **FaceROI\_Extractor**.  
   * **FaceROI\_Extractor** lặp qua từng bounding box:  
     * Cắt ROI, căn chỉnh, tiền xử lý (dùng RGA/OpenCV).  
     * Gửi ROI đã xử lý tới **FeatureExtractor** để trích xuất đặc trưng.  
   * **FeatureExtractor** gửi tác vụ inference tới **NPU\_Manager**.  
   * **NPU\_Manager** thực thi trên NPU (có thể batch nhiều ROI), trả về vector đặc trưng (embedding).  
   * **FeatureExtractor** trả embedding về cho **FaceROI\_Extractor** (hoặc module điều phối).  
   * Sau khi có tất cả embeddings cho frame, chúng được gửi tới **FaceMatcher**.  
   * **FaceMatcher** truy vấn **EmbeddingDB** để tìm các embedding tương tự.  
   * **EmbeddingDB** trả về các ứng viên và điểm số tương tự.  
   * **FaceMatcher** áp dụng ngưỡng, xác định danh tính.  
   * Kết quả nhận dạng (danh tính, bounding box, frame) được gửi tới **ResultHandler**.  
8. **Nếu không có khuôn mặt nào được phát hiện:**  
   * **FramePreprocessor** thông báo cho **ResultHandler**.  
9. **ResultHandler** tổng hợp, ghi log, và có thể hiển thị hoặc gửi thông báo tới **Application**.

## **6\. Kết luận**

Việc triển khai hệ thống phát hiện và nhận dạng khuôn mặt hiệu quả trên RK3588 đòi hỏi sự kết hợp chặt chẽ giữa lựa chọn mô hình AI phù hợp, tối ưu hóa ở từng giai đoạn xử lý, và đặc biệt là tận dụng tối đa các khối tăng tốc phần cứng như NPU, RKMPP, RGA. Kiến trúc đa luồng, bất đồng bộ, cùng với các kỹ thuật như batch processing và zero-copy là chìa khóa để tối đa hóa số lượng luồng camera có thể xử lý đồng thời mà vẫn đảm bảo độ chính xác và đáp ứng thời gian thực.