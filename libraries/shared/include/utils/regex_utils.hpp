#pragma once
#include <string>
#include <vector>
#include <optional>
#include <regex>
#include <map>

namespace utils {
    /**
     * @brief Regex builder for creating complex regular expressions easily
     * 
     * This class provides a fluent interface for building regular expressions
     * with common patterns and modifiers.
     */
    class RegexBuilder {
    public:
        /**
         * @brief Constructor
         */
        RegexBuilder();

        /**
         * @brief Copy constructor
         */
        RegexBuilder(const RegexBuilder& other);

        /**
         * @brief Assignment operator
         */
        RegexBuilder& operator=(const RegexBuilder& other);

        // =============================================================================
        // Basic Building Methods
        // =============================================================================

        /**
         * @brief Add literal text (escaped)
         * @param text Literal text to add
         * @return Reference to this builder
         */
        RegexBuilder& literal(const std::string& text);

        /**
         * @brief Add raw regex pattern (not escaped)
         * @param pattern Raw regex pattern
         * @return Reference to this builder
         */
        RegexBuilder& raw(const std::string& pattern);

        /**
         * @brief Add any character (.)
         * @return Reference to this builder
         */
        RegexBuilder& anyChar();

        /**
         * @brief Add digit character (\d)
         * @return Reference to this builder
         */
        RegexBuilder& digit();

        /**
         * @brief Add word character (\w)
         * @return Reference to this builder
         */
        RegexBuilder& word();

        /**
         * @brief Add whitespace character (\s)
         * @return Reference to this builder
         */
        RegexBuilder& whitespace();

        /**
         * @brief Add character class [abc]
         * @param chars Characters to include in class
         * @return Reference to this builder
         */
        RegexBuilder& charClass(const std::string& chars);

        /**
         * @brief Add negated character class [^abc]
         * @param chars Characters to exclude from class
         * @return Reference to this builder
         */
        RegexBuilder& notCharClass(const std::string& chars);

        /**
         * @brief Add character range [a-z]
         * @param from Start character
         * @param to End character
         * @return Reference to this builder
         */
        RegexBuilder& range(char from, char to);

        // =============================================================================
        // Quantifiers
        // =============================================================================

        /**
         * @brief Add zero or more quantifier (*)
         * @return Reference to this builder
         */
        RegexBuilder& zeroOrMore();

        /**
         * @brief Add one or more quantifier (+)
         * @return Reference to this builder
         */
        RegexBuilder& oneOrMore();

        /**
         * @brief Add zero or one quantifier (?)
         * @return Reference to this builder
         */
        RegexBuilder& zeroOrOne();

        /**
         * @brief Add exact count quantifier {n}
         * @param count Exact count
         * @return Reference to this builder
         */
        RegexBuilder& exactly(int count);

        /**
         * @brief Add range quantifier {min,max}
         * @param min Minimum count
         * @param max Maximum count
         * @return Reference to this builder
         */
        RegexBuilder& between(int min, int max);

        /**
         * @brief Add minimum quantifier {min,}
         * @param min Minimum count
         * @return Reference to this builder
         */
        RegexBuilder& atLeast(int min);

        // =============================================================================
        // Anchors and Boundaries
        // =============================================================================

        /**
         * @brief Add start of string anchor (^)
         * @return Reference to this builder
         */
        RegexBuilder& startOfString();

        /**
         * @brief Add end of string anchor ($)
         * @return Reference to this builder
         */
        RegexBuilder& endOfString();

        /**
         * @brief Add word boundary (\b)
         * @return Reference to this builder
         */
        RegexBuilder& wordBoundary();

        /**
         * @brief Add non-word boundary (\B)
         * @return Reference to this builder
         */
        RegexBuilder& notWordBoundary();

        // =============================================================================
        // Groups and Alternatives
        // =============================================================================

        /**
         * @brief Start capturing group
         * @return Reference to this builder
         */
        RegexBuilder& startGroup();

        /**
         * @brief Start non-capturing group
         * @return Reference to this builder
         */
        RegexBuilder& startNonCapturingGroup();

        /**
         * @brief End group
         * @return Reference to this builder
         */
        RegexBuilder& endGroup();

        /**
         * @brief Add alternative (|)
         * @return Reference to this builder
         */
        RegexBuilder& or_();

        // =============================================================================
        // Common Patterns
        // =============================================================================

        /**
         * @brief Add email pattern
         * @return Reference to this builder
         */
        RegexBuilder& email();

        /**
         * @brief Add URL pattern
         * @return Reference to this builder
         */
        RegexBuilder& url();

        /**
         * @brief Add phone number pattern
         * @return Reference to this builder
         */
        RegexBuilder& phoneNumber();

        /**
         * @brief Add IP address pattern
         * @return Reference to this builder
         */
        RegexBuilder& ipAddress();

        /**
         * @brief Add date pattern (YYYY-MM-DD)
         * @return Reference to this builder
         */
        RegexBuilder& date();

        /**
         * @brief Add time pattern (HH:MM:SS)
         * @return Reference to this builder
         */
        RegexBuilder& time();

        // =============================================================================
        // Build Methods
        // =============================================================================

        /**
         * @brief Build the regex pattern string
         * @return The constructed regex pattern
         */
        std::string build() const;

        /**
         * @brief Build and compile the regex
         * @param flags Regex flags (default: none)
         * @return Compiled regex object
         */
        std::regex buildRegex(std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript) const;

        /**
         * @brief Reset the builder
         * @return Reference to this builder
         */
        RegexBuilder& reset();

    private:
        std::string pattern_;
        
        /**
         * @brief Escape special regex characters
         * @param text Text to escape
         * @return Escaped text
         */
        std::string escape(const std::string& text) const;
    };

    /**
     * @brief Regex utilities for common regex operations
     */
    class RegexUtils {
    public:
        // =============================================================================
        // Matching Functions
        // =============================================================================

        /**
         * @brief Check if string matches regex pattern
         * @param text Text to check
         * @param pattern Regex pattern
         * @param flags Regex flags
         * @return true if matches
         */
        static bool matches(const std::string& text, const std::string& pattern, 
                           std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        /**
         * @brief Check if string matches compiled regex
         * @param text Text to check
         * @param regex Compiled regex
         * @return true if matches
         */
        static bool matches(const std::string& text, const std::regex& regex);

        /**
         * @brief Find first match in string
         * @param text Text to search
         * @param pattern Regex pattern
         * @param flags Regex flags
         * @return Optional match result
         */
        static std::optional<std::smatch> findFirst(const std::string& text, const std::string& pattern,
                                                   std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        /**
         * @brief Find all matches in string
         * @param text Text to search
         * @param pattern Regex pattern
         * @param flags Regex flags
         * @return Vector of match results
         */
        static std::vector<std::smatch> findAll(const std::string& text, const std::string& pattern,
                                               std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        // =============================================================================
        // Replacement Functions
        // =============================================================================

        /**
         * @brief Replace first match
         * @param text Text to process
         * @param pattern Regex pattern
         * @param replacement Replacement string
         * @param flags Regex flags
         * @return Modified string
         */
        static std::string replaceFirst(const std::string& text, const std::string& pattern, 
                                       const std::string& replacement,
                                       std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        /**
         * @brief Replace all matches
         * @param text Text to process
         * @param pattern Regex pattern
         * @param replacement Replacement string
         * @param flags Regex flags
         * @return Modified string
         */
        static std::string replaceAll(const std::string& text, const std::string& pattern, 
                                     const std::string& replacement,
                                     std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        // =============================================================================
        // Splitting Functions
        // =============================================================================

        /**
         * @brief Split string by regex pattern
         * @param text Text to split
         * @param pattern Regex pattern
         * @param flags Regex flags
         * @return Vector of split parts
         */
        static std::vector<std::string> split(const std::string& text, const std::string& pattern,
                                             std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        // =============================================================================
        // Extraction Functions
        // =============================================================================

        /**
         * @brief Extract all capture groups from first match
         * @param text Text to search
         * @param pattern Regex pattern with capture groups
         * @param flags Regex flags
         * @return Vector of captured groups
         */
        static std::vector<std::string> extractGroups(const std::string& text, const std::string& pattern,
                                                      std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        /**
         * @brief Extract named capture groups from first match
         * @param text Text to search
         * @param pattern Regex pattern with named capture groups
         * @param groupNames Names of capture groups
         * @param flags Regex flags
         * @return Map of group names to values
         */
        static std::map<std::string, std::string> extractNamedGroups(const std::string& text, const std::string& pattern,
                                                                    const std::vector<std::string>& groupNames,
                                                                    std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        // =============================================================================
        // Validation Functions
        // =============================================================================

        /**
         * @brief Validate email address
         * @param email Email to validate
         * @return true if valid email
         */
        static bool isValidEmail(const std::string& email);

        /**
         * @brief Validate URL
         * @param url URL to validate
         * @return true if valid URL
         */
        static bool isValidUrl(const std::string& url);

        /**
         * @brief Validate phone number
         * @param phone Phone number to validate
         * @return true if valid phone number
         */
        static bool isValidPhoneNumber(const std::string& phone);

        /**
         * @brief Validate IP address
         * @param ip IP address to validate
         * @return true if valid IP address
         */
        static bool isValidIpAddress(const std::string& ip);

        /**
         * @brief Validate date (YYYY-MM-DD)
         * @param date Date to validate
         * @return true if valid date format
         */
        static bool isValidDate(const std::string& date);

        /**
         * @brief Validate time (HH:MM:SS)
         * @param time Time to validate
         * @return true if valid time format
         */
        static bool isValidTime(const std::string& time);

        // =============================================================================
        // Utility Functions
        // =============================================================================

        /**
         * @brief Escape special regex characters
         * @param text Text to escape
         * @return Escaped text
         */
        static std::string escape(const std::string& text);

        /**
         * @brief Check if regex pattern is valid
         * @param pattern Regex pattern to validate
         * @param flags Regex flags
         * @return true if pattern is valid
         */
        static bool isValidPattern(const std::string& pattern,
                                  std::regex_constants::syntax_option_type flags = std::regex_constants::ECMAScript);

        /**
         * @brief Get regex error message
         * @param pattern Pattern that caused error
         * @return Error description
         */
        static std::string getPatternError(const std::string& pattern);
    };

    // =============================================================================
    // Convenience Functions
    // =============================================================================

    /**
     * @brief Convenience function for regex matching
     * @param text Text to check
     * @param pattern Regex pattern
     * @return true if matches
     */
    bool regexMatch(const std::string& text, const std::string& pattern);

    /**
     * @brief Convenience function for regex replacement
     * @param text Text to process
     * @param pattern Regex pattern
     * @param replacement Replacement string
     * @return Modified string
     */
    std::string regexReplace(const std::string& text, const std::string& pattern, const std::string& replacement);

    /**
     * @brief Convenience function for regex splitting
     * @param text Text to split
     * @param pattern Regex pattern
     * @return Vector of split parts
     */
    std::vector<std::string> regexSplit(const std::string& text, const std::string& pattern);

    /**
     * @brief Create a new regex builder
     * @return New RegexBuilder instance
     */
    RegexBuilder regex();
}
