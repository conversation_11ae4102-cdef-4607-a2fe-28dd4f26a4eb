#include <gtest/gtest.h>
#include "utils/crypto_utils.hpp"
#include <string>
#include <vector>

using namespace utils;

class CryptoUtilsSimpleTest : public ::testing::Test {
protected:
    void SetUp() override {
        testData = "Hello, <PERSON>!";
    }

    std::string testData;
};

// =============================================================================
// Base64 Encoding/Decoding Tests (No OpenSSL required)
// =============================================================================

TEST_F(CryptoUtilsSimpleTest, Base64_BasicTest) {
    std::string encoded = CryptoUtils::base64Encode(testData);
    EXPECT_FALSE(encoded.empty());

    auto decoded = CryptoUtils::base64DecodeString(encoded);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, testData);
}

TEST_F(CryptoUtilsSimpleTest, Base64_EmptyString) {
    std::string encoded = CryptoUtils::base64Encode("");
    // Base64 of empty string should be empty string
    EXPECT_TRUE(encoded.empty());

    auto decoded = CryptoUtils::base64DecodeString(encoded);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, "");
}

TEST_F(CryptoUtilsSimpleTest, Base64_ByteVector) {
    std::vector<uint8_t> data(testData.begin(), testData.end());
    std::string encoded = CryptoUtils::base64Encode(data);
    EXPECT_FALSE(encoded.empty());

    auto decoded = CryptoUtils::base64Decode(encoded);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, data);
}

TEST_F(CryptoUtilsSimpleTest, Base64_InvalidInput) {
    auto result = CryptoUtils::base64Decode("Invalid@Base64!");
    EXPECT_FALSE(result.has_value());
}

// =============================================================================
// Hex Encoding/Decoding Tests (No OpenSSL required)
// =============================================================================

TEST_F(CryptoUtilsSimpleTest, Hex_BasicTest) {
    std::vector<uint8_t> data = {0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF};
    std::string hex = CryptoUtils::bytesToHex(data);
    EXPECT_EQ(hex, "0123456789abcdef");

    auto decoded = CryptoUtils::hexToBytes(hex);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, data);
}

TEST_F(CryptoUtilsSimpleTest, Hex_EmptyData) {
    std::vector<uint8_t> data;
    std::string hex = CryptoUtils::bytesToHex(data);
    EXPECT_EQ(hex, "");

    auto decoded = CryptoUtils::hexToBytes(hex);
    ASSERT_TRUE(decoded.has_value());
    EXPECT_EQ(*decoded, data);
}

TEST_F(CryptoUtilsSimpleTest, Hex_InvalidInput) {
    auto result = CryptoUtils::hexToBytes("Invalid Hex!");
    EXPECT_FALSE(result.has_value());

    auto result2 = CryptoUtils::hexToBytes("123"); // Odd length
    EXPECT_FALSE(result2.has_value());
}

// =============================================================================
// Random Generation Tests (Fallback implementation)
// =============================================================================

TEST_F(CryptoUtilsSimpleTest, RandomBytes_BasicTest) {
    auto result = CryptoUtils::randomBytes(16);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 16);

    // Two calls should produce different results (with high probability)
    auto result2 = CryptoUtils::randomBytes(16);
    ASSERT_TRUE(result2.has_value());
    // Note: There's a tiny chance they could be equal, but very unlikely
}

TEST_F(CryptoUtilsSimpleTest, RandomBytes_ZeroLength) {
    auto result = CryptoUtils::randomBytes(0);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 0);
}

TEST_F(CryptoUtilsSimpleTest, RandomString_BasicTest) {
    auto result = CryptoUtils::randomString(10);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->length(), 10);
}

TEST_F(CryptoUtilsSimpleTest, RandomString_CustomCharset) {
    std::string charset = "0123456789";
    auto result = CryptoUtils::randomString(10, charset);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->length(), 10);

    // Check all characters are from charset
    for (char c : *result) {
        EXPECT_NE(charset.find(c), std::string::npos);
    }
}

TEST_F(CryptoUtilsSimpleTest, RandomHex_BasicTest) {
    auto result = CryptoUtils::randomHex(16);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->length(), 16);

    // Check all characters are hex
    for (char c : *result) {
        EXPECT_TRUE(std::isxdigit(c));
    }
}

TEST_F(CryptoUtilsSimpleTest, RandomHex_OddLength) {
    auto result = CryptoUtils::randomHex(15); // Odd length should fail
    EXPECT_FALSE(result.has_value());
}

TEST_F(CryptoUtilsSimpleTest, GenerateUuid_BasicTest) {
    auto result = CryptoUtils::generateUuid();
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->length(), 36); // UUID format: xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx

    // Check format
    EXPECT_EQ((*result)[8], '-');
    EXPECT_EQ((*result)[13], '-');
    EXPECT_EQ((*result)[18], '-');
    EXPECT_EQ((*result)[23], '-');
    EXPECT_EQ((*result)[14], '4'); // Version 4
}

TEST_F(CryptoUtilsSimpleTest, GenerateSalt_BasicTest) {
    auto result = CryptoUtils::generateSalt(16);
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 16);
}

TEST_F(CryptoUtilsSimpleTest, GenerateAes256Key_BasicTest) {
    auto result = CryptoUtils::generateAes256Key();
    ASSERT_TRUE(result.has_value());
    EXPECT_EQ(result->size(), 32); // 256 bits = 32 bytes
}

// =============================================================================
// Utility Functions Tests
// =============================================================================

TEST_F(CryptoUtilsSimpleTest, IsOpenSslAvailable_Test) {
    bool available = CryptoUtils::isOpenSslAvailable();
    // Just check it returns a boolean value
    EXPECT_TRUE(available == true || available == false);
}

TEST_F(CryptoUtilsSimpleTest, GetVersion_Test) {
    std::string version = CryptoUtils::getVersion();
    EXPECT_FALSE(version.empty());
}

TEST_F(CryptoUtilsSimpleTest, SecureCompare_BasicTest) {
    std::vector<uint8_t> data1 = {0x01, 0x02, 0x03, 0x04};
    std::vector<uint8_t> data2 = {0x01, 0x02, 0x03, 0x04};
    std::vector<uint8_t> data3 = {0x01, 0x02, 0x03, 0x05};

    EXPECT_TRUE(CryptoUtils::secureCompare(data1, data2));
    EXPECT_FALSE(CryptoUtils::secureCompare(data1, data3));
}

TEST_F(CryptoUtilsSimpleTest, SecureCompare_DifferentSizes) {
    std::vector<uint8_t> data1 = {0x01, 0x02, 0x03};
    std::vector<uint8_t> data2 = {0x01, 0x02, 0x03, 0x04};

    EXPECT_FALSE(CryptoUtils::secureCompare(data1, data2));
}

TEST_F(CryptoUtilsSimpleTest, SecureClear_Test) {
    std::vector<uint8_t> data = {0x01, 0x02, 0x03, 0x04};
    std::string str = "sensitive data";

    CryptoUtils::secureClear(data);
    CryptoUtils::secureClear(str);

    EXPECT_TRUE(data.empty());
    EXPECT_TRUE(str.empty());
}

// =============================================================================
// Convenience Functions Tests
// =============================================================================

TEST_F(CryptoUtilsSimpleTest, ConvenienceFunctions_Test) {
    // Test convenience functions that don't require OpenSSL
    std::string randomStr = randomString(10);
    EXPECT_EQ(randomStr.length(), 10);

    std::string uuid = generateUuid();
    EXPECT_EQ(uuid.length(), 36);

    std::string encoded = base64Encode(testData);
    EXPECT_FALSE(encoded.empty());

    std::string decoded = base64Decode(encoded);
    EXPECT_EQ(decoded, testData);
}

// =============================================================================
// Hash Functions Tests (Fallback implementation)
// =============================================================================

TEST_F(CryptoUtilsSimpleTest, SHA256_FallbackTest) {
    // Test fallback implementation (std::hash)
    auto result = CryptoUtils::sha256(testData);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());

    // Test consistency - same input should produce same output
    auto result2 = CryptoUtils::sha256(testData);
    ASSERT_TRUE(result2.has_value());
    EXPECT_EQ(*result, *result2);
}

TEST_F(CryptoUtilsSimpleTest, SHA256_EmptyString) {
    auto result = CryptoUtils::sha256("");
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());
}

TEST_F(CryptoUtilsSimpleTest, HMAC_SHA256_FallbackTest) {
    // Test fallback HMAC implementation
    std::string key = "test_key";
    auto result = CryptoUtils::hmacSha256(key, testData);
    ASSERT_TRUE(result.has_value());
    EXPECT_FALSE(result->empty());

    // Test consistency
    auto result2 = CryptoUtils::hmacSha256(key, testData);
    ASSERT_TRUE(result2.has_value());
    EXPECT_EQ(*result, *result2);
}

TEST_F(CryptoUtilsSimpleTest, ConvenienceHashFunction_Test) {
    // Test convenience hash function
    std::string hash = sha256(testData);
    EXPECT_FALSE(hash.empty());
}
