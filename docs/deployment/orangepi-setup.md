# Orange Pi 5 Plus Setup & Deployment Guide

This guide provides step-by-step instructions for setting up Orange Pi 5 Plus as a target platform and deploying c-aibox applications.

## 🎯 Quick Start

### For Developers (Host Machine)
```bash
# 1. Cross-compile for Orange Pi
./scripts/build/build-orangepi.sh --ram 8GB

# 2. Deploy to Orange Pi
./scripts/deploy/deploy-to-orangepi.sh --ip *************

# 3. Test on Orange Pi
./scripts/deploy/test-on-orangepi.sh --ip ************* --all
```

### For Orange Pi Setup (Target Machine)
```bash
# Install dependencies on Orange Pi
sudo ./scripts/deploy/install-orangepi-deps.sh

# Run applications
cd /home/<USER>/c-aibox
./server
./client_app
```

## 🏗️ Orange Pi 5 Plus Specifications

### Hardware
- **SoC**: RockChip RK3588 (4×Cortex-A76 + 4×Cortex-A55)
- **RAM**: 4GB/8GB/16GB LPDDR4X
- **GPU**: Mali-G610 MP4 with OpenGL ES 3.2
- **Video**: 8K@60fps decode, 8K@30fps encode
- **NPU**: 6 TOPS AI acceleration
- **Storage**: eMMC + microSD + M.2 NVMe SSD

### Software Stack
- **OS**: Ubuntu 22.04/24.04 ARM64
- **Kernel**: Linux 5.10+ with RockChip patches
- **Graphics**: Mali GPU drivers + Mesa
- **Video**: MPP (Media Process Platform)
- **Scaling**: RGA (Raster Graphic Acceleration)

## 📋 Prerequisites

### Development Machine Requirements
- **Linux x86_64**: Ubuntu 20.04+ (recommended)
- **Windows x86_64**: WSL2 with Ubuntu 22.04+
- **macOS ARM64**: macOS 12+ with Homebrew

### Orange Pi Requirements
- **Orange Pi 5 Plus**: Any RAM variant (4GB/8GB/16GB)
- **OS**: Ubuntu 22.04/24.04 ARM64 (official image)
- **Network**: Ethernet or WiFi connection
- **SSH**: Enabled for remote deployment

### Network Setup
- Both machines on same network or accessible via internet
- SSH access to Orange Pi (password or key-based)
- Firewall configured to allow SSH (port 22)

## 🛠️ Development Environment Setup

### 1. Install Cross-Compilation Toolchain

#### Linux (Ubuntu/Debian)
```bash
# Install ARM64 cross-compiler
sudo apt-get update
sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# Install development dependencies
sudo ./scripts/install-rtsp-deps.sh

# Verify installation
aarch64-linux-gnu-gcc --version
```

#### macOS
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install cross-compilation tools
brew install cmake pkg-config
brew install aarch64-elf-gcc

# Install dependencies
./scripts/install-rtsp-deps.sh
```

#### Windows (WSL2)
```bash
# Install WSL2 with Ubuntu 22.04
wsl --install Ubuntu-22.04

# Inside WSL2, follow Linux instructions
sudo apt-get update
sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu
sudo ./scripts/install-rtsp-deps.sh
```

### 2. Configure Orange Pi Sysroot (Optional)
```bash
# Create sysroot directory
mkdir -p ~/orangepi-sysroot

# Copy Orange Pi root filesystem (if available)
rsync -avz orangepi@*************:/ ~/orangepi-sysroot/

# Set environment variable
export ORANGE_PI_SYSROOT=~/orangepi-sysroot
```

## 🚀 Build Process

### 1. Local Development Build
```bash
# Debug build for development
./scripts/build/build-local.sh --debug

# Release build for testing
./scripts/build/build-local.sh --release --clean
```

### 2. Cross-Compilation for Orange Pi
```bash
# Basic cross-compilation (8GB variant)
./scripts/build/build-orangepi.sh

# Specific RAM variant
./scripts/build/build-orangepi.sh --ram 4GB   # For 4GB variant
./scripts/build/build-orangepi.sh --ram 16GB  # For 16GB variant

# Debug build with verbose output
./scripts/build/build-orangepi.sh --debug --verbose

# Create deployment package
./scripts/build/build-orangepi.sh --package
```

### 3. Build Options
```bash
# Available options for build-orangepi.sh
--release          # Release build (default)
--debug            # Debug build
--clean            # Clean build directory
--verbose          # Verbose build output
--jobs N           # Parallel jobs (default: auto)
--ram SIZE         # Orange Pi RAM: 4GB/8GB/16GB
--sysroot PATH     # Custom sysroot path
--package          # Create deployment package
```

## 📦 Deployment Process

### 1. Prepare Orange Pi
```bash
# SSH to Orange Pi
ssh orangepi@*************

# Update system
sudo apt-get update && sudo apt-get upgrade -y

# Install dependencies
sudo ./scripts/deploy/install-orangepi-deps.sh

# Reboot for optimal performance
sudo reboot
```

### 2. Deploy from Development Machine
```bash
# Basic deployment
./scripts/deploy/deploy-to-orangepi.sh --ip *************

# Deployment with options
./scripts/deploy/deploy-to-orangepi.sh \
    --ip ************* \
    --user orangepi \
    --deploy-dir /opt/c-aibox \
    --start-services

# Using SSH key
./scripts/deploy/deploy-to-orangepi.sh \
    --ip ************* \
    --ssh-key ~/.ssh/orangepi_key
```

### 3. Deployment Options
```bash
# Available options for deploy-to-orangepi.sh
--ip IP            # Orange Pi IP address (required)
--user USER        # SSH username (default: orangepi)
--port PORT        # SSH port (default: 22)
--deploy-dir DIR   # Deployment directory
--ssh-key PATH     # SSH private key
--no-deps          # Skip dependency installation
--start-services   # Start services after deployment
--no-backup        # Don't backup existing installation
```

## 🧪 Testing & Validation

### 1. Basic Functionality Tests
```bash
# Run basic tests
./scripts/deploy/test-on-orangepi.sh --ip *************

# Test specific components
./scripts/deploy/test-on-orangepi.sh --ip ************* --hardware
```

### 2. Performance Testing
```bash
# Run performance benchmarks
./scripts/deploy/test-on-orangepi.sh \
    --ip ************* \
    --performance \
    --duration 120

# Comprehensive testing
./scripts/deploy/test-on-orangepi.sh \
    --ip ************* \
    --all \
    --duration 300
```

### 3. Test Types Available
- **Basic**: Application startup, library dependencies
- **Performance**: CPU, memory, GPU utilization benchmarks
- **Hardware**: RK3588 MPP, RGA, GPU acceleration tests
- **Stress**: Long-running stability tests
- **Network**: Connectivity and RTSP streaming tests

## 🔧 Troubleshooting

### Common Issues

#### 1. Cross-Compilation Fails
```bash
# Check toolchain installation
aarch64-linux-gnu-gcc --version

# Install missing dependencies
sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# Clean and rebuild
./scripts/build/build-orangepi.sh --clean
```

#### 2. SSH Connection Issues
```bash
# Test SSH connection
ssh orangepi@*************

# Check SSH service on Orange Pi
sudo systemctl status ssh
sudo systemctl enable ssh
sudo systemctl start ssh

# Configure SSH key authentication
ssh-copy-id orangepi@*************
```

#### 3. Missing Dependencies on Orange Pi
```bash
# Reinstall dependencies
sudo ./scripts/deploy/install-orangepi-deps.sh

# Check library dependencies
ldd ./server
ldd ./client_app

# Install missing libraries manually
sudo apt-get install libgstreamer1.0-0 libqt5core5a
```

#### 4. Hardware Acceleration Issues
```bash
# Check GPU devices
ls -la /dev/dri/

# Check RK3588 specific devices
ls -la /dev/rga /dev/mpp*

# Test GPU acceleration
glxinfo | grep "OpenGL renderer"
gst-inspect-1.0 | grep rockchip
```

### Performance Optimization

#### 1. CPU Governor
```bash
# Set performance governor
echo performance | sudo tee /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor
```

#### 2. Memory Configuration
```bash
# Optimize memory settings
echo "vm.swappiness=10" | sudo tee -a /etc/sysctl.conf
echo "vm.vfs_cache_pressure=50" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

#### 3. GPU Configuration
```bash
# Enable GPU memory sharing
echo "MALI_SHARED_MEMORY=1" | sudo tee -a /etc/environment
```

## 📊 Performance Targets

### Orange Pi 5 Plus Performance Goals
- **CPU Usage**: <50% average load
- **Memory Usage**: <2GB for 4GB variant, <4GB for 8GB variant
- **GPU Utilization**: >70% for video processing
- **RTSP Latency**: <100ms stream processing
- **Video Decode**: 4K@60fps with hardware acceleration

### Monitoring Commands
```bash
# System resources
htop
iotop
free -h

# GPU usage
cat /sys/class/devfreq/fb000000.gpu/load

# Temperature monitoring
cat /sys/class/thermal/thermal_zone*/temp
```

## 🎯 Next Steps

1. **Setup Orange Pi**: Follow prerequisites and install dependencies
2. **Cross-compile**: Build applications for ARM64 architecture
3. **Deploy**: Transfer and install on Orange Pi
4. **Test**: Validate functionality and performance
5. **Optimize**: Fine-tune for your specific use case

This setup provides a robust development and deployment pipeline for Orange Pi 5 Plus with RK3588 hardware acceleration support.
