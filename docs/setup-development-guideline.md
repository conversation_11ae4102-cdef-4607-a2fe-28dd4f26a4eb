# Development Environment Setup Guide for C++ Monorepo Projects

## 1. Minimum Requirements
*   Git
*   CMake >= 3.18
*   GCC >= 9.0 or Clang >= 10
*   Ninja (optional, significantly speeds up builds)
*   Python 3 (if using auxiliary scripts, not mandatory for C++ build)
*   GoogleTest (typically installed automatically via script/CMake FetchContent)
*   Docker (if you plan to build/run Docker images)
*   VS Code (recommended) and C/C++ extension (or other IDEs like CLion, QtCreator, etc.)

## 2. Manual Build Environment Setup (Linux/macOS)

### Ubuntu/Debian
```bash
# Update package lists
sudo apt update
# Install essential build tools, CMake, Git, Clang tools, Ninja, Python
sudo apt install -y build-essential cmake git clang-format clang lldb ninja-build python3 python3-pip

# Install GoogleTest (if you want it pre-installed; usually handled by CMake FetchContent during build)
# For modern systems, this often involves building from source, which CMake will handle.
# If your distribution offers a pre-built package that works:
sudo apt install -y libgtest-dev
# On some systems, you might need to compile it manually after installing libgtest-dev:
# cd /usr/src/googletest
# sudo cmake .
# sudo make
# sudo cp lib/*.a /usr/lib/

# Docker (if not already installed)
sudo apt install -y docker.io
# Add your user to the docker group to run docker without sudo
# You will need to log out and log back in for this change to take effect.
sudo usermod -aG docker $USER

# VS Code (optional)
# See instructions at: https://code.visualstudio.com/download
```

### macOS
Install Homebrew, then:
```bash
brew install cmake gcc clang-format ninja python git
brew install --cask visual-studio-code # For VS Code
brew install docker # For Docker Desktop
```

## 3. First-time Project Build and Run
```bash
git clone <your_repo_url>
cd <your_repo_folder>

# Assuming a script named build.sh exists for convenience
./scripts/build.sh Release
```
*   Executable files will typically be in `build/bin/` or a similar output directory configured by CMake.
*   Libraries will typically be in `build/lib/`.

If you want to perform a clean build:
```bash
# Assuming the script supports a 'clean' argument
./scripts/build.sh Release clean
# Or manually:
# rm -rf build
# ./scripts/build.sh Release
```

If you want to build in Debug mode:
```bash
./scripts/build.sh Debug
```

## 4. Running Unit Tests
If the project integrates GoogleTest/Catch2, after building, test executables will typically be in `build/bin/` or `build/test/`.

Run tests using CTest (if configured):
```bash
ctest --test-dir build
# Or, to run with more verbosity:
# ctest --test-dir build -V
```
Or run the test executable directly:
```bash
./build/bin/your_test_executable_name # Adjust path and name as needed
```

## 5. Using Dev Containers (VS Code/Codespaces)
**Highly recommended for team consistency.**

**Setup:**
1.  Install VS Code.
2.  Install the "Dev Containers" ( `ms-vscode-remote.remote-containers` ) extension.
3.  Clone the project repository.
4.  Open the project folder in VS Code.
5.  Press `F1` (or `Ctrl+Shift+P` / `Cmd+Shift+P`) to open the command palette.
6.  Type and select: `Dev Containers: Reopen in Container`.
    (VS Code will automatically build the development environment based on the configuration in the `.devcontainer/` directory).
7.  All build, test, and debug operations are performed inside this container.

**Advantages:**
*   **Consistent Environment:** Uniform build/test environment for all team members, eliminating "it works on my machine" issues.
*   **Pre-configured Tools:** Often comes with tools like `clang-format`, `cmake`, `gdb`, `clangd` pre-installed and configured.

## 6. Using Docker (Build/Run Application Image)

To build an image for an application (e.g., `app1`):
```bash
docker build -f apps/app1/Dockerfile -t myorg/app1:latest .
```

To run the container:
```bash
docker run --rm myorg/app1:latest
```

## 7. Common Issues & Solutions

*   **Missing libraries or outdated compiler version:**
    *   **Solution:** Update your operating system's packages or manually install the required newer versions of tools/libraries.

*   **Cannot build with CMake / CMake errors:**
    *   **Solution:**
        1.  Verify your CMake version (`cmake --version`).
        2.  Delete the `build` directory (or your CMake build output directory) completely.
        3.  Try rebuilding from scratch.

*   **"Dev Containers: Reopen in Container" command not found in VS Code:**
    *   **Solution:** Ensure the "Dev Containers" ( `ms-vscode-remote.remote-containers` ) extension is installed and enabled.

*   **Cannot run Docker on Windows:**
    *   **Solution:** Install Docker Desktop and ensure WSL2 (Windows Subsystem for Linux 2) is enabled and configured correctly.

*   **IntelliSense (code completion/analysis) not working correctly in VS Code:**
    *   **Solution:**
        1.  Ensure a `compile_commands.json` file has been generated in your build directory (CMake usually does this with `set(CMAKE_EXPORT_COMPILE_COMMANDS ON)`).
        2.  Make sure the C/C++ extension in VS Code is configured to use this file (often detected automatically if it's at the project root or in the build directory, or symlinked to the root). Some build scripts might symlink it to the project root.

*   **Permission errors when building/running Docker:**
    *   **Solution:** Ensure your user is part of the `docker` group. You might need to run `sudo usermod -aG docker $USER` and then log out and log back in.

## 8. Code Style and Formatting
*   A `.clang-format` file should be present in the repository root to define the code style.
*   Enable "Format On Save" in your VS Code settings to automatically format code according to `.clang-format`, ensuring consistent style across the team.

If code is not auto-formatted, or to format manually:
```bash
clang-format -i <your_file.cpp> # Formats the file in-place
```

## 9. Quick References
*   [CMake Documentation](https://cmake.org/documentation/)
*   [GoogleTest Documentation](https://google.github.io/googletest/)
*   [VS Code Dev Containers](https://code.visualstudio.com/docs/devcontainers/containers)

## 10. Support
If you encounter any problems during the environment setup or maintenance, please contact your team lead or open an issue in the project repository.

**Recommendations:**
*   Using Dev Containers or Docker is strongly recommended to avoid manually managing toolchains and ensure consistency.
*   All steps in this guide are assumed to have been tested on common environments like Ubuntu, macOS, GitHub Codespaces, and generic Docker setups.
