#pragma once

#include <httplib.h>
#include <memory>
#include <utils/json_utils.hpp>
#include "base_controller.hpp"
#include "handlers/health_handler.hpp"

namespace server::controllers {

/**
 * @brief Controller for health and system information endpoints
 * Handles HTTP requests for health checks and API information
 */
class HealthController : public BaseController {
public:
    HealthController(std::shared_ptr<shared::ServerConfig> config,
                    std::shared_ptr<handlers::HealthHandler> health_handler);
    ~HealthController() override = default;

    // HTTP endpoint handlers
    void handleHealthCheck(const httplib::Request& req, httplib::Response& res);
    void handleApiInfo(const httplib::Request& req, httplib::Response& res);
    void handleSystemStatus(const httplib::Request& req, httplib::Response& res);

    // Route registration helper
    void registerRoutes(httplib::Server& server);

private:
    std::shared_ptr<handlers::HealthHandler> health_handler_;

    // HTTP processing helpers
    void sendJsonResponse(httplib::Response& res, const utils::JsonValue& json_response);
};

} // namespace server::controllers
