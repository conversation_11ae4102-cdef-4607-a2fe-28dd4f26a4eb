#pragma once

#include <string>
#include <unordered_map>
#include <optional>
#include <functional>
#include <memory>
#include <vector>

namespace utils {

    /**
     * @brief HTTP method enumeration
     */
    enum class HttpMethod {
        GET,
        POST,
        PUT,
        DELETE,
        PATCH,
        HEAD,
        OPTIONS
    };

    /**
     * @brief HTTP response structure
     */
    struct HttpResponse {
        int statusCode = 0;
        std::string statusText;
        std::unordered_map<std::string, std::string> headers;
        std::string body;
        bool success = false;
        std::string errorMessage;
        double responseTime = 0.0; // in seconds

        /**
         * @brief Check if response is successful (status code 200-299)
         */
        bool isSuccess() const {
            return statusCode >= 200 && statusCode < 300;
        }

        /**
         * @brief Check if response is client error (status code 400-499)
         */
        bool isClientError() const {
            return statusCode >= 400 && statusCode < 500;
        }

        /**
         * @brief Check if response is server error (status code 500-599)
         */
        bool isServerError() const {
            return statusCode >= 500 && statusCode < 600;
        }
    };

    /**
     * @brief HTTP request configuration
     */
    struct HttpConfig {
        std::unordered_map<std::string, std::string> headers;
        std::string body;
        int timeoutMs = 30000; // 30 seconds default
        bool followRedirects = true;
        int maxRedirects = 5;
        bool verifySSL = true;
        std::string userAgent = "C-AIBOX-HttpUtils/1.0";
        std::string proxy;
        std::string proxyAuth; // username:password format

        // Authentication
        std::string basicAuth; // username:password format
        std::string bearerToken;

        // Content type shortcuts
        void setContentTypeJson() {
            headers["Content-Type"] = "application/json";
        }

        void setContentTypeForm() {
            headers["Content-Type"] = "application/x-www-form-urlencoded";
        }

        void setContentTypeMultipart() {
            headers["Content-Type"] = "multipart/form-data";
        }

        void setBearerAuth(const std::string& token) {
            headers["Authorization"] = "Bearer " + token;
        }

        void setBasicAuth(const std::string& username, const std::string& password) {
            basicAuth = username + ":" + password;
        }
    };

    /**
     * @brief HTTP utilities class similar to axios
     */
    class HttpUtils {
    public:
        /**
         * @brief Perform HTTP GET request
         * @param url Target URL
         * @param config Request configuration
         * @return HTTP response
         */
        static HttpResponse get(const std::string& url, const HttpConfig& config = {});

        /**
         * @brief Perform HTTP POST request
         * @param url Target URL
         * @param body Request body
         * @param config Request configuration
         * @return HTTP response
         */
        static HttpResponse post(const std::string& url, const std::string& body = "", const HttpConfig& config = {});

        /**
         * @brief Perform HTTP PUT request
         * @param url Target URL
         * @param body Request body
         * @param config Request configuration
         * @return HTTP response
         */
        static HttpResponse put(const std::string& url, const std::string& body = "", const HttpConfig& config = {});

        /**
         * @brief Perform HTTP DELETE request
         * @param url Target URL
         * @param config Request configuration
         * @return HTTP response
         */
        static HttpResponse del(const std::string& url, const HttpConfig& config = {});

        /**
         * @brief Perform HTTP PATCH request
         * @param url Target URL
         * @param body Request body
         * @param config Request configuration
         * @return HTTP response
         */
        static HttpResponse patch(const std::string& url, const std::string& body = "", const HttpConfig& config = {});

        /**
         * @brief Perform HTTP HEAD request
         * @param url Target URL
         * @param config Request configuration
         * @return HTTP response
         */
        static HttpResponse head(const std::string& url, const HttpConfig& config = {});

        /**
         * @brief Perform generic HTTP request
         * @param method HTTP method
         * @param url Target URL
         * @param config Request configuration
         * @return HTTP response
         */
        static HttpResponse request(HttpMethod method, const std::string& url, const HttpConfig& config = {});

        /**
         * @brief Download file from URL
         * @param url Source URL
         * @param filepath Destination file path
         * @param config Request configuration
         * @return true if download successful
         */
        static bool downloadFile(const std::string& url, const std::string& filepath, const HttpConfig& config = {});

        /**
         * @brief Upload file to URL
         * @param url Target URL
         * @param filepath Source file path
         * @param fieldName Form field name for file
         * @param config Request configuration
         * @return HTTP response
         */
        static HttpResponse uploadFile(const std::string& url, const std::string& filepath, 
                                     const std::string& fieldName = "file", const HttpConfig& config = {});

        /**
         * @brief Check if HTTP utils is available (CURL compiled)
         * @return true if available
         */
        static bool isAvailable();

        /**
         * @brief Get HTTP method string representation
         * @param method HTTP method enum
         * @return Method string
         */
        static std::string methodToString(HttpMethod method);

        /**
         * @brief Parse HTTP method from string
         * @param methodStr Method string
         * @return HTTP method enum
         */
        static std::optional<HttpMethod> stringToMethod(const std::string& methodStr);

        /**
         * @brief URL encode string
         * @param str String to encode
         * @return URL encoded string
         */
        static std::string urlEncode(const std::string& str);

        /**
         * @brief URL decode string
         * @param str String to decode
         * @return URL decoded string
         */
        static std::string urlDecode(const std::string& str);

        /**
         * @brief Build query string from parameters
         * @param params Query parameters
         * @return Query string
         */
        static std::string buildQueryString(const std::unordered_map<std::string, std::string>& params);

        /**
         * @brief Parse URL and extract components
         * @param url URL to parse
         * @param scheme Output scheme (http/https)
         * @param host Output host
         * @param port Output port
         * @param path Output path
         * @param query Output query string
         * @return true if parsing successful
         */
        static bool parseUrl(const std::string& url, std::string& scheme, std::string& host, 
                           int& port, std::string& path, std::string& query);

    private:
        /**
         * @brief Internal implementation for HTTP requests
         */
        static HttpResponse performRequest(HttpMethod method, const std::string& url, const HttpConfig& config);

        /**
         * @brief Setup CURL handle with configuration
         */
        static void setupCurlHandle(void* curl, HttpMethod method, const std::string& url, const HttpConfig& config);

        /**
         * @brief Callback for writing response data
         */
        static size_t writeCallback(void* contents, size_t size, size_t nmemb, std::string* response);

        /**
         * @brief Callback for writing response headers
         */
        static size_t headerCallback(void* contents, size_t size, size_t nmemb, 
                                   std::unordered_map<std::string, std::string>* headers);

        /**
         * @brief Parse header line
         */
        static void parseHeaderLine(const std::string& headerLine, 
                                  std::unordered_map<std::string, std::string>& headers);
    };

    // Convenience functions for ease of use

    /**
     * @brief HTTP GET convenience function
     */
    HttpResponse httpGet(const std::string& url, const HttpConfig& config = {});

    /**
     * @brief HTTP POST convenience function
     */
    HttpResponse httpPost(const std::string& url, const std::string& body = "", const HttpConfig& config = {});

    /**
     * @brief HTTP PUT convenience function
     */
    HttpResponse httpPut(const std::string& url, const std::string& body = "", const HttpConfig& config = {});

    /**
     * @brief HTTP DELETE convenience function
     */
    HttpResponse httpDelete(const std::string& url, const HttpConfig& config = {});

    /**
     * @brief Download file convenience function
     */
    bool downloadFile(const std::string& url, const std::string& filepath, const HttpConfig& config = {});
}
