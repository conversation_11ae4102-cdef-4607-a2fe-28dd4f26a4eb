# RTSP Input Module - Technical Architecture

## Architecture Overview

The RTSP Input Module follows a layered, modular architecture designed for high performance, scalability, and maintainability. It integrates seamlessly with the existing project structure while providing robust RTSP stream handling capabilities.

## System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                    Application Layer                            │
├─────────────────────────────────────────────────────────────────┤
│  Client GUI (Qt)           │         Server API (HTTP)          │
│  - Stream Management       │         - REST Endpoints           │
│  - Statistics Dashboard    │         - WebSocket Events         │
│  - Configuration UI        │         - Authentication           │
├─────────────────────────────────────────────────────────────────┤
│                    RTSP Input Module                            │
├─────────────────────────────────────────────────────────────────┤
│  Stream Multiplexer                                             │
│  - Multi-stream coordination    - Resource allocation           │
│  - Load balancing              - Health monitoring              │
├─────────────────────────────────────────────────────────────────┤
│  Connection Manager        │         Packet Receiver            │
│  - RTSP Protocol           │         - RTP/RTCP Handling        │
│  - Authentication          │         - Jitter Buffer            │
│  - Retry Logic             │         - Packet Sequencing        │
├─────────────────────────────────────────────────────────────────┤
│  NAL Unit Parser                                                │
│  - H.264/H.265 Parsing     - Parameter Set Management          │
│  - Fragmentation Handling  - Stream Validation                 │
├─────────────────────────────────────────────────────────────────┤
│                    Core Infrastructure                          │
├─────────────────────────────────────────────────────────────────┤
│  Thread Pool Manager       │         Queue System               │
│  - Worker Threads          │         - Lock-free Queues         │
│  - I/O Threads             │         - Back-pressure Handling   │
│  - Priority Scheduling     │         - Memory Pool Management   │
├─────────────────────────────────────────────────────────────────┤
│  Configuration System      │         Logging & Monitoring       │
│  - JSON Configuration      │         - Structured Logging       │
│  - Runtime Updates         │         - Performance Metrics      │
│  - Validation              │         - Health Checks            │
├─────────────────────────────────────────────────────────────────┤
│                    External Dependencies                        │
├─────────────────────────────────────────────────────────────────┤
│  FFmpeg Libraries          │         System Libraries           │
│  - libavformat             │         - Threading (std::thread)  │
│  - libavcodec              │         - Networking (sockets)     │
│  - libavutil               │         - SSL/TLS (OpenSSL)        │
└─────────────────────────────────────────────────────────────────┘
```

## Component Architecture

### 1. Stream Multiplexer (Top-Level Coordinator)

**Responsibilities:**
- Manages multiple concurrent RTSP streams
- Coordinates resource allocation across streams
- Implements stream prioritization and QoS
- Monitors overall system health

**Key Classes:**
```cpp
class StreamMultiplexer {
public:
    // Stream lifecycle management
    StreamId addStream(const RTSPConnectionConfig& config);
    bool removeStream(StreamId id);
    bool updateStream(StreamId id, const RTSPConnectionConfig& config);
    
    // Resource management
    void setResourceLimits(const ResourceLimits& limits);
    ResourceUsage getCurrentUsage() const;
    
    // Health monitoring
    std::vector<StreamHealth> getStreamHealth() const;
    SystemHealth getSystemHealth() const;
    
private:
    std::unordered_map<StreamId, std::unique_ptr<StreamContext>> streams_;
    std::unique_ptr<ThreadPoolManager> thread_pool_;
    std::unique_ptr<ResourceManager> resource_manager_;
    std::unique_ptr<HealthMonitor> health_monitor_;
};

class StreamContext {
    StreamId id_;
    RTSPConnectionConfig config_;
    std::unique_ptr<ConnectionManager> connection_manager_;
    std::unique_ptr<PacketReceiver> packet_receiver_;
    std::unique_ptr<NALParser> nal_parser_;
    std::unique_ptr<PacketQueue> output_queue_;
    StreamStatistics statistics_;
    StreamState state_;
};
```

### 2. Connection Manager (RTSP Protocol Layer)

**Responsibilities:**
- Implements RTSP client protocol
- Handles authentication and session management
- Manages connection lifecycle and recovery
- Provides connection statistics

**Key Classes:**
```cpp
class ConnectionManager {
public:
    // Connection lifecycle
    ConnectionResult connect(const RTSPConnectionConfig& config);
    void disconnect();
    bool isConnected() const;

    // Session management
    bool maintainSession();
    void handleKeepAlive();

    // Statistics and monitoring
    ConnectionStatistics getStatistics() const;
    ConnectionHealth getHealth() const;

private:
    std::unique_ptr<GStreamerRTSPConnection> connection_;
    std::unique_ptr<AuthenticationHandler> auth_handler_;
    std::unique_ptr<RetryManager> retry_manager_;
    ConnectionState state_;
    ConnectionStatistics stats_;
};

class GStreamerRTSPConnection {
    // GStreamer pipeline management
    // RK3588 hardware acceleration integration
    // MPP decoder utilization
    // RGA scaler integration
    // DMABUF zero-copy operations
};
```

### 3. Packet Receiver (RTP/RTCP Layer)

**Responsibilities:**
- Receives and processes RTP packets
- Handles RTCP feedback
- Manages jitter buffer for packet reordering
- Provides transport layer abstraction

**Key Classes:**
```cpp
class PacketReceiver {
public:
    // Packet reception via GStreamer
    void startReceiving();
    void stopReceiving();

    // Transport configuration
    void setTransportMode(TransportMode mode);
    void configureJitterBuffer(const JitterBufferConfig& config);

    // RK3588 hardware integration
    void enableMPPDecoding(bool enable);
    void enableRGAScaling(bool enable);

    // Statistics
    PacketStatistics getStatistics() const;

private:
    std::unique_ptr<GStreamerRTPReceiver> rtp_receiver_;
    std::unique_ptr<GStreamerRTCPHandler> rtcp_handler_;
    std::unique_ptr<HardwareJitterBuffer> jitter_buffer_;
    std::unique_ptr<DMABufTransport> transport_;
};

class HardwareJitterBuffer {
    // Hardware-accelerated buffer management
    // DMABUF integration for zero-copy
    // RK3588 memory optimization
    // Adaptive sizing based on hardware capabilities
};
```

### 4. NAL Unit Parser (Video Processing Layer)

**Responsibilities:**
- Parses H.264/H.265 NAL units
- Handles fragmentation and reassembly
- Extracts codec parameters
- Validates bitstream integrity

**Key Classes:**
```cpp
class NALParser {
public:
    // Parsing interface
    std::vector<NALUnit> parsePacket(const RTPPacket& packet);
    bool isParameterSet(const NALUnit& nal);
    
    // Codec information
    CodecInfo getCodecInfo() const;
    bool hasParameterSets() const;
    
private:
    std::unique_ptr<H264Parser> h264_parser_;
    std::unique_ptr<H265Parser> h265_parser_;
    std::unique_ptr<ParameterSetManager> param_manager_;
    std::unique_ptr<FragmentationHandler> frag_handler_;
};

class NALUnit {
    NALUnitType type_;
    std::vector<uint8_t> data_;
    uint64_t timestamp_;
    uint32_t sequence_number_;
    StreamId stream_id_;
    // Metadata and validation info
};
```

## Data Flow Architecture

### 1. Packet Processing Pipeline

```
RTSP Stream → RTP Packets → Jitter Buffer → NAL Units → Output Queue
     ↓              ↓             ↓            ↓           ↓
Connection     Packet        Reordering   Parsing &    Application
Management     Reception     & Buffering  Validation   Consumption
```

### 2. Thread Architecture

```cpp
// Thread Pool Design
class ThreadPoolManager {
public:
    enum class ThreadType {
        IO_THREAD,      // Network I/O operations
        WORKER_THREAD,  // CPU-intensive processing
        MONITOR_THREAD  // Health monitoring and statistics
    };
    
    void submitTask(ThreadType type, std::function<void()> task);
    void setThreadCount(ThreadType type, size_t count);
    
private:
    std::unordered_map<ThreadType, std::unique_ptr<ThreadPool>> pools_;
};

// Per-stream thread allocation
struct StreamThreadAllocation {
    ThreadId io_thread;      // Network operations
    ThreadId worker_thread;  // Packet processing
    ThreadId monitor_thread; // Statistics collection
};
```

### 3. Memory Management

```cpp
// Buffer Pool for efficient memory management
class BufferPool {
public:
    std::unique_ptr<Buffer> acquire(size_t size);
    void release(std::unique_ptr<Buffer> buffer);
    
    // Statistics
    size_t getTotalAllocated() const;
    size_t getAvailableBuffers() const;
    
private:
    std::vector<std::unique_ptr<Buffer>> available_buffers_;
    std::atomic<size_t> total_allocated_;
    std::mutex pool_mutex_;
};

// Zero-copy buffer management
class Buffer {
    uint8_t* data_;
    size_t size_;
    size_t capacity_;
    std::atomic<int> ref_count_;
    
public:
    // Reference counting for zero-copy operations
    std::shared_ptr<Buffer> share();
    void resize(size_t new_size);
};
```

## Performance Architecture

### 1. Lock-Free Data Structures

```cpp
// High-performance queue implementation
template<typename T>
class LockFreeQueue {
public:
    bool enqueue(T&& item);
    bool dequeue(T& item);
    size_t size() const;
    
private:
    struct Node {
        std::atomic<T*> data;
        std::atomic<Node*> next;
    };
    
    std::atomic<Node*> head_;
    std::atomic<Node*> tail_;
};

// Specialized packet queue with back-pressure
class PacketQueue : public LockFreeQueue<NALUnit> {
public:
    void setBackPressureThreshold(size_t threshold);
    bool isBackPressureActive() const;
    
    // Priority handling
    bool enqueueWithPriority(NALUnit&& unit, Priority priority);
    
private:
    std::atomic<size_t> back_pressure_threshold_;
    std::atomic<bool> back_pressure_active_;
};
```

### 2. Performance Monitoring

```cpp
class PerformanceMonitor {
public:
    // Metrics collection
    void recordLatency(const std::string& operation, std::chrono::microseconds latency);
    void recordThroughput(const std::string& stream, size_t bytes_processed);
    void recordQueueDepth(const std::string& queue, size_t depth);
    
    // Performance analysis
    PerformanceReport generateReport() const;
    std::vector<PerformanceAlert> checkAlerts() const;
    
private:
    std::unordered_map<std::string, LatencyHistogram> latency_metrics_;
    std::unordered_map<std::string, ThroughputCounter> throughput_metrics_;
    std::unordered_map<std::string, QueueDepthGauge> queue_metrics_;
};
```

## Error Handling Architecture

### 1. Error Classification

```cpp
enum class ErrorCategory {
    NETWORK_ERROR,      // Connection failures, timeouts
    PROTOCOL_ERROR,     // RTSP/RTP protocol violations
    CODEC_ERROR,        // H.264/H.265 parsing errors
    RESOURCE_ERROR,     // Memory, thread exhaustion
    CONFIGURATION_ERROR // Invalid configuration
};

class RTSPError {
public:
    ErrorCategory category() const;
    std::string message() const;
    std::error_code code() const;
    bool isRecoverable() const;
    
private:
    ErrorCategory category_;
    std::string message_;
    std::error_code code_;
    bool recoverable_;
};
```

### 2. Recovery Strategies

```cpp
class ErrorRecoveryManager {
public:
    void handleError(const RTSPError& error, StreamId stream_id);
    void setRecoveryStrategy(ErrorCategory category, RecoveryStrategy strategy);
    
private:
    std::unordered_map<ErrorCategory, RecoveryStrategy> strategies_;
    std::unique_ptr<RetryManager> retry_manager_;
    std::unique_ptr<CircuitBreaker> circuit_breaker_;
};

enum class RecoveryStrategy {
    RETRY_IMMEDIATE,
    RETRY_WITH_BACKOFF,
    RESET_CONNECTION,
    MARK_FAILED,
    NOTIFY_APPLICATION
};
```

## Integration Architecture

### 1. API Layer Integration

```cpp
// Server-side API integration
class RTSPController {
public:
    // REST API endpoints
    httplib::Response addStream(const httplib::Request& req);
    httplib::Response getStreams(const httplib::Request& req);
    httplib::Response getStreamStats(const httplib::Request& req);
    httplib::Response controlStream(const httplib::Request& req);
    
private:
    std::shared_ptr<StreamMultiplexer> multiplexer_;
    std::shared_ptr<ConfigurationManager> config_manager_;
};

// Client-side integration
class RTSPService {
public:
    // Qt signals for real-time updates
    Q_SIGNAL void streamAdded(const StreamInfo& info);
    Q_SIGNAL void streamRemoved(StreamId id);
    Q_SIGNAL void statisticsUpdated(const StreamStatistics& stats);
    Q_SIGNAL void errorOccurred(const RTSPError& error);
    
private:
    std::unique_ptr<HTTPClient> http_client_;
    std::unique_ptr<WebSocketClient> ws_client_;
};
```

This technical architecture provides a solid foundation for implementing a high-performance, scalable RTSP Input Module that integrates seamlessly with the existing project structure while maintaining clean separation of concerns and robust error handling.
