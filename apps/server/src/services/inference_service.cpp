#include "services/inference_service.hpp"
#include "shared/config.hpp"
#include <iostream>
#include <fstream>
#include <algorithm>
#include <future>
#include <cctype>

namespace server::services {

InferenceService::InferenceService(std::shared_ptr<repositories::IAIModelRepository> repository)
    : repository_(repository) {
    std::cout << "[InferenceService] Initialized" << std::endl;
}

models::ArcFaceResponse InferenceService::processArcFaceInference(const models::ArcFaceRequest& request) {
    logInferenceRequest("ArcFace", request.image_path);

    // Validate request
    if (!validateRequest(request)) {
        return createErrorArcFaceResponse("Invalid request parameters", shared::AppConstants::ERROR_INVALID_REQUEST);
    }

    // Preprocess image path
    std::string processed_path = preprocessImagePath(request.image_path);

    // Validate image file
    if (!isImageFileValid(processed_path)) {
        return createErrorArcFaceResponse("Image file not found or invalid: " + processed_path,
                                        shared::AppConstants::ERROR_FILE_NOT_FOUND);
    }

    // Check image format
    if (!isImageFormatSupported(processed_path)) {
        return createErrorArcFaceResponse("Unsupported image format", shared::AppConstants::ERROR_INVALID_REQUEST);
    }

    try {
        // Create modified request with processed path
        models::ArcFaceRequest processed_request = request;
        processed_request.image_path = processed_path;

        // Perform inference
        auto start_time = std::chrono::high_resolution_clock::now();
        auto response = repository_->performArcFaceInference(processed_request);
        auto end_time = std::chrono::high_resolution_clock::now();

        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        logInferenceResult("ArcFace", response.success, duration);

        return response;

    } catch (const std::exception& e) {
        logInferenceResult("ArcFace", false, std::chrono::milliseconds(0));
        return createErrorArcFaceResponse("Inference failed: " + std::string(e.what()),
                                        shared::AppConstants::ERROR_INFERENCE_FAILED);
    }
}

models::YoloResponse InferenceService::processYoloInference(const models::YoloRequest& request) {
    logInferenceRequest("YOLO", request.image_path);

    // Validate request
    if (!validateRequest(request)) {
        return createErrorYoloResponse("Invalid request parameters", shared::AppConstants::ERROR_INVALID_REQUEST);
    }

    // Preprocess image path
    std::string processed_path = preprocessImagePath(request.image_path);

    // Validate image file
    if (!isImageFileValid(processed_path)) {
        return createErrorYoloResponse("Image file not found or invalid: " + processed_path,
                                     shared::AppConstants::ERROR_FILE_NOT_FOUND);
    }

    // Check image format
    if (!isImageFormatSupported(processed_path)) {
        return createErrorYoloResponse("Unsupported image format", shared::AppConstants::ERROR_INVALID_REQUEST);
    }

    try {
        // Create modified request with processed path
        models::YoloRequest processed_request = request;
        processed_request.image_path = processed_path;

        // Perform inference
        auto start_time = std::chrono::high_resolution_clock::now();
        auto response = repository_->performYoloInference(processed_request);
        auto end_time = std::chrono::high_resolution_clock::now();

        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        logInferenceResult("YOLO", response.success, duration);

        return response;

    } catch (const std::exception& e) {
        logInferenceResult("YOLO", false, std::chrono::milliseconds(0));
        return createErrorYoloResponse("Inference failed: " + std::string(e.what()),
                                     shared::AppConstants::ERROR_INFERENCE_FAILED);
    }
}

std::future<models::ArcFaceResponse> InferenceService::processArcFaceInferenceAsync(const models::ArcFaceRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        return processArcFaceInference(request);
    });
}

std::future<models::YoloResponse> InferenceService::processYoloInferenceAsync(const models::YoloRequest& request) {
    return std::async(std::launch::async, [this, request]() {
        return processYoloInference(request);
    });
}

bool InferenceService::validateRequest(const models::InferenceRequest& request) {
    // Basic validation
    if (request.image_path.empty()) {
        return false;
    }

    // Check timeout if specified
    if (request.timeout_ms.has_value() && request.timeout_ms.value() <= 0) {
        return false;
    }

    return request.isValid();
}

std::string InferenceService::preprocessImagePath(const std::string& image_path) {
    // Remove any leading/trailing whitespace
    std::string processed = image_path;
    processed.erase(processed.begin(), std::find_if(processed.begin(), processed.end(), [](unsigned char ch) {
        return !std::isspace(ch);
    }));
    processed.erase(std::find_if(processed.rbegin(), processed.rend(), [](unsigned char ch) {
        return !std::isspace(ch);
    }).base(), processed.end());

    // Convert relative paths to absolute if needed
    // For now, just return the processed path
    return processed;
}

// Helper methods
bool InferenceService::isImageFileValid(const std::string& image_path) const {
    return repository_->validateImageFile(image_path);
}

bool InferenceService::isImageFormatSupported(const std::string& image_path) const {
    // Check file extension
    std::string extension;
    size_t dot_pos = image_path.find_last_of('.');
    if (dot_pos != std::string::npos) {
        extension = image_path.substr(dot_pos + 1);
        std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);
    }

    // Supported formats
    return extension == "jpg" || extension == "jpeg" || extension == "png" ||
           extension == "bmp" || extension == "tiff" || extension == "webp";
}

models::ArcFaceResponse InferenceService::createErrorArcFaceResponse(const std::string& error, const std::string& error_code) {
    models::ArcFaceResponse response;
    response.success = false;
    response.message = error;
    response.error_code = error_code;
    response.processing_time = std::chrono::milliseconds(0);
    response.total_faces_detected = 0;
    return response;
}

models::YoloResponse InferenceService::createErrorYoloResponse(const std::string& error, const std::string& error_code) {
    models::YoloResponse response;
    response.success = false;
    response.message = error;
    response.error_code = error_code;
    response.processing_time = std::chrono::milliseconds(0);
    response.total_objects_detected = 0;
    return response;
}

void InferenceService::logInferenceRequest(const std::string& model_name, const std::string& image_path) const {
    std::cout << "[InferenceService] " << model_name << " inference request for: " << image_path << std::endl;
}

void InferenceService::logInferenceResult(const std::string& model_name, bool success, std::chrono::milliseconds duration) const {
    std::cout << "[InferenceService] " << model_name << " inference "
              << (success ? "completed" : "failed")
              << " in " << duration.count() << "ms" << std::endl;
}

} // namespace server::services
