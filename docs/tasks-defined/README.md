# C-AIBox Face Detection and Recognition System: Task Definitions

This directory contains detailed task definitions for implementing a high-performance facial detection and recognition system on the RK3588 NPU with RTSP camera inputs using C++.

## System Overview

The system is designed as a pipeline with several specialized modules, each responsible for a specific part of the processing chain. The modules are designed to work together efficiently while taking full advantage of the RK3588's heterogeneous computing architecture, including:

- VPU (Video Processing Unit) for hardware-accelerated video decoding
- RGA (2D Raster Graphics Accelerator) for image pre-processing
- NPU (Neural Processing Unit) for AI inference
- CPU for application logic and coordination

## Pipeline Architecture

The system follows a modular pipeline architecture with the following data flow:

```
RTSP Cameras → RTSP Input Module → MPP Decoding Module → Pre-Processing Module → 
Face Detection Module → Face Extraction Module → Face Recognition Module → Application Logic
```

Each module is designed to maximize hardware acceleration and implement zero-copy operations where possible using DMABUF.

## Task Structure

The implementation is divided into 7 primary tasks, each with its own dedicated directory:

1. **RTSP Input Module**: Handles connection to multiple RTSP camera streams
2. **MPP Decoding Module**: Decodes H.264/H.265 video using hardware acceleration
3. **Pre-Processing Module**: Prepares video frames for neural network input using RGA
4. **Face Detection Module**: Runs face detection models on the NPU
5. **Face Extraction Module**: Extracts and pre-processes face regions for recognition
6. **Face Recognition Module**: Generates facial embeddings using NPU
7. **Application Logic Module**: Handles face matching, tracking, and business logic

Each task directory contains a detailed task definition document that outlines:
- Overview and requirements
- Technical approach
- Interfaces (inputs and outputs)
- Dependencies
- Performance considerations

## Implementation Strategy

For optimal performance, the implementation should focus on:

1. **Zero-Copy Data Flow**: Using DMABUF throughout the pipeline to minimize memory bandwidth usage
2. **Multi-Core NPU Utilization**: Properly distributing workloads across all three NPU cores
3. **Batch Processing**: Processing multiple inputs simultaneously when possible
4. **Asynchronous Operations**: Using non-blocking operations to maximize throughput
5. **Thread Management**: Implementing efficient thread pools and work queues

## Model Selection

The system uses optimized, quantized models for optimal performance:
- **Face Detection**: RetinaFace (mobile variant, INT8 quantized)
- **Face Recognition**: ArcFace/FaceNet (INT8 quantized)

## Resource Management

Special attention should be paid to:
- NPU's 4GB addressable memory limitation
- Efficient core allocation between detection and recognition tasks
- Balance between accuracy and performance through proper model quantization
- Monitoring and adaptive throttling of the pipeline under heavy loads

## Next Steps

After reviewing these task definitions, implementation should proceed in the order of the pipeline, with initial focus on establishing the basic framework and data flow, followed by optimizations for performance and accuracy.