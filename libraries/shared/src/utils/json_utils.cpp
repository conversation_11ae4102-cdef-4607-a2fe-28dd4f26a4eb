#include "utils/json_utils.hpp"
#include <fstream>
#include <sstream>
#include <stdexcept>
#include <algorithm>
#include <cctype>

namespace utils {

    // JsonValue implementation
    JsonValue::JsonValue() : value_(nullptr) {}

    JsonValue::JsonValue(bool value) : value_(value) {}

    JsonValue::JsonValue(int value) : value_(static_cast<double>(value)) {}
    JsonValue::JsonValue(long value) : value_(static_cast<double>(value)) {}
    JsonValue::JsonValue(long long value) : value_(static_cast<double>(value)) {}
    JsonValue::JsonValue(float value) : value_(static_cast<double>(value)) {}
    JsonValue::JsonValue(double value) : value_(value) {}

    JsonValue::JsonValue(const std::string& value) : value_(value) {}
    JsonValue::JsonValue(const char* value) : value_(std::string(value)) {}

    JsonValue::JsonValue(const JsonArray& value) : value_(value) {}
    JsonValue::JsonValue(JsonArray&& value) : value_(std::move(value)) {}

    JsonValue::JsonValue(const JsonObject& value) : value_(value) {}
    JsonValue::JsonValue(JsonObject&& value) : value_(std::move(value)) {}

    JsonType JsonValue::getType() const {
        return static_cast<JsonType>(value_.index());
    }

    bool JsonValue::isNull() const { return getType() == JsonType::Null; }
    bool JsonValue::isBool() const { return getType() == JsonType::Boolean; }
    bool JsonValue::isNumber() const { return getType() == JsonType::Number; }
    bool JsonValue::isString() const { return getType() == JsonType::String; }
    bool JsonValue::isArray() const { return getType() == JsonType::Array; }
    bool JsonValue::isObject() const { return getType() == JsonType::Object; }

    bool JsonValue::asBool() const {
        if (!isBool()) {
            throw std::runtime_error("JsonValue is not a boolean");
        }
        return std::get<bool>(value_);
    }

    double JsonValue::asNumber() const {
        if (!isNumber()) {
            throw std::runtime_error("JsonValue is not a number");
        }
        return std::get<double>(value_);
    }

    int JsonValue::asInt() const {
        return static_cast<int>(asNumber());
    }

    const std::string& JsonValue::asString() const {
        if (!isString()) {
            throw std::runtime_error("JsonValue is not a string");
        }
        return std::get<std::string>(value_);
    }

    const JsonArray& JsonValue::asArray() const {
        if (!isArray()) {
            throw std::runtime_error("JsonValue is not an array");
        }
        return std::get<JsonArray>(value_);
    }

    JsonArray& JsonValue::asArray() {
        if (!isArray()) {
            throw std::runtime_error("JsonValue is not an array");
        }
        return std::get<JsonArray>(value_);
    }

    const JsonObject& JsonValue::asObject() const {
        if (!isObject()) {
            throw std::runtime_error("JsonValue is not an object");
        }
        return std::get<JsonObject>(value_);
    }

    JsonObject& JsonValue::asObject() {
        if (!isObject()) {
            throw std::runtime_error("JsonValue is not an object");
        }
        return std::get<JsonObject>(value_);
    }

    JsonValue& JsonValue::operator[](size_t index) {
        if (!isArray()) {
            throw std::runtime_error("JsonValue is not an array");
        }
        auto& arr = std::get<JsonArray>(value_);
        if (index >= arr.size()) {
            throw std::out_of_range("Array index out of range");
        }
        return arr[index];
    }

    const JsonValue& JsonValue::operator[](size_t index) const {
        if (!isArray()) {
            throw std::runtime_error("JsonValue is not an array");
        }
        const auto& arr = std::get<JsonArray>(value_);
        if (index >= arr.size()) {
            throw std::out_of_range("Array index out of range");
        }
        return arr[index];
    }

    JsonValue& JsonValue::operator[](const std::string& key) {
        if (!isObject()) {
            // Convert to object if null
            if (isNull()) {
                value_ = JsonObject{};
            } else {
                throw std::runtime_error("JsonValue is not an object");
            }
        }
        return std::get<JsonObject>(value_)[key];
    }

    const JsonValue& JsonValue::operator[](const std::string& key) const {
        if (!isObject()) {
            throw std::runtime_error("JsonValue is not an object");
        }
        const auto& obj = std::get<JsonObject>(value_);
        auto it = obj.find(key);
        if (it == obj.end()) {
            throw std::out_of_range("Object key not found: " + key);
        }
        return it->second;
    }

    size_t JsonValue::size() const {
        if (isArray()) {
            return std::get<JsonArray>(value_).size();
        } else if (isObject()) {
            return std::get<JsonObject>(value_).size();
        }
        return 0;
    }

    bool JsonValue::hasKey(const std::string& key) const {
        if (!isObject()) {
            return false;
        }
        const auto& obj = std::get<JsonObject>(value_);
        return obj.find(key) != obj.end();
    }

    std::vector<std::string> JsonValue::getKeys() const {
        std::vector<std::string> keys;
        if (isObject()) {
            const auto& obj = std::get<JsonObject>(value_);
            for (const auto& pair : obj) {
                keys.push_back(pair.first);
            }
        }
        return keys;
    }

    std::string JsonValue::toString(bool pretty, int indent) const {
        std::ostringstream oss;

        switch (getType()) {
            case JsonType::Null:
                oss << "null";
                break;
            case JsonType::Boolean:
                oss << (asBool() ? "true" : "false");
                break;
            case JsonType::Number: {
                double num = asNumber();
                if (num == static_cast<long long>(num)) {
                    oss << static_cast<long long>(num);
                } else {
                    oss << num;
                }
                break;
            }
            case JsonType::String:
                oss << "\"" << escapeString(asString()) << "\"";
                break;
            case JsonType::Array: {
                const auto& arr = asArray();
                oss << "[";
                if (pretty && !arr.empty()) {
                    oss << "\n";
                }
                for (size_t i = 0; i < arr.size(); ++i) {
                    if (pretty) {
                        oss << getIndent(indent + 1);
                    }
                    oss << arr[i].toString(pretty, indent + 1);
                    if (i < arr.size() - 1) {
                        oss << ",";
                    }
                    if (pretty) {
                        oss << "\n";
                    }
                }
                if (pretty && !arr.empty()) {
                    oss << getIndent(indent);
                }
                oss << "]";
                break;
            }
            case JsonType::Object: {
                const auto& obj = asObject();
                oss << "{";
                if (pretty && !obj.empty()) {
                    oss << "\n";
                }
                auto it = obj.begin();
                for (; it != obj.end(); ++it) {
                    if (pretty) {
                        oss << getIndent(indent + 1);
                    }
                    oss << "\"" << escapeString(it->first) << "\":";
                    if (pretty) {
                        oss << " ";
                    }
                    oss << it->second.toString(pretty, indent + 1);
                    auto next = it;
                    ++next;
                    if (next != obj.end()) {
                        oss << ",";
                    }
                    if (pretty) {
                        oss << "\n";
                    }
                }
                if (pretty && !obj.empty()) {
                    oss << getIndent(indent);
                }
                oss << "}";
                break;
            }
        }

        return oss.str();
    }

    std::string JsonValue::escapeString(const std::string& str) const {
        std::string result;
        for (char c : str) {
            switch (c) {
                case '"': result += "\\\""; break;
                case '\\': result += "\\\\"; break;
                case '\b': result += "\\b"; break;
                case '\f': result += "\\f"; break;
                case '\n': result += "\\n"; break;
                case '\r': result += "\\r"; break;
                case '\t': result += "\\t"; break;
                default:
                    if (c < 0x20) {
                        result += "\\u";
                        result += "0000";
                        // Simple hex conversion for control characters
                        char hex[3];
                        snprintf(hex, sizeof(hex), "%02x", static_cast<unsigned char>(c));
                        result[result.length() - 2] = hex[0];
                        result[result.length() - 1] = hex[1];
                    } else {
                        result += c;
                    }
                    break;
            }
        }
        return result;
    }

    std::string JsonValue::getIndent(int level) const {
        return std::string(static_cast<size_t>(level * 2), ' ');
    }

    // JsonUtils implementation
    std::optional<JsonValue> JsonUtils::parse(const std::string& jsonStr) {
        size_t pos = 0;
        skipWhitespace(jsonStr, pos);
        if (pos >= jsonStr.length()) {
            return std::nullopt;
        }

        auto result = parseValue(jsonStr, pos);
        if (!result.has_value()) {
            return std::nullopt;
        }

        skipWhitespace(jsonStr, pos);
        if (pos < jsonStr.length()) {
            // Extra characters after valid JSON
            return std::nullopt;
        }

        return result;
    }

    std::optional<JsonValue> JsonUtils::parseFromFile(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            return std::nullopt;
        }

        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();

        return parse(content);
    }

    std::string JsonUtils::stringify(const JsonValue& value, bool pretty) {
        return value.toString(pretty);
    }

    bool JsonUtils::writeToFile(const JsonValue& value, const std::string& filename, bool pretty) {
        std::ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }

        file << stringify(value, pretty);
        file.close();
        return true;
    }

    bool JsonUtils::isValid(const std::string& jsonStr) {
        return parse(jsonStr).has_value();
    }

    JsonValue JsonUtils::merge(const JsonValue& base, const JsonValue& overlay) {
        if (!base.isObject() || !overlay.isObject()) {
            return overlay;
        }

        JsonObject result = base.asObject();
        const auto& overlayObj = overlay.asObject();

        for (const auto& pair : overlayObj) {
            if (result.find(pair.first) != result.end() &&
                result[pair.first].isObject() && pair.second.isObject()) {
                result[pair.first] = merge(result[pair.first], pair.second);
            } else {
                result[pair.first] = pair.second;
            }
        }

        return JsonValue(result);
    }

    std::optional<JsonValue> JsonUtils::parseValue(const std::string& str, size_t& pos) {
        skipWhitespace(str, pos);
        if (pos >= str.length()) {
            return std::nullopt;
        }

        char c = str[pos];
        switch (c) {
            case 'n':
                if (str.substr(pos, 4) == "null") {
                    pos += 4;
                    return JsonValue();
                }
                return std::nullopt;
            case 't':
                if (str.substr(pos, 4) == "true") {
                    pos += 4;
                    return JsonValue(true);
                }
                return std::nullopt;
            case 'f':
                if (str.substr(pos, 5) == "false") {
                    pos += 5;
                    return JsonValue(false);
                }
                return std::nullopt;
            case '"': {
                auto str_result = parseString(str, pos);
                return str_result.has_value() ? std::optional<JsonValue>(JsonValue(str_result.value())) : std::nullopt;
            }
            case '[': {
                auto arr_result = parseArray(str, pos);
                return arr_result.has_value() ? std::optional<JsonValue>(JsonValue(arr_result.value())) : std::nullopt;
            }
            case '{': {
                auto obj_result = parseObject(str, pos);
                return obj_result.has_value() ? std::optional<JsonValue>(JsonValue(obj_result.value())) : std::nullopt;
            }
            default:
                if (c == '-' || std::isdigit(c)) {
                    auto num_result = parseNumber(str, pos);
                    return num_result.has_value() ? std::optional<JsonValue>(JsonValue(num_result.value())) : std::nullopt;
                }
                return std::nullopt;
        }
    }

    std::optional<JsonObject> JsonUtils::parseObject(const std::string& str, size_t& pos) {
        if (pos >= str.length() || str[pos] != '{') {
            return std::nullopt;
        }
        pos++; // Skip '{'

        JsonObject obj;
        skipWhitespace(str, pos);

        if (pos < str.length() && str[pos] == '}') {
            pos++; // Skip '}'
            return obj;
        }

        while (pos < str.length()) {
            skipWhitespace(str, pos);

            // Parse key
            auto key = parseString(str, pos);
            if (!key.has_value()) {
                return std::nullopt;
            }

            skipWhitespace(str, pos);
            if (pos >= str.length() || str[pos] != ':') {
                return std::nullopt;
            }
            pos++; // Skip ':'

            // Parse value
            auto value = parseValue(str, pos);
            if (!value.has_value()) {
                return std::nullopt;
            }

            obj[key.value()] = value.value();

            skipWhitespace(str, pos);
            if (pos >= str.length()) {
                return std::nullopt;
            }

            if (str[pos] == '}') {
                pos++; // Skip '}'
                return obj;
            } else if (str[pos] == ',') {
                pos++; // Skip ','
            } else {
                return std::nullopt;
            }
        }

        return std::nullopt;
    }

    std::optional<JsonArray> JsonUtils::parseArray(const std::string& str, size_t& pos) {
        if (pos >= str.length() || str[pos] != '[') {
            return std::nullopt;
        }
        pos++; // Skip '['

        JsonArray arr;
        skipWhitespace(str, pos);

        if (pos < str.length() && str[pos] == ']') {
            pos++; // Skip ']'
            return arr;
        }

        while (pos < str.length()) {
            auto value = parseValue(str, pos);
            if (!value.has_value()) {
                return std::nullopt;
            }

            arr.push_back(value.value());

            skipWhitespace(str, pos);
            if (pos >= str.length()) {
                return std::nullopt;
            }

            if (str[pos] == ']') {
                pos++; // Skip ']'
                return arr;
            } else if (str[pos] == ',') {
                pos++; // Skip ','
                skipWhitespace(str, pos);
            } else {
                return std::nullopt;
            }
        }

        return std::nullopt;
    }

    std::optional<std::string> JsonUtils::parseString(const std::string& str, size_t& pos) {
        if (pos >= str.length() || str[pos] != '"') {
            return std::nullopt;
        }
        pos++; // Skip opening '"'

        std::string result;
        while (pos < str.length()) {
            char c = str[pos];
            if (c == '"') {
                pos++; // Skip closing '"'
                return result;
            } else if (c == '\\') {
                pos++;
                if (pos >= str.length()) {
                    return std::nullopt;
                }
                char escaped = str[pos];
                switch (escaped) {
                    case '"': result += '"'; break;
                    case '\\': result += '\\'; break;
                    case '/': result += '/'; break;
                    case 'b': result += '\b'; break;
                    case 'f': result += '\f'; break;
                    case 'n': result += '\n'; break;
                    case 'r': result += '\r'; break;
                    case 't': result += '\t'; break;
                    case 'u':
                        // Unicode escape (simplified)
                        pos += 4; // Skip the 4 hex digits
                        result += '?'; // Placeholder
                        break;
                    default:
                        return std::nullopt;
                }
                pos++;
            } else {
                result += c;
                pos++;
            }
        }

        return std::nullopt;
    }

    std::optional<double> JsonUtils::parseNumber(const std::string& str, size_t& pos) {
        size_t start = pos;

        // Optional minus
        if (pos < str.length() && str[pos] == '-') {
            pos++;
        }

        // Integer part
        if (pos >= str.length() || !std::isdigit(str[pos])) {
            return std::nullopt;
        }

        if (str[pos] == '0') {
            pos++;
        } else {
            while (pos < str.length() && std::isdigit(str[pos])) {
                pos++;
            }
        }

        // Fractional part
        if (pos < str.length() && str[pos] == '.') {
            pos++;
            if (pos >= str.length() || !std::isdigit(str[pos])) {
                return std::nullopt;
            }
            while (pos < str.length() && std::isdigit(str[pos])) {
                pos++;
            }
        }

        // Exponent part
        if (pos < str.length() && (str[pos] == 'e' || str[pos] == 'E')) {
            pos++;
            if (pos < str.length() && (str[pos] == '+' || str[pos] == '-')) {
                pos++;
            }
            if (pos >= str.length() || !std::isdigit(str[pos])) {
                return std::nullopt;
            }
            while (pos < str.length() && std::isdigit(str[pos])) {
                pos++;
            }
        }

        try {
            return std::stod(str.substr(start, pos - start));
        } catch (const std::exception&) {
            return std::nullopt;
        }
    }

    void JsonUtils::skipWhitespace(const std::string& str, size_t& pos) {
        while (pos < str.length() && std::isspace(str[pos])) {
            pos++;
        }
    }

    // Convenience functions implementation
    std::optional<JsonValue> parseJson(const std::string& jsonStr) {
        return JsonUtils::parse(jsonStr);
    }

    std::string toJson(const JsonValue& value, bool pretty) {
        return JsonUtils::stringify(value, pretty);
    }

    JsonValue createObject() {
        return JsonValue(JsonObject{});
    }

    JsonValue createArray() {
        return JsonValue(JsonArray{});
    }
}
