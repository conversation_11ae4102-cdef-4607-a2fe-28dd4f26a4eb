#include "rtsp/stream_multiplexer.hpp"
#include "rtsp/rtsp_config.hpp"
#include <iostream>
#include <chrono>
#include <thread>
#include <signal.h>

using namespace aibox::rtsp;

std::atomic<bool> should_exit{false};

void signal_handler(int signal) {
    std::cout << "\nReceived signal " << signal << ", shutting down gracefully..." << std::endl;
    should_exit = true;
}

int main() {
    // Set up signal handlers for graceful shutdown
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    std::cout << "=== Stream Multiplexer Demo for Orange Pi 5 Plus/Ultra ===" << std::endl;
    std::cout << "This demo shows how to manage multiple RTSP streams with hardware acceleration" << std::endl;
    std::cout << "Press Ctrl+C to stop gracefully" << std::endl << std::endl;
    
    try {
        // Configure Stream Multiplexer for Orange Pi 5 Plus (4GB configuration)
        StreamManagementConfig config;
        config.max_concurrent_streams = 6;           // Conservative for 4GB RAM
        config.max_memory_usage_mb = 1200;           // Leave room for system
        config.cpu_usage_limit_percent = 80.0f;     // Don't overwhelm the system
        config.worker_thread_count = 4;             // Good for RK3588
        config.enable_load_balancing = true;        // Enable intelligent load balancing
        config.enable_thermal_management = true;    // Important for sustained operation
        config.enable_adaptive_quality = true;      // Adapt to system conditions
        
        // Queue configuration optimized for RK3588
        config.queue_config.high_priority_size = 200;
        config.queue_config.medium_priority_size = 150;
        config.queue_config.low_priority_size = 100;
        config.queue_config.enable_priority_scheduling = true;
        config.queue_config.enable_adaptive_sizing = true;
        
        // Error handling configuration
        config.max_connection_retries = 3;
        config.retry_delay_ms = 2000;
        config.auto_remove_failed_streams = false;  // Keep failed streams for manual retry
        
        // Monitoring configuration
        config.statistics_update_interval_ms = 1000;
        config.enable_performance_monitoring = true;
        
        std::cout << "Creating Stream Multiplexer with configuration:" << std::endl;
        std::cout << "  Max streams: " << config.max_concurrent_streams << std::endl;
        std::cout << "  Memory limit: " << config.max_memory_usage_mb << "MB" << std::endl;
        std::cout << "  Worker threads: " << config.worker_thread_count << std::endl;
        std::cout << "  Hardware acceleration: Enabled (RK3588)" << std::endl << std::endl;
        
        // Create Stream Multiplexer
        StreamMultiplexer multiplexer(config);
        
        // Set up callbacks for monitoring
        multiplexer.setNALUnitCallback([](const StreamId& stream_id, const NALUnit& nal_unit) {
            static std::atomic<uint64_t> nal_count{0};
            if (++nal_count % 100 == 0) {  // Log every 100th NAL unit
                std::cout << "[NAL] Stream " << stream_id << ": Processed " << nal_count 
                          << " NAL units (type: " << static_cast<int>(nal_unit.type) 
                          << ", size: " << nal_unit.data.size() << " bytes)" << std::endl;
            }
        });
        
        multiplexer.setStreamEventCallback([](const StreamId& stream_id, const std::string& event) {
            std::cout << "[EVENT] Stream " << stream_id << ": " << event << std::endl;
        });
        
        multiplexer.setErrorCallback([](const StreamId& stream_id, ErrorCategory category, const std::string& message) {
            std::cout << "[ERROR] Stream " << stream_id << " (category " << static_cast<int>(category) 
                      << "): " << message << std::endl;
        });
        
        // Start the multiplexer
        std::cout << "Starting Stream Multiplexer..." << std::endl;
        if (!multiplexer.start()) {
            std::cerr << "Failed to start Stream Multiplexer!" << std::endl;
            return 1;
        }
        std::cout << "✓ Stream Multiplexer started successfully" << std::endl << std::endl;
        
        // Configure RTSP streams
        std::vector<std::pair<std::string, RTSPConnectionConfig>> streams = {
            // High priority camera (security/critical monitoring)
            {"security_cam_01", {
                .stream_id = "security_cam_01",
                .rtsp_url = "rtsp://admin:CMC2024%21@***************:554/streaming/channels/01",
                .username = "admin",
                .password = "CMC2024!",
                .priority = StreamPriority::HIGH,
                .enabled = true,
                .timeout_ms = 5000,
                .retry_count = 5,
                .use_tcp = true
            }},
            
            // Medium priority cameras (general monitoring)
            {"monitor_cam_02", {
                .stream_id = "monitor_cam_02",
                .rtsp_url = "rtsp://admin:password@*************:554/stream1",
                .username = "admin",
                .password = "password",
                .priority = StreamPriority::MEDIUM,
                .enabled = true,
                .timeout_ms = 5000,
                .retry_count = 3,
                .use_tcp = true
            }},
            
            {"monitor_cam_03", {
                .stream_id = "monitor_cam_03",
                .rtsp_url = "rtsp://admin:password@192.168.1.103:554/stream1",
                .username = "admin",
                .password = "password",
                .priority = StreamPriority::MEDIUM,
                .enabled = true,
                .timeout_ms = 5000,
                .retry_count = 3,
                .use_tcp = true
            }},
            
            // Low priority camera (backup/archive)
            {"archive_cam_04", {
                .stream_id = "archive_cam_04",
                .rtsp_url = "rtsp://admin:password@*************:554/stream1",
                .username = "admin",
                .password = "password",
                .priority = StreamPriority::LOW,
                .enabled = true,
                .timeout_ms = 3000,
                .retry_count = 2,
                .use_tcp = false  // Use UDP for lower priority
            }}
        };
        
        // Add streams to multiplexer
        std::cout << "Adding RTSP streams..." << std::endl;
        for (const auto& [stream_id, stream_config] : streams) {
            if (multiplexer.addStream(stream_id, stream_config)) {
                std::cout << "✓ Added stream: " << stream_id 
                          << " (priority: " << static_cast<int>(stream_config.priority) << ")" << std::endl;
            } else {
                std::cout << "✗ Failed to add stream: " << stream_id << std::endl;
            }
        }
        std::cout << std::endl;
        
        // Connect all streams
        std::cout << "Connecting to RTSP streams..." << std::endl;
        multiplexer.connectAllStreams();
        
        // Wait a moment for connections to establish
        std::this_thread::sleep_for(std::chrono::seconds(3));
        
        // Main monitoring loop
        std::cout << "Monitoring streams (press Ctrl+C to stop)..." << std::endl;
        std::cout << "Time\t\tStreams\tMemory\tCPU\tHealth\tPackets" << std::endl;
        std::cout << "----\t\t-------\t------\t---\t------\t-------" << std::endl;
        
        auto start_time = std::chrono::steady_clock::now();
        auto last_stats_time = start_time;
        
        while (!should_exit) {
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time);
            
            // Print statistics every 5 seconds
            auto stats_elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_stats_time);
            if (stats_elapsed.count() >= 5) {
                auto stats = multiplexer.getStatistics();
                auto health = multiplexer.getSystemHealth();
                
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);
                auto tm = *std::localtime(&time_t);
                
                printf("%02d:%02d:%02d\t%u/%u\t%zuMB\t%u%%\t%s\t%lu\n",
                       tm.tm_hour, tm.tm_min, tm.tm_sec,
                       stats.connected_streams, stats.total_streams,
                       health.memory_usage_mb,
                       health.cpu_usage_percent,
                       health.status.c_str(),
                       stats.total_packets_processed);
                
                // Handle thermal management
                if (health.temperature_celsius > 75) {
                    std::cout << "[THERMAL] High temperature detected: " 
                              << health.temperature_celsius << "°C" << std::endl;
                    multiplexer.handleThermalThrottling(health.temperature_celsius);
                }
                
                // Handle memory pressure
                if (health.memory_usage_mb > config.max_memory_usage_mb * 0.9) {
                    std::cout << "[MEMORY] High memory usage detected: " 
                              << health.memory_usage_mb << "MB" << std::endl;
                    multiplexer.handleMemoryPressure();
                }
                
                last_stats_time = current_time;
            }
            
            // Check for failed streams and attempt reconnection
            auto stream_ids = multiplexer.getStreamIds();
            for (const auto& stream_id : stream_ids) {
                auto state = multiplexer.getStreamState(stream_id);
                if (state == ConnectionState::ERROR) {
                    std::cout << "[RECONNECT] Attempting to reconnect failed stream: " 
                              << stream_id << std::endl;
                    multiplexer.reconnectStream(stream_id);
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        
        // Graceful shutdown
        std::cout << std::endl << "Shutting down gracefully..." << std::endl;
        
        // Disconnect all streams
        std::cout << "Disconnecting streams..." << std::endl;
        multiplexer.disconnectAllStreams();
        
        // Stop the multiplexer
        std::cout << "Stopping Stream Multiplexer..." << std::endl;
        multiplexer.stop();
        
        // Print final statistics
        auto final_stats = multiplexer.getStatistics();
        std::cout << std::endl << "=== Final Statistics ===" << std::endl;
        std::cout << "Total streams: " << final_stats.total_streams << std::endl;
        std::cout << "Packets processed: " << final_stats.total_packets_processed << std::endl;
        std::cout << "Bytes processed: " << final_stats.total_bytes_processed << std::endl;
        std::cout << "Queue overruns: " << final_stats.queue_overruns << std::endl;
        std::cout << "Dropped packets: " << final_stats.dropped_packets << std::endl;
        
        if (final_stats.total_packets_processed > 0) {
            double drop_rate = final_stats.getPacketDropRate();
            std::cout << "Packet drop rate: " << (drop_rate * 100.0) << "%" << std::endl;
        }
        
        std::cout << std::endl << "Demo completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}

/*
 * Compilation instructions:
 * 
 * cd /workspaces/c-aibox-2
 * mkdir -p build && cd build
 * cmake .. -DBUILD_TESTING=ON
 * make
 * 
 * # Run the demo
 * ./examples/stream_multiplexer_demo
 * 
 * # Or compile standalone:
 * g++ -std=c++17 -I../libraries/rtsp/include -I../libraries/shared/include \
 *     -pthread -O2 -o stream_multiplexer_demo \
 *     examples/stream_multiplexer_demo.cpp \
 *     ../libraries/rtsp/src/stream_multiplexer.cpp \
 *     ../libraries/rtsp/src/connection_manager.cpp \
 *     ../libraries/rtsp/src/packet_receiver.cpp \
 *     ../libraries/rtsp/src/nal_parser.cpp \
 *     ../libraries/rtsp/src/rtsp_config.cpp
 */
