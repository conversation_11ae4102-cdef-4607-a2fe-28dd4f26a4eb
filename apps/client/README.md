# C-AIBOX Web Client Application

A simplified Qt5-based desktop application using QtWebEngine to display Google.

## Project Structure

```
apps/client/
├── CMakeLists.txt              # Build configuration
├── README.md                   # This file
├── src/                        # Source code
│   └── main.cpp               # Application entry point (simplified)
├── build/                      # Build directory
└── resources/                  # Application resources (optional)
```

## Architecture Overview

This is a minimal Qt5 application that creates a simple window with QtWebEngine to display Google.

### Key Features

- **Simple Web Browser**: Uses QtWebEngine to display web content
- **Minimal Dependencies**: Only requires Qt5 Core, Widgets, Gui, WebEngine, and WebEngineWidgets
- **Clean Interface**: Single window application with no complex UI components

## Dependencies

### Required Qt5 Modules
- Qt5::Core - Core functionality
- Qt5::Widgets - UI widgets
- Qt5::Gui - GUI components
- Qt5::WebEngine - Web engine core
- Qt5::WebEngineWidgets - Web engine widgets

## Building

```bash
# From apps/client directory
mkdir -p build && cd build
cmake ..
make

# Run the application
./client_app
```

## Usage

The application will open a window displaying Google's homepage. You can:

- Navigate to any website by typing in the address bar (if available)
- Use standard web browser controls
- Resize the window as needed

## Configuration

The application loads https://google.com by default. To change the URL, modify the `webView->load()` line in `src/main.cpp`.

## Troubleshooting

### Common Issues

- **Qt5WebEngine not found**: Install qtwebengine5-dev package
- **Build errors**: Ensure all Qt5 development packages are installed
- **Runtime errors**: Check that QtWebEngine libraries are properly installed
