# Best Practices for Face Recognition NPU Module Implementation

## Implementation Best Practices

### 1. Model Selection and Optimization

- **Recognition Model Selection**
  - Choose models with good accuracy-performance balance (MobileFaceNet backbone recommended)
  - Consider embedding size trade-offs (128 or 512 dimensions are common)
  - Evaluate different loss function variants (ArcFace, CosFace, SphereFace) for your use case

- **Quantization Strategy**
  - Use INT8 quantization for optimal NPU performance on RK3588
  - Ensure calibration dataset covers diverse faces and lighting conditions
  - Carefully validate recognition accuracy after quantization (at least 99% of FP32 performance)

- **Model Conversion Workflow**
  - Follow proper conversion pipeline: Original Framework → ONNX → RKNN
  - Specify RK3588 as target platform during conversion
  - Validate model structure after conversion with `rknn_query(RKNN_QUERY_IN_OUT_NUM)`

### 2. Efficient NPU Utilization

- **Core Allocation Strategy**
  - Allocate appropriate NPU cores based on workload balance with detection
  - Consider dedicated cores for recognition if accuracy is priority
  - For parallel processing, balance recognition instances across available cores

- **Context Management**
  - Create and maintain long-lived RKNN contexts for recognition models
  - Implement a context pool if multiple recognition models or configurations are needed
  - Consider context switching costs in your scheduling design

- **Memory Optimization**
  - Monitor recognition model memory footprint (weights + activations)
  - Stay within NPU's 4GB addressable memory limit
  - Consider model size when designing multi-model pipelines

### 3. Batch Processing Optimization

- **Batch Assembly**
  - Group multiple face crops into batches for efficient processing
  - Find optimal batch size through benchmarking (typically 8-16 faces)
  - Balance between batch size and latency requirements

- **Zero-Copy Integration**
  - Use DMABUF file descriptors directly from the extraction module
  - Implement proper tensor memory management for batched inputs
  - Minimize memory transfers between CPU and NPU

- **Asynchronous Execution**
  - Use `RKNN_FLAG_ASYNC_MODE` for non-blocking inference
  - Implement a callback or polling mechanism for completion
  - Pipeline batch preparation with inference execution

### 4. Embedding Processing and Storage

- **Vector Normalization**
  - Apply L2 normalization to raw embedding vectors if not done by the model
  - Consider fixed-point arithmetic for normalization on resource-constrained devices
  - Maintain consistent normalization across enrollment and matching

- **Efficient Storage**
  - Design compact embedding storage format for large face databases
  - Consider dimensionality reduction techniques for very large deployments
  - Implement efficient serialization/deserialization for embeddings

- **Metadata Management**
  - Associate embeddings with relevant metadata (face quality, timestamp, camera ID)
  - Implement versioning for embeddings if model updates are expected
  - Consider storing multiple embeddings per identity for robustness

### 5. Performance Optimization

- **Inference Pipeline Tuning**
  - Optimize tensor memory layout for recognition model
  - Balance resources between detection and recognition
  - Implement adaptive processing based on system load

- **Accuracy Enhancements**
  - Consider ensemble methods for critical applications
  - Implement face quality filtering before recognition
  - Use temporal consistency for video recognition

- **Throughput Maximization**
  - Process multiple batches in parallel if resources allow
  - Implement efficient work distribution across NPU cores
  - Consider priority queuing for important recognition tasks

## Implementation Pitfalls to Avoid

1. **Model Selection Issues**
   - Don't use overly complex models that exceed NPU capabilities
   - Avoid models with poor quantization characteristics
   - Don't ignore model architecture compatibility with NPU

2. **Recognition Accuracy Problems**
   - Don't skip proper pre-processing required by the model
   - Avoid using poor-quality face crops for recognition
   - Don't use inappropriate similarity thresholds for your application

3. **Performance Bottlenecks**
   - Avoid small batches that underutilize the NPU
   - Don't perform synchronous inference in the main processing thread
   - Prevent excessive context switching between models

4. **Memory Management Issues**
   - Don't leak NPU resources when processing many faces
   - Avoid unnecessary tensor memory allocations
   - Prevent embedding vector fragmentation

## Optimization Strategies

1. **Recognition Quality Tuning**
   - Fine-tune similarity thresholds based on your application's FAR/FRR requirements
   - Implement quality-aware recognition strategies
   - Consider adaptive thresholds based on face quality metrics

2. **Embedding Optimization**
   - Profile embedding computation performance with various batch sizes
   - Experiment with different embedding dimensions for your use case
   - Consider specialized similarity computation methods for your scenario

3. **Hardware Utilization**
   - Balance NPU core assignment between detection and recognition
   - Implement adaptive batching based on available resources
   - Monitor and optimize NPU utilization for sustained operation

4. **Latency Reduction**
   - Identify and eliminate bottlenecks in the recognition pipeline
   - Implement parallel processing where possible
   - Consider approximation techniques for non-critical paths

## Testing and Validation

1. **Accuracy Assessment**
   - Test recognition performance on standard face datasets (LFW, AgeDB, etc.)
   - Validate with your own application-specific face dataset
   - Measure impact of quantization on recognition accuracy

2. **Performance Benchmarking**
   - Measure recognition throughput (faces per second)
   - Profile NPU utilization during recognition
   - Evaluate batch processing efficiency

3. **Robustness Testing**
   - Test with challenging cases (different ages, ethnicities, lighting)
   - Validate performance with partially occluded faces
   - Test recognition consistency over time

## Hardware-Specific Considerations for RK3588

1. **NPU Architecture**
   - Understand RK3588's NPU core characteristics for recognition workloads
   - Optimize tensor operations for the NPU's processing patterns
   - Consider NPU clock frequency impact on recognition performance

2. **Memory Architecture**
   - Optimize embedding storage for RK3588's memory hierarchy
   - Consider IOMMU implications for recognition buffers
   - Use appropriate memory types for optimal performance

3. **Thermal Management**
   - Monitor NPU temperature during sustained recognition workloads
   - Implement thermal-aware scheduling for continuous operation
   - Balance recognition quality and performance under thermal constraints

## Implementation Examples

1. **Efficient Embedding Extraction**
   ```cpp
   // Example of efficient embedding extraction with batch processing
   std::vector<float*> extractFaceEmbeddings(
       const std::vector<rknn_tensor_mem*>& face_mems,
       rknn_context recognition_ctx,
       int embedding_size) {
       
       // Query I/O information
       rknn_input_output_num io_num;
       rknn_query(recognition_ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
       
       // Setup tensor attributes
       rknn_tensor_attr output_attrs[io_num.n_output];
       for (uint32_t i = 0; i < io_num.n_output; i++) {
           output_attrs[i].index = i;
           rknn_query(recognition_ctx, RKNN_QUERY_OUTPUT_ATTR, 
                    &output_attrs[i], sizeof(output_attrs[i]));
       }
       
       // Set input tensors (zero-copy)
       for (size_t i = 0; i < face_mems.size(); i++) {
           rknn_set_io_mem(recognition_ctx, i, face_mems[i]);
       }
       
       // Run inference asynchronously
       rknn_run(recognition_ctx, nullptr);
       
       // Get outputs
       rknn_output outputs[io_num.n_output];
       memset(outputs, 0, sizeof(outputs));
       
       for (uint32_t i = 0; i < io_num.n_output; i++) {
           outputs[i].want_float = true;
           outputs[i].is_prealloc = false;
       }
       
       rknn_outputs_get(recognition_ctx, io_num.n_output, outputs, nullptr);
       
       // Extract and normalize embeddings
       std::vector<float*> embeddings;
       
       for (uint32_t i = 0; i < face_mems.size(); i++) {
           float* embedding = new float[embedding_size];
           float* output_data = static_cast<float*>(outputs[0].buf) + i * embedding_size;
           
           // Copy embedding
           memcpy(embedding, output_data, embedding_size * sizeof(float));
           
           // L2 normalization
           float squared_sum = 0.0f;
           for (int j = 0; j < embedding_size; j++) {
               squared_sum += embedding[j] * embedding[j];
           }
           
           float norm = sqrt(squared_sum);
           for (int j = 0; j < embedding_size; j++) {
               embedding[j] /= norm;
           }
           
           embeddings.push_back(embedding);
       }
       
       // Release outputs
       rknn_outputs_release(recognition_ctx, io_num.n_output, outputs);
       
       return embeddings;
   }
   ```

2. **Similarity Computation Optimization**
   ```cpp
   // Efficient similarity computation with NEON acceleration
   float computeCosineSimilarity(const float* embedding1, 
                               const float* embedding2,
                               int embedding_size) {
   #ifdef __ARM_NEON
       // Use NEON intrinsics for accelerated dot product
       float32x4_t sum_vec = vdupq_n_f32(0);
       
       for (int i = 0; i < embedding_size; i += 4) {
           float32x4_t vec1 = vld1q_f32(embedding1 + i);
           float32x4_t vec2 = vld1q_f32(embedding2 + i);
           
           // Multiply and accumulate
           sum_vec = vmlaq_f32(sum_vec, vec1, vec2);
       }
       
       // Horizontal sum
       float sum[4];
       vst1q_f32(sum, sum_vec);
       float similarity = sum[0] + sum[1] + sum[2] + sum[3];
       
       // Handle remaining elements
       for (int i = (embedding_size / 4) * 4; i < embedding_size; i++) {
           similarity += embedding1[i] * embedding2[i];
       }
   #else
       // Standard dot product for non-NEON platforms
       float similarity = 0.0f;
       for (int i = 0; i < embedding_size; i++) {
           similarity += embedding1[i] * embedding2[i];
       }
   #endif
       
       return similarity;
   }
   ```

3. **Recognition Result Aggregation**
   ```cpp
   // Temporal aggregation of recognition results
   struct RecognitionResult {
       std::string person_id;
       float confidence;
       std::chrono::time_point<std::chrono::steady_clock> timestamp;
   };
   
   class TemporalAggregator {
   private:
       std::unordered_map<std::string, std::vector<RecognitionResult>> recent_results;
       std::chrono::milliseconds window_size;
       float confidence_threshold;
       
   public:
       TemporalAggregator(std::chrono::milliseconds window = std::chrono::milliseconds(3000),
                        float threshold = 0.7)
           : window_size(window), confidence_threshold(threshold) {}
       
       void addResult(const RecognitionResult& result) {
           if (result.confidence >= confidence_threshold) {
               recent_results[result.person_id].push_back(result);
           }
           
           // Clean up old results
           auto now = std::chrono::steady_clock::now();
           for (auto it = recent_results.begin(); it != recent_results.end();) {
               auto& results = it->second;
               
               results.erase(
                   std::remove_if(
                       results.begin(), results.end(),
                       [&](const RecognitionResult& r) {
                           return now - r.timestamp > window_size;
                       }),
                   results.end());
               
               if (results.empty()) {
                   it = recent_results.erase(it);
               } else {
                   ++it;
               }
           }
       }
       
       std::string getBestMatch() {
           std::string best_id;
           size_t max_count = 0;
           float best_avg_confidence = 0;
           
           for (const auto& pair : recent_results) {
               if (pair.second.size() > max_count) {
                   max_count = pair.second.size();
                   best_id = pair.first;
                   
                   // Calculate average confidence
                   float sum = 0;
                   for (const auto& result : pair.second) {
                       sum += result.confidence;
                   }
                   best_avg_confidence = sum / pair.second.size();
               }
           }
           
           return (max_count >= 3) ? best_id : "unknown";
       }
   };
   ```
