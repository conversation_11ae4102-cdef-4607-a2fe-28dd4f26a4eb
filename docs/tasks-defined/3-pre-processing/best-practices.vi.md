# Thực Hành Tốt Nhất cho Triển Khai Module Tiền Xử Lý RGA

## Thực Hành Tốt Nhất Triển Khai

### 1. Sử Dụng Phần Cứng RGA

- **Cấu Hình RGA**
  - Khởi tạo RGA với API phiên bản đúng cho RK3588 (RGA3)
  - Đặt chế độ hoạt động RGA phù hợp để có hiệu suất tối ưu
  - Sử dụng truy vấn tính năng RGA để phát hiện khả năng có sẵn: `querystring(RGA_IMPORT, RGA_OUTPUT, RGA_SCALE, RGA_FORMAT, etc.)`

- **Lựa Chọn Định Dạng**
  - Chọn định dạng nguồn và đích tối ưu dựa trên yêu cầu upstream/downstream
  - Ưu tiên đường dẫn định dạng trực tiếp trong RGA để tránh chuyển đổi trung gian
  - <PERSON> đầu vào NPU, chuẩn bị dữ liệu ở định dạng chính xác mà mô hình mong đợi (thường là BGR888 hoặc RGB888)

- **Điều Phối Đa RGA**
  - RK3588 có nhiều lõi RGA - phân phối khối lượng công việc một cách phù hợp
  - Sử dụng `imcheck()` để xác minh tính khả thi của thao tác trước khi gửi
  - Theo dõi sử dụng lõi RGA và cân bằng các thao tác

### 2. Triển Khai DMABUF Zero-Copy

- **Tích Hợp DMABUF**
  - Import DMABUF từ MPP sử dụng `importbuffer_fd()`
  - Export buffer đã xử lý dưới dạng DMABUF cho NPU sử dụng
  - Duy trì đồng bộ hóa phù hợp của việc truy cập buffer giữa các module

- **Quản Lý Vòng Đời Buffer**
  - Triển khai đếm tham chiếu cho DMABUF được chia sẻ
  - Tạo hệ thống quản lý buffer với chính sách tái sử dụng hiệu quả
  - Xử lý các trường hợp lỗi với việc dọn dẹp buffer phù hợp

- **Chiến Lược Ánh Xạ Bộ Nhớ**
  - Sử dụng `wrapbuffer_fd()` cho truy cập tạm thời khi cần
  - Giảm thiểu các thao tác ánh xạ bộ nhớ trong đường dẫn thông lượng cao
  - Ưu tiên các thao tác FD trực tiếp hơn truy cập con trỏ được ánh xạ khi có thể

### 3. Xử Lý Hình Ảnh Hiệu Quả

- **Xử Lý Theo Batch**
  - Nhóm các thao tác tương thích khi có thể (ví dụ: resize + chuyển đổi định dạng)
  - Sử dụng các thao tác tổng hợp của RGA thay vì các lệnh gọi riêng biệt
  - Triển khai bộ lập lịch tác vụ để tối ưu hóa chuỗi thao tác

- **Xử Lý Song Song**
  - Xử lý nhiều hình ảnh đồng thời sử dụng thread pool
  - Cân bằng số lượng thread với các lõi RGA có sẵn
  - Triển khai work stealing cho cân bằng tải động

- **Ưu Tiên Tác Vụ**
  - Gán mức ưu tiên cho các tác vụ xử lý
  - Triển khai preemption cho các luồng ưu tiên cao
  - Cân nhắc lập lịch dựa trên deadline cho yêu cầu thời gian thực

### 4. Tối Ưu cho Phát Hiện Khuôn Mặt

- **Quản Lý Độ Phân Giải**
  - Thay đổi kích thước khung hình theo kích thước chính xác mà mô hình phát hiện yêu cầu
  - Cân nhắc pipeline xử lý đa độ phân giải để có hiệu quả
  - Triển khai xử lý vùng quan tâm khi áp dụng được

- **Xử Lý Màu Sắc**
  - Thực hiện chuyển đổi không gian màu chính xác khớp với yêu cầu mô hình
  - Áp dụng chuẩn hóa trong quá trình chuyển đổi nếu RGA hỗ trợ
  - Cân nhắc triển khai chuyển đổi màu tùy chỉnh cho điều kiện ánh sáng cụ thể

- **Thích Ứng Tốc Độ Khung Hình**
  - Xử lý khung hình với tốc độ khớp với khả năng tiêu thụ downstream
  - Triển khai drop khung hình thông minh cho điều kiện quá tải
  - Ưu tiên chất lượng hơn số lượng cho đầu vào phát hiện khuôn mặt

## Lỗi Triển Khai Cần Tránh

1. **Sử Dụng Sai Khả Năng RGA**
   - Đừng giả định tất cả phiên bản RGA đều hỗ trợ cùng tính năng
   - Tránh các thao tác vượt quá giới hạn phần cứng RGA
   - Đừng trộn lẫn các thao tác đồng bộ và bất đồng bộ mà không có quản lý phù hợp

2. **Suy Giảm Hiệu Suất**
   - Tránh các thao tác nhỏ thường xuyên thay vì xử lý theo batch
   - Đừng import/export lặp đi lặp lại cùng buffer
   - Ngăn chuyển đổi định dạng không cần thiết

3. **Tranh Chấp Tài Nguyên**
   - Đừng oversubscribe lõi RGA với quá nhiều thao tác song song
   - Tránh các thao tác chạy lâu làm chặn các process khác
   - Ngăn priority inversion trong các kịch bản đa luồng

4. **Vấn Đề Quản Lý Bộ Nhớ**
   - Đừng rò rỉ DMABUF handle hoặc file descriptor
   - Tránh phân bổ/giải phóng buffer quá mức
   - Ngăn phân mảnh bằng cách sử dụng kích thước buffer nhất quán

## Chiến Lược Tối Ưu

1. **Điều Chỉnh Hiệu Suất RGA**
   - Profile các kết hợp thao tác khác nhau để có hiệu suất tốt nhất
   - Tối ưu stride và alignment để có thông lượng tối đa
   - Sử dụng thuật toán scaling hiệu quả nhất cho yêu cầu chất lượng của bạn

2. **Tối Ưu Truy Cập Bộ Nhớ**
   - Sử dụng pattern truy cập bộ nhớ được căn chỉnh
   - Tối ưu layout buffer cho hiệu quả cache
   - Cân nhắc sử dụng định dạng bộ nhớ tiled nếu được hỗ trợ

3. **Phân Phối Khối Lượng Công Việc**
   - Cân bằng xử lý trên các lõi RGA có sẵn
   - Triển khai lập lịch tác vụ thông minh dựa trên khả năng phần cứng
   - Cân nhắc xử lý bất đối xứng cho các luồng có ưu tiên khác nhau

4. **Đồng Bộ Pipeline**
   - Triển khai đồng bộ hóa hiệu quả giữa các giai đoạn xử lý
   - Sử dụng cơ chế signaling để điều phối luồng xử lý
   - Tối ưu độ sâu hàng đợi giữa các giai đoạn

## Kiểm Thử và Xác Thực

1. **Đánh Giá Chất Lượng**
   - Xác thực chất lượng hình ảnh sau xử lý so với yêu cầu
   - Xác minh độ chính xác màu sắc với hình ảnh tham chiếu
   - Đảm bảo xử lý không tạo ra artifacts ảnh hưởng đến phát hiện

2. **Benchmarking Hiệu Suất**
   - Đo thông lượng dưới các điều kiện tải khác nhau
   - Profile sử dụng băng thông bộ nhớ trong quá trình hoạt động
   - Xác minh chất lượng scaling và chuyển đổi ở các độ phân giải khác nhau

3. **Sử Dụng Tài Nguyên**
   - Giám sát sử dụng phần cứng RGA
   - Theo dõi pattern sử dụng bộ nhớ
   - Xác thực overhead CPU cho quản lý thao tác RGA

## Cân Nhắc Cụ Thể Phần Cứng cho RK3588

1. **Tính Năng RGA3 ("Orion")**
   - Tận dụng khả năng nâng cao của RGA3 trong RK3588
   - Sử dụng tối ưu hóa cụ thể RGA3 cho thông lượng cao hơn
   - Tận dụng độ phân giải tối đa được hỗ trợ (8192×8192) cho xử lý batch hiệu quả

2. **Hỗ Trợ Định Dạng**
   - Sử dụng hỗ trợ định dạng mở rộng của RGA3
   - Tận dụng chuyển đổi định dạng tăng tốc phần cứng
   - Xác minh tương thích định dạng với cả yêu cầu đầu ra MPP và đầu vào NPU

3. **Đặc Tính Hiệu Suất**
   - RGA3 có profile hiệu suất cụ thể cho các thao tác khác nhau
   - Tối ưu tham số thao tác dựa trên triển khai của RK3588
   - Cân nhắc hiệu suất RGA liên quan đến giới hạn băng thông bộ nhớ

4. **Sử Dụng RGA Đa Lõi**
   - Phân phối khối lượng công việc trên nhiều lõi RGA khi có sẵn
   - Cân bằng sử dụng RGA với các thành phần hệ thống khác
   - Triển khai chiến lược thích ứng dựa trên tính khả dụng RGA thời gian thực
