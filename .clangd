# Clangd configuration for C-AIBOX project
# This file configures clangd language server for optimal C++ development

CompileFlags:
  # Use compile_commands.json from build directory
  CompilationDatabase: build
  
  # Additional compiler flags
  Add:
    - "-std=c++20"
    - "-Wall"
    - "-Wextra"
    - "-Wpedantic"
    - "-I/usr/include/x86_64-linux-gnu/qt5"
    - "-I/usr/include/x86_64-linux-gnu/qt5/QtCore"
    - "-I/usr/include/x86_64-linux-gnu/qt5/QtGui"
    - "-I/usr/include/x86_64-linux-gnu/qt5/QtWidgets"
    - "-I/usr/include/x86_64-linux-gnu/qt5/QtWebEngine"
    - "-I/usr/include/x86_64-linux-gnu/qt5/QtWebEngineWidgets"
  
  # Remove problematic flags
  Remove:
    - "-m*"
    - "-f*san"

Index:
  # Enable background indexing for better performance
  Background: Build
  
  # Limit memory usage
  StandardLibrary: Yes

Diagnostics:
  # Enable clang-tidy integration
  ClangTidy:
    Add:
      - "clang-analyzer-*"
      - "cppcoreguidelines-*"
      - "modernize-*"
      - "performance-*"
      - "readability-*"
      - "bugprone-*"
    Remove:
      - "readability-magic-numbers"
      - "cppcoreguidelines-avoid-magic-numbers"
      - "modernize-use-trailing-return-type"
  
  # Suppress certain warnings in third-party code
  Suppress:
    - "third_party/*"
    - "build/_deps/*"

Completion:
  # Enable detailed completion
  AllScopes: Yes
  
InlayHints:
  # Enable helpful inline hints
  Enabled: Yes
  ParameterNames: Yes
  DeducedTypes: Yes

Hover:
  # Show detailed information on hover
  ShowAKA: Yes
