#!/bin/bash

# Comprehensive script to setup and run Qt GUI application in container
# This script handles all the OpenGL/X11 configuration automatically

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
BUILD_TYPE="Debug"
SETUP_OPENGL=true
SHOW_HELP=false

# Function to show help
show_help() {
    cat << EOF
Usage: $0 [OPTIONS]

Comprehensive script to setup and run Qt GUI application in container.
Automatically configures OpenGL, X11, and Qt environment.

OPTIONS:
    -h, --help              Show this help message
    -t, --type TYPE         Build type (Debug|Release) [default: Debug]
    --skip-opengl-setup     Skip OpenGL environment setup
    --headless              Run in headless mode

EXAMPLES:
    $0                      # Full setup and run GUI
    $0 --headless           # Run in headless mode
    $0 --skip-opengl-setup  # Skip OpenGL setup (if already done)
    $0 -t Release           # Build and run in Release mode

FEATURES:
    ✅ Automatic OpenGL/Mesa configuration
    ✅ X11 environment setup
    ✅ Qt optimization for containers
    ✅ WebEngine configuration
    ✅ Error handling and fallbacks
    ✅ Build system integration

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--type)
            BUILD_TYPE="$2"
            shift 2
            ;;
        --skip-opengl-setup)
            SETUP_OPENGL=false
            shift
            ;;
        --headless)
            exec ./scripts/run_client.sh --headless
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

print_info "C-AIBOX GUI Application Runner"
print_info "=============================="

# Check if we're in the right directory
if [[ ! -f "CMakeLists.txt" ]]; then
    print_error "CMakeLists.txt not found. Please run this script from the project root."
    exit 1
fi

# Setup OpenGL environment if requested
if [[ "$SETUP_OPENGL" == true ]]; then
    print_info "Setting up OpenGL environment..."
    if [[ -f "./scripts/setup_opengl_container.sh" ]]; then
        ./scripts/setup_opengl_container.sh
    else
        print_warning "OpenGL setup script not found, skipping..."
    fi
else
    print_info "Skipping OpenGL setup (--skip-opengl-setup specified)"
fi

# Load environment configurations
print_info "Loading GUI environment..."
if [[ -f "/tmp/gui_env_complete.sh" ]]; then
    source /tmp/gui_env_complete.sh
    print_success "Complete GUI environment loaded"
elif [[ -f "/tmp/qt_env.sh" ]]; then
    source /tmp/qt_env.sh
    print_success "Basic Qt environment loaded"
else
    print_warning "No environment configuration found, using defaults"
    export QT_QPA_PLATFORM=xcb
    export QT_OPENGL=software
    export LIBGL_ALWAYS_SOFTWARE=1
fi

# Check and fix X11 availability
print_info "Checking X11 environment..."
if [[ -z "$DISPLAY" ]]; then
    print_warning "DISPLAY environment variable not set"
    export DISPLAY=:0
    print_info "Set DISPLAY to :0"
fi

print_info "DISPLAY: $DISPLAY"

# Try to fix X11 permissions and connection
print_info "Fixing X11 permissions..."
if [[ -f "./scripts/fix_x11_permissions.sh" ]]; then
    ./scripts/fix_x11_permissions.sh >/dev/null 2>&1 || true
fi

# Test X11 connection
X11_WORKING=false
if command -v xdpyinfo >/dev/null 2>&1; then
    if timeout 3s xdpyinfo >/dev/null 2>&1; then
        X11_WORKING=true
        print_success "X11 connection verified"
    else
        print_warning "X11 connection test failed"
    fi
else
    print_warning "xdpyinfo not available for testing"
fi

if [[ "$X11_WORKING" == false ]]; then
    print_warning "X11 may not be working properly"
    print_info "The application will attempt to run but may show warnings"
    print_info "If GUI doesn't appear, try:"
    print_info "1. Setup X11 server on host (see scripts/setup_x11_host.sh)"
    print_info "2. Run in headless mode: ./scripts/run_client.sh --headless"
fi

# Build the application
print_info "Building C-AIBOX Client..."
BUILD_DIR="build"
mkdir -p "$BUILD_DIR"

# Configure CMake
print_info "Configuring CMake (Build type: $BUILD_TYPE)..."
cd "$BUILD_DIR"
cmake -DCMAKE_BUILD_TYPE="$BUILD_TYPE" ..

# Build the project
print_info "Building the project..."
cmake --build . --target client_app --parallel

# Check if build was successful
if [[ ! -f "apps/client/client_app" ]]; then
    print_error "Build failed! client_app executable not found."
    exit 1
fi

print_success "Build completed successfully!"

# Return to project root
cd ..

# Prepare to run the application
CLIENT_EXECUTABLE="./build/apps/client/client_app"

print_info "Starting C-AIBOX GUI Application..."
print_info "Configuration:"
print_info "  Build Type: $BUILD_TYPE"
print_info "  Executable: $CLIENT_EXECUTABLE"
print_info "  Display: $DISPLAY"
print_info "  OpenGL: ${QT_OPENGL:-default}"
print_info "  Platform: ${QT_QPA_PLATFORM:-default}"

# Run the application
print_success "Launching GUI application..."
print_info "Note: Some OpenGL warnings are normal in container environment"
print_info "The application should display a window with Google loaded"
print_info ""

# Set additional environment for clean output
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false;qt.qpa.gl.warning=false;qt.qpa.xcb.glx.warning=false"

# Run the application
"$CLIENT_EXECUTABLE"

print_success "Application finished."
