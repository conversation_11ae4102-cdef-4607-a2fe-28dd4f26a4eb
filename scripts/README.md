# C-AIBOX Scripts Documentation

This directory contains all build, development, and deployment scripts for the C-AIBOX project, organized by purpose and functionality.

## 📁 Directory Structure

```
scripts/
├── README.md              # This documentation
├── start-dev-flow.sh      # 🎯 Main development flow orchestrator (HOST)
├── dev-helper.sh          # 🛠️ Container-side development helper
├── DEV_FLOW.md           # 📖 Complete development flow documentation
├── QUICK_START.md        # ⚡ Quick reference guide
├── build.sh              # 🔨 Main build command
├── test.sh               # 🧪 Main test command
├── clean.sh              # 🧹 Main clean command
├── format.sh             # 📝 Main format command
├── run.sh                # 🚀 Main run command (unified)
├── setup.sh              # ⚙️ Main setup command (unified)
├── build/                # Build-related sub-scripts
├── gui/                  # GUI/X11 related scripts
│   ├── README.md         # GUI setup documentation
│   ├── setup-gui.sh      # GUI environment setup
│   ├── container-x11-env.sh # Container X11 environment
│   ├── fix-x11-auth.sh   # Fix X11 authentication
│   ├── fix-x11-permissions.sh # Fix X11 permissions
│   └── setup-opengl-container.sh # OpenGL container setup
├── dev/                  # Development helper scripts
│   ├── run-client.sh     # Run client application
│   └── run-gui-app.sh    # Run GUI application with setup
├── platform/             # Platform-specific scripts
│   ├── setup-x11-host.sh # X11 host setup (Linux)
│   ├── setup-xquartz-macos.sh # XQuartz setup (macOS)
│   ├── setup-vcxsrv-windows.ps1 # VcXsrv setup (Windows)
│   └── setup-x11-auth.sh # X11 authentication setup
└── utils/                # Utility scripts
    ├── install-x11-deps.sh # Install X11 dependencies
    ├── create-render-node.sh # Create GPU render nodes
    ├── start-x11.sh      # Start X11 server (Xvfb)
    └── setup-gui-env.sh  # Setup GUI environment variables
```

## 🚀 Main Commands

### 🎯 Development Flow Commands (NEW!)

#### Complete Development Environment
```bash
# [HOST] Start complete development environment
./scripts/start-dev-flow.sh

# [HOST] Build container only
./scripts/start-dev-flow.sh build

# [HOST] Get shell in container
./scripts/start-dev-flow.sh shell

# [HOST] Check environment status
./scripts/start-dev-flow.sh status
```

#### Container Development Helper
```bash
# [CONTAINER] Setup development environment
./scripts/dev-helper.sh setup

# [CONTAINER] Build project
./scripts/dev-helper.sh build

# [CONTAINER] Run tests
./scripts/dev-helper.sh test

# [CONTAINER] Run GUI application
./scripts/dev-helper.sh gui

# [CONTAINER] Check development status
./scripts/dev-helper.sh status
```

### Build Commands
```bash
# Build project in Debug mode
./scripts/build.sh

# Build in Release mode
./scripts/build.sh Release

# Clean and rebuild
./scripts/build.sh Debug clean
```

### Test Commands
```bash
# Run all tests
./scripts/test.sh

# Run tests with verbose output
./scripts/test.sh --verbose

# Run tests with coverage report
./scripts/test.sh --coverage
```

### Clean Commands
```bash
# Clean build directories
./scripts/clean.sh

# Clean everything (build + cache + deps)
./scripts/clean.sh --all

# Clean only CMake cache
./scripts/clean.sh --cache
```

### Format Commands
```bash
# Check code formatting
./scripts/format.sh check

# Fix code formatting
./scripts/format.sh fix
```

## 🎯 Unified Commands

### Run Command
```bash
# Run client application (default)
./scripts/run.sh

# Run client in headless mode
./scripts/run.sh client --headless

# Run server
./scripts/run.sh server

# Run with GUI setup
./scripts/run.sh gui

# Run examples
./scripts/run.sh examples

# Run with rebuild
./scripts/run.sh client --rebuild --build-type Release
```

### Setup Command
```bash
# Setup GUI environment (default)
./scripts/setup.sh

# Setup X11 server
./scripts/setup.sh x11

# Setup system dependencies
./scripts/setup.sh deps

# Setup complete development environment
./scripts/setup.sh dev

# Setup everything
./scripts/setup.sh all

# Force reinstall/reconfigure
./scripts/setup.sh all --force
```

## Guide for development
1. Setup x11 server on host machine `./scripts/setup.sh x11`
2. Setup dev container `./scripts/setup.sh dev` or `./scripts/setup.sh`

## 📂 Sub-Scripts by Category

### GUI/X11 Scripts (`gui/`)
- **`setup-gui.sh`** - Complete GUI environment setup
- **`container-x11-env.sh`** - Container X11 environment configuration
- **`fix-x11-auth.sh`** - Fix X11 authentication issues
- **`fix-x11-permissions.sh`** - Fix X11 permission issues
- **`setup-opengl-container.sh`** - Setup OpenGL in containers

### Development Scripts (`dev/`)
- **`run-client.sh`** - Run client application with build
- **`run-gui-app.sh`** - Run GUI application with full setup

### Platform Scripts (`platform/`)
- **`setup-x11-host.sh`** - Setup X11 on Linux host
- **`setup-xquartz-macos.sh`** - Setup XQuartz on macOS
- **`setup-vcxsrv-windows.ps1`** - Setup VcXsrv on Windows
- **`setup-x11-auth.sh`** - Setup X11 authentication

### Utility Scripts (`utils/`)
- **`install-x11-deps.sh`** - Install X11 system dependencies
- **`create-render-node.sh`** - Create GPU render nodes
- **`start-x11.sh`** - Start/manage X11 server (Xvfb)
- **`setup-gui-env.sh`** - Setup GUI environment variables

## 🏷️ Script Naming Convention

### Main Commands
- Use simple names: `build.sh`, `test.sh`, `run.sh`, `setup.sh`
- Located in root scripts directory
- Provide unified interface to functionality

### Sub-Scripts
- Use kebab-case: `setup-gui.sh`, `run-client.sh`
- Include purpose in name: `fix-x11-auth.sh`
- Organized in purpose-specific directories
- Tagged with clear functionality

## 🔧 Usage Examples

### Quick Start
```bash
# Complete setup and run
./scripts/setup.sh all
./scripts/run.sh gui

# Development workflow
./scripts/build.sh Debug
./scripts/test.sh
./scripts/run.sh client
```

### GUI Development
```bash
# Setup GUI environment
./scripts/setup.sh gui

# Run with GUI
./scripts/run.sh gui

# Troubleshoot X11
./scripts/gui/fix-x11-auth.sh
```

### Cross-Platform Setup
```bash
# Linux
./scripts/setup.sh x11

# macOS
./scripts/platform/setup-xquartz-macos.sh

# Windows (PowerShell)
./scripts/platform/setup-vcxsrv-windows.ps1
```

## 📋 Migration Notes

This new structure replaces the old flat scripts directory:
- Main commands remain at root level for easy access
- Sub-scripts are organized by purpose
- Unified `run.sh` and `setup.sh` provide simple interfaces
- All functionality is preserved with improved organization

## 🆘 Troubleshooting

### Common Issues
1. **Permission denied**: Run `chmod +x scripts/*.sh`
2. **X11 connection failed**: Run `./scripts/gui/fix-x11-auth.sh`
3. **Build failures**: Run `./scripts/clean.sh --all && ./scripts/build.sh`
4. **GUI not working**: Run `./scripts/setup.sh gui --force`

### Getting Help
- Each script supports `--help` or `-h` flag
- Check individual README files in subdirectories
- Review script comments for detailed usage
