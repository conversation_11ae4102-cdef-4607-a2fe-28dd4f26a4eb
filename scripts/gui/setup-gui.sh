#!/bin/bash

# Script to setup GUI environment in the container
# This script helps configure X11 forwarding and Qt dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info "Setting up GUI environment for C-AIBOX Client..."

# Check if we're running in a container
if [[ -f /.dockerenv ]]; then
    print_info "Running inside a container"
else
    print_warning "Not running in a container - some steps may not be necessary"
fi

# Check X11 forwarding
print_info "Checking X11 forwarding..."
if [[ -n "$DISPLAY" ]]; then
    print_success "DISPLAY is set: $DISPLAY"
    
    # Test X11 connection
    if command -v xset >/dev/null 2>&1; then
        if xset q >/dev/null 2>&1; then
            print_success "X11 connection is working"
        else
            print_warning "X11 connection test failed"
            print_info "This might be normal in some container environments"
        fi
    else
        print_info "xset not available - installing X11 utilities..."
        apt-get update && apt-get install -y x11-utils
    fi
else
    print_warning "DISPLAY environment variable is not set"
    print_info "GUI applications will run in headless mode"
fi

# Check Qt5 dependencies
print_info "Checking Qt5 dependencies..."
if dpkg -l | grep -q libqt5; then
    print_success "Qt5 libraries are installed"
else
    print_warning "Qt5 libraries not found"
    print_info "Installing Qt5 dependencies..."
    apt-get update && apt-get install -y \
        qt5-default \
        libqt5webkit5-dev \
        libqt5webengine5 \
        libqt5webenginewidgets5 \
        qtwebengine5-dev
fi

# Check for required libraries
print_info "Checking required system libraries..."
REQUIRED_LIBS=(
    "libgl1-mesa-dri"
    "libglib2.0-0"
    "libxkbcommon-x11-0"
    "libxcb-icccm4"
    "libxcb-image0"
    "libxcb-keysyms1"
    "libxcb-randr0"
    "libxcb-render-util0"
    "libxcb-xinerama0"
    "libxcb-xfixes0"
    "libegl1"
    "libxrandr2"
)

MISSING_LIBS=()
for lib in "${REQUIRED_LIBS[@]}"; do
    if ! dpkg -l | grep -q "$lib"; then
        MISSING_LIBS+=("$lib")
    fi
done

if [[ ${#MISSING_LIBS[@]} -gt 0 ]]; then
    print_info "Installing missing libraries: ${MISSING_LIBS[*]}"
    apt-get update && apt-get install -y "${MISSING_LIBS[@]}"
else
    print_success "All required libraries are installed"
fi

# Set up environment variables
print_info "Setting up environment variables..."
cat > /tmp/qt_env.sh << 'EOF'
# Qt5 Environment Variables for Container GUI
export QT_X11_NO_MITSHM=1
export QT_QPA_PLATFORM=xcb
export QTWEBENGINE_DISABLE_SANDBOX=1
export LIBGL_ALWAYS_INDIRECT=1
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false"

# Additional Qt settings for better container compatibility
export QT_AUTO_SCREEN_SCALE_FACTOR=0
export QT_SCALE_FACTOR=1
export QT_FONT_DPI=96
EOF

print_info "Environment variables saved to /tmp/qt_env.sh"
print_info "To use them, run: source /tmp/qt_env.sh"

# Test GUI capability
print_info "Testing GUI capability..."
if command -v xclock >/dev/null 2>&1; then
    print_info "xclock is available for testing"
    print_info "You can test X11 forwarding with: xclock"
else
    print_info "Installing xclock for testing..."
    apt-get update && apt-get install -y x11-apps
    print_info "Test X11 forwarding with: xclock"
fi

# Create a simple test script
cat > /tmp/test_gui.sh << 'EOF'
#!/bin/bash
echo "Testing GUI environment..."
source /tmp/qt_env.sh

if [[ -n "$DISPLAY" ]]; then
    echo "DISPLAY: $DISPLAY"
    echo "Testing X11 connection..."
    if command -v xclock >/dev/null 2>&1; then
        echo "Starting xclock for 5 seconds..."
        timeout 5s xclock || echo "xclock test completed"
    fi
else
    echo "No DISPLAY set - GUI not available"
fi
EOF

chmod +x /tmp/test_gui.sh
print_info "GUI test script created at /tmp/test_gui.sh"

print_success "GUI environment setup completed!"
print_info ""
print_info "Next steps:"
print_info "1. Source the environment: source /tmp/qt_env.sh"
print_info "2. Test GUI: /tmp/test_gui.sh"
print_info "3. Build and run client: ./scripts/run_client.sh"
print_info ""
print_info "For headless mode: ./scripts/run_client.sh --headless"
print_info "For GUI mode: ./scripts/run_client.sh --gui"
