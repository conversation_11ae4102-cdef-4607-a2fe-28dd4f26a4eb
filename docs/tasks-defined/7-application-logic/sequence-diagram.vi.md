# Sơ Đồ Tuần Tự Module Logic Ứng Dụng

Tài liệu này cung cấp các sơ đồ tuần tự chi tiết cho Module Logic Ứng Dụng, minh họa luồng logic và tương tác giữa các thành phần cho khớp khuôn mặt, theo dõi và logic nghiệp vụ.

## Tu<PERSON>n Tự Khởi Tạo Module

```mermaid
sequenceDiagram
    participant Main as Main Application
    participant AppLogic as ApplicationLogicModule
    participant DB as FaceDatabase
    participant Tracker as FaceTracker
    participant EventMgr as EventManager

    Main->>AppLogic: initialize(config)
    activate AppLogic

    AppLogic->>DB: initializeDatabase(db_config)
    activate DB
    DB->>DB: loadFaceRecords()
    DB->>DB: initializeSearchIndex()
    DB-->>AppLogic: database ready
    deactivate DB

    AppLogic->>Tracker: initialize(tracking_config)
    activate Tracker
    Tracker->>Tracker: setupTrackingParameters()
    Tracker-->>AppLogic: tracker ready
    deactivate Tracker

    AppLogic->>EventMgr: initialize(event_config)
    activate EventMgr
    EventMgr->>EventMgr: setupEventHandlers()
    EventMgr-->>AppLogic: event manager ready
    deactivate EventMgr

    AppLogic-->>Main: initialization complete
    deactivate AppLogic
```

## Tuần Tự Khớp Khuôn Mặt

```mermaid
sequenceDiagram
    participant InQueue as EmbeddingQueue
    participant Worker as MatchingWorker
    participant DB as FaceDatabase
    participant Matcher as SimilarityMatcher
    participant OutQueue as ResultQueue

    activate Worker
    Worker->>InQueue: dequeueEmbedding()
    activate InQueue
    InQueue-->>Worker: face embedding with metadata
    deactivate InQueue

    Worker->>DB: findCandidateMatches(embedding, top_k)
    activate DB

    alt Using Vector Index
        DB->>DB: performIndexSearch(embedding, top_k)
    else Linear Search
        DB->>DB: performLinearSearch(embedding)
    end

    DB-->>Worker: candidate_matches
    deactivate DB

    Worker->>Matcher: refineCandidates(embedding, candidates, threshold)
    activate Matcher

    loop For Each Candidate
        Matcher->>Matcher: computeCosineSimilarity(embedding, candidate.embedding)

        alt Similarity > Threshold
            Matcher->>Matcher: addToMatchResults(candidate, similarity)
        end
    end

    Matcher-->>Worker: refined_matches
    deactivate Matcher

    alt Match Found
        Worker->>Worker: createMatchResult(best_match, metadata)
    else No Match
        Worker->>Worker: createUnknownResult(metadata)
    end

    Worker->>OutQueue: enqueueMatchResult(result)
    activate OutQueue
    OutQueue-->>Worker: enqueue status
    deactivate OutQueue

    deactivate Worker
```

## Tuần Tự Theo Dõi Khuôn Mặt

```mermaid
sequenceDiagram
    participant Worker as TrackingWorker
    participant Tracker as FaceTracker
    participant History as TrackHistory
    participant RecogMgr as RecognitionManager

    activate Worker
    Worker->>Worker: receiveDetectionResults(frame_id, detections)

    Worker->>Tracker: updateTracks(frame_id, detections)
    activate Tracker

    loop For Each Detection
        Tracker->>Tracker: calculateIOU(detection, existing_tracks)

        alt Match Existing Track
            Tracker->>Tracker: updateTrack(track_id, detection)
        else Create New Track
            Tracker->>Tracker: createNewTrack(detection)
        end
    end

    Tracker->>Tracker: updateLostTracks()
    Tracker->>Tracker: removeStaleTracks(max_age)

    Tracker-->>Worker: updated_tracks
    deactivate Tracker

    loop For Each Active Track
        Worker->>History: getTrackHistory(track_id)
        activate History
        History-->>Worker: track_history
        deactivate History

        Worker->>RecogMgr: associateRecognition(track_id, recognition_result)
        activate RecogMgr

        alt New Recognition Available
            RecogMgr->>RecogMgr: updateTrackIdentity(track_id, recognition)
        else Use Existing Recognition
            RecogMgr->>RecogMgr: maintainIdentity(track_id)
        end

        RecogMgr-->>Worker: track_identity
        deactivate RecogMgr
    end

    Worker->>Worker: generateTrackingResults()
    deactivate Worker
```

## Tuần Tự Tổng Hợp Nhận Diện Thời Gian

```mermaid
sequenceDiagram
    participant InQueue as MatchQueue
    participant Aggregator as TemporalAggregator
    participant History as RecognitionHistory
    participant OutQueue as ResultQueue

    activate Aggregator
    Aggregator->>InQueue: dequeueMatchResult()
    activate InQueue
    InQueue-->>Aggregator: match_result
    deactivate InQueue

    Aggregator->>History: getTrackRecognitionHistory(track_id)
    activate History
    History-->>Aggregator: recognition_history
    deactivate History

    Aggregator->>Aggregator: addResultToHistory(match_result)

    alt History Sufficient
        Aggregator->>Aggregator: computeConfidenceWeightedVoting()
        Aggregator->>Aggregator: determineStableIdentity()

        alt Identity Changed
            Aggregator->>OutQueue: enqueueIdentityChangeEvent(track_id, old_id, new_id)
            activate OutQueue
            OutQueue-->>Aggregator: enqueue status
            deactivate OutQueue
        end

    else Insufficient History
        Aggregator->>Aggregator: maintainCurrentIdentity()
    end

    Aggregator->>Aggregator: cleanupOldEntries(max_age)
    deactivate Aggregator
```

## Tuần Tự Xử Lý Sự Kiện

```mermaid
sequenceDiagram
    participant Source as EventSource
    participant EventMgr as EventManager
    participant Rules as BusinessRules
    participant Actions as ActionExecutor
    participant Notifier as NotificationManager

    Source->>EventMgr: generateEvent(event_type, data)
    activate EventMgr

    EventMgr->>EventMgr: validateEvent(event)
    EventMgr->>EventMgr: enrichEventData(event)

    EventMgr->>Rules: evaluateEvent(event)
    activate Rules

    Rules->>Rules: applyRuleSet(event)

    alt Rules Triggered
        Rules->>Rules: determineActions(triggered_rules)
        Rules-->>EventMgr: actions_to_execute

        loop For Each Action
            EventMgr->>Actions: executeAction(action, event_data)
            activate Actions

            alt Notification Action
                Actions->>Notifier: sendNotification(notification_data)
                activate Notifier
                Notifier-->>Actions: notification_sent
                deactivate Notifier
            else Database Action
                Actions->>Actions: performDatabaseOperation()
            else External System Action
                Actions->>Actions: callExternalAPI()
            end

            Actions-->>EventMgr: action_result
            deactivate Actions
        end

    else No Rules Triggered
        Rules-->>EventMgr: no_actions
    end

    deactivate Rules

    EventMgr->>EventMgr: logEvent(event, results)
    EventMgr-->>Source: event_processed
    deactivate EventMgr
```

## Tuần Tự Xử Lý Lỗi và Phục Hồi

```mermaid
sequenceDiagram
    participant Worker as LogicWorker
    participant ErrorHandler as ErrorHandler
    participant Logger as Logger
    participant Recovery as RecoveryManager
    participant App as Application

    activate Worker
    Worker->>Worker: detectError(error_type)
    Worker->>ErrorHandler: handleApplicationError(error_type, details)
    activate ErrorHandler

    ErrorHandler->>Logger: logError(error_details)
    activate Logger
    Logger-->>ErrorHandler: logged
    deactivate Logger

    alt Recoverable Database Error
        ErrorHandler->>Recovery: attemptDatabaseRecovery()
        activate Recovery
        Recovery-->>ErrorHandler: recovery_result
        deactivate Recovery
        ErrorHandler-->>Worker: RETRY_OPERATION

    else Face Matching Error
        ErrorHandler->>ErrorHandler: logMatchingError(details)
        ErrorHandler-->>Worker: USE_FALLBACK_MATCHING

    else Tracking System Error
        ErrorHandler->>Recovery: resetTrackingState()
        activate Recovery
        Recovery-->>ErrorHandler: reset_complete
        deactivate Recovery
        ErrorHandler-->>Worker: RESET_TRACKING

    else Critical System Error
        ErrorHandler->>App: notifyCriticalError(details)
        ErrorHandler-->>Worker: INITIATE_SAFE_MODE
    end

    deactivate ErrorHandler

    alt Action is RETRY_OPERATION
        Worker->>Worker: retryFailedOperation()
    else Action is USE_FALLBACK_MATCHING
        Worker->>Worker: switchToSimpleMatching()
    else Action is RESET_TRACKING
        Worker->>Worker: clearTrackingData()
        Worker->>Worker: restartTracking()
    else Action is INITIATE_SAFE_MODE
        Worker->>Worker: enterSafeOperationMode()
        Worker->>App: notifySafeModeActive()
    end
    deactivate Worker
```

## Tuần Tự Tắt Máy Module

```mermaid
sequenceDiagram
    participant App as Application
    participant AppLogic as ApplicationLogicModule
    participant DB as FaceDatabase
    participant Tracker as FaceTracker
    participant EventMgr as EventManager

    App->>AppLogic: shutdown()
    activate AppLogic

    AppLogic->>EventMgr: shutdown()
    activate EventMgr
    EventMgr->>EventMgr: stopEventProcessing()
    EventMgr-->>AppLogic: event manager stopped
    deactivate EventMgr

    AppLogic->>Tracker: shutdown()
    activate Tracker
    Tracker->>Tracker: finalizeActiveTracks()
    Tracker-->>AppLogic: tracker stopped
    deactivate Tracker

    AppLogic->>DB: shutdown()
    activate DB
    DB->>DB: flushPendingOperations()
    DB->>DB: closeDatabase()
    DB-->>AppLogic: database closed
    deactivate DB

    AppLogic-->>App: shutdown complete
    deactivate AppLogic
```
