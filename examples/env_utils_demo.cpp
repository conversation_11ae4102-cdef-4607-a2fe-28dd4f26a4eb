#include <iostream>
#include <utils/env_utils.hpp>

int main() {
    std::cout << "=== C-AIBOX Environment Utils Demo ===" << std::endl;

    // 1. Load environment variables from config.ini file
    std::cout << "\n1. Loading environment variables from config.ini file..." << std::endl;
    if (utils::EnvUtils::loadConfigFromFile("config.ini")) {
        std::cout << "✓ Successfully loaded config.ini file" << std::endl;
    } else {
        std::cout << "⚠ Could not load config.ini file (this is normal if file doesn't exist)" << std::endl;
    }

    // 2. Set some environment variables programmatically
    std::cout << "\n2. Setting environment variables programmatically..." << std::endl;
    utils::EnvUtils::setEnv("DEMO_APP_NAME", "C-AIBOX");
    utils::EnvUtils::setEnv("DEMO_VERSION", "1.0.0");
    utils::EnvUtils::setEnv("DEMO_DEBUG", "true");
    utils::EnvUtils::setEnv("DEMO_PORT", "8080");
    std::cout << "✓ Set demo environment variables" << std::endl;

    // 3. Get environment variables with default values (INI format with sections)
    std::cout << "\n3. Getting environment variables..." << std::endl;
    std::cout << "app.name: " << utils::EnvUtils::getEnv("app.name", "Default App") << std::endl;
    std::cout << "app.version: " << utils::EnvUtils::getEnv("app.version", "Default Version") << std::endl;
    std::cout << "api.debug: " << utils::EnvUtils::getEnv("api.debug", "false") << std::endl;
    std::cout << "api.port: " << utils::EnvUtils::getEnv("api.port", "3000") << std::endl;
    std::cout << "database.host: " << utils::EnvUtils::getEnv("database.host", "not set") << std::endl;
    std::cout << "api.api_key: " << utils::EnvUtils::getEnv("api.api_key", "not set") << std::endl;
    std::cout << "security.jwt_secret: " << utils::EnvUtils::getEnv("security.jwt_secret", "not set") << std::endl;
    std::cout << "DEMO_APP_NAME: " << utils::EnvUtils::getEnv("DEMO_APP_NAME") << std::endl;
    std::cout << "DEMO_VERSION: " << utils::EnvUtils::getEnv("DEMO_VERSION") << std::endl;
    std::cout << "DEMO_DEBUG: " << utils::EnvUtils::getEnv("DEMO_DEBUG") << std::endl;
    std::cout << "DEMO_PORT: " << utils::EnvUtils::getEnv("DEMO_PORT") << std::endl;
    std::cout << "NON_EXISTENT: " << utils::EnvUtils::getEnv("NON_EXISTENT", "default_value") << std::endl;

    // 4. Check if environment variables exist
    std::cout << "\n4. Checking if environment variables exist..." << std::endl;
    std::cout << "DEMO_APP_NAME exists: " << (utils::EnvUtils::hasEnv("DEMO_APP_NAME") ? "Yes" : "No") << std::endl;
    std::cout << "NON_EXISTENT exists: " << (utils::EnvUtils::hasEnv("NON_EXISTENT") ? "Yes" : "No") << std::endl;

    // 5. Use optional getter
    std::cout << "\n5. Using optional getter..." << std::endl;
    auto optionalValue = utils::EnvUtils::getEnvOptional("DEMO_VERSION");
    if (optionalValue.has_value()) {
        std::cout << "DEMO_VERSION (optional): " << optionalValue.value() << std::endl;
    } else {
        std::cout << "DEMO_VERSION not found" << std::endl;
    }

    auto nonExistentOptional = utils::EnvUtils::getEnvOptional("NON_EXISTENT");
    if (nonExistentOptional.has_value()) {
        std::cout << "NON_EXISTENT (optional): " << nonExistentOptional.value() << std::endl;
    } else {
        std::cout << "NON_EXISTENT not found (as expected)" << std::endl;
    }

    // 6. Load from string (useful for testing or configuration) - INI format
    std::cout << "\n6. Loading from string..." << std::endl;
    std::string iniString = R"(
# Demo configuration
[demo]
host=localhost
port=3000
name="My Application"
secret='secret-key-123'
empty_value=
)";

    if (utils::EnvUtils::loadConfigFromString(iniString)) {
        std::cout << "✓ Successfully loaded from string" << std::endl;
        std::cout << "demo.host: " << utils::EnvUtils::getEnv("demo.host") << std::endl;
        std::cout << "demo.port: " << utils::EnvUtils::getEnv("demo.port") << std::endl;
        std::cout << "demo.name: " << utils::EnvUtils::getEnv("demo.name") << std::endl;
        std::cout << "demo.secret: " << utils::EnvUtils::getEnv("demo.secret") << std::endl;
        std::cout << "demo.empty_value: '" << utils::EnvUtils::getEnv("demo.empty_value") << "'" << std::endl;
    }

    // 7. Get all loaded environment variables
    std::cout << "\n7. All loaded environment variables:" << std::endl;
    auto allVars = utils::EnvUtils::getAllEnv();
    for (const auto& [key, value] : allVars) {
        std::cout << "  " << key << "=" << value << std::endl;
    }

    // 8. Demonstrate convenience functions
    std::cout << "\n8. Using convenience functions..." << std::endl;
    std::cout << "Using utils::getEnv(): " << utils::getEnv("DEMO_APP_NAME", "Default") << std::endl;
    std::cout << "Using utils::hasEnv(): " << (utils::hasEnv("DEMO_VERSION") ? "Yes" : "No") << std::endl;

    utils::setEnv("CONVENIENCE_TEST", "convenience_value");
    std::cout << "Set and get convenience: " << utils::getEnv("CONVENIENCE_TEST") << std::endl;

    // 9. Demonstrate overwrite behavior
    std::cout << "\n9. Testing overwrite behavior..." << std::endl;
    utils::EnvUtils::setEnv("OVERWRITE_TEST", "original_value");
    std::cout << "Original value: " << utils::EnvUtils::getEnv("OVERWRITE_TEST") << std::endl;

    utils::EnvUtils::setEnv("OVERWRITE_TEST", "new_value", true);  // overwrite = true
    std::cout << "After overwrite=true: " << utils::EnvUtils::getEnv("OVERWRITE_TEST") << std::endl;

    utils::EnvUtils::setEnv("OVERWRITE_TEST", "another_value", false);  // overwrite = false
    std::cout << "After overwrite=false: " << utils::EnvUtils::getEnv("OVERWRITE_TEST") << std::endl;

    // 10. Unset environment variable
    std::cout << "\n10. Unsetting environment variable..." << std::endl;
    std::cout << "Before unset: " << (utils::EnvUtils::hasEnv("OVERWRITE_TEST") ? "exists" : "not exists") << std::endl;
    utils::EnvUtils::unsetEnv("OVERWRITE_TEST");
    std::cout << "After unset: " << (utils::EnvUtils::hasEnv("OVERWRITE_TEST") ? "exists" : "not exists") << std::endl;

    std::cout << "\n=== Demo completed ===" << std::endl;
    return 0;
}
