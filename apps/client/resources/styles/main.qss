/* Main Application Stylesheet */

/* Global Styles */
* {
    font-family: 'Roboto', 'Arial', sans-serif;
}

QMainWindow {
    background-color: #f5f5f5;
    color: #333;
}

/* Widget Styles */
QWidget {
    background-color: transparent;
    color: #333;
}

/* Label Styles */
QLabel {
    color: #333;
    background-color: transparent;
}

QLabel[class="header"] {
    font-size: 18px;
    font-weight: bold;
    color: #4da8da;
    padding: 10px;
}

QLabel[class="subheader"] {
    font-size: 14px;
    font-weight: bold;
    color: #666;
    padding: 5px;
}

QLabel[class="info"] {
    font-size: 12px;
    color: #888;
    padding: 5px;
}

/* Button Styles */
QPushButton {
    background-color: #4da8da;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 16px;
    font-weight: bold;
    min-width: 80px;
}

QPushButton:hover {
    background-color: #3d8bb8;
}

QPushButton:pressed {
    background-color: #2d6b88;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #666666;
}

QPushButton[class="toolbar"] {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 5px;
    min-width: 32px;
}

QPushButton[class="toolbar"]:hover {
    background-color: rgba(77, 168, 218, 0.2);
}

QPushButton[class="toolbar"]:pressed {
    background-color: rgba(77, 168, 218, 0.4);
}

/* Frame Styles */
QFrame {
    border-radius: 5px;
    background-color: white;
}

QFrame[class="card"] {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin: 5px;
}

QFrame[class="camera-feed"] {
    background-color: #1e2a44;
    border-radius: 10px;
    padding: 10px;
}

QFrame[class="face-card"] {
    background-color: #333;
    border-radius: 5px;
    padding: 5px;
    margin: 2px;
}

QFrame[class="face-card-authorized"] {
    border: 2px solid #00ff00;
}

QFrame[class="face-card-unauthorized"] {
    border: 2px solid #ff0000;
}

QFrame[class="person-card"] {
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    margin: 5px 0;
}

QFrame[class="person-card"]:hover {
    background-color: #f8f8f8;
    border-color: #4da8da;
}

/* Scroll Area Styles */
QScrollArea {
    border: none;
    background-color: transparent;
}

QScrollArea > QWidget > QWidget {
    background-color: transparent;
}

/* Scroll Bar Styles */
QScrollBar:vertical {
    background-color: #e0e0e0;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar::handle:vertical:pressed {
    background-color: #808080;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0;
    width: 0;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: none;
}

/* Toolbar Styles */
QToolBar {
    border: none;
    background-color: transparent;
    spacing: 10px;
    padding: 5px;
}

QToolButton {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 5px;
    min-width: 32px;
    min-height: 32px;
}

QToolButton:hover {
    background-color: rgba(77, 168, 218, 0.2);
}

QToolButton:pressed {
    background-color: rgba(77, 168, 218, 0.4);
}

/* Status Indicator Styles */
QLabel[class="status-success"] {
    color: #00aa00;
    font-weight: bold;
}

QLabel[class="status-error"] {
    color: #aa0000;
    font-weight: bold;
}

QLabel[class="status-warning"] {
    color: #aa6600;
    font-weight: bold;
}

QLabel[class="status-info"] {
    color: #4da8da;
    font-weight: bold;
}

/* Camera Feed Styles */
QLabel[class="camera-header"] {
    font-size: 16px;
    padding: 10px;
    color: #4da8da;
    font-weight: bold;
    background-color: transparent;
}

QLabel[class="camera-feed"] {
    font-size: 18px;
    color: #888;
    padding: 20px;
    background-color: #1e2a44;
    border-radius: 10px;
    text-align: center;
}

/* Person List Styles */
QLabel[class="person-name"] {
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

QLabel[class="person-confidence"] {
    font-size: 12px;
    color: #666;
}

QLabel[class="person-department"] {
    font-size: 12px;
    color: #888;
}

QLabel[class="person-time"] {
    font-size: 11px;
    color: #aaa;
}

/* System Info Styles */
QLabel[class="system-title"] {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    padding: 10px;
}

QLabel[class="system-info"] {
    font-size: 12px;
    color: #666;
    padding: 5px 10px;
    line-height: 1.4;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.5s ease-in-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 800px) {
    QMainWindow {
        font-size: 12px;
    }
    
    QPushButton {
        padding: 6px 12px;
        min-width: 60px;
    }
    
    QLabel[class="header"] {
        font-size: 16px;
    }
    
    QFrame[class="card"] {
        padding: 8px;
        margin: 3px;
    }
}
