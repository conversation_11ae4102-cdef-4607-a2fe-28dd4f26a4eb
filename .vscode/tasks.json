{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build",
                "build",
                "--parallel"
            ],
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [
                "$gcc"
            ],
            "detail": "Build the entire project using CMake",
            "dependsOn": "configure"
        },
        {
            "label": "build-client",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build",
                "build",
                "--target",
                "client_app",
                "--parallel"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [
                "$gcc"
            ],
            "detail": "Build only the client application",
            "dependsOn": "configure"
        },
        {
            "label": "build-server",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build",
                "build",
                "--target",
                "server",
                "--parallel"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [
                "$gcc"
            ],
            "detail": "Build only the server application",
            "dependsOn": "configure"
        },
        {
            "label": "clean",
            "type": "shell",
            "command": "cmake",
            "args": [
                "--build",
                "build",
                "--target",
                "clean"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "detail": "Clean build artifacts"
        },
        {
            "label": "configure",
            "type": "shell",
            "command": "cmake",
            "args": [
                "-S",
                ".",
                "-B",
                "build",
                "-DCMAKE_BUILD_TYPE=Debug",
                "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "detail": "Configure CMake with Debug build type"
        },
        {
            "label": "configure-release",
            "type": "shell",
            "command": "cmake",
            "args": [
                "-S",
                ".",
                "-B",
                "build-release",
                "-DCMAKE_BUILD_TYPE=Release",
                "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "detail": "Configure CMake with Release build type"
        },
        {
            "label": "rebuild",
            "type": "shell",
            "command": "rm",
            "args": [
                "-rf",
                "build"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "detail": "Clean rebuild - remove build directory"
        },
        {
            "label": "run-client-gui",
            "type": "shell",
            "command": "./scripts/run_gui_app.sh",
            "args": [
                "--skip-opengl-setup"
            ],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "detail": "Run client application in GUI mode",
            "dependsOn": "build-client"
        },
        {
            "label": "run-client-headless",
            "type": "shell",
            "command": "./scripts/run_client.sh",
            "args": [
                "--headless"
            ],
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "detail": "Run client application in headless mode",
            "dependsOn": "build-client"
        },
        {
            "label": "setup-opengl",
            "type": "shell",
            "command": "./scripts/setup_opengl_container.sh",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "detail": "Setup OpenGL environment for GUI applications"
        },
        {
            "label": "setup-x11-auth",
            "type": "shell",
            "command": "./scripts/setup_x11_auth.sh",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": true
            },
            "detail": "Setup X11 authorization for GUI applications"
        },
        {
            "label": "format-code",
            "type": "shell",
            "command": "find",
            "args": [
                ".",
                "-name",
                "*.cpp",
                "-o",
                "-name",
                "*.hpp",
                "-o",
                "-name",
                "*.h",
                "-o",
                "-name",
                "*.c",
                "|",
                "xargs",
                "clang-format",
                "-i"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "detail": "Format all C/C++ source files using clang-format"
        },
        {
            "label": "analyze-code",
            "type": "shell",
            "command": "clang-tidy",
            "args": [
                "apps/client/src/*.cpp",
                "--",
                "-I${workspaceFolder}/apps/client/include",
                "-I${workspaceFolder}/libraries/shared/include"
            ],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared",
                "showReuseMessage": true,
                "clear": false
            },
            "problemMatcher": [
                "$gcc"
            ],
            "detail": "Run static code analysis using clang-tidy"
        }
    ]
}