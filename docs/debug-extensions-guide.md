# C++ Debug Extensions Guide

This guide covers the comprehensive C++ debugging setup for the C-AIBOX project, including all extensions, configurations, and debugging workflows.

## 🔧 Installed Extensions

### Core C++ Development
- **ms-vscode.cpptools** - C/C++ IntelliSense, debugging, and code browsing
- **ms-vscode.cpptools-extension-pack** - Complete C++ development pack
- **ms-vscode.cpptools-themes** - Enhanced syntax highlighting themes

### Build Systems
- **ms-vscode.cmake-tools** - CMake integration and project management
- **twxs.cmake** - CMake language support
- **ms-vscode.makefile-tools** - Makefile support

### Advanced Debugging Tools
- **llvm-vs-code-extensions.vscode-clangd** - Clang language server
- **vadimcn.vscode-lldb** - LLDB debugger integration
- **webfreak.debug** - Native debug adapter
- **ms-vscode.vscode-embedded-tools** - Embedded debugging tools

### Code Quality & Analysis
- **notskm.clang-tidy** - Static code analysis
- **xaver.clang-format** - Code formatting
- **cschlosser.doxdocgen** - Documentation generation
- **aaron-bond.better-comments** - Enhanced comment highlighting

### Memory & Performance Analysis
- **ms-vscode.vscode-memory-inspector** - Memory debugging and inspection
- **ms-vscode.hexeditor** - Binary file editing and analysis
- **intel.oneapi-analysis-configurator** - Performance analysis tools

### Qt Development
- **ms-vscode.qt-tools** - Qt framework support
- **tonka3000.qtvsctools** - Additional Qt development tools

### Productivity Tools
- **formulahendry.code-runner** - Quick code execution
- **augustocdias.tasks-shell-input** - Interactive task inputs
- **eamodio.gitlens** - Advanced Git integration

## 🚀 Debug Configurations

### Available Debug Modes

#### 1. **Debug Client (GUI)**
```json
{
    "name": "Debug Client (GUI)",
    "type": "cppdbg",
    "request": "launch",
    "program": "${workspaceFolder}/build/apps/client/client_app"
}
```
- Full GUI debugging with X11 forwarding
- WebEngine and Qt debugging support
- Breakpoints in GUI event handlers

#### 2. **Debug Client (Headless)**
```json
{
    "name": "Debug Client (Headless)",
    "type": "cppdbg",
    "request": "launch",
    "program": "${workspaceFolder}/build/apps/client/client_app",
    "args": ["--headless"]
}
```
- Headless mode for CI/testing
- Faster debugging without GUI overhead
- Memory and performance analysis

#### 3. **Debug Client (Memory Analysis)**
- Enhanced memory debugging with MALLOC_CHECK_
- Memory leak detection
- Buffer overflow detection
- Heap corruption analysis

#### 4. **Debug Client (LLDB)**
- Alternative debugger with LLDB
- Better C++ standard library support
- Enhanced pretty-printing

#### 5. **Debug Client (Core Dump)**
- Core dump generation on crashes
- Post-mortem debugging
- Crash analysis and stack traces

### Compound Configurations
- **Debug Client & Server (GUI)** - Debug both applications simultaneously
- **Debug Client & Server (Headless)** - Headless full-stack debugging

## 🛠️ Build Tasks

### Available Tasks

#### Build Tasks
- **build** - Default build (Debug, parallel)
- **build-client** - Build only client application
- **build-server** - Build only server application
- **build-release** - Release build configuration
- **clean** - Clean build artifacts
- **rebuild** - Complete rebuild from scratch

#### Configuration Tasks
- **configure** - Configure CMake (Debug)
- **configure-release** - Configure CMake (Release)

#### Run Tasks
- **run-client-gui** - Build and run client in GUI mode
- **run-client-headless** - Build and run client in headless mode

#### Setup Tasks
- **setup-opengl** - Configure OpenGL for GUI applications
- **setup-x11-auth** - Setup X11 authorization

#### Code Quality Tasks
- **format-code** - Format all C++ files with clang-format
- **analyze-code** - Run static analysis with clang-tidy

## 🔍 Debugging Workflows

### 1. **Standard GUI Debugging**
```bash
# Setup environment (first time)
Ctrl+Shift+P → "Tasks: Run Task" → "setup-opengl"

# Start debugging
F5 → Select "Debug Client (GUI)"
```

### 2. **Memory Debugging**
```bash
# Use memory analysis configuration
F5 → Select "Debug Client (Memory Analysis)"

# Or use Valgrind task
Ctrl+Shift+P → "Tasks: Run Task" → "run-valgrind-memcheck"
```

### 3. **Performance Debugging**
```bash
# Build with optimizations
Ctrl+Shift+P → "Tasks: Run Task" → "build-release"

# Profile with tools
F5 → Select "Debug Client (Performance)"
```

### 4. **Crash Analysis**
```bash
# Enable core dumps
F5 → Select "Debug Client (Core Dump)"

# Analyze crash
gdb ./build/apps/client/client_app core
```

## 🎯 IntelliSense Configuration

### Features Enabled
- **Enhanced Colorization** - Syntax highlighting for C++20
- **Error Squiggles** - Real-time error detection
- **Auto-completion** - Context-aware suggestions
- **Code Navigation** - Go to definition/declaration
- **Symbol Search** - Workspace-wide symbol lookup

### Include Paths
```json
"includePath": [
    "${workspaceFolder}/**",
    "${workspaceFolder}/apps/client/include",
    "${workspaceFolder}/apps/server/include",
    "${workspaceFolder}/libraries/shared/include",
    "/usr/include/x86_64-linux-gnu/qt5/**"
]
```

### Compiler Configuration
- **Standard**: C++20 / C17
- **Compiler**: GCC 11+
- **IntelliSense Mode**: linux-gcc-x64
- **Compile Commands**: Auto-generated by CMake

## 🧪 Testing Integration

### Unit Testing
```bash
# Build tests
Ctrl+Shift+P → "Tasks: Run Task" → "build-tests"

# Run tests with debugging
F5 → Select "Debug Tests"
```

### Integration Testing
```bash
# Test GUI components
F5 → Select "Debug Client (GUI)" → Set breakpoints in Qt code

# Test headless functionality
F5 → Select "Debug Client (Headless)"
```

## 🔧 Advanced Features

### Memory Inspector
- **Hex Editor** - Binary data inspection
- **Memory View** - Runtime memory analysis
- **Variable Watch** - Monitor variable changes
- **Call Stack** - Function call hierarchy

### Code Analysis
- **Clang-tidy** - Static analysis with 50+ checks
- **Clang-format** - Google style formatting
- **Documentation** - Doxygen comment generation

### Git Integration
- **GitLens** - Blame annotations and history
- **Git Graph** - Visual commit history
- **Diff View** - Side-by-side comparisons

## 📊 Performance Monitoring

### Profiling Tools
- **CPU Profiling** - Function-level performance analysis
- **Memory Profiling** - Heap and stack analysis
- **I/O Profiling** - File and network operations

### Metrics
- **Build Time** - CMake and compilation metrics
- **Test Coverage** - Code coverage analysis
- **Static Analysis** - Code quality metrics

## 🚨 Troubleshooting

### Common Issues

#### IntelliSense Not Working
```bash
# Regenerate compile commands
Ctrl+Shift+P → "C/C++: Reset IntelliSense Database"
Ctrl+Shift+P → "Tasks: Run Task" → "configure"
```

#### Debugger Not Attaching
```bash
# Check build configuration
Ctrl+Shift+P → "Tasks: Run Task" → "build"

# Verify debug symbols
file ./build/apps/client/client_app
```

#### Qt Debugging Issues
```bash
# Setup Qt environment
Ctrl+Shift+P → "Tasks: Run Task" → "setup-x11-auth"

# Check Qt installation
qmake --version
```

## 📚 Resources

### Documentation
- [VSCode C++ Documentation](https://code.visualstudio.com/docs/languages/cpp)
- [CMake Tools Documentation](https://github.com/microsoft/vscode-cmake-tools)
- [Qt Creator Integration](https://doc.qt.io/qtcreator/)

### Debugging Guides
- [GDB Quick Reference](https://sourceware.org/gdb/current/onlinedocs/gdb/)
- [LLDB Tutorial](https://lldb.llvm.org/use/tutorial.html)
- [Valgrind User Manual](https://valgrind.org/docs/manual/manual.html)

This comprehensive debugging setup provides professional-grade C++ development capabilities with full Qt5 GUI application support!
