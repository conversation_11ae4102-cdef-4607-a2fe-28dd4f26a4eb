# 🚀 Quick Start Guide

## One-Command Development Setup

```bash
# [HOST] Start everything
./scripts/start-dev-flow.sh
```

## 📋 Command Reference

### Host Commands (Run on your machine)
```bash
# Start complete development environment
./scripts/start-dev-flow.sh start

# Build/rebuild container only
./scripts/start-dev-flow.sh build

# Get shell in container
./scripts/start-dev-flow.sh shell

# Check environment status
./scripts/start-dev-flow.sh status

# Stop development container
./scripts/start-dev-flow.sh stop

# Clean up everything
./scripts/start-dev-flow.sh clean
```

### Container Commands (Run inside dev container)
```bash
# Setup container environment
./scripts/start-dev-flow.sh setup

# Build project
cmake --build build

# Run tests
./scripts/test.sh

# Format code
./scripts/format.sh

# Run GUI application
./scripts/dev/run-gui-app.sh

# Check GUI environment
./scripts/utils/setup-gui-env.sh status
```

## 🔄 Typical Development Flow

### First Time Setup
```bash
# [HOST] Clone and enter project
git clone <repository>
cd c-aibox

# [HOST] Start development environment
./scripts/start-dev-flow.sh start

# [CONTAINER] You're now inside the container!
# Build the project
cmake --build build

# [CONTAINER] Run tests
./scripts/test.sh

# [CONTAINER] Run your application
./build/your-app
```

### Daily Development
```bash
# [HOST] Start container (if not running)
./scripts/start-dev-flow.sh shell

# [CONTAINER] Make code changes...
# [CONTAINER] Build incrementally
cmake --build build

# [CONTAINER] Test changes
./scripts/test.sh

# [CONTAINER] Run application
./build/your-app
```

## 🐛 Quick Troubleshooting

### Container won't start
```bash
# [HOST] Check Docker
docker --version
docker info

# [HOST] Rebuild container
./scripts/start-dev-flow.sh build
```

### GUI apps don't work
```bash
# [HOST] Check X11
echo $DISPLAY
xset q

# [CONTAINER] Fix GUI environment
./scripts/utils/setup-gui-env.sh setup
```

### Build fails
```bash
# [CONTAINER] Clean and rebuild
./scripts/clean.sh
cmake -S . -B build
cmake --build build
```

### GPU acceleration issues
```bash
# [HOST] Check DRI devices
ls -la /dev/dri/

# [HOST] Create render node if missing
./scripts/utils/create-render-node.sh

# [CONTAINER] Test OpenGL
glxinfo | grep "OpenGL renderer"
```

## 📁 Key Directories

```
c-aibox/
├── .devcontainer/          # Container configuration
├── scripts/                # All development scripts
│   ├── start-dev-flow.sh  # Main development script
│   ├── DEV_FLOW.md        # Detailed documentation
│   ├── gui/               # GUI setup scripts
│   ├── utils/             # Utility scripts
│   └── dev/               # Development helpers
├── src/                   # Your source code
└── build/                 # Build output (created automatically)
```

## 🎯 Environment Context

### When you see `[HOST]` - Run on your machine
- Outside any container
- Has access to Docker commands
- Manages container lifecycle
- Handles X11 server and GPU drivers

### When you see `[CONTAINER]` - Run inside dev container
- Inside the development container
- Has development tools installed
- Mounts your source code
- Connects to host X11 and GPU

## 🆘 Need Help?

1. **Check status**: `./scripts/start-dev-flow.sh status`
2. **Read full docs**: `scripts/DEV_FLOW.md`
3. **GUI issues**: `scripts/gui/README.md`
4. **Check logs**: Look in `/tmp/` for log files

## 💡 Pro Tips

- Use `./scripts/start-dev-flow.sh status` to check everything
- The container auto-mounts your source code - changes are immediate
- Use incremental builds: `cmake --build build` (faster than full rebuild)
- GUI apps work automatically if X11 is properly forwarded
- All your development tools are pre-installed in the container
