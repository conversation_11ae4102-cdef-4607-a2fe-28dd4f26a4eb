#!/bin/bash

# <PERSON>ript to install and start X11 server on host machine
# Supports Linux, macOS, and Windows (WSL2)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to detect OS
detect_os() {
    # Check for WSL first
    if [ -f /proc/version ] && grep -q Microsoft /proc/version 2>/dev/null; then
        echo "wsl"
        return
    fi

    # Check for Linux
    if [ "$(uname -s)" = "Linux" ]; then
        echo "linux"
        return
    fi

    # Check for macOS
    if [ "$(uname -s)" = "Darwin" ]; then
        echo "macos"
        return
    fi

    # Check for Windows (Cygwin/MSYS)
    case "$(uname -s)" in
        CYGWIN*|MINGW*|MSYS*)
            echo "windows"
            return
            ;;
    esac

    # Fallback to unknown
    echo "unknown"
}

# Function to setup X11 on Linux
setup_linux() {
    print_info "Setting up X11 on Linux..."
    
    # Check if X11 is already running
    if pgrep -x "Xorg" > /dev/null || pgrep -x "X" > /dev/null; then
        print_success "X11 server is already running"
        print_info "DISPLAY: ${DISPLAY:-:0}"
        return 0
    fi
    
    # Install X11 if not present
    if command -v apt-get >/dev/null 2>&1; then
        print_info "Installing X11 packages (Ubuntu/Debian)..."
        sudo apt-get update
        sudo apt-get install -y xorg xserver-xorg-core xserver-xorg-input-all
    elif command -v yum >/dev/null 2>&1; then
        print_info "Installing X11 packages (RHEL/CentOS)..."
        sudo yum groupinstall -y "X Window System"
    elif command -v dnf >/dev/null 2>&1; then
        print_info "Installing X11 packages (Fedora)..."
        sudo dnf groupinstall -y "X Window System"
    else
        print_error "Package manager not supported. Please install X11 manually."
        return 1
    fi
    
    # Setup X11 forwarding for containers
    print_info "Configuring X11 for container access..."
    
    # Allow local connections
    if command -v xhost >/dev/null 2>&1; then
        xhost +local:docker 2>/dev/null || true
        xhost +local:root 2>/dev/null || true
        print_success "X11 access configured for containers"
    fi
    
    print_success "Linux X11 setup completed"
    print_info "DISPLAY: ${DISPLAY:-:0}"
}

# Function to setup X11 on macOS
setup_macos() {
    print_info "Setting up X11 on macOS..."
    
    # Check if XQuartz is installed
    if [[ -d "/Applications/Utilities/XQuartz.app" ]] || command -v xquartz >/dev/null 2>&1; then
        print_success "XQuartz is already installed"
    else
        print_info "Installing XQuartz..."
        
        # Check if Homebrew is available
        if command -v brew >/dev/null 2>&1; then
            brew install --cask xquartz
        else
            print_warning "Homebrew not found. Please install XQuartz manually:"
            print_info "1. Download from: https://www.xquartz.org/"
            print_info "2. Install the .dmg file"
            print_info "3. Restart your Mac"
            print_info "4. Run this script again"
            return 1
        fi
    fi
    
    # Start XQuartz if not running
    if ! pgrep -f "XQuartz" > /dev/null; then
        print_info "Starting XQuartz..."
        open -a XQuartz
        sleep 3
    fi
    
    # Configure XQuartz for network connections
    print_info "Configuring XQuartz for container access..."
    
    # Set DISPLAY for container access
    export DISPLAY=:0
    
    # Allow network connections (this requires manual configuration in XQuartz preferences)
    print_warning "Manual configuration required:"
    print_info "1. Open XQuartz"
    print_info "2. Go to XQuartz > Preferences > Security"
    print_info "3. Check 'Allow connections from network clients'"
    print_info "4. Restart XQuartz"
    
    # Allow local connections
    if command -v xhost >/dev/null 2>&1; then
        xhost +localhost 2>/dev/null || true
        xhost +127.0.0.1 2>/dev/null || true
        print_success "X11 access configured for containers"
    fi
    
    print_success "macOS X11 setup completed"
    print_info "DISPLAY: $DISPLAY"
}

# Function to setup X11 on Windows/WSL2
setup_windows() {
    print_info "Setting up X11 on Windows/WSL2..."
    
    # Check if we're in WSL
    if grep -q Microsoft /proc/version 2>/dev/null; then
        print_info "Detected WSL2 environment"
        
        # Get Windows host IP
        WINDOWS_HOST=$(ip route show default | awk '{print $3}')
        export DISPLAY="$WINDOWS_HOST:0"
        
        print_info "Windows host IP: $WINDOWS_HOST"
        print_info "DISPLAY set to: $DISPLAY"
        
        print_warning "X11 server required on Windows host:"
        print_info "Recommended options:"
        print_info "1. VcXsrv (recommended): https://sourceforge.net/projects/vcxsrv/"
        print_info "2. Xming: https://sourceforge.net/projects/xming/"
        print_info "3. X410 (Microsoft Store): https://x410.dev/"
        print_info ""
        print_info "VcXsrv configuration:"
        print_info "- Display number: 0"
        print_info "- Start no client: checked"
        print_info "- Clipboard: checked"
        print_info "- Primary Selection: checked"
        print_info "- Native opengl: unchecked"
        print_info "- Disable access control: checked"
        
    else
        print_info "Detected native Windows environment"
        print_warning "Please install an X11 server:"
        print_info "1. Download VcXsrv: https://sourceforge.net/projects/vcxsrv/"
        print_info "2. Install and run XLaunch"
        print_info "3. Configure as described above"
    fi
    
    print_success "Windows X11 setup instructions provided"
}

# Function to test X11 connection
test_x11() {
    print_info "Testing X11 connection..."
    
    if [[ -z "$DISPLAY" ]]; then
        print_error "DISPLAY environment variable not set"
        return 1
    fi
    
    print_info "DISPLAY: $DISPLAY"
    
    # Test with xdpyinfo if available
    if command -v xdpyinfo >/dev/null 2>&1; then
        if timeout 5s xdpyinfo >/dev/null 2>&1; then
            print_success "X11 connection successful"
            return 0
        else
            print_error "X11 connection failed"
            return 1
        fi
    fi
    
    # Test with xset if available
    if command -v xset >/dev/null 2>&1; then
        if timeout 5s xset q >/dev/null 2>&1; then
            print_success "X11 connection successful"
            return 0
        else
            print_error "X11 connection failed"
            return 1
        fi
    fi
    
    print_warning "Cannot test X11 connection (no test tools available)"
    return 0
}

# Function to create container environment file
create_container_env() {
    print_info "Creating container environment configuration..."
    
    cat > ./scripts/container_x11_env.sh << EOF
#!/bin/bash
# X11 environment for container
# Source this file before running GUI applications in container

export DISPLAY=${DISPLAY:-:0}
export QT_X11_NO_MITSHM=1
export QT_QPA_PLATFORM=xcb
export QTWEBENGINE_DISABLE_SANDBOX=1
export LIBGL_ALWAYS_INDIRECT=1
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false"

echo "Container X11 environment configured:"
echo "DISPLAY: \$DISPLAY"
echo "QT_QPA_PLATFORM: \$QT_QPA_PLATFORM"
EOF
    
    chmod +x ./scripts/container_x11_env.sh
    print_success "Container environment file created: ./scripts/container_x11_env.sh"
}

# Main function
main() {
    print_info "X11 Server Setup Script for C-AIBOX GUI"
    print_info "========================================"
    
    OS=$(detect_os)
    print_info "Detected OS: $OS"
    
    case $OS in
        "linux")
            setup_linux
            ;;
        "macos")
            print_info "For detailed macOS setup, run: ./scripts/setup_xquartz_macos.sh"
            setup_macos
            ;;
        "wsl"|"windows")
            print_info "For Windows setup, run PowerShell as Administrator:"
            print_info "  ./scripts/setup_vcxsrv_windows.ps1"
            setup_windows
            ;;
        *)
            print_error "Unsupported operating system: $OSTYPE"
            print_info "Supported platforms:"
            print_info "- Linux (native X11)"
            print_info "- macOS (XQuartz) - run: ./scripts/setup_xquartz_macos.sh"
            print_info "- Windows (VcXsrv) - run: ./scripts/setup_vcxsrv_windows.ps1"
            exit 1
            ;;
    esac
    
    # Test X11 connection
    if test_x11; then
        print_success "X11 server is ready for container GUI applications"
    else
        print_warning "X11 connection test failed - GUI may not work"
    fi
    
    # Create container environment
    create_container_env
    
    print_info ""
    print_success "Setup completed!"
    print_info ""
    print_info "Next steps:"
    print_info "1. Restart your dev container if it's already running"
    print_info "2. In container, run: source ./scripts/container_x11_env.sh"
    print_info "3. Test GUI: ./scripts/run_client.sh --gui"
    print_info "4. If GUI fails, use: ./scripts/run_client.sh --headless"
    print_info ""
    print_info "Troubleshooting:"
    print_info "- Check DISPLAY: echo \$DISPLAY"
    print_info "- Test X11: xdpyinfo"
    print_info "- Allow container access: xhost +local:docker"
}

# Run main function
main "$@"
