#!/bin/bash

# Simple Docker Deployment Script for Orange Pi
# Uses pre-built ARM64 binaries to create and deploy Docker images

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BOLD}${CYAN}=== $1 ===${NC}"; }

# Default configuration
ORANGE_PI_IP=""
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
SSH_KEY=""
IMAGE_NAME="c-aibox-arm64"
IMAGE_TAG="latest"
CONTAINER_NAME="c-aibox"
HOST_PORT="8090"
BUILD_DIR="build-container"

# Load SSH configuration if available
if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
    source "$PROJECT_ROOT/.orangepi_ssh"
    SSH_KEY="$ORANGE_PI_SSH_KEY"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip) ORANGE_PI_IP="$2"; shift 2 ;;
        --user) ORANGE_PI_USER="$2"; shift 2 ;;
        --port) ORANGE_PI_PORT="$2"; shift 2 ;;
        --ssh-key) SSH_KEY="$2"; shift 2 ;;
        --image-name) IMAGE_NAME="$2"; shift 2 ;;
        --container-name) CONTAINER_NAME="$2"; shift 2 ;;
        --host-port) HOST_PORT="$2"; shift 2 ;;
        --build-dir) BUILD_DIR="$2"; shift 2 ;;
        --help)
            cat << EOF
Simple Docker Deployment Script for Orange Pi

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP                 Orange Pi IP address

OPTIONS:
    --user USER             SSH username (default: orangepi)
    --port PORT             SSH port (default: 22)
    --ssh-key PATH          SSH private key file
    --image-name NAME       Docker image name (default: c-aibox-arm64)
    --container-name NAME   Container name (default: c-aibox)
    --host-port PORT        Host port mapping (default: 8090)
    --build-dir DIR         Build directory (default: build-container)
    --help                  Show this help

EXAMPLES:
    $0 --ip ***************                           # Basic deployment
    $0 --ip *************** --host-port 9000          # Custom port
    $0 --ip *************** --container-name my-app   # Custom container name
EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    log_info "Usage: $0 --ip IP_ADDRESS"
    exit 1
fi

# SSH command builder
build_ssh_cmd() {
    local ssh_cmd="ssh"
    if [[ -n "$SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY"
    fi
    ssh_cmd="$ssh_cmd -p $ORANGE_PI_PORT $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "$ssh_cmd"
}

# SCP command builder
build_scp_cmd() {
    local scp_cmd="scp"
    if [[ -n "$SSH_KEY" ]]; then
        scp_cmd="$scp_cmd -i $SSH_KEY"
    fi
    scp_cmd="$scp_cmd -P $ORANGE_PI_PORT"
    echo "$scp_cmd"
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"
    local ssh_cmd=$(build_ssh_cmd)
    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
        exit 1
    fi
}

# Check Docker on Orange Pi
check_docker_on_device() {
    log_step "Checking Docker on Orange Pi"
    local ssh_cmd=$(build_ssh_cmd)
    if $ssh_cmd "docker --version" >/dev/null 2>&1; then
        local docker_version=$($ssh_cmd "docker --version")
        log_success "Docker found: $docker_version"
    else
        log_error "Docker not found on Orange Pi"
        exit 1
    fi
}

# Check local build
check_local_build() {
    log_step "Checking Local Build"
    if [[ ! -d "$PROJECT_ROOT/$BUILD_DIR" ]]; then
        log_error "Build directory not found: $PROJECT_ROOT/$BUILD_DIR"
        log_info "Run cross-compilation first: ./scripts/cross-build/build-orangepi-docker.sh"
        exit 1
    fi
    
    local server_binary="$PROJECT_ROOT/$BUILD_DIR/bin/server"
    if [[ ! -f "$server_binary" ]]; then
        log_error "Server binary not found: $server_binary"
        exit 1
    fi
    
    log_success "Found ARM64 server binary"
}

# Transfer files and build image on Orange Pi
build_and_deploy() {
    log_step "Building Docker Image on Orange Pi"
    
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    
    # Create build directory on Orange Pi
    $ssh_cmd "mkdir -p /home/<USER>/docker-build/build-container/bin"
    
    # Transfer server binary
    log_info "Transferring server binary..."
    $scp_cmd "$PROJECT_ROOT/$BUILD_DIR/bin/server" "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/docker-build/build-container/bin/"
    
    # Transfer .env file
    log_info "Transferring configuration..."
    $scp_cmd "$PROJECT_ROOT/.env" "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/docker-build/"
    
    # Create Dockerfile on Orange Pi
    log_info "Creating Dockerfile..."
    $ssh_cmd "cat > /home/<USER>/docker-build/Dockerfile << 'EOF'
FROM arm64v8/debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    libstdc++6 \\
    libc6 \\
    libgcc-s1 \\
    ca-certificates \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy the ARM64 server binary
COPY build-container/bin/server /app/server

# Copy configuration files
COPY .env /app/.env

# Make server executable
RUN chmod +x /app/server

# Create models directory
RUN mkdir -p /app/models

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:8080/health || exit 1

# Run the server
CMD [\"./server\"]
EOF"
    
    # Build Docker image on Orange Pi
    log_info "Building Docker image..."
    $ssh_cmd "cd /home/<USER>/docker-build && docker build -t $IMAGE_NAME:$IMAGE_TAG ."
    
    log_success "Docker image built successfully"
}

# Stop and remove old container
cleanup_old_container() {
    log_step "Cleaning Up Old Container"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Stop and remove old container
    $ssh_cmd "docker ps -a --filter name=$CONTAINER_NAME --format '{{.Names}}' | xargs -r docker rm -f" || true
    
    log_success "Old container cleaned up"
}

# Start new container
start_container() {
    log_step "Starting Docker Container"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Run container
    log_info "Starting container: $CONTAINER_NAME on port $HOST_PORT"
    $ssh_cmd "docker run -d --name $CONTAINER_NAME --restart unless-stopped -p $HOST_PORT:8080 $IMAGE_NAME:$IMAGE_TAG"
    
    # Wait for container to start
    sleep 5
    
    # Check container status
    local container_status=$($ssh_cmd "docker ps --filter name=$CONTAINER_NAME --format '{{.Status}}'")
    if [[ -n "$container_status" ]]; then
        log_success "Container started: $container_status"
    else
        log_error "Container failed to start"
        $ssh_cmd "docker logs $CONTAINER_NAME" || true
        exit 1
    fi
}

# Verify deployment
verify_deployment() {
    log_step "Verifying Deployment"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Wait for service to be ready
    log_info "Waiting for service to be ready..."
    sleep 10
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    if $ssh_cmd "curl -f http://localhost:$HOST_PORT/health" >/dev/null 2>&1; then
        local health_response=$($ssh_cmd "curl -s http://localhost:$HOST_PORT/health")
        log_success "Health check passed"
        log_info "Response: $health_response"
    else
        log_warning "Health check failed"
        log_info "Container logs:"
        $ssh_cmd "docker logs $CONTAINER_NAME | tail -10" || true
    fi
}

# Cleanup build files
cleanup_build_files() {
    log_step "Cleaning Up Build Files"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Remove build directory
    $ssh_cmd "rm -rf /home/<USER>/docker-build"
    
    log_success "Build files cleaned up"
}

# Show deployment summary
show_summary() {
    log_step "Deployment Summary"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    echo "Orange Pi: $ORANGE_PI_USER@$ORANGE_PI_IP:$ORANGE_PI_PORT"
    echo "Docker Image: $IMAGE_NAME:$IMAGE_TAG"
    echo "Container: $CONTAINER_NAME"
    echo "Port: $HOST_PORT"
    echo ""
    
    # Show container info
    log_info "Container status:"
    $ssh_cmd "docker ps --filter name=$CONTAINER_NAME"
    
    echo ""
    log_success "Docker deployment completed successfully!"
    log_info "Access the service at: http://$ORANGE_PI_IP:$HOST_PORT"
    log_info "Health check: http://$ORANGE_PI_IP:$HOST_PORT/health"
    
    echo ""
    log_info "Management commands:"
    log_info "  View logs: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'docker logs $CONTAINER_NAME'"
    log_info "  Stop: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'docker stop $CONTAINER_NAME'"
    log_info "  Start: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'docker start $CONTAINER_NAME'"
    log_info "  Remove: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'docker rm -f $CONTAINER_NAME'"
}

# Main execution
main() {
    log_step "Simple Docker Deployment to Orange Pi"
    
    test_ssh_connection
    check_docker_on_device
    check_local_build
    build_and_deploy
    cleanup_old_container
    start_container
    verify_deployment
    cleanup_build_files
    show_summary
}

# Run main function
main "$@"
