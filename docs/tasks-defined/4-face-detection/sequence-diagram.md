# Face Detection NPU Module Sequence Diagrams

This document provides detailed sequence diagrams for the Face Detection NPU Module, illustrating the logical flow and component interactions for neural network inference on the RK3588 NPU.

## Module Initialization Sequence

```mermaid
sequenceDiagram
    participant Main as Main Application
    participant Detector as FaceDetectionModule
    participant ModelMgr as ModelManager
    participant NPU as NPUManager
    participant ThreadPool as InferenceThreadPool
    
    Main->>Detector: initialize(config)
    activate Detector
    
    Detector->>ModelMgr: loadDetectionModel(model_path)
    activate ModelMgr
    
    ModelMgr->>ModelMgr: readModelFile(model_path)
    
    ModelMgr->>NPU: createRKNNContext()
    activate NPU
    
    NPU->>NPU: prepareInitExtend(flags)
    Note over NPU: Set RKNN_FLAG_MEM_ALLOC_OUTSIDE,<br/>RKNN_FLAG_ASYNC_MODE flags
    
    NPU->>NPU: rknn_init2(&ctx, model_data, model_size, flags, &init_extend)
    NPU-->>ModelMgr: context handle
    
    ModelMgr->>NPU: rknn_set_core_mask(ctx, RKNN_NPU_CORE_AUTO)
    Note over ModelMgr,NPU: Optionally use RKNN_NPU_CORE_0_1_2<br/>for multi-core processing
    NPU-->>ModelMgr: core mask set status
    
    ModelMgr->>NPU: rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num)
    NPU-->>ModelMgr: input/output counts
    
    loop For each input/output
        ModelMgr->>NPU: rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &attr)
        NPU-->>ModelMgr: input attributes
        
        ModelMgr->>NPU: rknn_query(ctx, RKNN_QUERY_OUTPUT_ATTR, &attr)
        NPU-->>ModelMgr: output attributes
    end
    
    ModelMgr-->>Detector: model loaded
    deactivate ModelMgr
    
    Detector->>ThreadPool: createThreadPool(worker_count)
    activate ThreadPool
    ThreadPool-->>Detector: thread pool ready
    deactivate ThreadPool
    
    Detector-->>Main: initialization complete
    deactivate Detector
    deactivate NPU
```

## Zero-Copy Input Tensor Setup Sequence

```mermaid
sequenceDiagram
    participant InQueue as InputQueue
    participant Worker as InferenceWorker
    participant NPU as NPUManager
    participant BufferMgr as BufferManager
    
    activate Worker
    Worker->>InQueue: dequeueFrame()
    activate InQueue
    InQueue-->>Worker: preprocessed frame with DMABUF fd
    deactivate InQueue
    
    Worker->>BufferMgr: getInputTensorFromDMABUF(fd, tensor_info)
    activate BufferMgr
    
    BufferMgr->>NPU: rknn_create_mem_from_fd(ctx, fd, input_size)
    activate NPU
    NPU-->>BufferMgr: rknn_tensor_mem
    deactivate NPU
    
    BufferMgr-->>Worker: tensor memory
    deactivate BufferMgr
    
    Worker->>NPU: rknn_set_io_mem(ctx, input_idx, tensor_mem)
    activate NPU
    NPU-->>Worker: set status
    deactivate NPU
    
    Worker->>Worker: proceedWithInference()
    deactivate Worker
```

## Batch Processing Sequence

```mermaid
sequenceDiagram
    participant Scheduler as BatchScheduler
    participant Worker as InferenceWorker
    participant NPU as NPUManager
    participant OutQueue as OutputQueue
    
    activate Scheduler
    
    Scheduler->>Scheduler: collectFrameBatch(max_batch_size, timeout)
    
    alt Single Frame Processing
        Scheduler->>Worker: processSingleFrame(frame)
        activate Worker
        
        Worker->>NPU: setupInputTensor(frame)
        activate NPU
        NPU-->>Worker: input ready
        
        Worker->>NPU: rknn_run(ctx, NULL)
        NPU-->>Worker: inference complete
        
        Worker->>NPU: rknn_outputs_get(ctx, outputs)
        NPU-->>Worker: detection results
        
        Worker->>Worker: processDetectionResults()
        Worker->>NPU: rknn_outputs_release(ctx, outputs)
        NPU-->>Worker: outputs released
        deactivate NPU
        
        Worker-->>Scheduler: detection results
        deactivate Worker
        
    else Batch Processing
        Scheduler->>Worker: processBatch(frames)
        activate Worker
        
        loop For Each Frame in Batch
            Worker->>NPU: setupBatchInputTensor(frame, batch_idx)
            activate NPU
            NPU-->>Worker: input ready
            deactivate NPU
        end
        
        Worker->>NPU: rknn_inputs_set(ctx, io_num.n_input, inputs)
        activate NPU
        NPU-->>Worker: inputs set
        
        Worker->>NPU: rknn_run(ctx, NULL)
        NPU-->>Worker: batch inference complete
        
        Worker->>NPU: rknn_outputs_get(ctx, outputs)
        NPU-->>Worker: batch detection results
        
        Worker->>Worker: processBatchResults()
        Worker->>NPU: rknn_outputs_release(ctx, outputs)
        NPU-->>Worker: outputs released
        deactivate NPU
        
        Worker-->>Scheduler: batch detection results
        deactivate Worker
    end
    
    Scheduler->>OutQueue: enqueueDetectionResults(results)
    activate OutQueue
    OutQueue-->>Scheduler: enqueue status
    deactivate OutQueue
    
    deactivate Scheduler
```

## Asynchronous Inference Sequence

```mermaid
sequenceDiagram
    participant Worker as InferenceWorker
    participant NPU as NPUManager
    participant Callback as AsyncCallback
    participant OutQueue as OutputQueue
    
    activate Worker
    Worker->>NPU: setupInputTensor(frame)
    activate NPU
    NPU-->>Worker: input ready
    
    Worker->>NPU: rknn_run(ctx, NULL)
    Note over Worker,NPU: With RKNN_FLAG_ASYNC_MODE
    NPU-->>Worker: execution started (returns immediately)
    deactivate NPU
    
    Worker->>Worker: continueProcessingOtherTasks()
    
    activate Callback
    Callback->>NPU: pollInferenceCompletion(ctx, timeout)
    activate NPU
    
    alt Inference Complete
        NPU-->>Callback: inference complete
        
        Callback->>NPU: rknn_outputs_get(ctx, outputs)
        NPU-->>Callback: detection results
        
        Callback->>Callback: processDetectionResults()
        Callback->>NPU: rknn_outputs_release(ctx, outputs)
        NPU-->>Callback: outputs released
        
        Callback->>OutQueue: enqueueDetectionResults(results, frame_metadata)
        activate OutQueue
        OutQueue-->>Callback: enqueue status
        deactivate OutQueue
        
    else Not Complete Yet
        NPU-->>Callback: still processing
        Callback->>Callback: reschedulePolling()
    end
    
    deactivate NPU
    deactivate Callback
    deactivate Worker
```

## Detection Result Post-Processing Sequence

```mermaid
sequenceDiagram
    participant Worker as InferenceWorker
    participant PostProc as PostProcessor
    participant NMS as NMSProcessor
    participant OutQueue as OutputQueue
    
    activate Worker
    Worker->>Worker: getInferenceResults()
    
    Worker->>PostProc: decodeDetectionResults(outputs, anchors)
    activate PostProc
    
    PostProc->>PostProc: extractBoundingBoxes()
    PostProc->>PostProc: extractConfidenceScores()
    PostProc->>PostProc: extractLandmarks()
    
    PostProc->>PostProc: applyConfidenceThreshold(threshold)
    
    PostProc->>NMS: performNMS(boxes, scores, iou_threshold)
    activate NMS
    
    NMS->>NMS: sortDetectionsByConfidence()
    NMS->>NMS: calculateBoxOverlaps()
    NMS->>NMS: suppressOverlappingBoxes()
    
    NMS-->>PostProc: filtered detections
    deactivate NMS
    
    PostProc->>PostProc: scaleToOriginalResolution(detections, original_size)
    
    PostProc-->>Worker: final detection results
    deactivate PostProc
    
    Worker->>OutQueue: enqueueProcessedResults(detections, frame_metadata)
    activate OutQueue
    OutQueue-->>Worker: enqueue status
    deactivate OutQueue
    
    deactivate Worker
```

## Multi-Core NPU Utilization Sequence

```mermaid
sequenceDiagram
    participant App as Application
    participant NPUMgr as NPUResourceManager
    participant Context as RKNNContext
    participant Worker as InferenceWorker
    
    App->>NPUMgr: initializeMultiCoreStrategy(strategy_type)
    activate NPUMgr
    
    alt Single Model Multi-Core Strategy
        NPUMgr->>Context: createContext(model_path)
        activate Context
        
        Context->>Context: rknn_init2(...)
        Context->>Context: rknn_set_core_mask(ctx, RKNN_NPU_CORE_0_1_2)
        
        Context-->>NPUMgr: context with all cores assigned
        deactivate Context
        
    else Multi-Model Core Distribution
        NPUMgr->>NPUMgr: createMultipleContexts(model_path)
        
        NPUMgr->>Context: createContext1(model_path)
        activate Context
        Context->>Context: rknn_set_core_mask(ctx1, RKNN_NPU_CORE_0)
        Context-->>NPUMgr: context1 on core 0
        deactivate Context
        
        NPUMgr->>Context: createContext2(model_path)
        activate Context
        Context->>Context: rknn_set_core_mask(ctx2, RKNN_NPU_CORE_1)
        Context-->>NPUMgr: context2 on core 1
        deactivate Context
        
        NPUMgr->>Context: createContext3(model_path)
        activate Context
        Context->>Context: rknn_set_core_mask(ctx3, RKNN_NPU_CORE_2)
        Context-->>NPUMgr: context3 on core 2
        deactivate Context
    end
    
    NPUMgr-->>App: NPU core strategy initialized
    deactivate NPUMgr
    
    activate Worker
    Worker->>NPUMgr: getAvailableContext()
    activate NPUMgr
    
    alt Load Balancing
        NPUMgr->>NPUMgr: selectLeastLoadedContext()
        NPUMgr-->>Worker: selected_context
    else Round Robin
        NPUMgr->>NPUMgr: getNextContextInSequence()
        NPUMgr-->>Worker: next_context
    end
    
    deactivate NPUMgr
    
    Worker->>Worker: performInference(selected_context)
    deactivate Worker
```

## Error Handling Sequence

```mermaid
sequenceDiagram
    participant Worker as InferenceWorker
    participant ErrorHandler as ErrorHandler
    participant NPU as NPUManager
    participant App as Application
    
    activate Worker
    Worker->>Worker: detectError(error_type)
    Worker->>ErrorHandler: handleInferenceError(error_type, details)
    activate ErrorHandler
    
    alt Memory Allocation Error
        ErrorHandler->>ErrorHandler: logMemoryError(details)
        ErrorHandler->>ErrorHandler: attemptMemoryRecovery()
        ErrorHandler-->>Worker: RETRY_WITH_REDUCED_BATCH
        
    else Inference Timeout
        ErrorHandler->>NPU: rknn_destroy(problematic_ctx)
        activate NPU
        NPU-->>ErrorHandler: context destroyed
        
        ErrorHandler->>NPU: recreateContext(model_path)
        NPU-->>ErrorHandler: new context
        deactivate NPU
        
        ErrorHandler-->>Worker: RETRY_WITH_NEW_CONTEXT
        
    else Model Error
        ErrorHandler->>ErrorHandler: logModelError(details)
        ErrorHandler->>App: notifyModelError(details)
        ErrorHandler-->>Worker: SKIP_FRAME
        
    else Fatal NPU Error
        ErrorHandler->>App: notifyNPUFailure(details)
        ErrorHandler-->>Worker: TERMINATE_WORKER
    end
    
    deactivate ErrorHandler
    
    alt Action is RETRY_WITH_REDUCED_BATCH
        Worker->>Worker: reduceBatchSize()
        Worker->>Worker: retryInference()
    else Action is RETRY_WITH_NEW_CONTEXT
        Worker->>Worker: updateContextReference()
        Worker->>Worker: retryInference()
    else Action is SKIP_FRAME
        Worker->>Worker: discardCurrentFrame()
        Worker->>Worker: processNextFrame()
    else Action is TERMINATE_WORKER
        Worker->>Worker: cleanupResources()
        Worker->>Worker: signalTermination()
        deactivate Worker
    end
```

## Module Shutdown Sequence

```mermaid
sequenceDiagram
    participant App as Application
    participant Detector as FaceDetectionModule
    participant ThreadPool as InferenceThreadPool
    participant ModelMgr as ModelManager
    participant NPU as NPUManager
    
    App->>Detector: shutdown()
    activate Detector
    
    Detector->>ThreadPool: shutdownWorkers(timeout)
    activate ThreadPool
    ThreadPool-->>Detector: workers terminated
    deactivate ThreadPool
    
    Detector->>ModelMgr: unloadModels()
    activate ModelMgr
    
    loop For Each Model Context
        ModelMgr->>NPU: rknn_destroy(ctx)
        activate NPU
        NPU-->>ModelMgr: context destroyed
        deactivate NPU
    end
    
    ModelMgr-->>Detector: models unloaded
    deactivate ModelMgr
    
    Detector->>Detector: releaseResources()
    Detector-->>App: shutdown complete
    deactivate Detector
```
