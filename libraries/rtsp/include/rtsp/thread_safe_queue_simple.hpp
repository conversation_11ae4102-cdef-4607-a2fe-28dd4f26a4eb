#pragma once

#include <atomic>
#include <memory>
#include <chrono>
#include <condition_variable>
#include <mutex>
#include <queue>
#include <vector>

namespace aibox {
namespace rtsp {

/**
 * @brief Simplified thread-safe queue for immediate use
 * 
 * This is a working implementation of the thread-safe queue system
 * that can be immediately integrated into the Stream Multiplexer.
 */
template<typename T>
class SimpleThreadSafeQueue {
public:
    explicit SimpleThreadSafeQueue(size_t max_size = 1000) 
        : max_size_(max_size), should_stop_(false) {}
    
    ~SimpleThreadSafeQueue() {
        stop();
    }
    
    // Basic queue operations
    bool enqueue(const T& item, int timeout_ms = -1) {
        std::unique_lock<std::mutex> lock(mutex_);
        
        if (timeout_ms >= 0) {
            auto timeout = std::chrono::milliseconds(timeout_ms);
            if (!condition_not_full_.wait_for(lock, timeout, 
                [this] { return queue_.size() < max_size_ || should_stop_; })) {
                return false;
            }
        } else {
            condition_not_full_.wait(lock, 
                [this] { return queue_.size() < max_size_ || should_stop_; });
        }
        
        if (should_stop_) {
            return false;
        }
        
        queue_.push(item);
        condition_not_empty_.notify_one();
        return true;
    }
    
    bool enqueue(T&& item, int timeout_ms = -1) {
        std::unique_lock<std::mutex> lock(mutex_);
        
        if (timeout_ms >= 0) {
            auto timeout = std::chrono::milliseconds(timeout_ms);
            if (!condition_not_full_.wait_for(lock, timeout, 
                [this] { return queue_.size() < max_size_ || should_stop_; })) {
                return false;
            }
        } else {
            condition_not_full_.wait(lock, 
                [this] { return queue_.size() < max_size_ || should_stop_; });
        }
        
        if (should_stop_) {
            return false;
        }
        
        queue_.push(std::move(item));
        condition_not_empty_.notify_one();
        return true;
    }
    
    bool dequeue(T& item, int timeout_ms = -1) {
        std::unique_lock<std::mutex> lock(mutex_);
        
        if (timeout_ms >= 0) {
            auto timeout = std::chrono::milliseconds(timeout_ms);
            if (!condition_not_empty_.wait_for(lock, timeout, 
                [this] { return !queue_.empty() || should_stop_; })) {
                return false;
            }
        } else {
            condition_not_empty_.wait(lock, 
                [this] { return !queue_.empty() || should_stop_; });
        }
        
        if (should_stop_ && queue_.empty()) {
            return false;
        }
        
        item = std::move(queue_.front());
        queue_.pop();
        condition_not_full_.notify_one();
        return true;
    }
    
    bool tryEnqueue(const T& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() >= max_size_ || should_stop_) {
            return false;
        }
        queue_.push(item);
        condition_not_empty_.notify_one();
        return true;
    }
    
    bool tryEnqueue(T&& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.size() >= max_size_ || should_stop_) {
            return false;
        }
        queue_.push(std::move(item));
        condition_not_empty_.notify_one();
        return true;
    }
    
    bool tryDequeue(T& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.empty()) {
            return false;
        }
        item = std::move(queue_.front());
        queue_.pop();
        condition_not_full_.notify_one();
        return true;
    }
    
    // Queue status
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size();
    }
    
    size_t capacity() const {
        return max_size_;
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.empty();
    }
    
    bool full() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.size() >= max_size_;
    }
    
    void clear() {
        std::lock_guard<std::mutex> lock(mutex_);
        while (!queue_.empty()) {
            queue_.pop();
        }
    }
    
    // Control
    void stop() {
        should_stop_ = true;
        condition_not_empty_.notify_all();
        condition_not_full_.notify_all();
    }
    
    void resume() {
        should_stop_ = false;
    }
    
    // Statistics
    struct Statistics {
        size_t current_size;
        size_t max_capacity;
        bool is_stopped;
    };
    
    Statistics getStatistics() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return {queue_.size(), max_size_, should_stop_};
    }

private:
    std::queue<T> queue_;
    mutable std::mutex mutex_;
    std::condition_variable condition_not_empty_;
    std::condition_variable condition_not_full_;
    size_t max_size_;
    std::atomic<bool> should_stop_;
};

// Type aliases for common queue types
using SimpleNALUnitQueue = SimpleThreadSafeQueue<struct NALUnit>;
using SimplePacketQueue = SimpleThreadSafeQueue<struct RTPPacket>;
using SimpleFrameQueue = SimpleThreadSafeQueue<struct VideoFrame>;

} // namespace rtsp
} // namespace aibox
