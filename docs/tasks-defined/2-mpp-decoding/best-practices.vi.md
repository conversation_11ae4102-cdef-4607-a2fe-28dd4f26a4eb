# Thực Hành Tốt Nhất cho Triển Khai Module Giải Mã MPP

## Thực Hành Tốt Nhất Triển Khai

### 1. Quản Lý Context MPP

- **Khởi Tạo Context**
  - Tạo context MPP riêng cho mỗi luồng camera để cho phép xử lý song song
  - Khởi tạo context với các loại codec phù hợp (MPP_VIDEO_CodingAVC cho H.264, MPP_VIDEO_CodingHEVC cho H.265)
  - Đặt chế độ split decoder để đảm bảo song song ở mức khung hình: `mpi->control(ctx, MPP_DEC_SET_PARSER_SPLIT_MODE, &split_mode)`

- **K<PERSON><PERSON> Năng Chống Lỗi**
  - Cấu hình decoder cho khả năng chống lỗi sử dụng `MPP_DEC_SET_ERROR_RESILIENCE`
  - Triển khai xử lý lỗi mạnh mẽ cho bitstream bị hỏng
  - <PERSON> dõi trạng thái decoder để xác định khóa phần cứng tiềm ẩn

- **C<PERSON><PERSON> Hình Hiệu Suất**
  - Đặt tham số chế độ nhanh phù hợp để cân bằng tốc độ giải mã và chất lượng
  - Cấu hình kích thước và số lượng frame buffer dựa trên độ phân giải và framerate
  - Bật các tính năng tăng tốc phần cứng cụ thể cho loại codec

### 2. Triển Khai DMABUF Zero-Copy

- **Thiết Lập DMABUF**
  - Cấu hình MPP để sử dụng chế độ buffer ngoài: `mpi->control(ctx, MPP_DEC_SET_EXT_BUF_GROUP, &buffer_group)`
  - Sử dụng DRM allocator cho phân bổ buffer: `mpp_buffer_group_get_internal(&buffer_group, MPP_BUFFER_TYPE_DRM)`
  - Xuất file descriptor (FD) buffer sử dụng `mpp_buffer_get_fd(buffer)`

- **Quản Lý Vòng Đời Buffer**
  - Triển khai reference counting phù hợp cho DMABUF handle
  - Sử dụng pattern buffer pool để quản lý DMABUF có thể tái sử dụng
  - Đảm bảo giải phóng kịp thời các buffer không sử dụng để ngăn cạn kiệt tài nguyên

- **Tích Hợp với Module Downstream**
  - Truyền DMABUF FD trực tiếp đến module tiền xử lý RGA
  - Sử dụng cấu trúc metadata hiệu quả để liên kết FD với thông tin khung hình
  - Triển khai primitive đồng bộ để đảm bảo an toàn truy cập buffer

### 3. Pipeline Giải Mã Hiệu Quả

- **Xử Lý Gói**
  - Triển khai pattern producer-consumer cho xử lý gói
  - Xử lý các đơn vị NAL theo batch khi có thể để giảm overhead API call
  - Xử lý đúng cách các gián đoạn gói và thay đổi định dạng luồng

- **Giải Mã Bất Đồng Bộ**
  - Sử dụng chế độ không chặn cho `decode_put_packet` và `decode_get_frame`
  - Triển khai cơ chế polling với timeout phù hợp
  - Cân nhắc sử dụng condition variable cho thông báo frame-ready

- **Quản Lý Frame Rate**
  - Triển khai chiến lược drop frame cho tình huống tải cao
  - Ưu tiên I-frame hơn P-frame khi cần drop
  - Duy trì timing nhất quán cho phát lại đồng bộ multi-camera

### 4. Tối Ưu Tài Nguyên

- **Quản Lý Bộ Nhớ**
  - Cấu hình số lượng buffer phù hợp dựa trên độ phân giải (độ phân giải cao hơn = nhiều buffer hơn)
  - Công thức khuyến nghị: `buffer_count = max(3, min(resolution_width * resolution_height / (256 * 1024), 16))`
  - Sử dụng vùng bộ nhớ riêng cho giải mã video nếu có sẵn trên nền tảng

- **Phân Bổ Tài Nguyên Phần Cứng**
  - Giám sát sử dụng VPU và triển khai cân bằng tải
  - Lên lịch các tác vụ giải mã để tránh quá tải VPU
  - Triển khai đánh đổi chất lượng/hiệu suất động dựa trên tải hệ thống

- **Quản Lý Thread**
  - Sử dụng thread pool riêng cho các thao tác MPP
  - Triển khai work stealing cho sử dụng thread hiệu quả
  - Ưu tiên luồng dựa trên yêu cầu ứng dụng

## Lỗi Triển Khai Cần Tránh

1. **Rò Rỉ Tài Nguyên**
   - Luôn giải phóng đúng cách context MPP, packet và frame
   - Triển khai routine dọn dẹp cho các kịch bản kết thúc bất thường
   - Sử dụng pattern RAII cho quản lý tài nguyên MPP

2. **Vấn Đề Đồng Bộ Decoder**
   - Tránh cung cấp packet cho decoder nhanh hơn khả năng xử lý
   - Đừng bỏ qua mã trả về decoder cho biết buffer đầy
   - Ngăn tràn hàng đợi frame bằng cách giám sát tốc độ đầu ra decoder

3. **Nghẽn Cổ Chai Hiệu Suất**
   - Đừng sử dụng blocking call trong đường dẫn quan trọng
   - Tránh chuyển đổi định dạng không cần thiết trong module giải mã
   - Đừng giải mã ở độ phân giải cao hơn cần thiết cho xử lý downstream

4. **Thiếu Sót Xử Lý Lỗi**
   - Không bao giờ giả định bitstream luôn hợp lệ
   - Đừng bỏ qua lỗi decoder có thể cho biết vấn đề phần cứng
   - Tránh vòng lặp thử lại vô hạn có thể khóa hệ thống

## Chiến Lược Tối Ưu

1. **Điều Chỉnh Decoder**
   - Điều chỉnh tham số decoder dựa trên loại nội dung và yêu cầu
   - Cho phát hiện khuôn mặt, ưu tiên chất lượng trong vùng quan tâm
   - Cân nhắc sử dụng chế độ split_parse cho độ trễ thấp hơn khi áp dụng được

2. **Tối Ưu Băng Thông Bộ Nhớ**
   - Sử dụng phân bổ buffer stride-aligned cho truy cập bộ nhớ tối ưu
   - Giảm thiểu sao chép buffer bằng chia sẻ DMABUF trực tiếp
   - Cân nhắc sử dụng nén cho lưu trữ buffer nếu được hỗ trợ

3. **Profiling Hiệu Suất Decoder**
   - Đo và ghi log thời gian giải mã mỗi frame
   - Theo dõi sử dụng VPU trên nhiều luồng
   - Xác định và giải quyết nghẽn cổ chai giải mã theo thời gian thực

4. **Ưu Tiên Luồng**
   - Triển khai kiểm soát quality-of-service cho nhiều luồng
   - Phân bổ nhiều tài nguyên giải mã hơn cho camera ưu tiên cao
   - Cân nhắc scaling độ phân giải thích ứng dựa trên tầm quan trọng nội dung

## Kiểm Thử và Xác Thực

1. **Kiểm Thử Tuân Thủ**
   - Kiểm thử với các profile và level H.264/H.265 khác nhau
   - Xác minh xử lý đúng các phần tử cú pháp luồng
   - Xác thực khả năng chống lỗi bitstream với luồng bị hỏng

2. **Kiểm Thử Hiệu Suất**
   - Đo thông lượng giải mã bền vững tối đa
   - Kiểm thử với nhiều luồng đồng thời ở các độ phân giải khác nhau
   - Profile sử dụng bộ nhớ và CPU dưới tải đỉnh

3. **Kiểm Thử Tích Hợp**
   - Xác minh pipeline zero-copy từ RTSP đến module RGA
   - Kiểm thử đồng bộ giữa nhiều luồng camera
   - Xác thực độ trễ end-to-end từ nhận packet đến frame đã giải mã

## Cân Nhắc Cụ Thể Phần Cứng cho RK3588

1. **Tương Thích Phiên Bản MPP**
   - Sử dụng phiên bản MPP được tối ưu cụ thể cho RK3588 (kiểm tra tài liệu Rockchip)
   - Xác minh tương thích giữa driver kernel và phiên bản thư viện MPP
   - Tuân theo cấu hình khuyến nghị của Rockchip cho RK3588

2. **Khả Năng VPU**
   - RK3588 hỗ trợ giải mã video 8K - tận dụng điều này cho nhiều luồng độ phân giải thấp hơn
   - Tận dụng các tính năng tăng tốc phần cứng cụ thể trong VPU của RK3588
   - Cân nhắc sử dụng nhiều instance decoder để sử dụng VPU tốt hơn

3. **Kiến Trúc Bộ Nhớ**
   - Sử dụng hệ thống phân cấp bộ nhớ của RK3588 một cách hiệu quả
   - Cân nhắc cài đặt CMA (Contiguous Memory Allocator) cho phân bổ buffer tối ưu
   - Sử dụng loại bộ nhớ phù hợp (ví dụ: có khả năng IOMMU) để có hiệu suất tốt nhất
