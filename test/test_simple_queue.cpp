#include "rtsp/thread_safe_queue.hpp"
#include <iostream>

using namespace aibox::rtsp;

int main() {
    std::cout << "Testing Thread-Safe Queue Implementation..." << std::endl;
    
    try {
        // Test basic queue operations
        QueueConfig config;
        config.max_size = 10;
        config.use_lock_free = false;
        
        ThreadSafeQueue<int> queue(config);
        
        std::cout << "✓ Queue created successfully" << std::endl;
        
        // Test enqueue
        if (queue.enqueue(42)) {
            std::cout << "✓ Enqueue operation successful" << std::endl;
        } else {
            std::cout << "✗ Enqueue operation failed" << std::endl;
            return 1;
        }
        
        // Test dequeue
        int value;
        if (queue.dequeue(value) && value == 42) {
            std::cout << "✓ Dequeue operation successful" << std::endl;
        } else {
            std::cout << "✗ Dequeue operation failed" << std::endl;
            return 1;
        }
        
        // Test lock-free mode
        config.use_lock_free = true;
        ThreadSafeQueue<int> lock_free_queue(config);
        
        if (lock_free_queue.isLockFreeMode()) {
            std::cout << "✓ Lock-free mode enabled" << std::endl;
        } else {
            std::cout << "✗ Lock-free mode failed" << std::endl;
            return 1;
        }
        
        // Test statistics
        auto stats = queue.getStatistics();
        std::cout << "✓ Statistics: enqueue=" << stats.enqueue_count 
                  << ", dequeue=" << stats.dequeue_count << std::endl;
        
        std::cout << "\n🎉 All basic tests passed!" << std::endl;
        std::cout << "Task 3.1 Thread-Safe Queue System implementation is working correctly." << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "✗ Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "✗ Test failed with unknown exception" << std::endl;
        return 1;
    }
}
