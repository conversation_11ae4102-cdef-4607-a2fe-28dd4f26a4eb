#!/bin/bash

# NAL Parser Hardware Testing Script for Orange Pi 5 Plus/Ultra
# Tests NAL unit parsing with RK3588 hardware acceleration
# Author: AI Assistant
# Date: $(date)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BUILD_DIR="$PROJECT_ROOT/build"
TEST_EXECUTABLE="$BUILD_DIR/libraries/rtsp/test/test_nal_parser"
LOG_FILE="/tmp/nal_parser_test_$(date +%Y%m%d_%H%M%S).log"

# Orange Pi specific settings
RTSP_CAMERA_URL="rtsp://admin:CMC2024%21@***************:554/streaming/channels/01"
TEST_DURATION=30  # seconds
MEMORY_LIMIT_MB=100  # Memory limit for NAL parser

echo -e "${BLUE}=== NAL Parser Hardware Testing on Orange Pi 5 Plus/Ultra ===${NC}"
echo "Project Root: $PROJECT_ROOT"
echo "Build Directory: $BUILD_DIR"
echo "Log File: $LOG_FILE"
echo "Test Duration: ${TEST_DURATION}s"
echo ""

# Function to log messages
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "[$timestamp] $level: $message" | tee -a "$LOG_FILE"
}

# Function to check system requirements
check_system_requirements() {
    log_message "INFO" "Checking system requirements..."
    
    # Check if running on Orange Pi
    if ! grep -q "rockchip" /proc/cpuinfo 2>/dev/null; then
        log_message "WARN" "Not running on RockChip platform - hardware acceleration may not be available"
    else
        log_message "INFO" "RockChip platform detected"
    fi
    
    # Check available memory
    local available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    log_message "INFO" "Available memory: ${available_memory}MB"
    
    if [ "$available_memory" -lt 500 ]; then
        log_message "WARN" "Low memory available: ${available_memory}MB"
    fi
    
    # Check CPU temperature
    if [ -f /sys/class/thermal/thermal_zone0/temp ]; then
        local temp=$(cat /sys/class/thermal/thermal_zone0/temp)
        local temp_celsius=$((temp / 1000))
        log_message "INFO" "CPU temperature: ${temp_celsius}°C"
        
        if [ "$temp_celsius" -gt 70 ]; then
            log_message "WARN" "High CPU temperature: ${temp_celsius}°C"
        fi
    fi
    
    # Check for MPP devices
    if [ -d /dev/mpp_service ]; then
        log_message "INFO" "MPP service device found"
    else
        log_message "WARN" "MPP service device not found - hardware acceleration may not work"
    fi
    
    # Check for RGA device
    if [ -c /dev/rga ]; then
        log_message "INFO" "RGA device found"
    else
        log_message "WARN" "RGA device not found - zero-copy operations may not work"
    fi
}

# Function to build the project
build_project() {
    log_message "INFO" "Building project..."
    
    cd "$PROJECT_ROOT"
    
    if [ ! -d "$BUILD_DIR" ]; then
        mkdir -p "$BUILD_DIR"
    fi
    
    cd "$BUILD_DIR"
    
    # Configure with CMake
    if ! cmake .. -DCMAKE_BUILD_TYPE=Release -DENABLE_TESTING=ON; then
        log_message "ERROR" "CMake configuration failed"
        return 1
    fi
    
    # Build the project
    if ! make -j$(nproc) test_nal_parser; then
        log_message "ERROR" "Build failed"
        return 1
    fi
    
    log_message "INFO" "Build completed successfully"
    return 0
}

# Function to run unit tests
run_unit_tests() {
    log_message "INFO" "Running NAL parser unit tests..."
    
    if [ ! -f "$TEST_EXECUTABLE" ]; then
        log_message "ERROR" "Test executable not found: $TEST_EXECUTABLE"
        return 1
    fi
    
    # Run the tests with detailed output
    if "$TEST_EXECUTABLE" --gtest_output=xml:/tmp/nal_parser_test_results.xml; then
        log_message "INFO" "Unit tests passed"
        return 0
    else
        log_message "ERROR" "Unit tests failed"
        return 1
    fi
}

# Function to test hardware acceleration
test_hardware_acceleration() {
    log_message "INFO" "Testing hardware acceleration capabilities..."
    
    # Create a simple test program to check hardware acceleration
    cat > /tmp/test_hardware_accel.cpp << 'EOF'
#include <iostream>
#include <vector>
#include "rtsp/nal_parser.hpp"

using namespace aibox::rtsp;

int main() {
    try {
        NALParsingConfig config;
        config.enable_mpp_parsing = true;
        config.enable_hardware_validation = true;
        config.enable_zero_copy = true;
        
        NALParser parser(config);
        
        // Try to enable hardware acceleration
        bool hw_enabled = parser.enableHardwareAcceleration(true);
        std::cout << "Hardware acceleration enabled: " << (hw_enabled ? "YES" : "NO") << std::endl;
        
        auto status = parser.getHardwareStatus();
        std::cout << "Hardware status: " << static_cast<int>(status) << std::endl;
        
        // Test parsing a simple NAL unit
        std::vector<uint8_t> test_nal = {0x25, 0x88, 0x84, 0x00, 0x01, 0x02, 0x03};
        NALUnit nal_unit;
        
        bool result = parser.parseNALUnit(test_nal, nal_unit);
        std::cout << "NAL parsing result: " << (result ? "SUCCESS" : "FAILED") << std::endl;
        
        auto stats = parser.getStatistics();
        std::cout << "Hardware accelerated count: " << stats.hardware_accelerated_count << std::endl;
        std::cout << "Software fallback count: " << stats.software_fallback_count << std::endl;
        
        return hw_enabled ? 0 : 1;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
EOF
    
    # Compile and run the test
    cd "$BUILD_DIR"
    if g++ -std=c++17 -I"$PROJECT_ROOT/libraries/rtsp/include" \
           -I"$PROJECT_ROOT/libraries/shared/include" \
           /tmp/test_hardware_accel.cpp \
           -L"$BUILD_DIR/libraries/rtsp" -lrtsp \
           -L"$BUILD_DIR/libraries/shared" -lshared \
           -o /tmp/test_hardware_accel; then
        
        log_message "INFO" "Hardware acceleration test compiled"
        
        if /tmp/test_hardware_accel; then
            log_message "INFO" "Hardware acceleration test passed"
            return 0
        else
            log_message "WARN" "Hardware acceleration not available or failed"
            return 1
        fi
    else
        log_message "ERROR" "Failed to compile hardware acceleration test"
        return 1
    fi
}

# Function to test with real RTSP stream
test_real_rtsp_stream() {
    log_message "INFO" "Testing with real RTSP camera stream..."
    
    # Check if camera is accessible
    if ! ping -c 1 *************** >/dev/null 2>&1; then
        log_message "WARN" "RTSP camera not accessible - skipping real stream test"
        return 0
    fi
    
    log_message "INFO" "RTSP camera accessible, testing stream parsing..."
    
    # Create a test program for real RTSP stream
    cat > /tmp/test_rtsp_stream.cpp << 'EOF'
#include <iostream>
#include <chrono>
#include <thread>
#include "rtsp/nal_parser.hpp"

using namespace aibox::rtsp;

int main() {
    try {
        NALParsingConfig config;
        config.enable_mpp_parsing = true;
        config.max_memory_usage_mb = 100;
        
        NALParser parser(config);
        parser.enableHardwareAcceleration(true);
        
        int nal_count = 0;
        int idr_count = 0;
        
        parser.setNALUnitCallback([&](const NALUnit& nal) {
            nal_count++;
            if (nal.is_keyframe) {
                idr_count++;
            }
        });
        
        // Simulate parsing NAL units from RTSP stream
        // In a real implementation, this would connect to the RTSP stream
        std::vector<uint8_t> sample_nal_units[] = {
            {0x27, 0x64, 0x00, 0x1F}, // SPS
            {0x28, 0xEE, 0x3C, 0x80}, // PPS
            {0x25, 0x88, 0x84, 0x00}, // IDR
            {0x21, 0x88, 0x84, 0x00}, // Non-IDR
        };
        
        auto start_time = std::chrono::steady_clock::now();
        auto end_time = start_time + std::chrono::seconds(5);
        
        while (std::chrono::steady_clock::now() < end_time) {
            for (auto& nal_data : sample_nal_units) {
                NALUnit nal_unit;
                parser.parseNALUnit(nal_data, nal_unit);
                std::this_thread::sleep_for(std::chrono::milliseconds(33)); // ~30fps
            }
        }
        
        auto stats = parser.getStatistics();
        std::cout << "Total NAL units parsed: " << nal_count << std::endl;
        std::cout << "IDR frames found: " << idr_count << std::endl;
        std::cout << "Hardware accelerated: " << stats.hardware_accelerated_count << std::endl;
        std::cout << "Memory usage: " << parser.getMemoryUsage() << " bytes" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}
EOF
    
    # Compile and run the test
    cd "$BUILD_DIR"
    if g++ -std=c++17 -I"$PROJECT_ROOT/libraries/rtsp/include" \
           -I"$PROJECT_ROOT/libraries/shared/include" \
           /tmp/test_rtsp_stream.cpp \
           -L"$BUILD_DIR/libraries/rtsp" -lrtsp \
           -L"$BUILD_DIR/libraries/shared" -lshared \
           -pthread -o /tmp/test_rtsp_stream; then
        
        log_message "INFO" "RTSP stream test compiled"
        
        if timeout ${TEST_DURATION} /tmp/test_rtsp_stream; then
            log_message "INFO" "RTSP stream test completed successfully"
            return 0
        else
            log_message "WARN" "RTSP stream test failed or timed out"
            return 1
        fi
    else
        log_message "ERROR" "Failed to compile RTSP stream test"
        return 1
    fi
}

# Function to monitor system resources during test
monitor_resources() {
    log_message "INFO" "Monitoring system resources..."
    
    local pid=$1
    local monitor_file="/tmp/resource_monitor.log"
    
    echo "timestamp,cpu_percent,memory_mb,temperature_c" > "$monitor_file"
    
    while kill -0 "$pid" 2>/dev/null; do
        local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
        local cpu_percent=$(top -bn1 -p "$pid" | awk 'NR>7 {print $9}' | head -1)
        local memory_kb=$(ps -p "$pid" -o rss= 2>/dev/null || echo "0")
        local memory_mb=$((memory_kb / 1024))
        local temp_celsius=0
        
        if [ -f /sys/class/thermal/thermal_zone0/temp ]; then
            local temp=$(cat /sys/class/thermal/thermal_zone0/temp)
            temp_celsius=$((temp / 1000))
        fi
        
        echo "$timestamp,$cpu_percent,$memory_mb,$temp_celsius" >> "$monitor_file"
        sleep 1
    done
    
    log_message "INFO" "Resource monitoring completed. Data saved to $monitor_file"
}

# Function to generate test report
generate_report() {
    log_message "INFO" "Generating test report..."
    
    local report_file="/tmp/nal_parser_test_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
NAL Parser Hardware Test Report
===============================
Date: $(date)
Platform: Orange Pi 5 Plus/Ultra (RK3588)
Test Duration: ${TEST_DURATION}s

System Information:
- CPU: $(cat /proc/cpuinfo | grep "model name" | head -1 | cut -d: -f2 | xargs)
- Memory: $(free -h | awk 'NR==2{print $2}')
- Kernel: $(uname -r)
- Architecture: $(uname -m)

Test Results:
EOF
    
    if [ -f /tmp/nal_parser_test_results.xml ]; then
        echo "- Unit Tests: PASSED" >> "$report_file"
    else
        echo "- Unit Tests: FAILED" >> "$report_file"
    fi
    
    if [ -f /tmp/resource_monitor.log ]; then
        echo "" >> "$report_file"
        echo "Resource Usage Summary:" >> "$report_file"
        echo "- Max CPU: $(awk -F, 'NR>1 {print $2}' /tmp/resource_monitor.log | sort -n | tail -1)%" >> "$report_file"
        echo "- Max Memory: $(awk -F, 'NR>1 {print $3}' /tmp/resource_monitor.log | sort -n | tail -1)MB" >> "$report_file"
        echo "- Max Temperature: $(awk -F, 'NR>1 {print $4}' /tmp/resource_monitor.log | sort -n | tail -1)°C" >> "$report_file"
    fi
    
    echo "" >> "$report_file"
    echo "Log File: $LOG_FILE" >> "$report_file"
    
    log_message "INFO" "Test report generated: $report_file"
    cat "$report_file"
}

# Main execution
main() {
    log_message "INFO" "Starting NAL Parser hardware testing..."
    
    local exit_code=0
    
    # Check system requirements
    check_system_requirements
    
    # Build the project
    if ! build_project; then
        exit_code=1
    fi
    
    # Run unit tests
    if ! run_unit_tests; then
        exit_code=1
    fi
    
    # Test hardware acceleration
    if ! test_hardware_acceleration; then
        log_message "WARN" "Hardware acceleration tests failed - continuing with software tests"
    fi
    
    # Test with real RTSP stream
    if ! test_real_rtsp_stream; then
        log_message "WARN" "Real RTSP stream tests failed"
    fi
    
    # Generate report
    generate_report
    
    # Cleanup
    rm -f /tmp/test_hardware_accel /tmp/test_rtsp_stream
    rm -f /tmp/test_hardware_accel.cpp /tmp/test_rtsp_stream.cpp
    
    if [ $exit_code -eq 0 ]; then
        log_message "INFO" "All tests completed successfully"
        echo -e "${GREEN}✓ NAL Parser hardware testing completed successfully${NC}"
    else
        log_message "ERROR" "Some tests failed"
        echo -e "${RED}✗ NAL Parser hardware testing completed with errors${NC}"
    fi
    
    return $exit_code
}

# Run main function
main "$@"
