# Thực Hành Tốt Nhất cho Triển Khai Module Phát Hiện Khuôn Mặt NPU

## Thực Hành Tốt Nhất Triển Khai

### 1. Lựa Chọn và Tối Ưu Mô Hình NPU

- **Lựa Chọn Kiến Trúc Mô Hình**
  - Chọn mô hình nhẹ được tối ưu cho triển khai embedded (ưu tiên RetinaFace-MobileNet)
  - Cân bằng độ chính xác với tốc độ suy luận dựa trên yêu cầu của bạn
  - Cân nhắc các biến thể mô hình khác nhau cho độ phân giải camera khác nhau

- **Chiến Lược Lượng Tử Hóa**
  - Sử dụng lượng tử hóa INT8 để có hiệu suất tối đa trên NPU của RK3588
  - Đ<PERSON><PERSON> bảo dataset hiệu chuẩn đại diện cho đầu vào camera thực tế
  - Giám sát mất mát độ chính xác sau lượng tử hóa và điều chỉnh tham số nếu cần

- **Quy Trình Chuyển Đổi Mô Hình**
  - Tuân theo chuỗi chuyển đổi đúng: Framework Gốc → ONNX → RKNN
  - Sử dụng RKNN-Toolkit2 mới nhất với đặc tả target RK3588
  - Đặt flag tối ưu phù hợp trong quá trình chuyển đổi: `target_platform='rk3588'`

### 2. Sử Dụng Lõi NPU

- **Chiến Lược Phân Bổ Lõi**
  - Sử dụng `rknn_set_core_mask()` để quản lý phân bổ lõi NPU
  - Cho xử lý batch của một mô hình, cân nhắc `RKNN_NPU_CORE_0_1_2` để sử dụng tất cả lõi
  - Cho các instance mô hình song song, gán lõi chuyên dụng (ví dụ: lõi 0 cho phát hiện, lõi 1-2 cho nhận diện)

- **Quản Lý Đa Context**
  - Tạo context RKNN riêng cho các instance mô hình hoặc luồng camera khác nhau
  - Triển khai context manager để theo dõi và tối ưu việc sử dụng context
  - Cân nhắc rotation context để xử lý nhiều luồng hơn số lõi có sẵn

- **Cân Bằng Tải**
  - Giám sát sử dụng NPU mỗi lõi và điều chỉnh phân bổ động
  - Triển khai lập lịch dựa trên ưu tiên cho các thao tác NPU
  - Cân nhắc time-slicing cho lõi NPU khi xử lý nhiều luồng

### 3. Triển Khai DMABUF Zero-Copy

- **Thiết Lập Input Tensor**
  - Sử dụng `rknn_create_mem_from_fd()` để tạo bộ nhớ tensor NPU từ DMABUF FD
  - Cấu hình thuộc tính tensor phù hợp khớp với yêu cầu đầu vào mô hình
  - Đặt `RKNN_FLAG_MEM_ALLOC_OUTSIDE` trong quá trình khởi tạo context

- **Quản Lý Bộ Nhớ Hiệu Quả**
  - Triển khai buffer pooling để giảm overhead phân bổ
  - Theo dõi sử dụng bộ nhớ để duy trì trong giới hạn địa chỉ 4GB của NPU
  - Sử dụng reference counting cho quản lý buffer chia sẻ

- **Chiến Lược Đồng Bộ**
  - Triển khai đồng bộ hóa phù hợp giữa các thao tác RGA và NPU
  - Sử dụng cơ chế fence nếu có sẵn cho đồng bộ phần cứng
  - Tránh các điểm đồng bộ CPU không cần thiết

### 4. Tối Ưu Xử Lý Batch

- **Chiến Lược Batching Frame**
  - Nhóm frame từ nhiều camera hoặc frame tuần tự cho xử lý batch
  - Tìm kích thước batch tối ưu thông qua kiểm thử hiệu suất (thường 4-8)
  - Triển khai sizing batch động dựa trên tải hệ thống

- **Quản Lý Tensor**
  - Cho đầu vào batch, cấu hình dimension N phù hợp trong layout NHWC/NCHW
  - Sử dụng `rknn_inputs_set()` với cấu hình stride phù hợp cho batch
  - Triển khai layout bộ nhớ tensor hiệu quả cho suy luận batch

- **Xử Lý Bất Đồng Bộ**
  - Sử dụng `RKNN_FLAG_ASYNC_MODE` cho suy luận không chặn
  - Triển khai cơ chế callback hoặc polling cho thông báo hoàn thành
  - Pipeline chuẩn bị batch với thực thi suy luận

### 5. Tối Ưu Hậu Xử Lý

- **Giải Mã Kết Quả Hiệu Quả**
  - Triển khai giải mã anchor box được tối ưu cho RetinaFace
  - Sử dụng lệnh SIMD (NEON) cho hậu xử lý khi phù hợp
  - Cân nhắc chuyển hậu xử lý tốn thời gian sang thread riêng

- **Non-Maximum Suppression (NMS)**
  - Triển khai thuật toán NMS hiệu quả phù hợp cho phát hiện khuôn mặt
  - Cân nhắc các biến thể Fast NMS để có hiệu suất tốt hơn
  - Sử dụng ngưỡng IoU phù hợp cho kịch bản phát hiện khuôn mặt

- **Lọc Kết Quả**
  - Áp dụng ngưỡng confidence trước các thao tác NMS tốn kém
  - Lọc khuôn mặt dựa trên yêu cầu kích thước tối thiểu/tối đa
  - Cân nhắc lọc cụ thể cảnh (ví dụ: dựa trên khoảng cách, vị trí)

## Lỗi Triển Khai Cần Tránh

1. **Quản Lý Sai Tài Nguyên NPU**
   - Đừng tạo/hủy context RKNN thường xuyên trong runtime
   - Tránh load cùng mô hình nhiều lần
   - Đừng vượt quá giới hạn bộ nhớ địa chỉ 4GB của NPU

2. **Nghẽn Cổ Chai Hiệu Suất**
   - Tránh các thao tác đồng bộ trong vòng lặp xử lý chính
   - Đừng thực hiện chuyển đổi định dạng hoặc copy không cần thiết
   - Ngăn over-batching có thể tăng độ trễ không cần thiết

3. **Vấn Đề Bộ Nhớ**
   - Đừng rò rỉ tài nguyên bộ nhớ NPU bằng cách không giải phóng output
   - Tránh tạo/hủy quá mức bộ nhớ tensor RKNN
   - Ngăn phân mảnh bằng pattern phân bổ tensor nhất quán

4. **Vấn Đề Chất Lượng Suy Luận**
   - Đừng bỏ qua tác động lượng tử hóa lên độ chính xác phát hiện
   - Tránh downscaling hình ảnh quá mức có thể mất chi tiết khuôn mặt
   - Đừng bỏ qua các bước tiền xử lý cần thiết của mô hình

## Chiến Lược Tối Ưu

1. **Điều Chỉnh Hiệu Suất NPU**
  - Profile suy luận mô hình với các kích thước batch và cấu hình lõi khác nhau
  - Đo tác động của các tham số lượng tử hóa khác nhau
  - Thử nghiệm với các chiến lược phân bổ lõi khác nhau

2. **Tối Ưu Truy Cập Bộ Nhớ**
  - Đảm bảo alignment phù hợp của dữ liệu tensor
  - Sử dụng layout bộ nhớ nhất quán để cải thiện hiệu quả cache
  - Triển khai zero-copy trong toàn bộ pipeline phát hiện

3. **Phối Hợp CPU-NPU**
  - Cân bằng khối lượng công việc giữa CPU và NPU
  - Offload tiền/hậu xử lý phù hợp cho CPU để giải phóng tài nguyên NPU
  - Pipeline các thao tác CPU với suy luận NPU

4. **Cân Bằng Throughput vs. Latency**
  - Tối ưu cho throughput với batch lớn hơn cho hệ thống đa camera
  - Tối ưu cho latency với batch nhỏ hơn cho yêu cầu thời gian thực
  - Triển khai priority lane cho luồng camera quan trọng

## Kiểm Thử và Xác Thực

1. **Xác Thực Độ Chính Xác**
  - So sánh kết quả phát hiện giữa mô hình floating-point và lượng tử hóa
  - Xác thực hiệu suất qua các điều kiện ánh sáng và kịch bản khác nhau
  - Kiểm thử với tập hợp khuôn mặt đa dạng (tuổi, dân tộc, biểu cảm khác nhau)

2. **Benchmarking Hiệu Suất**
  - Đo frame per second (FPS) dưới các điều kiện tải khác nhau
  - Profile sử dụng NPU và tiêu thụ bộ nhớ
  - Đo độ trễ end-to-end từ đầu vào frame đến kết quả phát hiện

3. **Sử Dụng Tài Nguyên**
  - Giám sát sử dụng lõi NPU qua nhiều luồng
  - Theo dõi pattern phân bổ bộ nhớ trong quá trình hoạt động kéo dài
  - Đo tiêu thụ điện năng cho các chế độ hoạt động khác nhau

## Cân Nhắc Cụ Thể Phần Cứng cho RK3588

1. **Kiến Trúc NPU**
  - Tận dụng kiến trúc NPU tri-core của RK3588
  - Hiểu đặc tính hiệu suất của từng lõi NPU
  - Cân nhắc quản lý tần số clock NPU để kiểm soát nhiệt

2. **Ràng Buộc Bộ Nhớ**
  - Làm việc trong giới hạn bộ nhớ địa chỉ 4GB của NPU
  - Hiểu đặc tính truyền DMA giữa bộ nhớ hệ thống và NPU
  - Cân nhắc giới hạn băng thông bộ nhớ khi thiết kế pipeline

3. **Cân Nhắc Nhiệt**
  - Giám sát nhiệt độ NPU trong quá trình hoạt động liên tục
  - Triển khai chiến lược throttling nhiệt cho triển khai chạy lâu
  - Cân bằng khối lượng công việc NPU để ngăn hotspot

## Ví Dụ Triển Khai

1. **Khởi Tạo Context Hiệu Quả**
   ```cpp
   // Load mô hình một lần và tái sử dụng context
   rknn_context ctx;

   // Đọc file mô hình
   FILE* fp = fopen(model_path, "rb");
   fseek(fp, 0, SEEK_END);
   size_t model_size = ftell(fp);
   fseek(fp, 0, SEEK_SET);

   void* model = malloc(model_size);
   fread(model, 1, model_size, fp);
   fclose(fp);

   // Init RKNN với zero-copy và async mode flag
   rknn_init_extend init_extend;
   memset(&init_extend, 0, sizeof(init_extend));
   init_extend.flags = RKNN_FLAG_MEM_ALLOC_OUTSIDE | RKNN_FLAG_ASYNC_MODE;

   int ret = rknn_init2(&ctx, model, model_size, 0, &init_extend);

   // Đặt core mask để sử dụng cả ba lõi
   rknn_core_mask core_mask = RKNN_NPU_CORE_0_1_2;
   ret = rknn_set_core_mask(ctx, core_mask);

   free(model);
   ```

2. **Thiết Lập Input Zero-Copy**
   ```cpp
   // Query yêu cầu input
   rknn_input_output_num io_num;
   ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));

   rknn_tensor_attr input_attrs[io_num.n_input];
   for (uint32_t i = 0; i < io_num.n_input; i++) {
       input_attrs[i].index = i;
       ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &input_attrs[i], sizeof(input_attrs[i]));
   }

   // Tạo NPU tensor từ DMABUF FD
   rknn_tensor_mem* tensor_mem = rknn_create_mem_from_fd(ctx, fd_from_rga, input_size);

   // Đặt làm input
   rknn_input inputs[1];
   inputs[0].index = 0;
   inputs[0].type = RKNN_TENSOR_UINT8;
   inputs[0].size = input_size;
   inputs[0].fmt = RKNN_TENSOR_NHWC;
   inputs[0].pass_through = true;  // Pass through cho zero-copy
   inputs[0].buf = NULL;  // Không sử dụng trong chế độ zero-copy

   ret = rknn_set_io_mem(ctx, inputs[0].index, tensor_mem);
   ```
