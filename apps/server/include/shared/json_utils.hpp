#pragma once

#include <string>
#include <sstream>
#include <vector>
#include <optional>
#include <chrono>
#include "models/inference_request.hpp"
#include "models/inference_response.hpp"

namespace server::shared {

/**
 * @brief JSON utilities for serialization and deserialization
 */
class JsonUtils {
public:
    // Request parsing
    static std::optional<models::ArcFaceRequest> parseArcFaceRequest(const std::string& json);
    static std::optional<models::YoloRequest> parseYoloRequest(const std::string& json);

    // Response serialization
    static std::string serializeResponse(const models::InferenceResponse& response);
    static std::string serializeArcFaceResponse(const models::ArcFaceResponse& response);
    static std::string serializeYoloResponse(const models::YoloResponse& response);
    static std::string serializeHealthResponse(const models::HealthResponse& response);
    static std::string serializeApiInfoResponse(const models::ApiInfoResponse& response);

    // Error responses
    static std::string createErrorResponse(const std::string& error,
                                         const std::string& error_code = "",
                                         int http_code = 400);
    static std::string createSuccessResponse(const std::string& message,
                                           const std::string& data = "");

    // Utility functions
    static std::string escapeJsonString(const std::string& str);
    static std::string formatTimestamp(const std::chrono::system_clock::time_point& time);
    static std::string formatDuration(const std::chrono::milliseconds& duration);

private:
    // Helper functions for parsing
    static std::string extractStringValue(const std::string& json, const std::string& key);
    static std::optional<float> extractFloatValue(const std::string& json, const std::string& key);
    static std::optional<int> extractIntValue(const std::string& json, const std::string& key);
    static std::optional<bool> extractBoolValue(const std::string& json, const std::string& key);
    static std::vector<std::string> extractStringArray(const std::string& json, const std::string& key);

    // Helper functions for serialization
    static std::string serializeBoundingBox(const models::BoundingBox& bbox);
    static std::string serializeFaceDetection(const models::FaceDetection& face);
    static std::string serializeFaceEmbedding(const models::FaceEmbedding& embedding);
    static std::string serializeObjectDetection(const models::ObjectDetection& detection);
    static std::string serializeFloatArray(const std::vector<float>& arr);
    static std::string serializeStringArray(const std::vector<std::string>& arr);
};

} // namespace server::shared

// Include functional header for std::function
#include <functional>
