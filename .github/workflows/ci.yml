name: Build and Deploy C++ Monorepo

on:
  push:
    branches: [main, master, release]
  pull_request:
    branches: [main, master, release]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_PREFIX: ${{ github.repository_owner }}/c-aibox

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        app: [client, server]   # <-- Thêm tên app tại đây, ví dụ: [client, server]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      # Cài đặt tool build C++ & GTest (nếu các app C++ dùng unit test)
      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y build-essential cmake libgtest-dev

      # Build từng app (nếu cần build riêng biệt từng app)
      - name: Configure CMake for ${{ matrix.app }}
        run: |
          cmake -S . -B build-${{ matrix.app }} -DAPPS=${{ matrix.app }}

      - name: Build ${{ matrix.app }}
        run: |
          cmake --build build-${{ matrix.app }} --config Release

      # (Optional) Test từng app nếu có test riêng
      - name: Run tests for ${{ matrix.app }}
        run: |
          ctest --test-dir build-${{ matrix.app }} || echo "No tests for ${{ matrix.app }}"

      # Đăng nhập GHCR
      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Metadata cho từng image (tag branch, sha, version...)
      - name: Extract Docker metadata for ${{ matrix.app }}
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${{ matrix.app }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha
            type=raw,value=latest,enable=${{ contains(fromJSON('["refs/heads/main", "refs/heads/master", "refs/heads/release"]'), github.ref) }}

      # Thiết lập QEMU & Buildx đa nền tảng
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          install: true

      # Build và push Docker image từng app
      - name: Build and push Docker image for ${{ matrix.app }}
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./apps/${{ matrix.app }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # Summary cho từng app
      - name: Summary
        run: |
          echo "✅ Build and deployment for ${{ matrix.app }} completed successfully!"
          echo "📦 Image: ${{ env.REGISTRY }}/${{ env.IMAGE_PREFIX }}-${{ matrix.app }}:latest"
          echo "🔗 GHCR: https://github.com/${{ github.repository_owner }}?tab=packages"

