# AI Box Team Knowledge Base

## Project Context & Memories

### Platform Specifications
- **Target Hardware**: Orange Pi 5 Plus/Ultra with RK3588 chip
- **Memory Variants**: 4GB/8GB/16GB LPDDR4X-4224
- **Storage**: 240GB SSD
- **Architecture**: ARM64 with big.LITTLE (4x A76 + 4x A55)
- **OS**: Ubuntu 22.04 LTS ARM64

### Technology Stack Decisions
- **Primary Video Processing**: GStreamer (NOT FFmpeg) for better RK3588 support
- **Hardware Acceleration**: MPP decoder, RGA scaler, NPU (6 TOPS)
- **Memory Management**: DMABUF zero-copy operations
- **Cross-compilation**: aarch64-linux-gnu toolchain

### Resource Allocation Strategy
```
CPU Cores (RK3588):
- Core 0-1 (A55): UI Client + System Services
- Core 2-3 (A55): RTSP Input Module
- Core 4-5 (A76): Face Detection (NPU coordination)
- Core 6-7 (A76): Face Recognition & Identification

Memory Allocation:
4GB Config: RTSP ≤ 1.2GB, Face Detection ≤ 800MB, Face Recognition ≤ 600MB
8GB Config: RTSP ≤ 2.5GB, Face Detection ≤ 1.5GB, Face Recognition ≤ 1.5GB
16GB Config: RTSP ≤ 6GB, Face Detection ≤ 2.5GB, Face Recognition ≤ 3GB
```

### Performance Targets
- **4GB Orange Pi**: 6 concurrent streams, <200ms latency, <75% total CPU
- **8GB Orange Pi**: 12 concurrent streams, <150ms latency, <80% total CPU
- **16GB Orange Pi**: 24 concurrent streams, <120ms latency, <85% total CPU
- **Thermal Limit**: 85°C max, throttle at 80°C
- **Hardware Utilization**: >80% video processing via hardware accelerators

### Development Constraints
- **Hardware-in-the-Loop Testing**: Mandatory on actual Orange Pi hardware
- **Cross-compilation**: All builds must support ARM64
- **RK3588-First**: All decisions prioritize RK3588 compatibility
- **Thermal Management**: Critical for sustained operation

### Current Project Status
- **RTSP Module**: Planning phase, GStreamer-based implementation
- **Face Detection**: NPU optimization in progress
- **Face Recognition**: Database and embedding optimization
- **UI Client**: Qt-based, embedded display optimization

### Key Decisions Made
1. GStreamer over FFmpeg for RK3588 hardware support
2. Resource sharing between RTSP, Face AI, and UI modules
3. Thermal-aware performance scaling implementation
4. DMABUF zero-copy for memory efficiency
5. Hardware accelerator mandatory usage with software fallback

### Team Roles & Responsibilities
- **Technical Lead**: RK3588 optimization and architecture decisions
- **Network Developer**: GStreamer RTSP implementation
- **Video Developer**: MPP decoder and NAL parsing
- **AI Developer**: NPU optimization for face processing
- **Performance Engineer**: Thermal management and optimization
- **QA Engineer**: Orange Pi hardware validation

### Critical Risks Identified
1. GStreamer RockChip plugin compatibility
2. Hardware accelerator resource contention
3. Thermal throttling under maximum load
4. Memory constraints limiting performance
5. Integration complexity between modules

### Next Actions
1. Set up Orange Pi hardware testing environment
2. Validate GStreamer + RockChip plugins compatibility
3. Implement resource allocation framework
4. Begin RTSP module foundation (Phase 1)
5. Establish thermal monitoring system

---
**Last Updated**: [Date]
**Updated By**: [Team Member]
**Next Review**: [Date]
