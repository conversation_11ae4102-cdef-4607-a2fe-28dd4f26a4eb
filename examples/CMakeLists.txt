# Examples for C-AIBOX project

# Environment Utils Demo
add_executable(env_utils_demo
    env_utils_demo.cpp
)

target_link_libraries(env_utils_demo
    PRIVATE
        shared  # Link with shared library for env_utils
)

# Set target properties
set_target_properties(env_utils_demo PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# DateTime Utils Demo
add_executable(datetime_utils_demo
    datetime_utils_demo.cpp
)

target_link_libraries(datetime_utils_demo
    PRIVATE
        shared  # Link with shared library for datetime_utils
)

# Set target properties
set_target_properties(datetime_utils_demo PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# String Utils Demo
add_executable(string_utils_demo
    string_utils_demo.cpp
)

target_link_libraries(string_utils_demo
    PRIVATE
        shared  # Link with shared library for string_utils
)

# Set target properties
set_target_properties(string_utils_demo PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# Number Utils Demo
add_executable(number_utils_demo
    number_utils_demo.cpp
)

target_link_libraries(number_utils_demo
    PRIVATE
        shared  # Link with shared library for number_utils
)

# Set target properties
set_target_properties(number_utils_demo PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# JSON Utils Demo
add_executable(json_utils_demo
    json_utils_demo.cpp
)

target_link_libraries(json_utils_demo
    PRIVATE
        shared  # Link with shared library for json_utils
)

# Set target properties
set_target_properties(json_utils_demo PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# HTTP Utils Demo
add_executable(http_utils_demo
    http_utils_demo.cpp
)

target_link_libraries(http_utils_demo
    PRIVATE
        shared  # Link with shared library for http_utils
)

# Set target properties
set_target_properties(http_utils_demo PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# File Utils Demo
add_executable(file_utils_demo
    file_utils_demo.cpp
)

target_link_libraries(file_utils_demo
    PRIVATE
        shared  # Link with shared library for file_utils
)

# Set target properties
set_target_properties(file_utils_demo PROPERTIES
    CXX_STANDARD 20
    CXX_STANDARD_REQUIRED ON
    CXX_EXTENSIONS OFF
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin/examples
)

# Copy config.ini to build directory for demo
configure_file(
    ${CMAKE_SOURCE_DIR}/config.ini
    ${CMAKE_BINARY_DIR}/bin/examples/config.ini
    COPYONLY
)
