#include <gtest/gtest.h>
#include "rtsp/stream_multiplexer.hpp"
#include "rtsp/connection_manager.hpp"
#include "rtsp/packet_receiver.hpp"
#include "rtsp/nal_parser.hpp"
#include "rtsp/rtsp_config.hpp"
#include "rtsp/rtsp_types.hpp"

#include <thread>
#include <chrono>
#include <atomic>
#include <vector>

using namespace aibox::rtsp;

class StreamMultiplexerIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration optimized for testing
        config_.max_concurrent_streams = 3;
        config_.max_memory_usage_mb = 50;  // Small for testing
        config_.worker_thread_count = 2;
        config_.enable_load_balancing = true;
        config_.enable_thermal_management = false;  // Disable for testing
        
        // Initialize queue configuration
        config_.queue_config.high_priority_size = 20;
        config_.queue_config.medium_priority_size = 15;
        config_.queue_config.low_priority_size = 10;
        
        multiplexer_ = std::make_unique<StreamMultiplexer>(config_);
    }

    void TearDown() override {
        if (multiplexer_) {
            multiplexer_->stop();
            multiplexer_.reset();
        }
    }

    RTSPConnectionConfig createTestStreamConfig(const std::string& id, StreamPriority priority = StreamPriority::MEDIUM) {
        RTSPConnectionConfig config;
        config.stream_id = id;
        config.rtsp_url = "rtsp://test.example.com:554/stream" + id;
        config.username = "test";
        config.password = "test";
        config.priority = priority;
        config.enabled = true;
        config.timeout_ms = 1000;  // Short timeout for testing
        config.retry_count = 1;    // Minimal retries for testing
        return config;
    }

    StreamManagementConfig config_;
    std::unique_ptr<StreamMultiplexer> multiplexer_;
};

// Test integration with ConnectionManager
TEST_F(StreamMultiplexerIntegrationTest, ConnectionManagerIntegration) {
    EXPECT_TRUE(multiplexer_->start());
    
    // Add a stream (this should create internal ConnectionManager)
    auto config = createTestStreamConfig("test1", StreamPriority::HIGH);
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    
    // Check that stream was added
    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 1);
    EXPECT_EQ(stream_ids[0], "test1");
    
    // Check initial state
    EXPECT_EQ(multiplexer_->getStreamState("test1"), ConnectionState::DISCONNECTED);
    
    // Test connection attempt (will fail with test URL, but should not crash)
    EXPECT_TRUE(multiplexer_->connectStream("test1"));
    
    // Clean up
    EXPECT_TRUE(multiplexer_->removeStream("test1"));
}

// Test integration with PacketReceiver
TEST_F(StreamMultiplexerIntegrationTest, PacketReceiverIntegration) {
    EXPECT_TRUE(multiplexer_->start());
    
    // Add multiple streams with different priorities
    auto high_config = createTestStreamConfig("high", StreamPriority::HIGH);
    auto med_config = createTestStreamConfig("medium", StreamPriority::MEDIUM);
    auto low_config = createTestStreamConfig("low", StreamPriority::LOW);
    
    EXPECT_TRUE(multiplexer_->addStream("high", high_config));
    EXPECT_TRUE(multiplexer_->addStream("medium", med_config));
    EXPECT_TRUE(multiplexer_->addStream("low", low_config));
    
    // Check that all streams were added
    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 3);
    
    // Test priority management
    EXPECT_EQ(multiplexer_->getStreamPriority("high"), StreamPriority::HIGH);
    EXPECT_EQ(multiplexer_->getStreamPriority("medium"), StreamPriority::MEDIUM);
    EXPECT_EQ(multiplexer_->getStreamPriority("low"), StreamPriority::LOW);
    
    // Test priority changes
    EXPECT_TRUE(multiplexer_->setStreamPriority("low", StreamPriority::CRITICAL));
    EXPECT_EQ(multiplexer_->getStreamPriority("low"), StreamPriority::CRITICAL);
}

// Test integration with NALParser
TEST_F(StreamMultiplexerIntegrationTest, NALParserIntegration) {
    std::atomic<int> nal_callback_count{0};
    std::atomic<int> error_callback_count{0};
    
    // Set up callbacks to monitor NAL unit processing
    multiplexer_->setNALUnitCallback([&](const StreamId& id, const NALUnit& nal) {
        nal_callback_count++;
        // Verify NAL unit has expected fields
        EXPECT_FALSE(id.empty());
        EXPECT_FALSE(nal.data.empty());
    });
    
    multiplexer_->setErrorCallback([&](const StreamId& id, ErrorCategory category, const std::string& message) {
        error_callback_count++;
        EXPECT_FALSE(id.empty());
        EXPECT_FALSE(message.empty());
    });
    
    EXPECT_TRUE(multiplexer_->start());
    
    // Add a stream
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    
    // Callbacks are set up correctly
    EXPECT_TRUE(true);
}

// Test multi-stream coordination
TEST_F(StreamMultiplexerIntegrationTest, MultiStreamCoordination) {
    EXPECT_TRUE(multiplexer_->start());
    
    const int num_streams = 3;
    std::vector<std::string> stream_ids;
    
    // Add multiple streams
    for (int i = 1; i <= num_streams; ++i) {
        std::string id = "stream_" + std::to_string(i);
        auto priority = (i == 1) ? StreamPriority::HIGH : 
                       (i == 2) ? StreamPriority::MEDIUM : StreamPriority::LOW;
        
        auto config = createTestStreamConfig(id, priority);
        EXPECT_TRUE(multiplexer_->addStream(id, config));
        stream_ids.push_back(id);
    }
    
    // Check all streams were added
    auto added_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(added_ids.size(), num_streams);
    
    // Test bulk operations
    multiplexer_->connectAllStreams();
    multiplexer_->disconnectAllStreams();
    
    // Test rebalancing
    multiplexer_->rebalanceStreams();
    
    // Check statistics
    auto stats = multiplexer_->getStatistics();
    EXPECT_EQ(stats.total_streams, num_streams);
    EXPECT_EQ(stats.active_streams, num_streams);
}

// Test resource management integration
TEST_F(StreamMultiplexerIntegrationTest, ResourceManagementIntegration) {
    EXPECT_TRUE(multiplexer_->start());
    
    // Monitor initial memory usage
    size_t initial_memory = multiplexer_->getTotalMemoryUsage();
    
    // Add streams and monitor memory growth
    std::vector<std::string> stream_ids;
    for (int i = 1; i <= 3; ++i) {
        std::string id = "stream_" + std::to_string(i);
        auto config = createTestStreamConfig(id);
        EXPECT_TRUE(multiplexer_->addStream(id, config));
        stream_ids.push_back(id);
        
        size_t current_memory = multiplexer_->getTotalMemoryUsage();
        EXPECT_GE(current_memory, initial_memory);
    }
    
    // Test resource optimization
    multiplexer_->optimizeResourceUsage();
    
    // Test memory pressure handling
    multiplexer_->handleMemoryPressure();
    
    // Remove streams and check memory decreases
    for (const auto& id : stream_ids) {
        EXPECT_TRUE(multiplexer_->removeStream(id));
    }
    
    size_t final_memory = multiplexer_->getTotalMemoryUsage();
    EXPECT_LE(final_memory, initial_memory + 1024);  // Allow some overhead
}

// Test statistics and monitoring integration
TEST_F(StreamMultiplexerIntegrationTest, StatisticsAndMonitoringIntegration) {
    EXPECT_TRUE(multiplexer_->start());
    
    // Add streams
    for (int i = 1; i <= 2; ++i) {
        auto config = createTestStreamConfig("stream_" + std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream("stream_" + std::to_string(i), config));
    }
    
    // Get initial statistics
    auto stats = multiplexer_->getStatistics();
    EXPECT_EQ(stats.total_streams, 2);
    EXPECT_EQ(stats.active_streams, 2);
    EXPECT_EQ(stats.connected_streams, 0);  // Not connected yet
    
    // Get system health
    auto health = multiplexer_->getSystemHealth();
    EXPECT_FALSE(health.status.empty());
    EXPECT_GE(health.score, 0.0f);
    EXPECT_LE(health.score, 1.0f);
    EXPECT_EQ(health.active_streams, 2);
    
    // Test active stream count
    EXPECT_EQ(multiplexer_->getActiveStreamCount(), 2);
    
    // Disable one stream and check counts
    EXPECT_TRUE(multiplexer_->enableStream("stream_1", false));
    EXPECT_EQ(multiplexer_->getActiveStreamCount(), 1);
}

// Test configuration management integration
TEST_F(StreamMultiplexerIntegrationTest, ConfigurationManagementIntegration) {
    // Test initial configuration
    auto initial_config = multiplexer_->getConfig();
    EXPECT_EQ(initial_config.max_concurrent_streams, 3);
    EXPECT_EQ(initial_config.worker_thread_count, 2);
    
    // Update configuration
    StreamManagementConfig new_config = initial_config;
    new_config.max_concurrent_streams = 5;
    new_config.worker_thread_count = 3;
    new_config.max_memory_usage_mb = 100;
    
    multiplexer_->updateConfig(new_config);
    
    // Verify configuration was updated
    auto updated_config = multiplexer_->getConfig();
    EXPECT_EQ(updated_config.max_concurrent_streams, 5);
    EXPECT_EQ(updated_config.worker_thread_count, 3);
    EXPECT_EQ(updated_config.max_memory_usage_mb, 100);
    
    // Start with new configuration
    EXPECT_TRUE(multiplexer_->start());
    
    // Test that new limits are respected
    for (int i = 1; i <= 6; ++i) {
        auto config = createTestStreamConfig("stream_" + std::to_string(i));
        bool result = multiplexer_->addStream("stream_" + std::to_string(i), config);
        
        if (i <= 5) {
            EXPECT_TRUE(result);  // Should succeed within new limit
        } else {
            EXPECT_FALSE(result);  // Should fail beyond new limit
        }
    }
}

// Test error handling integration
TEST_F(StreamMultiplexerIntegrationTest, ErrorHandlingIntegration) {
    std::atomic<int> error_count{0};
    std::vector<std::string> error_messages;
    std::mutex error_mutex;
    
    // Set up error callback
    multiplexer_->setErrorCallback([&](const StreamId& id, ErrorCategory category, const std::string& message) {
        error_count++;
        std::lock_guard<std::mutex> lock(error_mutex);
        error_messages.push_back(message);
    });
    
    EXPECT_TRUE(multiplexer_->start());
    
    // Add stream with invalid configuration (should trigger errors)
    RTSPConnectionConfig invalid_config;
    invalid_config.stream_id = "invalid";
    invalid_config.rtsp_url = "invalid://url";  // Invalid URL
    invalid_config.timeout_ms = 100;  // Very short timeout
    invalid_config.retry_count = 1;
    
    EXPECT_TRUE(multiplexer_->addStream("invalid", invalid_config));
    
    // Try to connect (should fail and trigger error callback)
    EXPECT_TRUE(multiplexer_->connectStream("invalid"));
    
    // Give some time for error processing
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    // Error handling is working (actual errors depend on implementation details)
    EXPECT_TRUE(true);
}

// Test load balancing integration
TEST_F(StreamMultiplexerIntegrationTest, LoadBalancingIntegration) {
    EXPECT_TRUE(multiplexer_->start());
    
    // Check load balancing is enabled by default
    EXPECT_TRUE(multiplexer_->isLoadBalancingEnabled());
    
    // Add streams with different priorities
    auto high_config = createTestStreamConfig("high", StreamPriority::HIGH);
    auto med_config = createTestStreamConfig("medium", StreamPriority::MEDIUM);
    auto low_config = createTestStreamConfig("low", StreamPriority::LOW);
    
    EXPECT_TRUE(multiplexer_->addStream("high", high_config));
    EXPECT_TRUE(multiplexer_->addStream("medium", med_config));
    EXPECT_TRUE(multiplexer_->addStream("low", low_config));
    
    // Test load redistribution
    multiplexer_->redistributeLoad();
    
    // Test rebalancing
    multiplexer_->rebalanceStreams();
    
    // Disable load balancing
    multiplexer_->enableLoadBalancing(false);
    EXPECT_FALSE(multiplexer_->isLoadBalancingEnabled());
    
    // Re-enable load balancing
    multiplexer_->enableLoadBalancing(true);
    EXPECT_TRUE(multiplexer_->isLoadBalancingEnabled());
}

// Test performance mode integration
TEST_F(StreamMultiplexerIntegrationTest, PerformanceModeIntegration) {
    EXPECT_TRUE(multiplexer_->start());
    
    // Add some streams
    for (int i = 1; i <= 2; ++i) {
        auto config = createTestStreamConfig("stream_" + std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream("stream_" + std::to_string(i), config));
    }
    
    // Test high performance mode
    multiplexer_->setPerformanceMode(true);
    
    // Check that configuration might have changed
    auto config_after_high = multiplexer_->getConfig();
    
    // Test low performance mode
    multiplexer_->setPerformanceMode(false);
    
    // Check that configuration might have changed again
    auto config_after_low = multiplexer_->getConfig();
    
    // Performance mode changes should not crash
    EXPECT_TRUE(true);
}
