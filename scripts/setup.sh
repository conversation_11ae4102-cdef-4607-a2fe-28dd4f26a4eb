#!/bin/bash

# Main setup script for C-AIBOX project
# Usage: ./scripts/setup.sh [component] [options]

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMPONENT] [OPTIONS]"
    echo ""
    echo "COMPONENTS:"
    echo "  gui            Setup GUI environment (X11, Qt5)"
    echo "  x11            Setup X11 server for host"
    echo "  deps           Install system dependencies"
    echo "  dev            Setup development environment"
    echo "  all            Setup everything"
    echo ""
    echo "OPTIONS:"
    echo "  --force        Force reinstall/reconfigure"
    echo "  --help, -h     Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 gui                       # Setup GUI environment"
    echo "  $0 x11                       # Setup X11 server"
    echo "  $0 all                       # Complete setup"
    echo "  $0 deps --force              # Force reinstall dependencies"
}

# Function to detect OS
detect_os() {
    if [ -f /proc/version ] && grep -q Microsoft /proc/version 2>/dev/null; then
        echo "wsl"
    elif [ "$(uname -s)" = "Linux" ]; then
        echo "linux"
    elif [ "$(uname -s)" = "Darwin" ]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

# Function to setup GUI environment
setup_gui() {
    local force="${1:-false}"
    
    print_info "Setting up GUI environment..."
    
    # Run GUI setup script
    "$SCRIPT_DIR/gui/setup-gui.sh" ${force:+--force}
    
    print_success "GUI environment setup completed!"
}

# Function to setup X11 server
setup_x11() {
    local force="${1:-false}"
    
    print_info "Setting up X11 server..."
    
    local os=$(detect_os)
    case $os in
        "linux")
            "$SCRIPT_DIR/platform/setup-x11-host.sh"
            ;;
        "macos")
            "$SCRIPT_DIR/platform/setup-xquartz-macos.sh"
            ;;
        "wsl")
            print_info "For WSL, please run on Windows host:"
            print_info "  PowerShell: ./scripts/platform/setup-vcxsrv-windows.ps1"
            ;;
        *)
            print_error "Unsupported OS for X11 setup: $os"
            exit 1
            ;;
    esac
    
    print_success "X11 server setup completed!"
}

# Function to setup dependencies
setup_deps() {
    local force="${1:-false}"
    
    print_info "Setting up system dependencies..."
    
    # Install X11 dependencies
    "$SCRIPT_DIR/utils/install-x11-deps.sh" ${force:+--force}
    
    print_success "Dependencies setup completed!"
}

# Function to setup development environment
setup_dev() {
    local force="${1:-false}"
    
    print_info "Setting up development environment..."
    
    # Setup GUI environment
    setup_gui "$force"
    
    # Create render nodes if needed
    if [[ -f "$SCRIPT_DIR/utils/create-render-node.sh" ]]; then
        "$SCRIPT_DIR/utils/create-render-node.sh"
    fi
    
    # Initial build
    print_info "Performing initial build..."
    "$SCRIPT_DIR/build.sh" Debug
    
    print_success "Development environment setup completed!"
}

# Function to setup everything
setup_all() {
    local force="${1:-false}"
    
    print_info "Setting up complete C-AIBOX environment..."
    
    setup_deps "$force"
    setup_x11 "$force"
    setup_gui "$force"
    setup_dev "$force"
    
    print_success "Complete setup finished!"
    print_info ""
    print_info "Next steps:"
    print_info "1. Test the setup: ./scripts/run.sh client --headless"
    print_info "2. Run GUI application: ./scripts/run.sh gui"
    print_info "3. Run tests: ./scripts/test.sh"
}

# Parse command line arguments
COMPONENT=""
FORCE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        gui|x11|deps|dev|all)
            COMPONENT="$1"
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help|-h)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Default component if none specified
if [[ -z "$COMPONENT" ]]; then
    COMPONENT="gui"
fi

# Change to project root
cd "$PROJECT_ROOT"

# Execute component setup
case $COMPONENT in
    gui)
        setup_gui "$FORCE"
        ;;
    x11)
        setup_x11 "$FORCE"
        ;;
    deps)
        setup_deps "$FORCE"
        ;;
    dev)
        setup_dev "$FORCE"
        ;;
    all)
        setup_all "$FORCE"
        ;;
    *)
        print_error "Unknown component: $COMPONENT"
        show_usage
        exit 1
        ;;
esac

print_success "Setup completed!"
