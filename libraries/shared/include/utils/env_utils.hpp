#pragma once
#include <string>
#include <unordered_map>
#include <optional>

namespace utils {
    /**
     * @brief Environment utilities for loading and managing environment variables from config.ini files
     */
    class EnvUtils {
    public:
        /**
         * @brief Load environment variables from a config.ini file
         * @param filepath Path to the config.ini file (default: "config.ini")
         * @return true if file was loaded successfully, false otherwise
         */
        static bool loadConfigFromFile(const std::string& filepath = "config.ini");

        /**
         * @brief Load environment variables from a string content in INI format
         * @param content String content in INI format
         * @param silent Whether to suppress warning messages (default: false)
         * @return true if content was parsed successfully, false otherwise
         */
        static bool loadConfigFromString(const std::string& content, bool silent = false);

        /**
         * @brief Get environment variable value
         * @param key Environment variable name
         * @param defaultValue Default value if variable doesn't exist
         * @return Environment variable value or default value
         */
        static std::string getEnv(const std::string& key, const std::string& defaultValue = "");

        /**
         * @brief Get environment variable value as optional
         * @param key Environment variable name
         * @return Optional containing the value if exists, nullopt otherwise
         */
        static std::optional<std::string> getEnvOptional(const std::string& key);

        /**
         * @brief Set environment variable
         * @param key Environment variable name
         * @param value Environment variable value
         * @param overwrite Whether to overwrite existing variable (default: true)
         * @return true if set successfully, false otherwise
         */
        static bool setEnv(const std::string& key, const std::string& value, bool overwrite = true);

        /**
         * @brief Check if environment variable exists
         * @param key Environment variable name
         * @return true if variable exists, false otherwise
         */
        static bool hasEnv(const std::string& key);

        /**
         * @brief Remove environment variable
         * @param key Environment variable name
         * @return true if removed successfully, false otherwise
         */
        static bool unsetEnv(const std::string& key);

        /**
         * @brief Get all loaded environment variables
         * @return Map of all environment variables
         */
        static std::unordered_map<std::string, std::string> getAllEnv();

        /**
         * @brief Clear all loaded environment variables from memory
         */
        static void clearLoadedEnv();

    private:
        /**
         * @brief Parse a single line from INI file
         * @param line Line to parse
         * @param currentSection Current section name
         * @param key Output parameter for key (will include section prefix)
         * @param value Output parameter for value
         * @return true if line was parsed successfully, false otherwise
         */
        static bool parseIniLine(const std::string& line, std::string& currentSection, std::string& key, std::string& value);

        /**
         * @brief Trim whitespace from string
         * @param str String to trim
         * @return Trimmed string
         */
        static std::string trim(const std::string& str);

        /**
         * @brief Remove quotes from string if present
         * @param str String to unquote
         * @return Unquoted string
         */
        static std::string unquote(const std::string& str);

        // Storage for loaded environment variables
        static std::unordered_map<std::string, std::string> loadedEnvVars;
    };

    // Convenience functions for ease of use

    /**
     * @brief Load configuration from INI file (convenience function)
     * @param filepath Path to config.ini file
     * @return true if loaded successfully
     */
    bool loadConfig(const std::string& filepath = "config.ini");

    /**
     * @brief Get environment variable (convenience function)
     * @param key Variable name
     * @param defaultValue Default value
     * @return Variable value or default
     */
    std::string getEnv(const std::string& key, const std::string& defaultValue = "");

    /**
     * @brief Set environment variable (convenience function)
     * @param key Variable name
     * @param value Variable value
     * @return true if set successfully
     */
    bool setEnv(const std::string& key, const std::string& value);

    /**
     * @brief Check if environment variable exists (convenience function)
     * @param key Variable name
     * @return true if exists
     */
    bool hasEnv(const std::string& key);
}
