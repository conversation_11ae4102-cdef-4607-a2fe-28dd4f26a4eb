# AI Box System Resource Allocation for RK3588

## System Overview

AI Box system bao gồm nhiều module xử lý đồng thời trên Orange Pi 5 Plus/Ultra với RK3588. <PERSON>i<PERSON><PERSON> phân chia tài nguyên hợp lý là then chốt để đảm bảo hiệu suất tối ưu cho toàn bộ hệ thống.

## System Components

### Core Modules
1. **UI Client** - Qt-based GUI interface
2. **RTSP Input Module** - Camera stream processing
3. **Face Detection Module** - NPU-accelerated face detection
4. **Face Recognition Module** - Face identification and matching
5. **System Services** - Configuration, logging, monitoring

### Hardware Resources
- **CPU**: 8 cores (4x A76 + 4x A55)
- **NPU**: 6 TOPS AI accelerator
- **GPU**: Mali-G610 MP4
- **VPU**: MPP decoder/encoder
- **Memory**: 4GB/8GB/16GB LPDDR4X
- **Storage**: 240GB SSD

## CPU Core Allocation Strategy

### Detailed Core Assignment

```cpp
// RK3588 CPU Core Allocation for AI Box System
struct CoreAllocation {
    // Little Cores (Cortex-A55) - Power Efficient
    struct LittleCores {
        Core0: UI Client main thread, Qt event loop
        Core1: System services, configuration, logging
        Core2: RTSP connection management, network I/O
        Core3: RTSP packet processing, NAL parsing
    };
    
    // Big Cores (Cortex-A76) - High Performance
    struct BigCores {
        Core4: Face detection preprocessing, NPU coordination
        Core5: Face detection postprocessing, result handling
        Core6: Face recognition feature extraction
        Core7: Face identification, database matching
    };
};

// Thread Priority Configuration
enum ThreadPriority {
    REALTIME = 99,      // Critical face processing
    HIGH = 80,          // RTSP stream processing
    NORMAL = 50,        // UI and system services
    LOW = 20            // Background tasks
};
```

### CPU Affinity Implementation

```cpp
class SystemResourceManager {
public:
    void setupCPUAffinity() {
        // UI Client threads (responsive interface)
        setThreadAffinity(ui_main_thread_, {0});
        setThreadAffinity(ui_render_threads_, {0, 1});
        
        // RTSP Module threads (network and video processing)
        setThreadAffinity(rtsp_network_threads_, {2});
        setThreadAffinity(rtsp_processing_threads_, {3});
        
        // Face Detection threads (NPU coordination)
        setThreadAffinity(face_detection_threads_, {4, 5});
        
        // Face Recognition threads (CPU-intensive)
        setThreadAffinity(face_recognition_threads_, {6, 7});
        
        // System services (low priority)
        setThreadAffinity(system_service_threads_, {1});
    }
    
    void setCPUGovernors() {
        // Power-efficient for UI and services
        setCPUGovernor({0, 1}, "ondemand");
        
        // Balanced for RTSP processing
        setCPUGovernor({2, 3}, "schedutil");
        
        // Maximum performance for AI processing
        setCPUGovernor({4, 5, 6, 7}, "performance");
    }
};
```

## Memory Allocation Strategy

### 4GB Configuration (Conservative)

```cpp
struct Memory4GBAllocation {
    // System and OS
    SystemReserve: 800MB,
    
    // Application modules
    UIClient: {
        qt_framework: 200MB,
        gui_buffers: 100MB,
        video_display: 100MB,
        total: 400MB
    },
    
    RTSPModule: {
        stream_buffers: 800MB,      // 6 streams × 133MB each
        nal_unit_queues: 200MB,
        connection_pools: 100MB,
        gstreamer_overhead: 100MB,
        total: 1200MB
    },
    
    FaceDetection: {
        npu_model_cache: 300MB,
        input_buffers: 200MB,       // Frame preprocessing
        detection_results: 100MB,
        working_memory: 200MB,
        total: 800MB
    },
    
    FaceRecognition: {
        recognition_models: 200MB,
        feature_database: 200MB,    // ~10K faces
        processing_buffers: 100MB,
        embedding_cache: 100MB,
        total: 600MB
    },
    
    SharedBuffers: {
        inter_module_queues: 100MB,
        dmabuf_pools: 100MB,
        total: 200MB
    },
    
    // Total: 4000MB (4GB)
};
```

### 8GB Configuration (Optimal)

```cpp
struct Memory8GBAllocation {
    // System and OS
    SystemReserve: 1000MB,
    
    // Application modules
    UIClient: {
        qt_framework: 250MB,
        gui_buffers: 150MB,
        video_display: 100MB,
        total: 500MB
    },
    
    RTSPModule: {
        stream_buffers: 1800MB,     // 12 streams × 150MB each
        nal_unit_queues: 300MB,
        connection_pools: 200MB,
        gstreamer_overhead: 200MB,
        total: 2500MB
    },
    
    FaceDetection: {
        npu_model_cache: 500MB,     // Larger models
        input_buffers: 400MB,       // More parallel processing
        detection_results: 200MB,
        working_memory: 400MB,
        total: 1500MB
    },
    
    FaceRecognition: {
        recognition_models: 400MB,  // Multiple model variants
        feature_database: 600MB,    // ~50K faces
        processing_buffers: 300MB,
        embedding_cache: 200MB,
        total: 1500MB
    },
    
    SharedBuffers: {
        inter_module_queues: 500MB,
        dmabuf_pools: 500MB,
        total: 1000MB
    },
    
    DynamicReserve: 500MB,          // Runtime allocation

    // Total: 8000MB (8GB)
};
```

### 16GB Configuration (High Performance)

```cpp
struct Memory16GBAllocation {
    // System and OS
    SystemReserve: 1500MB,

    // Application modules
    UIClient: {
        qt_framework: 300MB,
        gui_buffers: 200MB,
        video_display: 100MB,
        total: 600MB
    },

    RTSPModule: {
        stream_buffers: 4800MB,     // 24 streams × 200MB each
        nal_unit_queues: 600MB,
        connection_pools: 300MB,
        gstreamer_overhead: 300MB,
        total: 6000MB
    },

    FaceDetection: {
        npu_model_cache: 800MB,     // Multiple large models
        input_buffers: 800MB,       // High parallel processing
        detection_results: 400MB,
        working_memory: 500MB,
        total: 2500MB
    },

    FaceRecognition: {
        recognition_models: 600MB,  // Multiple model variants
        feature_database: 1500MB,  // ~100K faces
        processing_buffers: 600MB,
        embedding_cache: 300MB,
        total: 3000MB
    },

    SharedBuffers: {
        inter_module_queues: 1000MB,
        dmabuf_pools: 1000MB,
        total: 2000MB
    },

    DynamicReserve: 400MB,          // Runtime allocation

    // Total: 16000MB (16GB)
};
```

## Hardware Accelerator Allocation

### NPU (6 TOPS) Usage Distribution

```cpp
class NPUResourceManager {
public:
    struct NPUAllocation {
        // Primary allocation (80% of 6 TOPS = 4.8 TOPS)
        FaceDetection: {
            model_inference: 3.0_TOPS,     // Main detection model
            preprocessing: 0.8_TOPS,       // Image preprocessing
            postprocessing: 0.5_TOPS,      // Result processing
            parallel_streams: 0.5_TOPS,    // Multi-stream handling
            total: 4.8_TOPS
        },
        
        // Secondary allocation (20% of 6 TOPS = 1.2 TOPS)
        FaceRecognition: {
            feature_extraction: 1.0_TOPS,  // Face embedding
            similarity_computation: 0.2_TOPS,
            total: 1.2_TOPS
        }
    };
    
    void optimizeNPUUsage() {
        // Dynamic allocation based on workload
        if (face_detection_load_ > 80%) {
            reallocateNPU(5.5_TOPS, 0.5_TOPS);  // Prioritize detection
        } else if (recognition_queue_full_) {
            reallocateNPU(4.0_TOPS, 2.0_TOPS);  // Boost recognition
        }
    }
};
```

### MPP Decoder Allocation

```cpp
class MPPResourceManager {
public:
    struct MPPAllocation {
        // Hardware decoder instances
        H264Decoders: 8,    // For RTSP streams
        H265Decoders: 4,    // For high-efficiency streams
        
        // Performance allocation
        MaxResolution: "1920x1080",
        MaxFramerate: 30,
        MaxBitrate: "50Mbps total",
        
        // Memory allocation
        DecoderBuffers: "400MB",
        OutputBuffers: "200MB"
    };
    
    void manageMPPLoad() {
        // Dynamic stream quality adjustment
        if (decoder_load_ > 90%) {
            reduceStreamQuality();
        }
        
        // Failover to software decoding if needed
        if (mpp_error_rate_ > 5%) {
            enableSoftwareFallback();
        }
    }
};
```

### RGA (2D Graphics) Allocation

```cpp
class RGAResourceManager {
public:
    struct RGAAllocation {
        // Primary usage (70%)
        RTSPPreprocessing: {
            format_conversion: "NV12 to RGB",
            scaling: "Various to 640x640",
            rotation: "Auto-orientation",
            bandwidth: "70% RGA capacity"
        },
        
        // Secondary usage (30%)
        UIRendering: {
            video_overlay: "Stream thumbnails",
            gui_acceleration: "Qt rendering",
            bandwidth: "30% RGA capacity"
        }
    };
};
```

## Inter-Module Communication

### Shared Memory Architecture

```cpp
class SharedMemoryManager {
public:
    struct SharedBuffers {
        // RTSP → Face Detection
        VideoFrameQueue: {
            buffer_count: 16,
            frame_size: "1920x1080 NV12",
            total_size: "200MB",
            access_pattern: "SPSC queue"
        },
        
        // Face Detection → Face Recognition
        DetectionResultQueue: {
            buffer_count: 100,
            result_size: "Face ROI + metadata",
            total_size: "50MB",
            access_pattern: "MPMC queue"
        },
        
        // Face Recognition → UI
        RecognitionResultQueue: {
            buffer_count: 50,
            result_size: "Identity + confidence",
            total_size: "10MB",
            access_pattern: "MPSC queue"
        }
    };
    
    void optimizeDataFlow() {
        // Zero-copy operations using DMABUF
        enableDMABufSharing();
        
        // Lock-free queues for high throughput
        useLockFreeQueues();
        
        // Memory-mapped files for large data
        useMemoryMappedBuffers();
    }
};
```

## Performance Monitoring and Adaptation

### Resource Usage Monitoring

```cpp
class SystemMonitor {
public:
    struct ResourceMetrics {
        // CPU utilization per core
        std::array<float, 8> cpu_usage_per_core;
        
        // Memory usage per module
        struct MemoryUsage {
            size_t ui_client_mb;
            size_t rtsp_module_mb;
            size_t face_detection_mb;
            size_t face_recognition_mb;
            size_t shared_buffers_mb;
            size_t system_available_mb;
        } memory;
        
        // Hardware accelerator usage
        struct HardwareUsage {
            float npu_utilization_percent;
            float mpp_utilization_percent;
            float rga_utilization_percent;
            float gpu_utilization_percent;
        } hardware;
        
        // System health
        int soc_temperature_celsius;
        float power_consumption_watts;
        std::vector<float> module_fps;
    };
    
    void adaptResourceAllocation(const ResourceMetrics& metrics) {
        // Thermal throttling
        if (metrics.soc_temperature_celsius > 80) {
            reducePerformanceMode();
        }
        
        // Memory pressure handling
        if (metrics.memory.system_available_mb < 100) {
            triggerMemoryCleanup();
        }
        
        // Load balancing
        if (metrics.cpu_usage_per_core[2] > 90) {  // RTSP overload
            redistributeRTSPLoad();
        }
    }
};
```

## Configuration Templates

### Production Configuration (4GB)

```json
{
  "system_config": {
    "platform": "rk3588_4gb",
    "performance_mode": "balanced",
    "thermal_limit_celsius": 80
  },
  "resource_allocation": {
    "rtsp_module": {
      "max_streams": 6,
      "memory_limit_mb": 1200,
      "cpu_cores": [2, 3],
      "thread_priority": "high"
    },
    "face_detection": {
      "npu_allocation_tops": 4.8,
      "memory_limit_mb": 800,
      "cpu_cores": [4, 5],
      "thread_priority": "realtime"
    },
    "face_recognition": {
      "npu_allocation_tops": 1.2,
      "memory_limit_mb": 600,
      "cpu_cores": [6, 7],
      "thread_priority": "realtime"
    },
    "ui_client": {
      "memory_limit_mb": 400,
      "cpu_cores": [0, 1],
      "thread_priority": "normal"
    }
  }
}
```

### Production Configuration (8GB)

```json
{
  "system_config": {
    "platform": "rk3588_8gb",
    "performance_mode": "high_performance",
    "thermal_limit_celsius": 85
  },
  "resource_allocation": {
    "rtsp_module": {
      "max_streams": 12,
      "memory_limit_mb": 2500,
      "cpu_cores": [2, 3],
      "thread_priority": "high"
    },
    "face_detection": {
      "npu_allocation_tops": 4.8,
      "memory_limit_mb": 1500,
      "cpu_cores": [4, 5],
      "thread_priority": "realtime"
    },
    "face_recognition": {
      "npu_allocation_tops": 1.2,
      "memory_limit_mb": 1500,
      "cpu_cores": [6, 7],
      "thread_priority": "realtime"
    },
    "ui_client": {
      "memory_limit_mb": 500,
      "cpu_cores": [0, 1],
      "thread_priority": "normal"
    }
  }
}
```

Phân chia tài nguyên này đảm bảo mỗi module có đủ tài nguyên để hoạt động hiệu quả trong khi tránh xung đột và tối ưu hóa hiệu suất tổng thể của hệ thống AI Box.
