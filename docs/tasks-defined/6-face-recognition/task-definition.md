# Face Recognition NPU Module Task Definition

## Overview
This module is responsible for running face recognition models (such as ArcFace or FaceNet) on the RK3588 NPU to generate facial embeddings for extracted face regions. These embeddings are then used for face identification and verification.

## Requirements
- Run optimized face recognition models on the RK3588 NPU
- Process multiple face images in batches for optimal performance
- Maximize NPU utilization through proper core management
- Maintain zero-copy operations for input/output data
- Generate high-quality facial embeddings for accurate recognition

## Technical Approach
1. **RKNN Model Preparation and Initialization**:
   - Load optimized, quantized face recognition model (.rknn format)
   - Initialize RKNN context with appropriate flags:
     - `RKNN_FLAG_MEM_ALLOC_OUTSIDE` for zero-copy
     - `RKNN_FLAG_ASYNC_MODE` for asynchronous inference
     - `RKNN_FLAG_BATCH_MODE` for batch processing when applicable
   - Configure NPU core assignment using `rknn_set_core_mask` (consider using different cores than detection)

2. **Zero-Copy Input Processing**:
   - Create RKNN tensor memory from DMABUF file descriptors
   - Configure tensor input attributes properly
   - Implement efficient batch processing for multiple face images

3. **Model Inference**:
   - Execute model inference on the NPU
   - Manage asynchronous execution and result synchronization
   - Implement proper resource management to avoid NPU overload

4. **Embedding Processing**:
   - Extract and process facial embeddings from model output
   - Perform L2 normalization if required by the recognition algorithm
   - Maintain associations between embeddings and original detections

5. **NPU Resource Management**:
   - Balance NPU core usage between detection and recognition
   - Implement efficient context switching if using the same cores
   - Monitor and optimize NPU utilization

## Interfaces
- **Input**: DMABUF file descriptors pointing to pre-processed face images
- **Output**: Facial embeddings (feature vectors) for each processed face

## Dependencies
- RKNN Runtime library (librknnrt.so)
- RKNN API headers (rknn_api.h)
- Optimized face recognition models (e.g., facenet.rknn, arcface.rknn)
- C++ standard library

## Performance Considerations
- Efficient NPU core utilization
- Batch processing to maximize throughput
- Asynchronous inference to avoid blocking
- Proper memory management to stay within NPU's 4GB addressable memory limit
- Balance between recognition accuracy and performance
- Consider using INT8 quantized models for optimal performance
