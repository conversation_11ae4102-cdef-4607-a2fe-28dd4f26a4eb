# MPP Decoding Module Sequence Diagrams

This document provides detailed sequence diagrams for the MPP Decoding Module, illustrating the logical flow and component interactions for hardware-accelerated video decoding.

## Module Initialization Sequence

```mermaid
sequenceDiagram
    participant Main as Main Application
    participant Decoder as MPPDecoderModule
    participant <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as DecoderContextManager
    participant <PERSON><PERSON><PERSON><PERSON><PERSON> as DecoderThreadPool
    participant <PERSON>ufferMgr as BufferManager
    
    Main->>Decoder: initialize(config)
    activate Decoder
    Decoder->>CtxManager: create()
    activate CtxManager
    Decoder->>ThreadPool: createThreadPool(thread_count)
    activate ThreadPool
    Decoder->>BufferMgr: initialize(buffer_options)
    activate BufferMgr
    
    BufferMgr->>BufferMgr: initializeDMABUFSupport()
    BufferMgr->>BufferMgr: createBufferPools()
    
    Decoder-->>Main: initialization complete
    deactivate BufferMgr
    deactivate Thread<PERSON>ool
    deactivate CtxManager
    deactivate Decoder
```

## Decoder Context Creation Sequence

```mermaid
sequenceDiagram
    participant A<PERSON> as Application
    participant Decoder as MPPDecoderModule
    participant <PERSON>t<PERSON><PERSON><PERSON><PERSON> as DecoderContextManager
    participant MPP as RockchipMPP
    participant BufferMgr as BufferManager
    
    App->>Decoder: createDecoderForCamera(camera_id, codec_type)
    activate Decoder
    Decoder->>CtxManager: createContext(camera_id, codec_type)
    activate CtxManager
    
    CtxManager->>MPP: mpp_create(&ctx, &mpi)
    activate MPP
    MPP-->>CtxManager: return ctx, mpi
    
    CtxManager->>MPP: mpp_init(ctx, MPP_CTX_DEC, codec_type)
    MPP-->>CtxManager: init status
    
    CtxManager->>MPP: mpi->control(ctx, MPP_DEC_SET_PARSER_SPLIT_MODE, &split_mode)
    MPP-->>CtxManager: control status
    
    CtxManager->>MPP: mpi->control(ctx, MPP_DEC_SET_EXT_BUF_GROUP, &buffer_group)
    MPP-->>CtxManager: control status
    
    CtxManager->>BufferMgr: createBufferGroup(camera_id)
    activate BufferMgr
    BufferMgr->>MPP: mpp_buffer_group_get_internal(&buffer_group, MPP_BUFFER_TYPE_DRM)
    MPP-->>BufferMgr: buffer_group
    
    BufferMgr->>MPP: mpp_buffer_group_limit_config(buffer_group, buffer_limit)
    MPP-->>BufferMgr: limit status
    
    BufferMgr-->>CtxManager: return buffer_group
    deactivate BufferMgr
    
    CtxManager-->>Decoder: decoder_context
    deactivate CtxManager
    Decoder-->>App: decoder_handle
    deactivate Decoder
    deactivate MPP
```

## Packet Decoding Sequence

```mermaid
sequenceDiagram
    participant Queue as InputQueue
    participant Worker as DecoderWorker
    participant Context as DecoderContext
    participant MPP as RockchipMPP
    participant BufferMgr as BufferManager
    participant OutQueue as OutputQueue
    
    activate Worker
    loop Until Shutdown or Error
        Worker->>Queue: dequeuePacket(timeout)
        activate Queue
        
        alt Packet Available
            Queue-->>Worker: return NAL packet
            deactivate Queue
            
            Worker->>Context: getContextForCamera(packet.camera_id)
            activate Context
            Context-->>Worker: return decoder_context
            deactivate Context
            
            Worker->>MPP: mpp_packet_init(&packet, packet_data, packet_size)
            activate MPP
            MPP-->>Worker: packet initialized
            
            Worker->>MPP: mpi->decode_put_packet(ctx, packet)
            
            alt Packet Accepted
                MPP-->>Worker: MPP_OK
                
                Worker->>MPP: mpi->decode_get_frame(ctx, &frame)
                
                alt Frame Ready
                    MPP-->>Worker: return frame
                    
                    Worker->>MPP: mpp_frame_get_info(frame, &info)
                    MPP-->>Worker: frame info
                    
                    alt Valid Frame
                        Worker->>MPP: mpp_frame_get_fd(frame)
                        MPP-->>Worker: return DMABUF fd
                        
                        Worker->>BufferMgr: registerFrameBuffer(fd, info)
                        activate BufferMgr
                        BufferMgr-->>Worker: buffer_handle
                        deactivate BufferMgr
                        
                        Worker->>OutQueue: enqueueFrame(buffer_handle, metadata)
                        activate OutQueue
                        OutQueue-->>Worker: enqueue status
                        deactivate OutQueue
                    else Error Frame
                        Worker->>Worker: handleErrorFrame(info)
                    end
                    
                    Worker->>MPP: mpp_frame_deinit(&frame)
                    MPP-->>Worker: frame deinitialized
                    
                else No Frame Yet
                    MPP-->>Worker: MPP_ERR_TIMEOUT
                end
                
            else Buffer Full
                MPP-->>Worker: MPP_ERR_BUFFER_FULL
                Worker->>Worker: handleBufferFull()
                Worker->>Worker: reschedulePacket()
            else Other Error
                MPP-->>Worker: MPP_ERR_XXX
                Worker->>Worker: handleMPPError()
            end
            
            Worker->>MPP: mpp_packet_deinit(&packet)
            MPP-->>Worker: packet deinitialized
            deactivate MPP
            
        else Queue Empty (Timeout)
            Queue-->>Worker: timeout
            deactivate Queue
            Worker->>Worker: continueLoop()
        else Shutdown Signaled
            Queue-->>Worker: shutdown signal
            deactivate Queue
            Worker->>Worker: break loop
        end
    end
    deactivate Worker
```

## Zero-Copy DMABUF Management Sequence

```mermaid
sequenceDiagram
    participant Worker as DecoderWorker
    participant MPP as RockchipMPP
    participant BufferMgr as BufferManager
    participant DRM as DRMManager
    participant OutQueue as OutputQueue
    
    activate Worker
    Worker->>MPP: mpp_frame_get_fd(frame)
    activate MPP
    MPP-->>Worker: dmabuf_fd
    deactivate MPP
    
    Worker->>BufferMgr: createBufferReference(dmabuf_fd, frame_info)
    activate BufferMgr
    
    BufferMgr->>DRM: drmPrimeHandleToFD(fd)
    activate DRM
    DRM-->>BufferMgr: exportable_fd
    deactivate DRM
    
    BufferMgr->>BufferMgr: createBufferMetadata(exportable_fd, info)
    BufferMgr-->>Worker: buffer_reference
    deactivate BufferMgr
    
    Worker->>OutQueue: enqueueDecodedFrame(buffer_reference)
    activate OutQueue
    OutQueue-->>Worker: enqueue status
    deactivate OutQueue
    deactivate Worker
```

## Error Handling Sequence

```mermaid
sequenceDiagram
    participant Worker as DecoderWorker
    participant ErrorHandler as ErrorHandler
    participant Context as DecoderContext
    participant MPP as RockchipMPP
    participant App as Application
    
    activate Worker
    Worker->>Worker: detectError(error_type)
    Worker->>ErrorHandler: handleDecoderError(camera_id, error_type, details)
    activate ErrorHandler
    
    alt Corrupted Bitstream
        ErrorHandler->>Context: getContext(camera_id)
        activate Context
        ErrorHandler->>MPP: mpi->reset(ctx)
        activate MPP
        MPP-->>ErrorHandler: reset status
        deactivate MPP
        deactivate Context
        ErrorHandler-->>Worker: RESET_DECODER
    else Hardware Error
        ErrorHandler->>Context: recreateContext(camera_id)
        ErrorHandler-->>Worker: RECREATE_DECODER
    else Unsupported Format
        ErrorHandler->>App: notifyUnsupportedFormat(camera_id, format)
        ErrorHandler-->>Worker: SKIP_STREAM
    else Fatal Error
        ErrorHandler->>App: notifyDecoderFailure(camera_id, details)
        ErrorHandler-->>Worker: TERMINATE_DECODER
    end
    
    deactivate ErrorHandler
    
    alt Action is RESET_DECODER
        Worker->>Worker: clearInternalState()
        Worker->>Worker: continueProcessing()
    else Action is RECREATE_DECODER
        Worker->>Worker: reinitializeDecoder()
        Worker->>Worker: continueProcessing()
    else Action is SKIP_STREAM
        Worker->>Worker: skipCurrentStream()
        Worker->>Worker: processNextStream()
    else Action is TERMINATE_DECODER
        Worker->>Worker: cleanupResources()
        Worker->>Worker: signalTermination()
        deactivate Worker
    end
```

## Resource Cleanup Sequence

```mermaid
sequenceDiagram
    participant App as Application
    participant Decoder as MPPDecoderModule
    participant CtxManager as DecoderContextManager
    participant MPP as RockchipMPP
    participant BufferMgr as BufferManager
    
    App->>Decoder: shutdown()
    activate Decoder
    
    Decoder->>CtxManager: shutdownAllDecoders()
    activate CtxManager
    
    loop For Each Decoder Context
        CtxManager->>MPP: mpi->reset(ctx)
        activate MPP
        MPP-->>CtxManager: reset status
        
        CtxManager->>MPP: mpp_destroy(ctx)
        MPP-->>CtxManager: destroy status
        deactivate MPP
    end
    
    CtxManager-->>Decoder: all contexts shutdown
    deactivate CtxManager
    
    Decoder->>BufferMgr: releaseAllBuffers()
    activate BufferMgr
    
    loop For Each Buffer Group
        BufferMgr->>MPP: mpp_buffer_group_put(group)
        activate MPP
        MPP-->>BufferMgr: group released
        deactivate MPP
    end
    
    BufferMgr-->>Decoder: all buffers released
    deactivate BufferMgr
    
    Decoder-->>App: shutdown complete
    deactivate Decoder
```

## Frame Format Conversion Sequence (When Needed)

```mermaid
sequenceDiagram
    participant Worker as DecoderWorker
    participant MPP as RockchipMPP
    participant Converter as FormatConverter
    participant BufferMgr as BufferManager
    participant OutQueue as OutputQueue
    
    activate Worker
    Worker->>MPP: mpp_frame_get_info(frame, &info)
    activate MPP
    MPP-->>Worker: frame format info
    
    alt Format Conversion Needed
        Worker->>Converter: convertFormat(frame, target_format)
        activate Converter
        
        Converter->>MPP: mpp_frame_get_data(frame)
        MPP-->>Converter: source data pointer
        
        Converter->>BufferMgr: allocateTargetBuffer(target_format, dimensions)
        activate BufferMgr
        BufferMgr-->>Converter: target buffer
        deactivate BufferMgr
        
        Converter->>Converter: performFormatConversion()
        Converter-->>Worker: converted buffer
        deactivate Converter
        
        Worker->>OutQueue: enqueueConvertedFrame(buffer, metadata)
        activate OutQueue
        OutQueue-->>Worker: enqueue status
        deactivate OutQueue
    else No Conversion Needed
        Worker->>MPP: mpp_frame_get_fd(frame)
        MPP-->>Worker: dmabuf_fd
        
        Worker->>OutQueue: enqueueDecodedFrame(dmabuf_fd, metadata)
        activate OutQueue
        OutQueue-->>Worker: enqueue status
        deactivate OutQueue
    end
    
    deactivate MPP
    deactivate Worker
```
