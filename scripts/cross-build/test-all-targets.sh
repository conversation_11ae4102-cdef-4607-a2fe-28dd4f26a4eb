#!/bin/bash

# Cross-Compilation Test Script
# Tests all supported cross-compilation targets

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Test results tracking
declare -A test_results
declare -A build_times
declare -A build_sizes

# Test configuration
QUICK_TEST=false
CLEAN_BUILDS=true
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --quick)
            QUICK_TEST=true
            shift
            ;;
        --no-clean)
            CLEAN_BUILDS=false
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            cat << EOF
Cross-Compilation Test Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --quick         Quick test (skip some targets)
    --no-clean      Don't clean build directories
    --verbose       Enable verbose output
    --help          Show this help

DESCRIPTION:
    Tests all supported cross-compilation targets to ensure they build successfully.
    Reports build status, times, and binary sizes for each target.

SUPPORTED TARGETS:
    - Local (native x86_64)
    - Orange Pi 5 Plus (ARM64/RK3588)
    - Raspberry Pi 4/5 (ARM64)
    - Raspberry Pi 3 (ARM hard-float) [requires sysroot]

EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Function to test a cross-compilation target
test_target() {
    local target_name="$1"
    local build_script="$2"
    local build_args="$3"
    
    log_step "Testing $target_name"
    
    local start_time=$(date +%s)
    local build_dir=""
    
    # Determine build directory based on script
    case "$build_script" in
        *local*)
            build_dir="build-local"
            ;;
        *orangepi*)
            build_dir="build-orangepi"
            ;;
        *raspberrypi*)
            build_dir="build-raspberrypi"
            ;;
    esac
    
    # Add clean flag if requested
    if [[ "$CLEAN_BUILDS" == "true" ]]; then
        build_args="--clean $build_args"
    fi
    
    # Add verbose flag if requested
    if [[ "$VERBOSE" == "true" ]]; then
        build_args="--verbose $build_args"
    fi
    
    # Run the build
    cd "$PROJECT_ROOT"
    if $build_script $build_args > "test_${target_name// /_}.log" 2>&1; then
        local end_time=$(date +%s)
        local build_time=$((end_time - start_time))
        
        # Get build size
        local build_size="unknown"
        if [[ -d "$build_dir" ]]; then
            build_size=$(du -sh "$build_dir" 2>/dev/null | cut -f1 || echo "unknown")
        fi
        
        # Check for executables
        local exe_count=0
        if [[ -d "$build_dir" ]]; then
            exe_count=$(find "$build_dir" -type f -executable -name "*" | grep -E "(server|client)" | wc -l)
        fi
        
        test_results["$target_name"]="PASS"
        build_times["$target_name"]="${build_time}s"
        build_sizes["$target_name"]="$build_size"
        
        log_success "$target_name: Build completed in ${build_time}s (${build_size}, ${exe_count} executables)"
    else
        test_results["$target_name"]="FAIL"
        build_times["$target_name"]="N/A"
        build_sizes["$target_name"]="N/A"
        
        log_error "$target_name: Build failed"
        log_info "Check test_${target_name// /_}.log for details"
        
        # Show last few lines of error log
        if [[ -f "test_${target_name// /_}.log" ]]; then
            echo "Last 10 lines of error log:"
            tail -10 "test_${target_name// /_}.log" | sed 's/^/  /'
        fi
    fi
}

# Function to check cross-compiler availability
check_cross_compiler() {
    local compiler="$1"
    local description="$2"
    
    if command -v "$compiler" >/dev/null 2>&1; then
        local version=$($compiler --version | head -n1)
        log_success "$description: $version"
        return 0
    else
        log_warning "$description: Not available ($compiler)"
        return 1
    fi
}

# Function to verify binary architecture
verify_binary_architecture() {
    local binary="$1"
    local expected_arch="$2"
    
    if [[ ! -f "$binary" ]]; then
        return 1
    fi
    
    if command -v file >/dev/null 2>&1; then
        local arch_info=$(file "$binary" 2>/dev/null)
        case "$expected_arch" in
            "x86_64")
                echo "$arch_info" | grep -q "x86-64" && return 0
                ;;
            "aarch64"|"arm64")
                echo "$arch_info" | grep -q "aarch64\|ARM aarch64" && return 0
                ;;
            "armhf"|"arm")
                echo "$arch_info" | grep -q "ARM" && ! echo "$arch_info" | grep -q "aarch64" && return 0
                ;;
        esac
    fi
    
    return 1
}

# Main test execution
main() {
    log_step "Cross-Compilation Target Testing"
    
    cd "$PROJECT_ROOT"
    
    # Check prerequisites
    log_step "Checking Prerequisites"
    
    if ! command -v cmake >/dev/null 2>&1; then
        log_error "CMake is not installed"
        exit 1
    fi
    
    log_info "CMake version: $(cmake --version | head -n1)"
    
    # Check available cross-compilers
    log_step "Checking Cross-Compilers"
    
    local aarch64_available=false
    local armhf_available=false
    
    if check_cross_compiler "aarch64-linux-gnu-gcc" "ARM64 cross-compiler"; then
        aarch64_available=true
    fi
    
    if check_cross_compiler "arm-linux-gnueabihf-gcc" "ARM hard-float cross-compiler"; then
        armhf_available=true
    fi
    
    # Test targets
    log_step "Running Cross-Compilation Tests"
    
    # Test 1: Local build (native)
    test_target "Local x86_64" "./scripts/cross-build/build-local.sh" ""
    
    # Test 2: Orange Pi (ARM64)
    if [[ "$aarch64_available" == "true" ]]; then
        test_target "Orange Pi ARM64" "./scripts/cross-build/build-orangepi.sh" ""
    else
        test_results["Orange Pi ARM64"]="SKIP"
        log_warning "Orange Pi ARM64: Skipped (no cross-compiler)"
    fi
    
    # Test 3: Raspberry Pi ARM64
    if [[ "$aarch64_available" == "true" ]]; then
        test_target "Raspberry Pi ARM64" "./scripts/cross-build/build-raspberrypi.sh" "--arch arm64"
    else
        test_results["Raspberry Pi ARM64"]="SKIP"
        log_warning "Raspberry Pi ARM64: Skipped (no cross-compiler)"
    fi
    
    # Test 4: Raspberry Pi ARM hard-float (requires sysroot)
    if [[ "$armhf_available" == "true" && "$QUICK_TEST" != "true" ]]; then
        log_warning "Raspberry Pi ARM hard-float requires proper sysroot configuration"
        log_info "Skipping ARM hard-float test (known sysroot issue)"
        test_results["Raspberry Pi ARMhf"]="SKIP"
    else
        test_results["Raspberry Pi ARMhf"]="SKIP"
        log_warning "Raspberry Pi ARMhf: Skipped (requires sysroot or quick test mode)"
    fi
    
    # Binary architecture verification
    log_step "Verifying Binary Architectures"
    
    # Install file command if not available
    if ! command -v file >/dev/null 2>&1; then
        log_info "Installing file command for binary verification..."
        apt-get update && apt-get install -y file >/dev/null 2>&1 || true
    fi
    
    # Verify each successful build
    for target in "${!test_results[@]}"; do
        if [[ "${test_results[$target]}" == "PASS" ]]; then
            local build_dir=""
            local expected_arch=""
            
            case "$target" in
                "Local x86_64")
                    build_dir="build-local"
                    expected_arch="x86_64"
                    ;;
                "Orange Pi ARM64")
                    build_dir="build-orangepi"
                    expected_arch="aarch64"
                    ;;
                "Raspberry Pi ARM64")
                    build_dir="build-raspberrypi"
                    expected_arch="aarch64"
                    ;;
            esac
            
            if [[ -n "$build_dir" && -d "$build_dir" ]]; then
                local server_binary="$build_dir/bin/server"
                if verify_binary_architecture "$server_binary" "$expected_arch"; then
                    log_success "$target: Binary architecture verified ($expected_arch)"
                else
                    log_warning "$target: Binary architecture verification failed"
                fi
            fi
        fi
    done
}

# Show final results
show_results() {
    log_step "Cross-Compilation Test Results"
    
    echo ""
    printf "%-25s %-10s %-15s %-15s\n" "Target" "Status" "Build Time" "Build Size"
    printf "%-25s %-10s %-15s %-15s\n" "------" "------" "----------" "----------"
    
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    local skipped_tests=0
    
    for target in "${!test_results[@]}"; do
        local status="${test_results[$target]}"
        local time="${build_times[$target]:-N/A}"
        local size="${build_sizes[$target]:-N/A}"
        
        case "$status" in
            "PASS")
                printf "%-25s ${GREEN}%-10s${NC} %-15s %-15s\n" "$target" "$status" "$time" "$size"
                ((passed_tests++))
                ;;
            "FAIL")
                printf "%-25s ${RED}%-10s${NC} %-15s %-15s\n" "$target" "$status" "$time" "$size"
                ((failed_tests++))
                ;;
            "SKIP")
                printf "%-25s ${YELLOW}%-10s${NC} %-15s %-15s\n" "$target" "$status" "$time" "$size"
                ((skipped_tests++))
                ;;
        esac
        ((total_tests++))
    done
    
    echo ""
    log_info "Summary: $passed_tests passed, $failed_tests failed, $skipped_tests skipped (total: $total_tests)"
    
    if [[ $failed_tests -eq 0 ]]; then
        log_success "All available cross-compilation targets are working!"
    else
        log_error "Some cross-compilation targets failed. Check logs for details."
    fi
    
    # Recommendations
    echo ""
    log_step "Recommendations"
    
    if [[ "${test_results[Orange Pi ARM64]}" == "PASS" ]]; then
        log_info "✓ Orange Pi cross-compilation is ready for deployment"
    fi
    
    if [[ "${test_results[Raspberry Pi ARM64]}" == "PASS" ]]; then
        log_info "✓ Raspberry Pi ARM64 cross-compilation is ready for deployment"
    fi
    
    if [[ "${test_results[Raspberry Pi ARMhf]}" == "SKIP" ]]; then
        log_info "• For Raspberry Pi 32-bit support, configure a proper sysroot:"
        log_info "  export RPI_SYSROOT=/path/to/raspberrypi-sysroot"
    fi
    
    if [[ $failed_tests -gt 0 ]]; then
        log_info "• Check individual test logs: test_*.log"
        log_info "• Ensure cross-compilers are installed: ./scripts/cross-build/install-cross-deps.sh"
    fi
    
    echo ""
    log_success "Cross-compilation testing completed!"
}

# Run main function and show results
main "$@"
show_results
