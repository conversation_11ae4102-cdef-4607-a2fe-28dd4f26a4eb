#pragma once

#include <httplib.h>
#include <string>
#include <memory>
#include "shared/config.hpp"
#include "shared/json_utils.hpp"

namespace server::controllers {

/**
 * @brief Base controller class
 * Provides common functionality for all controllers
 */
class BaseController {
public:
    explicit BaseController(std::shared_ptr<shared::ServerConfig> config);
    virtual ~BaseController() = default;
    
protected:
    std::shared_ptr<shared::ServerConfig> config_;
    
    // Response helpers
    void sendJsonResponse(httplib::Response& res, const std::string& json, int status_code = 200);
    void sendErrorResponse(httplib::Response& res, const std::string& error, 
                          const std::string& error_code = "", int status_code = 400);
    void sendSuccessResponse(httplib::Response& res, const std::string& message, 
                           const std::string& data = "");
    
    // Request helpers
    std::string getRequestBody(const httplib::Request& req);
    bool isRequestBodyEmpty(const httplib::Request& req);
    std::string getClientIP(const httplib::Request& req);
    
    // Validation helpers
    bool validateContentType(const httplib::Request& req);
    bool validateRequestSize(const httplib::Request& req, size_t max_size = 1024 * 1024); // 1MB default
    
    // Logging helpers
    void logRequest(const httplib::Request& req, const std::string& controller_name);
    void logResponse(const httplib::Response& res, const std::string& controller_name, 
                    std::chrono::milliseconds processing_time);
    void logError(const std::string& error, const std::string& controller_name);
    
    // CORS helpers
    void setCorsHeaders(httplib::Response& res);
    
private:
    std::string getCurrentTimestamp() const;
};

} // namespace server::controllers
