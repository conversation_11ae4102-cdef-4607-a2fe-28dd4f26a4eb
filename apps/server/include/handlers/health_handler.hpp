#pragma once

#include <memory>
#include <utils/json_utils.hpp>
#include "base_handler.hpp"
#include "services/health_service.hpp"

namespace server::handlers {

/**
 * @brief Handler for health and system information operations
 * Processes health checks and system status requests
 */
class HealthHandler : public BaseHandler {
public:
    HealthHandler(std::shared_ptr<shared::ServerConfig> config,
                 std::shared_ptr<services::IHealthService> health_service);
    ~HealthHandler() override = default;

    // Main processing methods
    utils::JsonValue handleHealthCheck();
    utils::JsonValue handleApiInfo();
    utils::JsonValue handleSystemStatus();

private:
    std::shared_ptr<services::IHealthService> health_service_;

    // Response transformation
    utils::JsonValue transformHealthResponse(const models::HealthResponse& response);
    utils::JsonValue transformApiInfoResponse(const models::ApiInfoResponse& response);
    utils::JsonValue createSystemStatusResponse(const models::HealthResponse& health_response);
    
    // Helper methods
    utils::JsonValue createEndpointsList();
};

} // namespace server::handlers
