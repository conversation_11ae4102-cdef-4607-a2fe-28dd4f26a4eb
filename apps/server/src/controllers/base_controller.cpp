#include "controllers/base_controller.hpp"
#include "shared/config.hpp"
#include <utils/json_utils.hpp>
#include <iostream>
#include <chrono>
#include <iomanip>
#include <sstream>

namespace server::controllers {

BaseController::BaseController(std::shared_ptr<shared::ServerConfig> config)
    : config_(config) {
}

// Response helpers
void BaseController::sendJsonResponse(httplib::Response& res, const std::string& json, int status_code) {
    res.status = status_code;
    res.set_content(json, shared::AppConstants::CONTENT_TYPE_JSON);
    setCorsHeaders(res);
}

void BaseController::sendErrorResponse(httplib::Response& res, const std::string& error,
                                     const std::string& error_code, int status_code) {
    // Create error response using JSON utilities
    utils::JsonObject error_obj;
    error_obj["success"] = utils::JsonValue(false);
    error_obj["error"] = utils::JsonValue(error);
    error_obj["code"] = utils::JsonValue(status_code);
    if (!error_code.empty()) {
        error_obj["error_code"] = utils::JsonValue(error_code);
    }

    std::string json = utils::JsonUtils::stringify(utils::JsonValue(error_obj));
    sendJsonResponse(res, json, status_code);
}

void BaseController::sendSuccessResponse(httplib::Response& res, const std::string& message,
                                       const std::string& data) {
    // Create success response using JSON utilities
    utils::JsonObject success_obj;
    success_obj["success"] = utils::JsonValue(true);
    success_obj["message"] = utils::JsonValue(message);
    if (!data.empty()) {
        auto data_opt = utils::JsonUtils::parse(data);
        if (data_opt.has_value()) {
            success_obj["data"] = data_opt.value();
        } else {
            success_obj["data"] = utils::JsonValue(data);
        }
    }

    std::string json = utils::JsonUtils::stringify(utils::JsonValue(success_obj));
    sendJsonResponse(res, json, shared::AppConstants::HTTP_OK);
}

// Request helpers
std::string BaseController::getRequestBody(const httplib::Request& req) {
    return req.body;
}

bool BaseController::isRequestBodyEmpty(const httplib::Request& req) {
    return req.body.empty();
}

std::string BaseController::getClientIP(const httplib::Request& req) {
    // Try to get real IP from headers first
    auto x_forwarded_for = req.get_header_value("X-Forwarded-For");
    if (!x_forwarded_for.empty()) {
        // Take the first IP in the list
        size_t comma_pos = x_forwarded_for.find(',');
        return comma_pos != std::string::npos ?
               x_forwarded_for.substr(0, comma_pos) : x_forwarded_for;
    }

    auto x_real_ip = req.get_header_value("X-Real-IP");
    if (!x_real_ip.empty()) {
        return x_real_ip;
    }

    // Fallback to remote address
    return req.remote_addr;
}

// Validation helpers
bool BaseController::validateContentType(const httplib::Request& req) {
    auto content_type = req.get_header_value("Content-Type");
    return content_type.find("application/json") != std::string::npos;
}

bool BaseController::validateRequestSize(const httplib::Request& req, size_t max_size) {
    return req.body.size() <= max_size;
}

// Logging helpers
void BaseController::logRequest(const httplib::Request& req, const std::string& controller_name) {
    if (config_->enable_request_logging) {
        std::cout << "[" << getCurrentTimestamp() << "] "
                  << controller_name << " - "
                  << req.method << " " << req.path
                  << " from " << getClientIP(req) << std::endl;
    }
}

void BaseController::logResponse(const httplib::Response& res, const std::string& controller_name,
                                std::chrono::milliseconds processing_time) {
    if (config_->enable_request_logging) {
        std::cout << "[" << getCurrentTimestamp() << "] "
                  << controller_name << " - Response " << res.status
                  << " (" << processing_time.count() << "ms)" << std::endl;
    }
}

void BaseController::logError(const std::string& error, const std::string& controller_name) {
    std::cout << "[" << getCurrentTimestamp() << "] "
              << controller_name << " - ERROR: " << error << std::endl;
}

// CORS helpers
void BaseController::setCorsHeaders(httplib::Response& res) {
    if (config_->enable_cors) {
        res.set_header("Access-Control-Allow-Origin", config_->cors_origin);
        res.set_header("Access-Control-Allow-Methods", config_->cors_methods);
        res.set_header("Access-Control-Allow-Headers", config_->cors_headers);
    }
}

// Private helpers
std::string BaseController::getCurrentTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

} // namespace server::controllers
