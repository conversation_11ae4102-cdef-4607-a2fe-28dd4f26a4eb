# Development Environment Flow Documentation

This document provides a comprehensive guide for starting and managing the development environment, clearly distinguishing between **HOST** and **CONTAINER** operations.

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                           HOST SYSTEM                          │
├─────────────────────────────────────────────────────────────────┤
│  • Docker Engine                                               │
│  • X11 Server (for GUI)                                        │
│  • GPU Drivers (NVIDIA/Mesa)                                   │
│  • DRI devices (/dev/dri/*)                                    │
│  • Source code (mounted to container)                          │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      DEV CONTAINER                             │
├─────────────────────────────────────────────────────────────────┤
│  • Ubuntu 24.04 + Development Tools                            │
│  • Qt5 + WebEngine                                             │
│  • CMake + Build Tools                                         │
│  • X11 Client Libraries                                        │
│  • OpenGL/Mesa Libraries                                       │
│  • Your Application Code                                       │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start Flow

### Step 1: Host Preparation

```bash
# [HOST] Navigate to project root
cd /path/to/c-aibox

# [HOST] Run the development flow script
./scripts/start-dev-flow.sh
```

### Step 2: Container Development

```bash
# [CONTAINER] Build your project
cmake -S . -B build
cmake --build build

# [CONTAINER] Run GUI applications
./build/your-qt-app

# [CONTAINER] Run tests
./scripts/test.sh
```

## 📋 Detailed Flow Steps

### Phase 1: Host System Setup

#### 1.1 Prerequisites Check (HOST)

- ✅ Docker Engine installed and running
- ✅ User in docker group
- ✅ X11 server available (for GUI apps)
- ✅ GPU drivers installed (if using GPU acceleration)

#### 1.2 Environment Preparation (HOST)

```bash
# Check Docker
docker --version
docker info

# Check X11
echo $DISPLAY
xset q

# Check GPU/DRI
ls -la /dev/dri/

# Ensure render node exists
./scripts/utils/create-render-node.sh
```

#### 1.3 Container Build/Start (HOST)

```bash
# Build dev container (first time or after Dockerfile changes)
docker build -f .devcontainer/Dockerfile -t c-aibox-dev .

# Or use VS Code Dev Containers
# - Open in VS Code
# - Ctrl+Shift+P -> "Dev Containers: Rebuild Container"
```

### Phase 2: Container Environment Setup

#### 2.1 Initial Container Setup (CONTAINER)

```bash
# Verify environment
./scripts/utils/setup-gui-env.sh status

# Setup GUI environment if needed
./scripts/gui/setup-gui.sh

# Verify X11 connection
xset q
```

#### 2.2 Project Build Setup (CONTAINER)

```bash
# Configure CMake
cmake -S . -B build -DCMAKE_BUILD_TYPE=Debug

# Build project
cmake --build build -j$(nproc)

# Install dependencies if needed
./scripts/setup.sh
```

### Phase 3: Development Workflow

#### 3.1 Code Development (CONTAINER)

```bash
# Format code
./scripts/format.sh

# Build incrementally
cmake --build build

# Run tests
./scripts/test.sh
```

#### 3.2 GUI Application Testing (CONTAINER)

```bash
# Test GUI environment
./scripts/gui/setup-gui.sh --test

# Run GUI applications
./scripts/dev/run-gui-app.sh

# Debug GUI issues
./scripts/gui/fix-x11-auth.sh
```

#### 3.3 Cross-Platform Development (HOST)

```bash
# Local development build
./scripts/build/build-local.sh --debug

# Cross-compile for Orange Pi
./scripts/build/build-orangepi.sh --ram 8GB

# Deploy to Orange Pi
./scripts/deploy/deploy-to-orangepi.sh --ip *************

# Test on Orange Pi
./scripts/deploy/test-on-orangepi.sh --ip ************* --all
```

#### 3.4 Debugging (CONTAINER)

```bash
# Debug with GDB
gdb ./build/your-app

# Debug with LLDB
lldb ./build/your-app

# Memory debugging with Valgrind
valgrind --tool=memcheck ./build/your-app
```

## 🔧 Environment Variables

### Host Environment

```bash
# X11 Display (automatically set)
DISPLAY=:0

# Docker socket
DOCKER_HOST=unix:///var/run/docker.sock
```

### Container Environment

```bash
# X11 forwarding
DISPLAY=:0
QT_QPA_PLATFORM=xcb
QTWEBENGINE_DISABLE_SANDBOX=1

# OpenGL settings
LIBGL_ALWAYS_INDIRECT=0

# Development settings
CMAKE_BUILD_TYPE=Debug
```

## 🐛 Troubleshooting

### Common Issues and Solutions

#### Issue: "Cannot connect to X server"

```bash
# [HOST] Check X11 server
echo $DISPLAY
xset q

# [CONTAINER] Fix X11 authentication
./scripts/gui/fix-x11-auth.sh

# [CONTAINER] Check container X11 setup
./scripts/utils/setup-gui-env.sh test
```

#### Issue: "No render node found"

```bash
# [HOST] Create missing render node
./scripts/utils/create-render-node.sh

# [HOST] Check DRI devices
ls -la /dev/dri/
```

#### Issue: "Container build fails"

```bash
# [HOST] Clean Docker cache
docker system prune -f

# [HOST] Rebuild without cache
docker build --no-cache -f .devcontainer/Dockerfile -t c-aibox-dev .
```

#### Issue: "GUI apps crash or don't display"

```bash
# [CONTAINER] Check OpenGL
glxinfo | grep "OpenGL renderer"

# [CONTAINER] Test basic GUI
xeyes

# [CONTAINER] Fix OpenGL setup
./scripts/gui/setup-opengl-container.sh
```

## 📁 File Locations

### Host Files

```
/home/<USER>/workspace/c-aibox/          # Project root
├── .devcontainer/                     # Container configuration
├── scripts/                           # Development scripts
└── src/                               # Source code
```

### Container Files

```
/app/                                  # Working directory
├── build/                             # Build output
├── scripts/                           # Development scripts (mounted)
└── src/                               # Source code (mounted)

/tmp/                                  # Temporary files
├── x11-server.log                     # X11 server log
└── xvfb.pid                           # Xvfb process ID
```

## 🔄 Development Lifecycle

### Daily Development Flow

1. **[HOST]** Start development environment: `./scripts/start-dev-flow.sh`
2. **[CONTAINER]** Make code changes
3. **[CONTAINER]** Build: `cmake --build build`
4. **[CONTAINER]** Test: `./scripts/test.sh`
5. **[CONTAINER]** Run GUI app: `./scripts/dev/run-gui-app.sh`
6. **[HOST]** Commit changes when ready

### Weekly Maintenance

1. **[HOST]** Update base image: Rebuild container
2. **[CONTAINER]** Clean build: `./scripts/clean.sh`
3. **[CONTAINER]** Full rebuild: `cmake -S . -B build && cmake --build build`

## 🎯 Best Practices

### Host System

- Keep Docker updated
- Maintain GPU drivers
- Regular system updates
- Monitor disk space for Docker images

### Container Development

- Use incremental builds
- Run tests frequently
- Format code before commits
- Use debugging tools effectively

### Performance Tips

- Use ccache for faster C++ builds
- Mount source as bind volume for better performance
- Use ninja build system for parallel builds
- Enable compiler optimizations for release builds

## 📚 Related Documentation

- [X11 Usage Guide](./X11_USAGE.md)
- [GUI Setup Scripts](./gui/README.md)
- [Platform-specific Setup](./platform/)
- [Development Scripts](./dev/)
- [Utility Scripts](./utils/)
- [Cross-Platform Development](../docs/deployment/cross-platform-development.md)
- [Orange Pi Setup Guide](../docs/deployment/orangepi-setup.md)

## 🆘 Getting Help

If you encounter issues:

1. Check this documentation
2. Run diagnostic scripts: `./scripts/utils/setup-gui-env.sh status`
3. Check logs: `/tmp/x11-server.log`
4. Review container logs: `docker logs <container-id>`
