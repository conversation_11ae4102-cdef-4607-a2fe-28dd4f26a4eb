{"rtsp_module": {"version": "1.0.0", "enabled": true, "auto_detect_platform": false, "platform_override": "8gb", "performance": {"max_concurrent_streams": 12, "thread_pool_size": 6, "io_thread_count": 2, "worker_thread_count": 4, "max_memory_usage_mb": 2500, "cpu_usage_limit_percent": 30.0, "cpu_affinity": [2, 3], "buffer_pool_size": 64, "shared_buffer_size_mb": 150, "enable_mpp_decoder": true, "enable_rga_scaler": true, "enable_dmabuf_zerocopy": true, "mpp_decoder_instances": 12, "adaptive_quality": true, "thermal_management": true, "target_latency_ms": 150, "thermal_throttle_temperature": 80, "thermal_shutdown_temperature": 85}, "network": {"prefer_tcp": true, "tcp_nodelay": true, "udp_buffer_size": 65536, "tcp_buffer_size": 65536, "rtp_port_range_start": 10000, "rtp_port_range_end": 20000, "keep_alive_interval_ms": 30000, "enable_keep_alive": true, "ssl_verify_peer": true, "ssl_verify_hostname": true, "ssl_ca_file": "/etc/ssl/certs/ca-certificates.crt", "dns_timeout_ms": 5000, "connect_timeout_ms": 10000, "socket_timeout_ms": 30000}, "monitoring": {"enable_statistics": true, "statistics_interval_ms": 1000, "statistics_history_size": 3600, "enable_performance_monitoring": true, "enable_thermal_monitoring": true, "enable_memory_monitoring": true, "log_level": "INFO", "log_file_path": "/var/log/aibox/rtsp.log", "max_log_size_mb": 100, "log_rotation_count": 5, "enable_packet_tracing": false, "enable_nal_unit_dumping": false, "enable_gstreamer_debug": false, "export_prometheus": false, "prometheus_port": 9090}}, "streams": [{"rtsp_url": "rtsp://*************:554/stream1", "username": "admin", "password": "password123", "enabled": true, "priority": "high", "transport": "tcp", "timeout_ms": 5000, "connection_timeout_ms": 10000, "read_timeout_ms": 5000, "retry_count": 3, "retry_delay_ms": 1000, "retry_delay_max_ms": 60000, "retry_jitter_ms": 500, "target_resolution": {"width": 1920, "height": 1080}, "target_framerate": {"numerator": 30, "denominator": 1}, "target_bitrate_kbps": 4000, "preferred_codec": "H264", "buffer_size_bytes": 1048576, "queue_size": 100, "jitter_buffer_size_ms": 200, "use_mpp_decoder": true, "use_rga_scaler": true, "use_dmabuf_zerocopy": true, "audio_enabled": false, "metadata": {"location": "entrance", "zone": "security", "camera_type": "dome", "manufacturer": "Hikvision", "model": "DS-2CD2185FWD-I", "description": "Main entrance security camera"}}, {"rtsp_url": "rtsp://*************:554/stream1", "username": "admin", "password": "password123", "enabled": true, "priority": "medium", "transport": "tcp", "timeout_ms": 5000, "connection_timeout_ms": 10000, "read_timeout_ms": 5000, "retry_count": 3, "retry_delay_ms": 1000, "target_resolution": {"width": 1280, "height": 720}, "target_framerate": {"numerator": 25, "denominator": 1}, "target_bitrate_kbps": 2000, "preferred_codec": "H264", "buffer_size_bytes": 524288, "queue_size": 80, "jitter_buffer_size_ms": 150, "use_mpp_decoder": true, "use_rga_scaler": true, "use_dmabuf_zerocopy": true, "audio_enabled": false, "metadata": {"location": "parking", "zone": "monitoring", "camera_type": "bullet", "manufacturer": "Dahua", "model": "IPC-HFW4431R-Z", "description": "Parking area monitoring camera"}}, {"rtsp_url": "rtsp://*************:554/stream1", "username": "admin", "password": "password123", "enabled": false, "priority": "low", "transport": "udp", "timeout_ms": 8000, "connection_timeout_ms": 15000, "read_timeout_ms": 8000, "retry_count": 5, "retry_delay_ms": 2000, "target_resolution": {"width": 640, "height": 480}, "target_framerate": {"numerator": 15, "denominator": 1}, "target_bitrate_kbps": 500, "preferred_codec": "H264", "buffer_size_bytes": 262144, "queue_size": 50, "jitter_buffer_size_ms": 300, "use_mpp_decoder": false, "use_rga_scaler": false, "use_dmabuf_zerocopy": false, "audio_enabled": false, "metadata": {"location": "backup", "zone": "archive", "camera_type": "ptz", "manufacturer": "Generic", "model": "PTZ-001", "description": "Backup camera for testing (currently disabled)"}}]}