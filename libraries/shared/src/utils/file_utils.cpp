#include "utils/file_utils.hpp"
#include <fstream>
#include <sstream>
#include <random>
#include <algorithm>
#include <system_error>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#else
#include <unistd.h>
#include <sys/stat.h>
#endif

namespace utils {

    std::optional<std::string> FileUtils::readFile(const std::string& filename) {
        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return std::nullopt;
        }

        std::string content((std::istreambuf_iterator<char>(file)),
                           std::istreambuf_iterator<char>());
        file.close();
        return content;
    }

    std::optional<std::vector<std::string>> FileUtils::readLines(const std::string& filename) {
        std::ifstream file(filename);
        if (!file.is_open()) {
            return std::nullopt;
        }

        std::vector<std::string> lines;
        std::string line;
        while (std::getline(file, line)) {
            lines.push_back(line);
        }
        file.close();
        return lines;
    }

    std::optional<std::vector<uint8_t>> FileUtils::readBinary(const std::string& filename) {
        std::ifstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return std::nullopt;
        }

        file.seekg(0, std::ios::end);
        size_t size = static_cast<size_t>(file.tellg());
        file.seekg(0, std::ios::beg);

        std::vector<uint8_t> data(size);
        file.read(reinterpret_cast<char*>(data.data()), static_cast<std::streamsize>(size));
        file.close();
        return data;
    }

    bool FileUtils::writeFile(const std::string& filename, const std::string& content, bool append) {
        std::ios::openmode mode = std::ios::out;
        if (append) {
            mode |= std::ios::app;
        }

        std::ofstream file(filename, mode);
        if (!file.is_open()) {
            return false;
        }

        file << content;
        file.close();
        return true;
    }

    bool FileUtils::writeLines(const std::string& filename, const std::vector<std::string>& lines, bool append) {
        std::ios::openmode mode = std::ios::out;
        if (append) {
            mode |= std::ios::app;
        }

        std::ofstream file(filename, mode);
        if (!file.is_open()) {
            return false;
        }

        for (const auto& line : lines) {
            file << line << "\n";
        }
        file.close();
        return true;
    }

    bool FileUtils::writeBinary(const std::string& filename, const std::vector<uint8_t>& data, bool append) {
        std::ios::openmode mode = std::ios::out | std::ios::binary;
        if (append) {
            mode |= std::ios::app;
        }

        std::ofstream file(filename, mode);
        if (!file.is_open()) {
            return false;
        }

        file.write(reinterpret_cast<const char*>(data.data()), static_cast<std::streamsize>(data.size()));
        file.close();
        return true;
    }

    bool FileUtils::exists(const std::string& filename) {
        return std::filesystem::exists(filename);
    }

    bool FileUtils::isFile(const std::string& path) {
        return std::filesystem::is_regular_file(path);
    }

    bool FileUtils::isDirectory(const std::string& path) {
        return std::filesystem::is_directory(path);
    }

    std::optional<size_t> FileUtils::getFileSize(const std::string& filename) {
        std::error_code ec;
        auto size = std::filesystem::file_size(filename, ec);
        if (ec) {
            return std::nullopt;
        }
        return size;
    }

    std::optional<std::chrono::system_clock::time_point> FileUtils::getModificationTime(const std::string& filename) {
        std::error_code ec;
        auto ftime = std::filesystem::last_write_time(filename, ec);
        if (ec) {
            return std::nullopt;
        }

        // Convert file_time to system_clock time_point
        auto sctp = std::chrono::time_point_cast<std::chrono::system_clock::duration>(
            ftime - std::filesystem::file_time_type::clock::now() + std::chrono::system_clock::now());
        return sctp;
    }

    bool FileUtils::copyFile(const std::string& source, const std::string& destination, bool overwrite) {
        std::error_code ec;
        auto options = overwrite ? std::filesystem::copy_options::overwrite_existing
                                 : std::filesystem::copy_options::none;
        return std::filesystem::copy_file(source, destination, options, ec) && !ec;
    }

    bool FileUtils::moveFile(const std::string& source, const std::string& destination) {
        std::error_code ec;
        std::filesystem::rename(source, destination, ec);
        return !ec;
    }

    bool FileUtils::deleteFile(const std::string& filename) {
        std::error_code ec;
        return std::filesystem::remove(filename, ec) && !ec;
    }

    bool FileUtils::createDirectory(const std::string& path) {
        std::error_code ec;
        return std::filesystem::create_directories(path, ec) && !ec;
    }

    bool FileUtils::deleteDirectory(const std::string& path) {
        std::error_code ec;
        return std::filesystem::remove_all(path, ec) > 0 && !ec;
    }

    std::optional<std::vector<std::string>> FileUtils::listFiles(const std::string& path, bool recursive) {
        std::vector<std::string> files;
        std::error_code ec;

        try {
            if (recursive) {
                for (const auto& entry : std::filesystem::recursive_directory_iterator(path, ec)) {
                    if (ec) break;
                    if (entry.is_regular_file()) {
                        files.push_back(entry.path().string());
                    }
                }
            } else {
                for (const auto& entry : std::filesystem::directory_iterator(path, ec)) {
                    if (ec) break;
                    if (entry.is_regular_file()) {
                        files.push_back(entry.path().string());
                    }
                }
            }
        } catch (const std::exception&) {
            return std::nullopt;
        }

        if (ec) {
            return std::nullopt;
        }
        return files;
    }

    std::optional<std::vector<std::string>> FileUtils::listDirectories(const std::string& path, bool recursive) {
        std::vector<std::string> directories;
        std::error_code ec;

        try {
            if (recursive) {
                for (const auto& entry : std::filesystem::recursive_directory_iterator(path, ec)) {
                    if (ec) break;
                    if (entry.is_directory()) {
                        directories.push_back(entry.path().string());
                    }
                }
            } else {
                for (const auto& entry : std::filesystem::directory_iterator(path, ec)) {
                    if (ec) break;
                    if (entry.is_directory()) {
                        directories.push_back(entry.path().string());
                    }
                }
            }
        } catch (const std::exception&) {
            return std::nullopt;
        }

        if (ec) {
            return std::nullopt;
        }
        return directories;
    }

    std::optional<std::vector<std::string>> FileUtils::listAll(const std::string& path, bool recursive) {
        std::vector<std::string> entries;
        std::error_code ec;

        try {
            if (recursive) {
                for (const auto& entry : std::filesystem::recursive_directory_iterator(path, ec)) {
                    if (ec) break;
                    entries.push_back(entry.path().string());
                }
            } else {
                for (const auto& entry : std::filesystem::directory_iterator(path, ec)) {
                    if (ec) break;
                    entries.push_back(entry.path().string());
                }
            }
        } catch (const std::exception&) {
            return std::nullopt;
        }

        if (ec) {
            return std::nullopt;
        }
        return entries;
    }

    std::string FileUtils::getExtension(const std::string& filename) {
        std::filesystem::path path(filename);
        std::string ext = path.extension().string();
        if (!ext.empty() && ext[0] == '.') {
            ext = ext.substr(1);
        }
        return ext;
    }

    std::string FileUtils::getBasename(const std::string& filename) {
        std::filesystem::path path(filename);
        return path.stem().string();
    }

    std::string FileUtils::getDirectory(const std::string& filename) {
        std::filesystem::path path(filename);
        return path.parent_path().string();
    }

    std::string FileUtils::getFilename(const std::string& path) {
        std::filesystem::path p(path);
        return p.filename().string();
    }

    std::string FileUtils::joinPath(const std::vector<std::string>& components) {
        if (components.empty()) {
            return "";
        }

        std::filesystem::path result(components[0]);
        for (size_t i = 1; i < components.size(); ++i) {
            result /= components[i];
        }
        return result.string();
    }

    std::string FileUtils::joinPath(const std::string& path1, const std::string& path2) {
        std::filesystem::path p1(path1);
        std::filesystem::path p2(path2);
        return (p1 / p2).string();
    }

    std::string FileUtils::normalizePath(const std::string& path) {
        std::filesystem::path p(path);
        return std::filesystem::weakly_canonical(p).string();
    }

    std::string FileUtils::getAbsolutePath(const std::string& path) {
        return std::filesystem::absolute(path).string();
    }

    std::string FileUtils::getRelativePath(const std::string& base, const std::string& target) {
        std::filesystem::path basePath(base);
        std::filesystem::path targetPath(target);
        std::error_code ec;
        auto result = std::filesystem::relative(targetPath, basePath, ec);
        if (ec) {
            return target; // Return original if relative path calculation fails
        }
        return result.string();
    }

    std::string FileUtils::getCurrentDirectory() {
        return std::filesystem::current_path().string();
    }

    bool FileUtils::setCurrentDirectory(const std::string& path) {
        std::error_code ec;
        std::filesystem::current_path(path, ec);
        return !ec;
    }

    std::string FileUtils::getTempDirectory() {
        return std::filesystem::temp_directory_path().string();
    }

    std::optional<std::string> FileUtils::createTempFile(const std::string& prefix, const std::string& suffix) {
        std::string tempDir = getTempDirectory();
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 15);

        for (int attempts = 0; attempts < 100; ++attempts) {
            std::string randomStr;
            for (int i = 0; i < 8; ++i) {
                randomStr += "0123456789ABCDEF"[dis(gen)];
            }

            std::string tempFile = joinPath(tempDir, prefix + randomStr + suffix);
            std::ofstream file(tempFile);
            if (file.is_open()) {
                file.close();
                return tempFile;
            }
        }

        return std::nullopt;
    }

    std::optional<std::string> FileUtils::createTempDirectory(const std::string& prefix) {
        std::string tempDir = getTempDirectory();
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 15);

        for (int attempts = 0; attempts < 100; ++attempts) {
            std::string randomStr;
            for (int i = 0; i < 8; ++i) {
                randomStr += "0123456789ABCDEF"[dis(gen)];
            }

            std::string tempDirPath = joinPath(tempDir, prefix + randomStr);
            if (createDirectory(tempDirPath)) {
                return tempDirPath;
            }
        }

        return std::nullopt;
    }

    std::optional<std::vector<std::string>> FileUtils::findFiles(const std::string& directory, const std::string& pattern, bool recursive) {
        auto files = listFiles(directory, recursive);
        if (!files.has_value()) {
            return std::nullopt;
        }

        std::vector<std::string> matches;
        for (const auto& file : files.value()) {
            std::string filename = getFilename(file);
            if (matchGlob(pattern, filename)) {
                matches.push_back(file);
            }
        }
        return matches;
    }

    std::optional<size_t> FileUtils::getDirectorySize(const std::string& path) {
        auto files = listFiles(path, true);
        if (!files.has_value()) {
            return std::nullopt;
        }

        size_t totalSize = 0;
        for (const auto& file : files.value()) {
            auto size = getFileSize(file);
            if (size.has_value()) {
                totalSize += size.value();
            }
        }
        return totalSize;
    }

    bool FileUtils::isReadable(const std::string& filename) {
        std::ifstream file(filename);
        return file.good();
    }

    bool FileUtils::isWritable(const std::string& filename) {
        if (!exists(filename)) {
            // Try to create a test file
            std::ofstream file(filename);
            bool writable = file.good();
            file.close();
            if (writable) {
                deleteFile(filename);
            }
            return writable;
        } else {
            // Try to open existing file for writing
            std::ofstream file(filename, std::ios::app);
            return file.good();
        }
    }

    bool FileUtils::isExecutable(const std::string& filename) {
#ifdef _WIN32
        return getExtension(filename) == "exe" || getExtension(filename) == "bat" || getExtension(filename) == "cmd";
#else
        return access(filename.c_str(), X_OK) == 0;
#endif
    }

    std::optional<std::string> FileUtils::getPermissions(const std::string& filename) {
        std::error_code ec;
        auto perms = std::filesystem::status(filename, ec).permissions();
        if (ec) {
            return std::nullopt;
        }
        return permissionsToString(perms);
    }

    bool FileUtils::setPermissions(const std::string& filename, int permissions) {
        std::error_code ec;
        std::filesystem::permissions(filename, static_cast<std::filesystem::perms>(permissions), ec);
        return !ec;
    }

    std::optional<bool> FileUtils::compareFiles(const std::string& file1, const std::string& file2) {
        auto content1 = readBinary(file1);
        auto content2 = readBinary(file2);

        if (!content1.has_value() || !content2.has_value()) {
            return std::nullopt;
        }

        return content1.value() == content2.value();
    }

    std::optional<std::string> FileUtils::getFileHash(const std::string& filename) {
        // Simplified hash implementation (not actual SHA-256)
        auto content = readBinary(filename);
        if (!content.has_value()) {
            return std::nullopt;
        }

        std::hash<std::string> hasher;
        std::string contentStr(content.value().begin(), content.value().end());
        size_t hash = hasher(contentStr);

        std::ostringstream oss;
        oss << std::hex << hash;
        return oss.str();
    }

    bool FileUtils::watchFile(const std::string& filename, [[maybe_unused]] std::function<void()> callback) {
        // Simplified implementation - just check if file exists
        // In a real implementation, you would use platform-specific file watching APIs
        return exists(filename);
    }

    bool FileUtils::matchGlob(const std::string& pattern, const std::string& text) {
        // Simplified glob matching - supports * and ?
        size_t p = 0, t = 0;
        size_t starIdx = std::string::npos, match = 0;

        while (t < text.length()) {
            if (p < pattern.length() && (pattern[p] == '?' || pattern[p] == text[t])) {
                p++;
                t++;
            } else if (p < pattern.length() && pattern[p] == '*') {
                starIdx = p;
                match = t;
                p++;
            } else if (starIdx != std::string::npos) {
                p = starIdx + 1;
                match++;
                t = match;
            } else {
                return false;
            }
        }

        while (p < pattern.length() && pattern[p] == '*') {
            p++;
        }

        return p == pattern.length();
    }

    std::string FileUtils::permissionsToString(std::filesystem::perms perms) {
        std::string result;

        // Owner permissions
        result += (perms & std::filesystem::perms::owner_read) != std::filesystem::perms::none ? 'r' : '-';
        result += (perms & std::filesystem::perms::owner_write) != std::filesystem::perms::none ? 'w' : '-';
        result += (perms & std::filesystem::perms::owner_exec) != std::filesystem::perms::none ? 'x' : '-';

        // Group permissions
        result += (perms & std::filesystem::perms::group_read) != std::filesystem::perms::none ? 'r' : '-';
        result += (perms & std::filesystem::perms::group_write) != std::filesystem::perms::none ? 'w' : '-';
        result += (perms & std::filesystem::perms::group_exec) != std::filesystem::perms::none ? 'x' : '-';

        // Others permissions
        result += (perms & std::filesystem::perms::others_read) != std::filesystem::perms::none ? 'r' : '-';
        result += (perms & std::filesystem::perms::others_write) != std::filesystem::perms::none ? 'w' : '-';
        result += (perms & std::filesystem::perms::others_exec) != std::filesystem::perms::none ? 'x' : '-';

        return result;
    }

    // Convenience functions implementation
    std::optional<std::string> readFile(const std::string& filename) {
        return FileUtils::readFile(filename);
    }

    bool writeFile(const std::string& filename, const std::string& content) {
        return FileUtils::writeFile(filename, content);
    }

    bool fileExists(const std::string& filename) {
        return FileUtils::exists(filename);
    }

    bool createDir(const std::string& path) {
        return FileUtils::createDirectory(path);
    }

    std::string joinPath(const std::string& path1, const std::string& path2) {
        return FileUtils::joinPath(path1, path2);
    }
}
