{"name": "C++ Monorepo Dev", "build": {"dockerfile": "Dockerfile", "context": ".."}, "customizations": {"vscode": {"settings": {"terminal.integrated.copyOnSelection": true, "editor.defaultFormatter": "ms-vscode.cpptools", "[cpp]": {"editor.defaultFormatter": "ms-vscode.cpptools"}, "[c]": {"editor.defaultFormatter": "ms-vscode.cpptools"}, "editor.formatOnSave": true, "workbench.editor.enablePreview": false}, "extensions": ["ms-vscode.cpptools", "ms-vscode.cmake-tools", "ms-azuretools.vscode-docker", "Augment.vscode-augment"]}}, "postCreateCommand": "echo 'DevContaine<PERSON> started successfully'", "remoteUser": "root", "forwardPorts": [8080, 8081, 5000], "mounts": ["source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"], "runArgs": ["--cap-add=SYS_PTRACE", "--security-opt=seccomp=unconfined"], "containerEnv": {}, "features": {}}