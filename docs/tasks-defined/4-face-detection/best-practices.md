# Best Practices for Face Detection NPU Module Implementation

## Implementation Best Practices

### 1. NPU Model Selection and Optimization

- **Model Architecture Selection**
  - Choose lightweight models optimized for embedded deployment (RetinaFace-MobileNet preferred)
  - Balance accuracy with inference speed based on your requirements
  - Consider different model variants for different camera resolutions

- **Quantization Strategy**
  - Use INT8 quantization for maximum performance on RK3588's NPU
  - Ensure calibration dataset is representative of real-world camera input
  - Monitor accuracy loss after quantization and adjust parameters if needed

- **Model Conversion Workflow**
  - Follow proper conversion sequence: Original Framework → ONNX → RKNN
  - Use the latest RKNN-Toolkit2 with RK3588 target specification
  - Set proper optimization flags during conversion: `target_platform='rk3588'`

### 2. NPU Core Utilization

- **Core Assignment Strategy**
  - Use `rknn_set_core_mask()` to manage NPU core allocation
  - For batch processing of a single model, consider `RKNN_NPU_CORE_0_1_2` to use all cores
  - For parallel model instances, assign dedicated cores (e.g., core 0 for detection, cores 1-2 for recognition)

- **Multi-Context Management**
  - Create separate RKNN contexts for different model instances or camera streams
  - Implement a context manager to track and optimize context usage
  - Consider context rotation for handling more streams than available cores

- **Load Balancing**
  - Monitor per-core NPU utilization and adjust assignments dynamically
  - Implement priority-based scheduling for NPU operations
  - Consider time-slicing for NPU cores when handling many streams

### 3. Zero-Copy DMABUF Implementation

- **Input Tensor Setup**
  - Use `rknn_create_mem_from_fd()` to create NPU tensor memory from DMABUF FDs
  - Configure proper tensor attributes matching model input requirements
  - Set `RKNN_FLAG_MEM_ALLOC_OUTSIDE` during context initialization

- **Efficient Memory Management**
  - Implement buffer pooling to reduce allocation overhead
  - Track memory usage to stay within NPU's 4GB addressable limit
  - Use reference counting for shared buffer management

- **Synchronization Strategy**
  - Implement proper synchronization between RGA and NPU operations
  - Use fence mechanisms if available for hardware synchronization
  - Avoid unnecessary CPU synchronization points

### 4. Batch Processing Optimization

- **Frame Batching Strategy**
  - Group frames from multiple cameras or sequential frames for batch processing
  - Find optimal batch size through performance testing (typically 4-8)
  - Implement dynamic batch sizing based on system load

- **Tensor Management**
  - For batched input, configure proper N dimension in NHWC/NCHW layout
  - Use `rknn_inputs_set()` with appropriate stride configuration for batches
  - Implement efficient tensor memory layout for batched inference

- **Asynchronous Processing**
  - Use `RKNN_FLAG_ASYNC_MODE` for non-blocking inference
  - Implement a callback or polling mechanism for completion notification
  - Pipeline batch preparation with inference execution

### 5. Post-Processing Optimization

- **Efficient Result Decoding**
  - Implement optimized anchor box decoding for RetinaFace
  - Use SIMD instructions (NEON) for post-processing when appropriate
  - Consider moving time-consuming post-processing to a separate thread

- **Non-Maximum Suppression (NMS)**
  - Implement efficient NMS algorithms appropriate for face detection
  - Consider Fast NMS variants for better performance
  - Use appropriate IoU thresholds for face detection scenarios

- **Result Filtering**
  - Apply confidence thresholding before expensive NMS operations
  - Filter faces based on minimum/maximum size requirements
  - Consider scene-specific filtering (e.g., distance-based, position-based)

## Implementation Pitfalls to Avoid

1. **NPU Resource Mismanagement**
   - Don't create/destroy RKNN contexts frequently during runtime
   - Avoid loading the same model multiple times
   - Don't exceed the 4GB addressable memory limit of the NPU

2. **Performance Bottlenecks**
   - Avoid synchronous operations in the main processing loop
   - Don't perform unnecessary format conversions or copies
   - Prevent over-batching that might increase latency unnecessarily

3. **Memory Issues**
   - Don't leak NPU memory resources by failing to release outputs
   - Avoid excessive creation/destruction of RKNN tensor memory
   - Prevent fragmentation by consistent tensor allocation patterns

4. **Inference Quality Problems**
   - Don't ignore quantization effects on detection accuracy
   - Avoid excessive image downscaling that might lose face details
   - Don't skip proper pre-processing steps required by the model

## Optimization Strategies

1. **NPU Performance Tuning**
  - Profile model inference with various batch sizes and core configurations
  - Measure impact of different quantization parameters
  - Experiment with different core allocation strategies

2. **Memory Access Optimization**
  - Ensure proper alignment of tensor data
  - Use consistent memory layouts to improve cache efficiency
  - Implement zero-copy throughout the entire detection pipeline

3. **CPU-NPU Coordination**
  - Balance workload between CPU and NPU
  - Offload appropriate pre/post-processing to CPU to free NPU resources
  - Pipeline CPU operations with NPU inference

4. **Throughput vs. Latency Balancing**
  - Optimize for throughput with larger batches for multi-camera systems
  - Optimize for latency with smaller batches for real-time requirements
  - Implement priority lanes for critical camera streams

## Testing and Validation

1. **Accuracy Validation**
  - Compare detection results between floating-point and quantized models
  - Validate performance across different lighting conditions and scenarios
  - Test with a diverse set of faces (different ages, ethnicities, expressions)

2. **Performance Benchmarking**
  - Measure frames per second (FPS) under various load conditions
  - Profile NPU utilization and memory consumption
  - Measure end-to-end latency from frame input to detection results

3. **Resource Utilization**
  - Monitor NPU core usage across multiple streams
  - Track memory allocation patterns during extended operation
  - Measure power consumption for different operational modes

## Hardware-Specific Considerations for RK3588

1. **NPU Architecture**
  - Take advantage of RK3588's tri-core NPU architecture
  - Understand the performance characteristics of each NPU core
  - Consider NPU clock frequency management for thermal control

2. **Memory Constraints**
  - Work within the 4GB addressable memory limitation of the NPU
  - Understand DMA transfer characteristics between system memory and NPU
  - Consider memory bandwidth limitations when designing the pipeline

3. **Thermal Considerations**
  - Monitor NPU temperature during sustained operation
  - Implement thermal throttling strategies for long-running deployments
  - Balance NPU workload to prevent hotspots

## Implementation Examples

1. **Efficient Context Initialization**
   ```cpp
   // Load model once and reuse context
   rknn_context ctx;
   
   // Read model file
   FILE* fp = fopen(model_path, "rb");
   fseek(fp, 0, SEEK_END);
   size_t model_size = ftell(fp);
   fseek(fp, 0, SEEK_SET);
   
   void* model = malloc(model_size);
   fread(model, 1, model_size, fp);
   fclose(fp);
   
   // Init RKNN with zero-copy and async mode flags
   rknn_init_extend init_extend;
   memset(&init_extend, 0, sizeof(init_extend));
   init_extend.flags = RKNN_FLAG_MEM_ALLOC_OUTSIDE | RKNN_FLAG_ASYNC_MODE;
   
   int ret = rknn_init2(&ctx, model, model_size, 0, &init_extend);
   
   // Set core mask to use all three cores
   rknn_core_mask core_mask = RKNN_NPU_CORE_0_1_2;
   ret = rknn_set_core_mask(ctx, core_mask);
   
   free(model);
   ```

2. **Zero-Copy Input Setup**
   ```cpp
   // Query input requirements
   rknn_input_output_num io_num;
   ret = rknn_query(ctx, RKNN_QUERY_IN_OUT_NUM, &io_num, sizeof(io_num));
   
   rknn_tensor_attr input_attrs[io_num.n_input];
   for (uint32_t i = 0; i < io_num.n_input; i++) {
       input_attrs[i].index = i;
       ret = rknn_query(ctx, RKNN_QUERY_INPUT_ATTR, &input_attrs[i], sizeof(input_attrs[i]));
   }
   
   // Create NPU tensor from DMABUF FD
   rknn_tensor_mem* tensor_mem = rknn_create_mem_from_fd(ctx, fd_from_rga, input_size);
   
   // Set as input
   rknn_input inputs[1];
   inputs[0].index = 0;
   inputs[0].type = RKNN_TENSOR_UINT8;
   inputs[0].size = input_size;
   inputs[0].fmt = RKNN_TENSOR_NHWC;
   inputs[0].pass_through = true;  // Pass through for zero-copy
   inputs[0].buf = NULL;  // Not used in zero-copy mode
   
   ret = rknn_set_io_mem(ctx, inputs[0].index, tensor_mem);
   ```

3. **Batch Processing Implementation**
   ```cpp
   // For batch processing with N=4
   std::vector<rknn_tensor_mem*> batch_mems;
   
   // Create tensor memories for each input in batch
   for (int i = 0; i < 4; i++) {
       rknn_tensor_mem* mem = rknn_create_mem_from_fd(ctx, fd_array[i], input_size);
       batch_mems.push_back(mem);
   }
   
   // Setup batch inputs
   rknn_input inputs[1];
   inputs[0].index = 0;
   inputs[0].type = RKNN_TENSOR_UINT8;
   inputs[0].size = input_size * 4;  // 4 times for batch size 4
   inputs[0].fmt = RKNN_TENSOR_NHWC;
   inputs[0].pass_through = false;  // We'll handle batch setup manually
   
   // Use rknn_inputs_set for setting batch inputs
   ret = rknn_inputs_set(ctx, io_num.n_input, inputs);
   
   // Run inference
   ret = rknn_run(ctx, NULL);
   
   // Get outputs
   rknn_output outputs[io_num.n_output];
   memset(outputs, 0, sizeof(outputs));
   for (uint32_t i = 0; i < io_num.n_output; i++) {
       outputs[i].want_float = false;
       outputs[i].is_prealloc = false;
   }
   
   ret = rknn_outputs_get(ctx, io_num.n_output, outputs, NULL);
   
   // Process results...
   
   // Release outputs
   ret = rknn_outputs_release(ctx, io_num.n_output, outputs);
   ```
