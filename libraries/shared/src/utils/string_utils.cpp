#include "utils/string_utils.hpp"
#include <sstream>
#include <algorithm>
#include <cctype>
#include <random>
#include <regex>

namespace utils {

    std::vector<std::string> StringUtils::split(const std::string& str, char delimiter) {
        std::vector<std::string> result;
        if (str.empty()) {
            result.push_back("");
            return result;
        }

        std::stringstream ss(str);
        std::string token;
        while (std::getline(ss, token, delimiter)) {
            result.push_back(token);
        }

        // Handle case where string ends with delimiter
        if (!str.empty() && str.back() == delimiter) {
            result.push_back("");
        }

        return result;
    }

    std::vector<std::string> StringUtils::split(const std::string& str, const std::string& delimiter) {
        std::vector<std::string> result;
        if (delimiter.empty()) {
            result.push_back(str);
            return result;
        }

        size_t start = 0;
        size_t end = str.find(delimiter);

        while (end != std::string::npos) {
            result.push_back(str.substr(start, end - start));
            start = end + delimiter.length();
            end = str.find(delimiter, start);
        }

        result.push_back(str.substr(start));
        return result;
    }

    std::string StringUtils::join(const std::vector<std::string>& strings, const std::string& delimiter) {
        if (strings.empty()) {
            return "";
        }

        std::ostringstream oss;
        for (size_t i = 0; i < strings.size(); ++i) {
            if (i > 0) {
                oss << delimiter;
            }
            oss << strings[i];
        }
        return oss.str();
    }

    std::string StringUtils::trim(const std::string& str) {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos) {
            return "";
        }

        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }

    std::string StringUtils::ltrim(const std::string& str) {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == std::string::npos) {
            return "";
        }
        return str.substr(start);
    }

    std::string StringUtils::rtrim(const std::string& str) {
        size_t end = str.find_last_not_of(" \t\r\n");
        if (end == std::string::npos) {
            return "";
        }
        return str.substr(0, end + 1);
    }

    std::string StringUtils::trim(const std::string& str, const std::string& chars) {
        size_t start = str.find_first_not_of(chars);
        if (start == std::string::npos) {
            return "";
        }

        size_t end = str.find_last_not_of(chars);
        return str.substr(start, end - start + 1);
    }

    std::string StringUtils::toLower(const std::string& str) {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), ::tolower);
        return result;
    }

    std::string StringUtils::toUpper(const std::string& str) {
        std::string result = str;
        std::transform(result.begin(), result.end(), result.begin(), ::toupper);
        return result;
    }

    std::string StringUtils::capitalize(const std::string& str) {
        if (str.empty()) {
            return str;
        }

        std::string result = str;
        result[0] = static_cast<char>(std::toupper(static_cast<unsigned char>(result[0])));
        return result;
    }

    std::string StringUtils::toTitleCase(const std::string& str) {
        std::string result = str;
        bool capitalizeNext = true;

        for (char& c : result) {
            if (std::isspace(c) || std::ispunct(c)) {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                c = static_cast<char>(std::toupper(c));
                capitalizeNext = false;
            } else {
                c = static_cast<char>(std::tolower(c));
            }
        }

        return result;
    }

    bool StringUtils::startsWith(const std::string& str, const std::string& prefix, bool ignoreCase) {
        if (prefix.length() > str.length()) {
            return false;
        }

        if (ignoreCase) {
            return toLowerHelper(str.substr(0, prefix.length())) == toLowerHelper(prefix);
        } else {
            return str.substr(0, prefix.length()) == prefix;
        }
    }

    bool StringUtils::endsWith(const std::string& str, const std::string& suffix, bool ignoreCase) {
        if (suffix.length() > str.length()) {
            return false;
        }

        if (ignoreCase) {
            return toLowerHelper(str.substr(str.length() - suffix.length())) == toLowerHelper(suffix);
        } else {
            return str.substr(str.length() - suffix.length()) == suffix;
        }
    }

    bool StringUtils::contains(const std::string& str, const std::string& substring, bool ignoreCase) {
        if (ignoreCase) {
            return toLowerHelper(str).find(toLowerHelper(substring)) != std::string::npos;
        } else {
            return str.find(substring) != std::string::npos;
        }
    }

    std::string StringUtils::replace(const std::string& str, const std::string& from, const std::string& to) {
        if (from.empty()) {
            return str;
        }

        std::string result = str;
        size_t pos = 0;

        while ((pos = result.find(from, pos)) != std::string::npos) {
            result.replace(pos, from.length(), to);
            pos += to.length();
        }

        return result;
    }

    std::string StringUtils::replaceFirst(const std::string& str, const std::string& from, const std::string& to) {
        if (from.empty()) {
            return str;
        }

        size_t pos = str.find(from);
        if (pos == std::string::npos) {
            return str;
        }

        std::string result = str;
        result.replace(pos, from.length(), to);
        return result;
    }

    std::string StringUtils::replaceLast(const std::string& str, const std::string& from, const std::string& to) {
        if (from.empty()) {
            return str;
        }

        size_t pos = str.rfind(from);
        if (pos == std::string::npos) {
            return str;
        }

        std::string result = str;
        result.replace(pos, from.length(), to);
        return result;
    }

    std::string StringUtils::padLeft(const std::string& str, size_t length, char padChar) {
        if (str.length() >= length) {
            return str;
        }

        return std::string(length - str.length(), padChar) + str;
    }

    std::string StringUtils::padRight(const std::string& str, size_t length, char padChar) {
        if (str.length() >= length) {
            return str;
        }

        return str + std::string(length - str.length(), padChar);
    }

    std::string StringUtils::padCenter(const std::string& str, size_t length, char padChar) {
        if (str.length() >= length) {
            return str;
        }

        size_t totalPadding = length - str.length();
        size_t leftPadding = totalPadding / 2;
        size_t rightPadding = totalPadding - leftPadding;

        return std::string(leftPadding, padChar) + str + std::string(rightPadding, padChar);
    }

    std::string StringUtils::reverse(const std::string& str) {
        std::string result = str;
        std::reverse(result.begin(), result.end());
        return result;
    }

    std::string StringUtils::repeat(const std::string& str, size_t count) {
        std::string result;
        result.reserve(str.length() * count);

        for (size_t i = 0; i < count; ++i) {
            result += str;
        }

        return result;
    }

    bool StringUtils::isBlank(const std::string& str) {
        return trim(str).empty();
    }

    bool StringUtils::isNumeric(const std::string& str) {
        if (str.empty()) {
            return false;
        }

        std::regex numericRegex(R"(^[+-]?(\d+\.?\d*|\.\d+)([eE][+-]?\d+)?$)");
        return std::regex_match(str, numericRegex);
    }

    bool StringUtils::isInteger(const std::string& str) {
        if (str.empty()) {
            return false;
        }

        std::regex integerRegex(R"(^[+-]?\d+$)");
        return std::regex_match(str, integerRegex);
    }

    bool StringUtils::isAlpha(const std::string& str) {
        if (str.empty()) {
            return false;
        }

        return std::all_of(str.begin(), str.end(), [](char c) {
            return std::isalpha(c);
        });
    }

    bool StringUtils::isAlphaNumeric(const std::string& str) {
        if (str.empty()) {
            return false;
        }

        return std::all_of(str.begin(), str.end(), [](char c) {
            return std::isalnum(c);
        });
    }

    size_t StringUtils::count(const std::string& str, const std::string& substring, bool ignoreCase) {
        if (substring.empty()) {
            return 0;
        }

        std::string searchStr = ignoreCase ? toLowerHelper(str) : str;
        std::string searchSubstr = ignoreCase ? toLowerHelper(substring) : substring;

        size_t count = 0;
        size_t pos = 0;

        while ((pos = searchStr.find(searchSubstr, pos)) != std::string::npos) {
            ++count;
            pos += searchSubstr.length();
        }

        return count;
    }

    std::vector<size_t> StringUtils::findAll(const std::string& str, const std::string& substring, bool ignoreCase) {
        std::vector<size_t> positions;

        if (substring.empty()) {
            return positions;
        }

        std::string searchStr = ignoreCase ? toLowerHelper(str) : str;
        std::string searchSubstr = ignoreCase ? toLowerHelper(substring) : substring;

        size_t pos = 0;
        while ((pos = searchStr.find(searchSubstr, pos)) != std::string::npos) {
            positions.push_back(pos);
            pos += searchSubstr.length();
        }

        return positions;
    }

    std::optional<std::string> StringUtils::extractBetween(const std::string& str, const std::string& start, const std::string& end) {
        size_t startPos = str.find(start);
        if (startPos == std::string::npos) {
            return std::nullopt;
        }

        startPos += start.length();
        size_t endPos = str.find(end, startPos);
        if (endPos == std::string::npos) {
            return std::nullopt;
        }

        return str.substr(startPos, endPos - startPos);
    }

    std::string StringUtils::remove(const std::string& str, const std::string& substring, bool /* ignoreCase */) {
        // TODO: Implement ignoreCase functionality
        return replace(str, substring, "");
    }

    std::string StringUtils::truncate(const std::string& str, size_t length, const std::string& suffix) {
        if (str.length() <= length) {
            return str;
        }

        if (length <= suffix.length()) {
            return suffix.substr(0, length);
        }

        return str.substr(0, length - suffix.length()) + suffix;
    }

    bool StringUtils::equalsIgnoreCase(const std::string& str1, const std::string& str2) {
        return toLowerHelper(str1) == toLowerHelper(str2);
    }

    std::string StringUtils::random(size_t length, const std::string& charset) {
        if (charset.empty() || length == 0) {
            return "";
        }

        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<size_t> dis(0, charset.size() - 1);

        std::string result;
        result.reserve(length);

        for (size_t i = 0; i < length; ++i) {
            result += charset[dis(gen)];
        }

        return result;
    }

    std::string StringUtils::toLowerHelper(const std::string& str) {
        return toLower(str);
    }

    // Convenience functions implementation
    std::vector<std::string> split(const std::string& str, char delimiter) {
        return StringUtils::split(str, delimiter);
    }

    std::string join(const std::vector<std::string>& strings, const std::string& delimiter) {
        return StringUtils::join(strings, delimiter);
    }

    std::string trim(const std::string& str) {
        return StringUtils::trim(str);
    }

    std::string toLower(const std::string& str) {
        return StringUtils::toLower(str);
    }

    std::string toUpper(const std::string& str) {
        return StringUtils::toUpper(str);
    }
}
