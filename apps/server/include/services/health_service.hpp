#pragma once

#include <memory>
#include <chrono>
#include "models/inference_response.hpp"
#include "repositories/ai_model_repository.hpp"

namespace server::services {

/**
 * @brief Interface for health service
 * Provides health monitoring and system information
 */
class IHealthService {
public:
    virtual ~IHealthService() = default;

    virtual models::HealthResponse getHealthStatus() = 0;
    virtual models::ApiInfoResponse getApiInfo() = 0;
    virtual bool isSystemHealthy() = 0;
    virtual void recordServerStart() = 0;
    virtual std::chrono::system_clock::time_point getServerStartTime() const = 0;
    virtual std::chrono::milliseconds getUptime() const = 0;
};

/**
 * @brief Implementation of health service
 * Monitors system health and provides API information
 */
class HealthService : public IHealthService {
public:
    explicit HealthService(std::shared_ptr<repositories::IAIModelRepository> repository);
    ~HealthService() override = default;

    models::HealthResponse getHealthStatus() override;
    models::ApiInfoResponse getApiInfo() override;
    bool isSystemHealthy() override;
    void recordServerStart() override;
    std::chrono::system_clock::time_point getServerStartTime() const override;
    std::chrono::milliseconds getUptime() const override;

private:
    std::shared_ptr<repositories::IAIModelRepository> repository_;
    std::chrono::system_clock::time_point server_start_time_;

    // Helper methods
    bool checkModelAvailability() const;
    bool checkSystemResources() const;
    std::string getSystemStatus() const;
    std::vector<std::string> getAvailableEndpoints() const;
};

} // namespace server::services
