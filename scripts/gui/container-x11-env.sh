#!/bin/bash
# X11 environment for container
# Source this file before running GUI applications in container

export DISPLAY=:0
export QT_X11_NO_MITSHM=1
export QT_QPA_PLATFORM=xcb
export QTWEBENGINE_DISABLE_SANDBOX=1
export LIBGL_ALWAYS_INDIRECT=1
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false"

echo "Container X11 environment configured:"
echo "DISPLAY: $DISPLAY"
echo "QT_QPA_PLATFORM: $QT_QPA_PLATFORM"
