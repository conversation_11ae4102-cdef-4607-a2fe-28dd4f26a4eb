# RK3588 Platform-Specific Development Guidelines

## Platform Commitment

**AI Box system được phát triển và tối ưu hóa đặc biệt cho Orange Pi 5 Plus/Ultra với chip RK3588.**

T<PERSON><PERSON> cả các quyết định kỹ thuật, thi<PERSON><PERSON> kế architecture, l<PERSON><PERSON> chọn thư viện, và implementation phải tuân thủ nguyên tắc **RK3588-First Development**.

## Hardware Platform Specifications

### Target Hardware: Orange Pi 5 Plus/Ultra
```
SoC: RockChip RK3588 (8nm, ARM64)
CPU: 4x Cortex-A76 @ 2.4GHz + 4x Cortex-A55 @ 1.8GHz
GPU: Mali-G610 MP4
NPU: 6 TOPS AI accelerator
VPU: MPP (Media Process Platform) - 8K@60fps H.265/VP9 decoder
RGA: 2D Raster Graphics Accelerator
Memory: 4GB/8GB LPDDR4X-4224
Storage: 240GB SSD
Network: Gigabit Ethernet + WiFi 6
OS: ARM64 Linux (Ubuntu 22.04 LTS for Orange Pi)
```

### Hardware Accelerators (Mandatory Usage)
1. **MPP Decoder**: Primary video decoding (H.264/H.265)
2. **RGA Scaler**: Image format conversion and scaling
3. **NPU**: AI inference for face detection/recognition
4. **DMABUF**: Zero-copy memory management
5. **Mali GPU**: UI rendering and optional image processing

## Technology Stack Requirements

### Primary Libraries (RockChip Ecosystem)
```
✅ REQUIRED - RockChip Optimized:
├── GStreamer 1.18+ with RockChip plugins
├── librockchip_mpp (MPP decoder/encoder)
├── librga (RGA 2D graphics acceleration)
├── libdrm_rockchip (DRM/DMABUF support)
├── RKNN-Toolkit2 (NPU inference)
└── Mali GPU drivers

✅ SYSTEM LIBRARIES:
├── Qt 5.15+ (ARM64 optimized)
├── OpenCV 4.5+ (with RK3588 optimizations)
├── OpenSSL (ARM64 native)
└── nlohmann/json (header-only)
```

### Forbidden/Discouraged Libraries
```
❌ AVOID - Poor RK3588 Support:
├── FFmpeg (use only as fallback)
├── x86-specific optimizations
├── CUDA/NVIDIA libraries
├── Intel-specific libraries
└── Heavy desktop frameworks
```

## Development Constraints and Requirements

### 1. Cross-Compilation Mandatory
```bash
# All development must support ARM64 cross-compilation
export CC=aarch64-linux-gnu-gcc
export CXX=aarch64-linux-gnu-g++
export PKG_CONFIG_PATH=/usr/lib/aarch64-linux-gnu/pkgconfig

# CMake toolchain for RK3588
cmake -DCMAKE_TOOLCHAIN_FILE=cmake/rk3588-toolchain.cmake \
      -DCMAKE_BUILD_TYPE=Release \
      -DRK3588_PLATFORM=ON \
      -DORANGE_PI_TARGET=ON \
      ..
```

### 2. Memory Constraints (Strict Limits)
```cpp
// Memory allocation must respect embedded constraints
struct PlatformLimits {
    // 4GB Orange Pi 5 Plus
    static constexpr size_t MAX_SYSTEM_MEMORY_4GB = 3800 * 1024 * 1024;  // 3.8GB usable
    static constexpr size_t RTSP_MODULE_LIMIT_4GB = 1200 * 1024 * 1024;  // 1.2GB max
    
    // 8GB Orange Pi 5 Ultra  
    static constexpr size_t MAX_SYSTEM_MEMORY_8GB = 7500 * 1024 * 1024;  // 7.5GB usable
    static constexpr size_t RTSP_MODULE_LIMIT_8GB = 2500 * 1024 * 1024;  // 2.5GB max
    
    // Thermal constraints
    static constexpr int MAX_SOC_TEMPERATURE = 85;  // Celsius
    static constexpr int THROTTLE_TEMPERATURE = 80; // Start throttling
};
```

### 3. CPU Architecture Optimization
```cpp
// ARM64 specific compiler flags (mandatory)
target_compile_options(${TARGET} PRIVATE
    -march=armv8-a+crc+crypto      # RK3588 architecture
    -mtune=cortex-a76.cortex-a55   # big.LITTLE optimization
    -mcpu=cortex-a76.cortex-a55    # Specific CPU tuning
    -mfpu=neon-fp-armv8            # NEON SIMD optimization
    -ftree-vectorize               # Auto-vectorization
    -ffast-math                    # ARM math optimizations
)

// Thread affinity must respect big.LITTLE architecture
void setupRK3588ThreadAffinity() {
    // Little cores (0-3): UI, system services, light processing
    // Big cores (4-7): AI processing, video processing, heavy computation
}
```

### 4. Hardware Accelerator Integration (Mandatory)
```cpp
// All video processing MUST use MPP decoder
class VideoDecoder {
public:
    bool initializeMPPDecoder() {
        // Primary: Use MPP hardware decoder
        if (!initMPP()) {
            // Fallback: Software decoder (performance warning)
            logWarning("MPP decoder failed, using software fallback");
            return initSoftwareDecoder();
        }
        return true;
    }
};

// All image processing MUST attempt RGA first
class ImageProcessor {
public:
    bool processImage(const Image& input, Image& output) {
        // Primary: Use RGA hardware acceleration
        if (rga_available_) {
            return processWithRGA(input, output);
        }
        // Fallback: Software processing
        return processWithCPU(input, output);
    }
};
```

## Performance Requirements (Platform-Specific)

### Mandatory Performance Targets
```cpp
struct RK3588PerformanceRequirements {
    // RTSP Module specific targets
    struct RTSPTargets {
        // 4GB configuration
        static constexpr int MAX_STREAMS_4GB = 6;
        static constexpr int CPU_USAGE_LIMIT_4GB = 25;  // % of total system
        static constexpr int MEMORY_LIMIT_4GB = 1200;   // MB
        
        // 8GB configuration  
        static constexpr int MAX_STREAMS_8GB = 12;
        static constexpr int CPU_USAGE_LIMIT_8GB = 30;  // % of total system
        static constexpr int MEMORY_LIMIT_8GB = 2500;   // MB
        
        // Common requirements
        static constexpr int MAX_LATENCY_MS = 150;
        static constexpr int MIN_FPS = 25;              // Minimum acceptable
        static constexpr int TARGET_FPS = 30;           // Target performance
    };
    
    // System-wide requirements
    static constexpr int MAX_TOTAL_CPU_USAGE = 80;     // % across all cores
    static constexpr int MAX_SOC_TEMPERATURE = 85;     // Celsius
    static constexpr float MIN_AVAILABLE_MEMORY_GB = 0.5; // Always keep reserve
};
```

### Thermal Management (Critical)
```cpp
class ThermalManager {
public:
    void monitorAndAdapt() {
        int soc_temp = readSoCTemperature();
        
        if (soc_temp > 80) {
            // Reduce performance to prevent overheating
            reduceStreamCount();
            lowerCPUFrequency();
            enableAdaptiveQuality();
        }
        
        if (soc_temp > 85) {
            // Emergency throttling
            emergencyPerformanceReduction();
            logCritical("SoC overheating - emergency throttling activated");
        }
    }
};
```

## Development Workflow Requirements

### 1. Hardware-in-the-Loop Testing (Mandatory)
```bash
# All features must be tested on actual Orange Pi hardware
# No simulation-only testing allowed for final validation

# Required test scenarios:
1. Thermal stress testing (continuous operation)
2. Memory pressure testing (maximum stream count)
3. Network resilience testing (connection failures)
4. Power efficiency testing (sustained operation)
5. Hardware accelerator failure testing (fallback scenarios)
```

### 2. Cross-Platform Build Validation
```cmake
# CMake configuration must validate RK3588 requirements
if(NOT RK3588_PLATFORM)
    message(FATAL_ERROR "This project requires RK3588_PLATFORM=ON")
endif()

if(NOT CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64")
    message(WARNING "Cross-compilation recommended for ARM64 target")
endif()

# Validate required RockChip libraries
find_package(PkgConfig REQUIRED)
pkg_check_modules(MPP REQUIRED rockchip_mpp)
pkg_check_modules(RGA REQUIRED rga)
```

### 3. Performance Profiling Requirements
```cpp
// All modules must include RK3588-specific profiling
class PerformanceProfiler {
public:
    struct RK3588Metrics {
        // CPU metrics per core type
        float little_core_usage[4];  // Cores 0-3
        float big_core_usage[4];     // Cores 4-7
        
        // Hardware accelerator usage
        float mpp_utilization;
        float rga_utilization;
        float npu_utilization;
        float gpu_utilization;
        
        // Thermal and power
        int soc_temperature;
        float power_consumption;
        
        // Memory breakdown
        size_t dmabuf_usage;
        size_t system_memory_usage;
        size_t module_memory_usage;
    };
    
    void collectRK3588Metrics();
    void validatePerformanceTargets();
    void generateOptimizationReport();
};
```

## Deployment Requirements

### 1. Orange Pi Specific Configuration
```json
{
  "platform_config": {
    "target_platform": "orange_pi_5_plus_ultra",
    "soc": "rk3588",
    "architecture": "aarch64",
    "os": "ubuntu_22.04_arm64",
    "hardware_acceleration": {
      "mpp_decoder": "required",
      "rga_scaler": "required", 
      "npu_inference": "required",
      "dmabuf_zerocopy": "required"
    },
    "thermal_management": {
      "enabled": true,
      "max_temperature": 85,
      "throttle_temperature": 80
    }
  }
}
```

### 2. Package Dependencies (ARM64)
```bash
# Required Orange Pi packages
sudo apt install -y \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    gstreamer1.0-rockchip \
    librockchip-mpp-dev \
    librga-dev \
    libdrm-rockchip-dev \
    libmali-g610-dev \
    rknn-toolkit2

# Cross-compilation toolchain
sudo apt install -y \
    gcc-aarch64-linux-gnu \
    g++-aarch64-linux-gnu \
    libc6-dev-arm64-cross
```

## Quality Assurance Requirements

### 1. Platform-Specific Testing
- [ ] Hardware accelerator functionality validation
- [ ] Thermal stress testing under maximum load
- [ ] Memory pressure testing with resource limits
- [ ] Power consumption measurement and optimization
- [ ] Cross-compilation build verification
- [ ] ARM64 runtime performance validation

### 2. Compliance Checklist
- [ ] All video decoding uses MPP hardware decoder
- [ ] All image processing attempts RGA acceleration first
- [ ] Memory usage stays within platform limits
- [ ] CPU affinity respects big.LITTLE architecture
- [ ] Thermal monitoring and throttling implemented
- [ ] Cross-compilation support verified
- [ ] Hardware-in-the-loop testing completed

## Documentation Requirements

All technical documentation must include:
1. **RK3588 Compatibility Notes**: Specific optimizations and constraints
2. **Hardware Accelerator Usage**: Which accelerators are used and why
3. **Performance Benchmarks**: Measured on actual Orange Pi hardware
4. **Thermal Considerations**: Temperature monitoring and throttling
5. **Memory Profiling**: Actual memory usage on target platform
6. **Cross-Compilation Instructions**: Complete build setup for ARM64

---

**Remember: Every technical decision must answer the question: "How does this optimize performance and compatibility specifically for RK3588 and Orange Pi 5 Plus/Ultra?"**

This document serves as the authoritative guide for all RK3588-specific development decisions in the AI Box project.
