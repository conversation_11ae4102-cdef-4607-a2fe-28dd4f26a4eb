#include "handlers/health_handler.hpp"
#include "shared/config.hpp"
#include <iostream>

namespace server::handlers {

HealthHandler::HealthHandler(std::shared_ptr<shared::ServerConfig> config,
                           std::shared_ptr<services::IHealthService> health_service)
    : BaseHandler(config), health_service_(health_service) {
    logInfo("HealthHandler initialized", "HealthHandler");
}

// Main processing methods
utils::JsonValue HealthHandler::handleHealthCheck() {
    logInfo("Processing health check request", "HealthHandler");
    
    try {
        auto health_response = health_service_->getHealthStatus();
        return transformHealthResponse(health_response);
        
    } catch (const std::exception& e) {
        logError("Health check exception: " + std::string(e.what()), "HealthHandler");
        return createErrorResponse("Health check failed", shared::AppConstants::ERROR_INTERNAL, 500);
    }
}

utils::JsonValue HealthHandler::handleApiInfo() {
    logInfo("Processing API info request", "HealthHandler");
    
    try {
        auto api_info = health_service_->getApiInfo();
        auto transformed_response = transformApiInfoResponse(api_info);
        return createSuccessResponse("API information", transformed_response);
        
    } catch (const std::exception& e) {
        logError("API info exception: " + std::string(e.what()), "HealthHandler");
        return createErrorResponse("Failed to get API information", shared::AppConstants::ERROR_INTERNAL, 500);
    }
}

utils::JsonValue HealthHandler::handleSystemStatus() {
    logInfo("Processing system status request", "HealthHandler");
    
    try {
        auto health_response = health_service_->getHealthStatus();
        auto system_status = createSystemStatusResponse(health_response);
        return createSuccessResponse("System status retrieved", system_status);
        
    } catch (const std::exception& e) {
        logError("System status exception: " + std::string(e.what()), "HealthHandler");
        return createErrorResponse("Failed to get system status", shared::AppConstants::ERROR_INTERNAL, 500);
    }
}

// Response transformation
utils::JsonValue HealthHandler::transformHealthResponse(const models::HealthResponse& response) {
    utils::JsonObject result;
    
    result["healthy"] = utils::JsonValue(response.healthy);
    result["status"] = utils::JsonValue(response.status);
    result["timestamp"] = createTimestamp(response.timestamp);
    result["version"] = utils::JsonValue(response.version);
    
    return utils::JsonValue(result);
}

utils::JsonValue HealthHandler::transformApiInfoResponse(const models::ApiInfoResponse& response) {
    utils::JsonObject result;
    
    result["version"] = utils::JsonValue(response.version);
    result["description"] = utils::JsonValue(response.description);
    result["server_start_time"] = createTimestamp(response.server_start_time);
    
    // Transform endpoints array
    utils::JsonArray endpoints_array;
    for (const auto& endpoint : response.endpoints) {
        endpoints_array.push_back(utils::JsonValue(endpoint));
    }
    result["endpoints"] = utils::JsonValue(endpoints_array);
    
    return utils::JsonValue(result);
}

utils::JsonValue HealthHandler::createSystemStatusResponse(const models::HealthResponse& health_response) {
    utils::JsonObject result;
    
    result["healthy"] = utils::JsonValue(health_response.healthy);
    result["status"] = utils::JsonValue(health_response.status);
    result["uptime_ms"] = createDuration(health_service_->getUptime());
    result["server_start_time"] = createTimestamp(health_service_->getServerStartTime());
    result["version"] = utils::JsonValue(shared::AppConstants::APP_VERSION);
    result["timestamp"] = createTimestamp(std::chrono::system_clock::now());
    
    return utils::JsonValue(result);
}

// Helper methods
utils::JsonValue HealthHandler::createEndpointsList() {
    utils::JsonArray endpoints;
    endpoints.push_back(utils::JsonValue("GET /health"));
    endpoints.push_back(utils::JsonValue("GET /api/v1/info"));
    endpoints.push_back(utils::JsonValue("GET /api/v1/status"));
    endpoints.push_back(utils::JsonValue("POST /api/v1/arcface/infer"));
    endpoints.push_back(utils::JsonValue("POST /api/v1/yolo/detect"));
    
    return utils::JsonValue(endpoints);
}

} // namespace server::handlers
