#!/bin/bash

# Orange Pi Docker-based Cross-Compilation Build Script
# Uses Docker container with Ubuntu 22.04 for GLIBC 2.36 compatibility

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
BUILD_IMAGE=false
RUN_BUILD=true
INTERACTIVE=false
DOCKER_TAG="c-aibox-orangepi-compat"
CONTAINER_NAME="c-aibox-build-$$"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --build-image)
            BUILD_IMAGE=true
            shift
            ;;
        --no-build)
            RUN_BUILD=false
            shift
            ;;
        --interactive)
            INTERACTIVE=true
            RUN_BUILD=false
            shift
            ;;
        --tag)
            DOCKER_TAG="$2"
            shift 2
            ;;
        --help)
            cat << EOF
Orange Pi Docker-based Cross-Compilation Build Script

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --build-image       Build Docker image first
    --no-build          Don't run build, just prepare container
    --interactive       Start interactive shell in container
    --tag TAG           Docker image tag (default: c-aibox-orangepi-compat)
    --help              Show this help

DESCRIPTION:
    Uses Docker container with Ubuntu 22.04 to cross-compile c-aibox
    for Orange Pi with GLIBC 2.36 compatibility.

EXAMPLES:
    $0                          # Build using existing image
    $0 --build-image            # Build Docker image first, then compile
    $0 --interactive            # Start interactive shell in container
    $0 --build-image --tag my-tag  # Use custom tag

EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            log_info "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check Docker availability
check_docker() {
    log_step "Checking Docker Availability"

    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker is not installed"
        log_info "Install Docker: https://docs.docker.com/get-docker/"
        exit 1
    fi

    # Try to start Docker daemon if not running
    if ! docker info >/dev/null 2>&1; then
        log_warning "Docker daemon not running, attempting to start..."

        # Try to start dockerd in background
        if command -v dockerd >/dev/null 2>&1; then
            log_info "Starting Docker daemon..."
            dockerd > /tmp/docker.log 2>&1 &
            sleep 10

            # Check again
            if ! docker info >/dev/null 2>&1; then
                log_error "Failed to start Docker daemon"
                log_info "Check /tmp/docker.log for details"
                exit 1
            fi
        else
            log_error "Docker daemon is not running and dockerd not found"
            log_info "Start Docker daemon manually or check permissions"
            exit 1
        fi
    fi

    log_success "Docker is available"
}

# Build Docker image
build_docker_image() {
    log_step "Building Docker Image for Orange Pi Cross-Compilation"
    
    local dockerfile="$PROJECT_ROOT/docker/Dockerfile.orangepi-compat"
    
    if [[ ! -f "$dockerfile" ]]; then
        log_error "Dockerfile not found: $dockerfile"
        exit 1
    fi
    
    log_info "Building Docker image: $DOCKER_TAG"
    log_info "This may take several minutes..."
    
    cd "$PROJECT_ROOT"
    
    if docker build -f "$dockerfile" -t "$DOCKER_TAG" .; then
        log_success "Docker image built successfully: $DOCKER_TAG"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Check if Docker image exists
check_docker_image() {
    if docker image inspect "$DOCKER_TAG" >/dev/null 2>&1; then
        log_info "Docker image found: $DOCKER_TAG"
        return 0
    else
        log_warning "Docker image not found: $DOCKER_TAG"
        return 1
    fi
}

# Run build in Docker container
run_docker_build() {
    log_step "Running Cross-Compilation in Docker Container"
    
    cd "$PROJECT_ROOT"
    
    local docker_args=(
        "--rm"
        "--name" "$CONTAINER_NAME"
    )
    
    if [[ "$INTERACTIVE" == "true" ]]; then
        docker_args+=("-it")
        docker_args+=("--entrypoint" "/bin/bash")
        log_info "Starting interactive shell in container..."
        log_info "Run '/workspace/build-in-container.sh' to build the project"
    else
        log_info "Starting automated build in container..."
    fi
    
    log_info "Docker command: docker run ${docker_args[*]} $DOCKER_TAG"
    
    if docker run "${docker_args[@]}" "$DOCKER_TAG"; then
        if [[ "$INTERACTIVE" != "true" ]]; then
            log_success "Docker build completed successfully"

            # Copy build artifacts from container to host
            log_info "Copying build artifacts from container..."

            # Run the container again just to copy files
            local copy_container="copy-artifacts-$$"
            if docker run --name "$copy_container" "$DOCKER_TAG" echo "Container ready for copy" >/dev/null 2>&1; then
                # Copy build artifacts
                if docker cp "$copy_container:/workspace/build-container" "$PROJECT_ROOT/" 2>/dev/null; then
                    log_success "Build artifacts copied to: build-container/"

                    # List built executables
                    local executables
                    mapfile -t executables < <(find "$PROJECT_ROOT/build-container" -type f -executable -name "*server*" 2>/dev/null || true)

                    if [[ ${#executables[@]} -gt 0 ]]; then
                        log_info "Built executables:"
                        for exe in "${executables[@]}"; do
                            echo "  $(basename "$exe")"
                        done
                    fi

                    # Also list other important binaries
                    local other_binaries
                    mapfile -t other_binaries < <(find "$PROJECT_ROOT/build-container" -type f -executable \( -name "*client*" -o -name "*demo*" -o -name "*test*" \) 2>/dev/null | head -10 || true)

                    if [[ ${#other_binaries[@]} -gt 0 ]]; then
                        log_info "Other built binaries:"
                        for bin in "${other_binaries[@]}"; do
                            echo "  $(basename "$bin")"
                        done
                    fi
                else
                    log_warning "Could not copy build artifacts from container"
                    log_info "Build artifacts remain in the Docker image"
                fi

                # Clean up copy container
                docker rm "$copy_container" >/dev/null 2>&1 || true
            else
                log_warning "Could not create container for copying artifacts"
                log_info "Build artifacts remain in the Docker image"
            fi
        fi
    else
        log_error "Docker build failed"
        exit 1
    fi
}

# Clean up function
cleanup() {
    if docker ps -q -f name="$CONTAINER_NAME" >/dev/null 2>&1; then
        log_info "Cleaning up container: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" >/dev/null 2>&1 || true
        docker rm "$CONTAINER_NAME" >/dev/null 2>&1 || true
    fi
}

# Set up cleanup trap
trap cleanup EXIT

# Print configuration
print_config() {
    log_step "Docker Build Configuration"
    echo "Docker Tag:        $DOCKER_TAG"
    echo "Build Image:       $BUILD_IMAGE"
    echo "Run Build:         $RUN_BUILD"
    echo "Interactive:       $INTERACTIVE"
    echo "Project Root:      $PROJECT_ROOT"
    echo ""
}

# Main execution
main() {
    print_config
    check_docker
    
    # Build image if requested or if it doesn't exist
    if [[ "$BUILD_IMAGE" == "true" ]] || ! check_docker_image; then
        build_docker_image
    fi
    
    # Run build or interactive session
    if [[ "$RUN_BUILD" == "true" ]] || [[ "$INTERACTIVE" == "true" ]]; then
        run_docker_build
    fi
    
    if [[ "$INTERACTIVE" != "true" ]]; then
        log_success "Orange Pi Docker cross-compilation completed!"
        log_info "Build artifacts are in: $PROJECT_ROOT/build-container"
        log_info ""
        log_info "To check GLIBC compatibility:"
        log_info "  ./scripts/cross-build/check-glibc-compat.sh --dir build-container"
        log_info ""
        log_info "To deploy to Orange Pi device:"
        log_info "  ./scripts/deploy/deploy-to-orangepi.sh --ip *************** --build-dir build-container"
        log_info ""
        log_info "To test on Orange Pi:"
        log_info "  ./scripts/deploy/test-on-orangepi.sh --ip ***************"
        log_info ""
        log_info "For integrated build and deploy:"
        log_info "  ./scripts/cross-build/build-and-deploy-orangepi.sh --ip ***************"
    fi
}

# Run main function
main "$@"
