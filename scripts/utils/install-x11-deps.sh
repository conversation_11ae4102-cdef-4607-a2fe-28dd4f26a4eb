#!/bin/bash

# Script to install X11 dependencies for GUI applications
# This script installs Xvfb, window managers, and GUI testing tools

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect OS
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    else
        print_error "Cannot detect OS"
        exit 1
    fi
}

# Install packages for Ubuntu/Debian
install_ubuntu_debian() {
    print_status "Installing X11 dependencies for Ubuntu/Debian..."
    
    # Update package list
    apt-get update
    
    # Install X11 server and utilities
    apt-get install -y \
        xvfb \
        x11-utils \
        x11-xserver-utils \
        xauth \
        xfonts-base \
        xfonts-75dpi \
        xfonts-100dpi \
        fonts-liberation \
        fonts-dejavu-core
    
    # Install window managers (lightweight options)
    apt-get install -y \
        fluxbox \
        openbox \
        xfwm4
    
    # Install GUI testing and utility applications
    apt-get install -y \
        x11-apps \
        mesa-utils \
        glx-utils
    
    print_success "X11 dependencies installed successfully"
}

# Install packages for CentOS/RHEL/Fedora
install_redhat() {
    print_status "Installing X11 dependencies for Red Hat based systems..."
    
    if command -v dnf >/dev/null 2>&1; then
        PKG_MGR="dnf"
    else
        PKG_MGR="yum"
    fi
    
    # Install X11 server and utilities
    $PKG_MGR install -y \
        xorg-x11-server-Xvfb \
        xorg-x11-utils \
        xorg-x11-server-utils \
        xorg-x11-xauth \
        xorg-x11-fonts-base \
        liberation-fonts \
        dejavu-sans-fonts
    
    # Install window managers
    $PKG_MGR install -y \
        fluxbox \
        openbox
    
    # Install GUI testing applications
    $PKG_MGR install -y \
        xorg-x11-apps \
        mesa-utils
    
    print_success "X11 dependencies installed successfully"
}

# Install packages for Alpine Linux
install_alpine() {
    print_status "Installing X11 dependencies for Alpine Linux..."
    
    # Update package index
    apk update
    
    # Install X11 server and utilities
    apk add \
        xvfb \
        xauth \
        x11vnc \
        font-misc-misc \
        font-cursor-misc \
        ttf-liberation \
        ttf-dejavu
    
    # Install window managers
    apk add \
        fluxbox \
        openbox
    
    # Install GUI testing applications
    apk add \
        xeyes \
        xclock \
        mesa-utils
    
    print_success "X11 dependencies installed successfully"
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Main installation function
main() {
    print_status "X11 Dependencies Installer"
    print_status "=========================="
    
    check_root
    detect_os
    
    print_status "Detected OS: $OS"
    
    case "$OS" in
        ubuntu|debian)
            install_ubuntu_debian
            ;;
        centos|rhel|fedora)
            install_redhat
            ;;
        alpine)
            install_alpine
            ;;
        *)
            print_error "Unsupported OS: $OS"
            print_status "Please install the following packages manually:"
            echo "  - Xvfb (X Virtual Framebuffer)"
            echo "  - xauth (X11 authorization utilities)"
            echo "  - A window manager (fluxbox, openbox, etc.)"
            echo "  - X11 fonts"
            echo "  - x11-apps (for testing)"
            exit 1
            ;;
    esac
    
    print_success "Installation complete!"
    print_status "You can now use the start-x11.sh script to start the X11 server"
}

# Show help
show_help() {
    echo "X11 Dependencies Installer"
    echo "=========================="
    echo ""
    echo "This script installs the necessary packages for running X11 GUI applications"
    echo "in a headless environment using Xvfb (X Virtual Framebuffer)."
    echo ""
    echo "Supported systems:"
    echo "  - Ubuntu/Debian"
    echo "  - CentOS/RHEL/Fedora"
    echo "  - Alpine Linux"
    echo ""
    echo "Usage:"
    echo "  sudo $0          # Install X11 dependencies"
    echo "  $0 --help       # Show this help"
    echo ""
    echo "After installation, use start-x11.sh to start the X11 server."
}

# Handle command line arguments
case "${1:-install}" in
    --help|-h|help)
        show_help
        ;;
    install|"")
        main
        ;;
    *)
        echo "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
