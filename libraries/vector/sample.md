```cpp
#include <opencv2/opencv.hpp>
#include <Eigen/Dense>
#include <vector>
#include <thread>
#include <mutex>
#include <httplib.h>
#include <nlohmann/json.hpp>
#include <iostream>

// Sử dụng namespace để tiện
using namespace Eigen;
using json = nlohmann::json;

// Cấu trúc lưu trữ vector và ID
struct VectorData {
    VectorXf vector; // Vector đặc trưng
    int id;          // ID để nhận diện
};

// Kho lưu trữ vector (thread-safe)
class VectorStore {
private:
    std::vector<VectorData> vectors;
    std::mutex mtx;

public:
    void add_vector(const std::vector<float>& vec, int id) {
        std::lock_guard<std::mutex> lock(mtx);
        VectorXf eigen_vec(vec.size());
        for (size_t i = 0; i < vec.size(); ++i) eigen_vec(i) = vec[i];
        vectors.push_back({eigen_vec, id});
    }

    std::vector<std::pair<int, float>> search_cosine_similarity(const std::vector<float>& query, int top_k) {
        VectorXf query_vec(query.size());
        for (size_t i = 0; i < query.size(); ++i) query_vec(i) = query[i];
        
        std::vector<std::pair<int, float>> results;
        std::lock_guard<std::mutex> lock(mtx);
        for (const auto& data : vectors) {
            float sim = query_vec.dot(data.vector) / (query_vec.norm() * data.vector.norm());
            results.emplace_back(data.id, sim);
        }
        // Sắp xếp theo similarity giảm dần
        std::sort(results.begin(), results.end(), 
                  [](const auto& a, const auto& b) { return a.second > b.second; });
        // Lấy top_k kết quả
        if (results.size() > static_cast<size_t>(top_k)) results.resize(top_k);
        return results;
    }
};

// Hàm giả lập trích xuất vector từ frame (thay thế bằng mô hình AI thực tế)
std::vector<float> extract_vector(const cv::Mat& frame) {
    std::vector<float> vec(128, 0.0f); // Giả lập vector 128 chiều
    // Thêm logic trích xuất thực tế (ví dụ: từ mô hình CNN)
    return vec;
}

// Xử lý luồng RTSP
void process_rtsp_stream(const std::string& rtsp_url, VectorStore& store, int camera_id) {
    cv::VideoCapture cap(rtsp_url, cv::CAP_FFMPEG);
    if (!cap.isOpened()) {
        std::cerr << "Cannot open RTSP stream: " << rtsp_url << std::endl;
        return;
    }

    int frame_id = camera_id * 1000; // ID phân biệt frame từ các camera
    cv::Mat frame;
    while (true) {
        if (!cap.read(frame)) break;
        auto vector = extract_vector(frame);
        store.add_vector(vector, frame_id++);
        std::this_thread::sleep_for(std::chrono::milliseconds(100)); // Giả lập xử lý khung hình
    }
    cap.release();
}

int main() {
    // Khởi tạo kho vector
    VectorStore vector_store;

    // Danh sách RTSP URL (thay bằng URL thực tế)
    std::vector<std::string> rtsp_urls = {
        "rtsp://your_camera_1",
        "rtsp://your_camera_2"
    };

    // Khởi động thread cho từng luồng RTSP
    std::vector<std::thread> threads;
    for (size_t i = 0; i < rtsp_urls.size(); ++i) {
        threads.emplace_back(process_rtsp_stream, rtsp_urls[i], std::ref(vector_store), i);
    }

    // Khởi động REST API
    httplib::Server svr;

    svr.Post("/search", [&vector_store](const httplib::Request& req, httplib::Response& res) {
        try {
            auto body = json::parse(req.body);
            std::vector<float> query = body["vector"].get<std::vector<float>>();
            int top_k = body.contains("top_k") ? body["top_k"].get<int>() : 5;
            
            auto results = vector_store.search_cosine_similarity(query, top_k);
            json response;
            for (const auto& [id, sim] : results) {
                response.push_back({{"id", id}, {"similarity", sim}});
            }
            res.set_content(response.dump(), "application/json");
        } catch (const std::exception& e) {
            res.status = 400;
            res.set_content("{\"error\":\"Invalid request\"}", "application/json");
        }
    });

    std::cout << "Server started at http://0.0.0.0:8080" << std::endl;
    svr.listen("0.0.0.0", 8080);

    // Chờ các thread hoàn thành
    for (auto& t : threads) t.join();
    return 0;
}
```
