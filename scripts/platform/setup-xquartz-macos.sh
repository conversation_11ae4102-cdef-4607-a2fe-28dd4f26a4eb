#!/bin/bash

# Script to install and configure XQuartz on macOS for container GUI support
# Supports both Intel and Apple Silicon Macs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if running on macOS
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "This script is designed for macOS only"
        exit 1
    fi
    
    print_info "Detected macOS $(sw_vers -productVersion)"
    
    # Check architecture
    ARCH=$(uname -m)
    if [[ "$ARCH" == "arm64" ]]; then
        print_info "Apple Silicon Mac detected"
    else
        print_info "Intel Mac detected"
    fi
}

# Function to check if XQuartz is installed
check_xquartz_installed() {
    if [[ -d "/Applications/Utilities/XQuartz.app" ]]; then
        return 0
    elif command -v xquartz >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to install Homebrew if not present
install_homebrew() {
    if command -v brew >/dev/null 2>&1; then
        print_success "Homebrew is already installed"
        return 0
    fi
    
    print_info "Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH for Apple Silicon
    if [[ "$ARCH" == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
        eval "$(/opt/homebrew/bin/brew shellenv)"
    fi
    
    print_success "Homebrew installed successfully"
}

# Function to install XQuartz
install_xquartz() {
    if check_xquartz_installed; then
        print_success "XQuartz is already installed"
        return 0
    fi
    
    print_info "Installing XQuartz..."
    
    # Try to install via Homebrew first
    if command -v brew >/dev/null 2>&1; then
        print_info "Installing XQuartz via Homebrew..."
        brew install --cask xquartz
    else
        print_warning "Homebrew not available. Manual installation required."
        print_info "Please download XQuartz from: https://www.xquartz.org/"
        print_info "After installation, run this script again."
        return 1
    fi
    
    print_success "XQuartz installed successfully"
    print_warning "You may need to log out and log back in for XQuartz to work properly"
}

# Function to configure XQuartz
configure_xquartz() {
    print_info "Configuring XQuartz for container access..."
    
    # XQuartz preferences file
    XQUARTZ_PLIST="$HOME/Library/Preferences/org.xquartz.X11.plist"
    
    # Enable network connections
    defaults write org.xquartz.X11 nolisten_tcp -bool false
    defaults write org.xquartz.X11 no_auth -bool false
    defaults write org.xquartz.X11 no_tcp -bool false
    
    # Enable clipboard integration
    defaults write org.xquartz.X11 enable_iglx -bool true
    defaults write org.xquartz.X11 enable_test_extensions -bool true
    
    # Set up authentication
    defaults write org.xquartz.X11 app_to_run /usr/bin/xterm
    defaults write org.xquartz.X11 apps_menu -bool true
    
    print_success "XQuartz configured for container access"
}

# Function to start XQuartz
start_xquartz() {
    print_info "Starting XQuartz..."
    
    # Check if XQuartz is already running
    if pgrep -f "XQuartz" > /dev/null; then
        print_success "XQuartz is already running"
        return 0
    fi
    
    # Start XQuartz
    open -a XQuartz
    
    # Wait for XQuartz to start
    print_info "Waiting for XQuartz to start..."
    sleep 5
    
    # Verify XQuartz is running
    local attempts=0
    while [[ $attempts -lt 10 ]]; do
        if pgrep -f "XQuartz" > /dev/null; then
            print_success "XQuartz started successfully"
            break
        fi
        sleep 2
        ((attempts++))
    done
    
    if [[ $attempts -eq 10 ]]; then
        print_error "XQuartz failed to start"
        return 1
    fi
}

# Function to configure X11 forwarding
setup_x11_forwarding() {
    print_info "Setting up X11 forwarding..."
    
    # Set DISPLAY environment variable
    export DISPLAY=:0
    
    # Add to shell profile
    SHELL_PROFILE=""
    if [[ -f "$HOME/.zshrc" ]]; then
        SHELL_PROFILE="$HOME/.zshrc"
    elif [[ -f "$HOME/.bash_profile" ]]; then
        SHELL_PROFILE="$HOME/.bash_profile"
    elif [[ -f "$HOME/.bashrc" ]]; then
        SHELL_PROFILE="$HOME/.bashrc"
    fi
    
    if [[ -n "$SHELL_PROFILE" ]]; then
        if ! grep -q "export DISPLAY=:0" "$SHELL_PROFILE"; then
            echo "" >> "$SHELL_PROFILE"
            echo "# X11 forwarding for containers" >> "$SHELL_PROFILE"
            echo "export DISPLAY=:0" >> "$SHELL_PROFILE"
            print_success "Added DISPLAY export to $SHELL_PROFILE"
        fi
    fi
    
    # Allow local connections
    if command -v xhost >/dev/null 2>&1; then
        xhost +localhost 2>/dev/null || true
        xhost +127.0.0.1 2>/dev/null || true
        xhost +local: 2>/dev/null || true
        print_success "X11 access configured for local connections"
    fi
}

# Function to test X11 connection
test_x11_connection() {
    print_info "Testing X11 connection..."
    
    # Test with xdpyinfo
    if command -v xdpyinfo >/dev/null 2>&1; then
        if timeout 5s xdpyinfo >/dev/null 2>&1; then
            print_success "X11 connection test successful"
            return 0
        fi
    fi
    
    # Test with xset
    if command -v xset >/dev/null 2>&1; then
        if timeout 5s xset q >/dev/null 2>&1; then
            print_success "X11 connection test successful"
            return 0
        fi
    fi
    
    print_warning "X11 connection test failed - this may be normal if XQuartz just started"
    return 1
}

# Function to create helper scripts
create_helper_scripts() {
    print_info "Creating helper scripts..."
    
    # Create start script
    cat > "$HOME/start_xquartz_for_containers.sh" << 'EOF'
#!/bin/bash
# Helper script to start XQuartz for container GUI support

echo "Starting XQuartz for container GUI support..."

# Kill any existing XQuartz processes
pkill -f XQuartz 2>/dev/null || true
sleep 2

# Start XQuartz
open -a XQuartz
sleep 5

# Configure X11 access
export DISPLAY=:0
xhost +localhost 2>/dev/null || true
xhost +127.0.0.1 2>/dev/null || true
xhost +local: 2>/dev/null || true

echo "XQuartz started and configured for containers"
echo "DISPLAY: $DISPLAY"
echo ""
echo "In your container, use:"
echo "  export DISPLAY=host.docker.internal:0"
echo "  # or for local containers:"
echo "  export DISPLAY=:0"
EOF
    
    chmod +x "$HOME/start_xquartz_for_containers.sh"
    print_success "Helper script created: $HOME/start_xquartz_for_containers.sh"
    
    # Create container environment script
    cat > "./scripts/container_x11_env_macos.sh" << 'EOF'
#!/bin/bash
# X11 environment for container on macOS
# Source this file before running GUI applications in container

# Try different DISPLAY formats for macOS
export DISPLAY=host.docker.internal:0

# Qt settings for container
export QT_X11_NO_MITSHM=1
export QT_QPA_PLATFORM=xcb
export QTWEBENGINE_DISABLE_SANDBOX=1
export LIBGL_ALWAYS_INDIRECT=1
export QT_LOGGING_RULES="qt.qpa.xcb.warning=false"

echo "Container X11 environment configured for macOS:"
echo "DISPLAY: $DISPLAY"
echo "QT_QPA_PLATFORM: $QT_QPA_PLATFORM"

# Test connection
if command -v xdpyinfo >/dev/null 2>&1; then
    if timeout 3s xdpyinfo >/dev/null 2>&1; then
        echo "✅ X11 connection successful"
    else
        echo "❌ X11 connection failed"
        echo "Make sure XQuartz is running on the host"
    fi
fi
EOF
    
    chmod +x "./scripts/container_x11_env_macos.sh"
    print_success "Container environment script created: ./scripts/container_x11_env_macos.sh"
}

# Function to show manual configuration steps
show_manual_steps() {
    print_info ""
    print_warning "Manual configuration required:"
    print_info "1. Open XQuartz"
    print_info "2. Go to XQuartz > Preferences (⌘,)"
    print_info "3. Click on the 'Security' tab"
    print_info "4. Check 'Allow connections from network clients'"
    print_info "5. Restart XQuartz"
    print_info ""
    print_info "Alternative: Run the helper script:"
    print_info "  $HOME/start_xquartz_for_containers.sh"
}

# Main function
main() {
    print_info "XQuartz Setup Script for C-AIBOX GUI on macOS"
    print_info "=============================================="
    
    # Check if running on macOS
    check_macos
    
    # Install Homebrew if needed
    install_homebrew
    
    # Install XQuartz
    if ! install_xquartz; then
        exit 1
    fi
    
    # Configure XQuartz
    configure_xquartz
    
    # Start XQuartz
    start_xquartz
    
    # Setup X11 forwarding
    setup_x11_forwarding
    
    # Create helper scripts
    create_helper_scripts
    
    # Test connection
    test_x11_connection
    
    # Show manual steps
    show_manual_steps
    
    print_info ""
    print_success "XQuartz setup completed!"
    print_info ""
    print_info "Next steps:"
    print_info "1. Complete the manual configuration steps above"
    print_info "2. Restart your dev container if it's already running"
    print_info "3. In container, run: source ./scripts/container_x11_env_macos.sh"
    print_info "4. Test GUI: ./scripts/run_client.sh --gui"
    print_info "5. If GUI fails, use: ./scripts/run_client.sh --headless"
    print_info ""
    print_info "Troubleshooting:"
    print_info "- Restart XQuartz if connection fails"
    print_info "- Check firewall settings if needed"
    print_info "- Use the helper script: $HOME/start_xquartz_for_containers.sh"
}

# Run main function
main "$@"
