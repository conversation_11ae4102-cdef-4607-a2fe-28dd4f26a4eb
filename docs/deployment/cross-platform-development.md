# Cross-Platform Development & Orange Pi 5 Plus Deployment

This document outlines the development workflow for building applications on multiple platforms (Linux x86_64, Windows x86_64, macOS ARM) and deploying to Orange Pi 5 Plus (RK3588 ARM64).

## 🏗️ Development Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    DEVELOPMENT PLATFORMS                       │
├─────────────────────────────────────────────────────────────────┤
│  Linux x86_64        Windows x86_64        macOS ARM64         │
│  ├─ Native Build     ├─ Cross Compile     ├─ Cross Compile     │
│  ├─ Cross Compile    ├─ WSL2 Support      ├─ Docker Support    │
│  └─ Docker Support   └─ Docker Support    └─ Native Build      │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    TARGET PLATFORM                             │
├─────────────────────────────────────────────────────────────────┤
│  Orange Pi 5 Plus (RK3588 ARM64)                              │
│  ├─ Ubuntu 22.04/24.04 ARM64                                  │
│  ├─ Hardware Acceleration (MPP, RGA)                          │
│  ├─ GStreamer + RockChip Plugins                              │
│  └─ 4GB/8GB/16GB RAM Variants                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🎯 Development Workflow

### Phase 1: Local Development
1. **Develop on host platform** (Linux/Windows/macOS)
2. **Test with emulated dependencies** (software rendering)
3. **Build and test locally** (x86_64 or ARM64)

### Phase 2: Cross-Compilation
1. **Cross-compile for ARM64** using toolchains
2. **Package dependencies** for Orange Pi
3. **Create deployment package**

### Phase 3: Orange Pi Deployment
1. **Transfer build artifacts** to Orange Pi
2. **Install runtime dependencies** on target
3. **Test with hardware acceleration**
4. **Performance optimization**

## 🛠️ Build Configurations

### Local Development Build
```bash
# Standard development build (host architecture)
mkdir build-dev && cd build-dev
cmake .. -DCMAKE_BUILD_TYPE=Debug
make -j$(nproc)
```

### Cross-Compilation Build
```bash
# ARM64 cross-compilation build
mkdir build-arm64 && cd build-arm64
cmake .. \
  -DCMAKE_BUILD_TYPE=Release \
  -DCMAKE_TOOLCHAIN_FILE=../cmake/toolchains/aarch64-linux-gnu.cmake \
  -DORANGE_PI_TARGET=ON \
  -DORANGE_PI_RAM_8GB=ON
make -j$(nproc)
```

### Orange Pi Native Build
```bash
# Native build on Orange Pi (for testing/development)
mkdir build-native && cd build-native
cmake .. \
  -DCMAKE_BUILD_TYPE=Release \
  -DORANGE_PI_TARGET=ON \
  -DORANGE_PI_RAM_8GB=ON \
  -DENABLE_HARDWARE_ACCELERATION=ON
make -j4  # Orange Pi has 4+4 cores
```

## 📦 Deployment Strategies

### Strategy 1: Binary Transfer
- **Use case**: Quick testing and development
- **Method**: Copy compiled binaries and dependencies
- **Pros**: Fast deployment, minimal Orange Pi setup
- **Cons**: Dependency management complexity

### Strategy 2: Source + Build
- **Use case**: Development and debugging on target
- **Method**: Transfer source code and build on Orange Pi
- **Pros**: Native optimization, easier debugging
- **Cons**: Slower deployment, requires build tools on target

### Strategy 3: Container Deployment
- **Use case**: Production deployment
- **Method**: Docker containers with all dependencies
- **Pros**: Consistent environment, easy scaling
- **Cons**: Additional overhead, container setup required

## 🔧 Platform-Specific Setup

### Linux x86_64 (Primary Development)
```bash
# Install cross-compilation toolchain
sudo apt-get install gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# Install development dependencies
sudo ./scripts/install-rtsp-deps.sh

# Setup Orange Pi deployment
./scripts/setup-orangepi-deployment.sh
```

### Windows x86_64
```powershell
# Using WSL2 (Recommended)
wsl --install Ubuntu-22.04
# Then follow Linux setup inside WSL2

# Or using MSYS2/MinGW64
# Install MSYS2 and cross-compilation tools
```

### macOS ARM64
```bash
# Install Homebrew dependencies
brew install cmake pkg-config

# Install cross-compilation tools
brew install aarch64-elf-gcc

# Setup deployment scripts
./scripts/setup-orangepi-deployment.sh
```

## 🚀 Automated Deployment Pipeline

### Development Workflow
```bash
# 1. Develop locally
./scripts/dev/build-local.sh

# 2. Test locally
./scripts/dev/test-local.sh

# 3. Cross-compile for Orange Pi
./scripts/build/build-orangepi.sh

# 4. Deploy to Orange Pi
./scripts/deploy/deploy-to-orangepi.sh [IP_ADDRESS]

# 5. Test on Orange Pi
./scripts/deploy/test-on-orangepi.sh [IP_ADDRESS]
```

### Continuous Integration
```yaml
# GitHub Actions / GitLab CI example
stages:
  - build-local     # Build on x86_64
  - test-local      # Unit tests
  - build-arm64     # Cross-compile
  - deploy-test     # Deploy to test Orange Pi
  - integration     # Integration tests
```

## 📋 Orange Pi 5 Plus Specifications

### Hardware Configuration
- **SoC**: RockChip RK3588 (4×A76 + 4×A55)
- **RAM**: 4GB/8GB/16GB LPDDR4X
- **GPU**: Mali-G610 MP4
- **Video**: 8K@60fps decode, 8K@30fps encode
- **NPU**: 6 TOPS AI acceleration

### Software Stack
- **OS**: Ubuntu 22.04/24.04 ARM64
- **Kernel**: Linux 5.10+ with RockChip patches
- **Graphics**: Mali GPU drivers
- **Video**: MPP (Media Process Platform)
- **Scaling**: RGA (Raster Graphic Acceleration)

### Performance Targets
- **Memory Usage**: <2GB for 4GB variant
- **CPU Usage**: <50% average load
- **GPU Utilization**: >70% for video processing
- **Latency**: <100ms RTSP stream processing

## 🔍 Testing Strategy

### Local Testing (Development Platform)
```bash
# Unit tests
./scripts/test/run-unit-tests.sh

# Integration tests (software rendering)
./scripts/test/run-integration-tests.sh

# Performance benchmarks
./scripts/test/run-benchmarks.sh
```

### Orange Pi Testing (Target Platform)
```bash
# Hardware acceleration tests
./scripts/test/test-hardware-accel.sh

# RTSP streaming tests
./scripts/test/test-rtsp-streaming.sh

# Performance profiling
./scripts/test/profile-performance.sh

# Memory usage analysis
./scripts/test/analyze-memory.sh
```

## 📁 Directory Structure

```
c-aibox/
├── scripts/
│   ├── build/
│   │   ├── build-local.sh           # Local development build
│   │   ├── build-orangepi.sh        # Cross-compile for Orange Pi
│   │   └── build-docker.sh          # Docker-based build
│   ├── deploy/
│   │   ├── deploy-to-orangepi.sh    # Deploy to Orange Pi
│   │   ├── setup-orangepi.sh        # Initial Orange Pi setup
│   │   └── sync-dependencies.sh     # Sync runtime dependencies
│   ├── test/
│   │   ├── test-local.sh            # Local testing
│   │   ├── test-orangepi.sh         # Orange Pi testing
│   │   └── benchmark-orangepi.sh    # Performance benchmarking
│   └── dev/
│       ├── setup-dev-env.sh         # Development environment setup
│       └── cross-compile-setup.sh   # Cross-compilation setup
├── cmake/
│   └── toolchains/
│       ├── aarch64-linux-gnu.cmake  # ARM64 cross-compilation
│       └── orangepi-rk3588.cmake    # Orange Pi specific settings
├── docker/
│   ├── Dockerfile.dev               # Development container
│   ├── Dockerfile.orangepi          # Orange Pi deployment container
│   └── docker-compose.yml           # Multi-platform setup
└── docs/
    ├── deployment/
    │   ├── cross-platform-development.md  # This document
    │   ├── orangepi-setup.md              # Orange Pi setup guide
    │   └── troubleshooting.md              # Common issues and solutions
    └── testing/
        ├── testing-strategy.md             # Testing methodology
        └── performance-benchmarks.md       # Performance targets
```

## 🎯 Next Steps

1. **Create deployment scripts** for automated Orange Pi deployment
2. **Setup cross-compilation toolchain** for each development platform
3. **Implement testing framework** for Orange Pi hardware validation
4. **Create performance benchmarking** tools for optimization
5. **Document troubleshooting** procedures for common deployment issues

This cross-platform development strategy ensures efficient development while maintaining compatibility with the target Orange Pi 5 Plus platform and its RK3588 hardware acceleration capabilities.
