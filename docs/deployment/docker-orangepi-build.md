# Docker-based Orange Pi Cross-Compilation Guide

This guide provides instructions for building c-aibox for Orange Pi devices using Docker containers, ensuring compatibility with Orange Pi 5 Plus and similar ARM64 devices.

## 🎯 Quick Start

### Prerequisites
- Docker installed and running
- At least 2GB free disk space
- Orange Pi device with SSH access

### One-Command Build and Deploy
```bash
# Build and deploy to Orange Pi at ***************
./scripts/cross-build/build-and-deploy-orangepi.sh --ip ***************
```

## 🏗️ Docker Build Process

### Step 1: Build Docker Image
```bash
# Build the Docker image for Orange Pi cross-compilation
./scripts/cross-build/build-orangepi-docker.sh --build-image --no-build
```

### Step 2: Cross-Compile Project
```bash
# Run the cross-compilation inside Docker container
./scripts/cross-build/build-orangepi-docker.sh
```

### Step 3: Deploy to Orange Pi
```bash
# Deploy built artifacts to Orange Pi device
./scripts/deploy/deploy-to-orangepi.sh --ip *************** --build-dir build-container
```

## 🔧 Advanced Usage

### Interactive Development
```bash
# Start interactive shell in Docker container
./scripts/cross-build/build-orangepi-docker.sh --interactive
```

### Custom Docker Tag
```bash
# Use custom Docker image tag
./scripts/cross-build/build-orangepi-docker.sh --tag my-custom-tag --build-image
```

### Skip Build Steps
```bash
# Only build Docker image
./scripts/cross-build/build-orangepi-docker.sh --build-image --no-build

# Use existing build artifacts
./scripts/cross-build/build-and-deploy-orangepi.sh --ip *************** --skip-build
```

## 📁 Build Artifacts

After successful compilation, artifacts are available in:
- **Host Directory**: `build-container/`
- **Main Executable**: `build-container/bin/server`
- **Examples**: `build-container/bin/examples/`
- **Libraries**: `build-container/lib/`

## 🎛️ Configuration

### Docker Image Features
- **Base**: Ubuntu 22.04 (GLIBC 2.35)
- **Cross-Compiler**: aarch64-linux-gnu-gcc 11.4.0
- **Target Architecture**: ARM64 (aarch64)
- **Static Linking**: Enabled for maximum compatibility
- **GLIBC Compatibility**: 2.34-2.36

### Build Configuration
- **Build Type**: Release
- **Optimizations**: ARM64 NEON enabled
- **Hardware Acceleration**: RK3588 support
- **Memory Configuration**: 8GB Orange Pi variant
- **Static Libraries**: libgcc, libstdc++ statically linked

## 🚀 Deployment Options

### Basic Deployment
```bash
./scripts/deploy/deploy-to-orangepi.sh --ip *************** --build-dir build-container
```

### Deployment with Services
```bash
./scripts/deploy/deploy-to-orangepi.sh --ip *************** --build-dir build-container --start-services
```

### SSH Key Authentication
```bash
./scripts/deploy/deploy-to-orangepi.sh --ip *************** --ssh-key ~/.ssh/orangepi_key --build-dir build-container
```

## 🧪 Testing

### Basic Functionality Test
```bash
./scripts/deploy/test-on-orangepi.sh --ip ***************
```

### Comprehensive Testing
```bash
./scripts/deploy/test-on-orangepi.sh --ip *************** --all
```

### GLIBC Compatibility Check
```bash
./scripts/cross-build/check-glibc-compat.sh --dir build-container
```

## 🔍 Troubleshooting

### Docker Issues
```bash
# Check Docker status
docker info

# Rebuild Docker image
./scripts/cross-build/build-orangepi-docker.sh --build-image --tag fresh-build
```

### Build Failures
```bash
# Interactive debugging
./scripts/cross-build/build-orangepi-docker.sh --interactive

# Inside container, run:
/usr/local/bin/build-in-container.sh
```

### Deployment Issues
```bash
# Test SSH connection
ssh orangepi@***************

# Check Orange Pi system info
./scripts/deploy/test-on-orangepi.sh --ip ***************
```

## 📋 Build Specifications

### Target Device: Orange Pi 5 Plus
- **SoC**: RockChip RK3588
- **Architecture**: ARM64 (aarch64)
- **CPU**: 4x Cortex-A76 + 4x Cortex-A55
- **RAM**: 4GB/8GB/16GB variants supported
- **OS**: Ubuntu 22.04 or compatible ARM64 Linux

### Compatibility Matrix
| Component | Version | Status |
|-----------|---------|--------|
| GLIBC | 2.34-2.36 | ✅ Compatible |
| GCC | 11.4.0 | ✅ Supported |
| CMake | 3.22+ | ✅ Required |
| Ubuntu | 22.04 | ✅ Tested |

## 🔗 Related Documentation

- [Orange Pi Setup Guide](orangepi-setup.md)
- [Cross-Platform Deployment](../CROSS_PLATFORM_DEPLOYMENT_COMPLETE.md)
- [SSH Key Setup](../scripts/deploy/setup-ssh-key.sh)

## 💡 Tips and Best Practices

1. **First Build**: Always use `--build-image` for the first run
2. **Development**: Use `--interactive` for debugging build issues
3. **Production**: Use the integrated script for automated deployment
4. **Testing**: Always run compatibility checks before deployment
5. **SSH Keys**: Set up passwordless SSH for automated deployment

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review build logs in the terminal output
3. Test with interactive mode for debugging
4. Verify Orange Pi device compatibility
