# Định Nghĩa Tác Vụ Module Nhận Diện Khuôn Mặt NPU

## Tổng Quan
Module này chịu trách nhiệm chạy các mô hình nhận diện khuôn mặt (như ArcFace hoặc FaceNet) trên NPU RK3588 để tạo embedding khuôn mặt cho các vùng khuôn mặt đã được trích xuất. C<PERSON>c embedding này sau đó được sử dụng để nhận dạng và xác minh khuôn mặt.

## Yêu Cầu
- Chạy các mô hình nhận diện khuôn mặt được tối ưu hóa trên NPU RK3588
- Xử lý nhiều hình ảnh khuôn mặt theo batch để có hiệu suất tối ưu
- Tối đa hóa việc sử dụng NPU thông qua quản lý lõi phù hợp
- <PERSON><PERSON> tr<PERSON> các thao tác zero-copy cho dữ liệu đầu vào/đầu ra
- Tạo embedding khuôn mặt chất lượng cao để nhận diện chính xác

## Phương Pháp Kỹ Thuật
1. **Chuẩn Bị và Khởi Tạo Mô Hình RKNN**:
   - Tải mô hình nhận diện khuôn mặt được tối ưu hóa, lượng tử hóa (định dạng .rknn)
   - Khởi tạo context RKNN với các flag phù hợp:
     - `RKNN_FLAG_MEM_ALLOC_OUTSIDE` cho zero-copy
     - `RKNN_FLAG_ASYNC_MODE` cho suy luận bất đồng bộ
     - `RKNN_FLAG_BATCH_MODE` cho xử lý batch khi áp dụng được
   - Cấu hình phân bổ lõi NPU sử dụng `rknn_set_core_mask` (cân nhắc sử dụng lõi khác với phát hiện)

2. **Xử Lý Đầu Vào Zero-Copy**:
   - Tạo bộ nhớ tensor RKNN từ file descriptor DMABUF
   - Cấu hình thuộc tính đầu vào tensor đúng cách
   - Triển khai xử lý batch hiệu quả cho nhiều hình ảnh khuôn mặt

3. **Suy Luận Mô Hình**:
   - Thực thi suy luận mô hình trên NPU
   - Quản lý thực thi bất đồng bộ và đồng bộ hóa kết quả
   - Triển khai quản lý tài nguyên phù hợp để tránh quá tải NPU

4. **Xử Lý Embedding**:
   - Trích xuất và xử lý embedding khuôn mặt từ đầu ra mô hình
   - Thực hiện chuẩn hóa L2 nếu thuật toán nhận diện yêu cầu
   - Duy trì liên kết giữa embedding và các phát hiện gốc

5. **Quản Lý Tài Nguyên NPU**:
   - Cân bằng việc sử dụng lõi NPU giữa phát hiện và nhận diện
   - Triển khai chuyển đổi context hiệu quả nếu sử dụng cùng lõi
   - Giám sát và tối ưu hóa việc sử dụng NPU

## Giao Diện
- **Đầu vào**: File descriptor DMABUF trỏ đến các hình ảnh khuôn mặt đã tiền xử lý
- **Đầu ra**: Embedding khuôn mặt (vector đặc trưng) cho mỗi khuôn mặt được xử lý

## Phụ Thuộc
- Thư viện RKNN Runtime (librknnrt.so)
- Header RKNN API (rknn_api.h)
- Mô hình nhận diện khuôn mặt được tối ưu hóa (ví dụ: facenet.rknn, arcface.rknn)
- Thư viện chuẩn C++

## Cân Nhắc Hiệu Suất
- Sử dụng lõi NPU hiệu quả
- Xử lý batch để tối đa hóa thông lượng
- Suy luận bất đồng bộ để tránh chặn
- Quản lý bộ nhớ phù hợp để duy trì trong giới hạn bộ nhớ địa chỉ 4GB của NPU
- Cân bằng giữa độ chính xác nhận diện và hiệu suất
- Cân nhắc sử dụng mô hình lượng tử hóa INT8 để có hiệu suất tối ưu
