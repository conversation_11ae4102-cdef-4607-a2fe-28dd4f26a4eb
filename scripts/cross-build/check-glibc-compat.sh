#!/bin/bash

# GLIBC Compatibility Checker for Orange Pi
# Checks if binaries are compatible with Orange Pi GLIBC 2.36

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
TARGET_GLIBC="2.36"
CHECK_DIR=""
VERBOSE=false
REMOTE_CHECK=false
ORANGE_PI_IP=""
SSH_USER="orangepi"
SSH_KEY=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dir)
            CHECK_DIR="$2"
            shift 2
            ;;
        --target-glibc)
            TARGET_GLIBC="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --remote)
            REMOTE_CHECK=true
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --ssh-user)
            SSH_USER="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --help)
            cat << EOF
GLIBC Compatibility Checker for Orange Pi

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --dir PATH          Directory to check (default: build directories)
    --target-glibc VER  Target GLIBC version (default: 2.36)
    --verbose           Enable verbose output
    --remote IP         Check compatibility on remote Orange Pi
    --ssh-user USER     SSH username for remote check (default: orangepi)
    --ssh-key PATH      SSH private key file
    --help              Show this help

DESCRIPTION:
    Checks if cross-compiled binaries are compatible with Orange Pi GLIBC version.
    Can check locally built binaries or test on actual Orange Pi device.

EXAMPLES:
    $0                                    # Check default build directories
    $0 --dir build-orangepi              # Check specific directory
    $0 --target-glibc 2.35               # Check for GLIBC 2.35 compatibility
    $0 --remote *************            # Test on actual Orange Pi device

EOF
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            log_info "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Find build directories if not specified
find_build_directories() {
    if [[ -n "$CHECK_DIR" ]]; then
        if [[ ! -d "$CHECK_DIR" ]]; then
            log_error "Directory not found: $CHECK_DIR"
            exit 1
        fi
        echo "$CHECK_DIR"
        return
    fi
    
    # Look for common build directories
    local build_dirs=()
    for dir in "build-orangepi" "build-orangepi-compat" "build" "build-release"; do
        if [[ -d "$PROJECT_ROOT/$dir" ]]; then
            build_dirs+=("$PROJECT_ROOT/$dir")
        fi
    done
    
    if [[ ${#build_dirs[@]} -eq 0 ]]; then
        log_error "No build directories found"
        log_info "Build the project first or specify --dir option"
        exit 1
    fi
    
    printf '%s\n' "${build_dirs[@]}"
}

# Check GLIBC compatibility of a binary
check_binary_glibc() {
    local binary="$1"
    local max_glibc=""
    local all_versions=()
    
    if [[ "$VERBOSE" == "true" ]]; then
        log_info "Analyzing binary: $binary"
    fi
    
    # Get GLIBC version requirements
    local glibc_output
    if ! glibc_output=$(aarch64-linux-gnu-objdump -T "$binary" 2>/dev/null | grep "GLIBC_"); then
        if [[ "$VERBOSE" == "true" ]]; then
            log_info "  No GLIBC dependencies found"
        fi
        return 0
    fi
    
    # Extract version numbers
    while IFS= read -r line; do
        if [[ "$line" =~ GLIBC_([0-9]+\.[0-9]+) ]]; then
            local version="${BASH_REMATCH[1]}"
            all_versions+=("$version")
            
            # Compare versions (simple string comparison works for x.y format)
            if [[ "$version" > "$max_glibc" ]]; then
                max_glibc="$version"
            fi
        fi
    done <<< "$glibc_output"
    
    if [[ -n "$max_glibc" ]]; then
        echo "  Required GLIBC: $max_glibc"
        
        # Check compatibility
        if [[ "$max_glibc" > "$TARGET_GLIBC" ]]; then
            log_error "  INCOMPATIBLE: Requires GLIBC $max_glibc, target has $TARGET_GLIBC"
            return 1
        else
            log_success "  COMPATIBLE: GLIBC $max_glibc <= $TARGET_GLIBC"
        fi
        
        if [[ "$VERBOSE" == "true" ]]; then
            echo "  All GLIBC versions: ${all_versions[*]}"
        fi
    fi
    
    return 0
}

# Check all binaries in directories
check_local_binaries() {
    log_step "Checking Local Binary Compatibility"
    
    local build_dirs
    mapfile -t build_dirs < <(find_build_directories)
    
    local total_binaries=0
    local compatible_binaries=0
    local incompatible_binaries=0
    
    for build_dir in "${build_dirs[@]}"; do
        log_info "Checking binaries in: $build_dir"
        
        # Find ARM64 executables
        local executables
        mapfile -t executables < <(find "$build_dir" -type f -executable -exec file {} \; | grep "ELF.*ARM aarch64" | cut -d: -f1)
        
        if [[ ${#executables[@]} -eq 0 ]]; then
            log_warning "No ARM64 executables found in $build_dir"
            continue
        fi
        
        for exe in "${executables[@]}"; do
            total_binaries=$((total_binaries + 1))
            
            echo ""
            echo "Binary: $(basename "$exe")"
            
            if check_binary_glibc "$exe"; then
                compatible_binaries=$((compatible_binaries + 1))
            else
                incompatible_binaries=$((incompatible_binaries + 1))
            fi
        done
    done
    
    echo ""
    log_step "Local Compatibility Summary"
    echo "Total binaries checked: $total_binaries"
    echo "Compatible binaries:    $compatible_binaries"
    echo "Incompatible binaries:  $incompatible_binaries"
    
    if [[ $incompatible_binaries -eq 0 ]]; then
        log_success "All binaries are compatible with Orange Pi GLIBC $TARGET_GLIBC"
        return 0
    else
        log_error "Some binaries are NOT compatible with Orange Pi GLIBC $TARGET_GLIBC"
        return 1
    fi
}

# Build SSH command
build_ssh_cmd() {
    local ssh_cmd="ssh"
    
    if [[ -n "$SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY"
    fi
    
    ssh_cmd="$ssh_cmd -o ConnectTimeout=10 -o StrictHostKeyChecking=no"
    ssh_cmd="$ssh_cmd $SSH_USER@$ORANGE_PI_IP"
    
    echo "$ssh_cmd"
}

# Check GLIBC on remote Orange Pi
check_remote_glibc() {
    log_step "Checking Remote Orange Pi GLIBC"
    
    if [[ -z "$ORANGE_PI_IP" ]]; then
        log_error "Orange Pi IP address required for remote check"
        exit 1
    fi
    
    local ssh_cmd
    ssh_cmd=$(build_ssh_cmd)
    
    log_info "Connecting to Orange Pi at $ORANGE_PI_IP..."
    
    # Check if we can connect
    if ! $ssh_cmd "echo 'Connected successfully'" >/dev/null 2>&1; then
        log_error "Cannot connect to Orange Pi at $ORANGE_PI_IP"
        log_info "Check IP address, SSH key, and network connectivity"
        exit 1
    fi
    
    log_success "Connected to Orange Pi"
    
    # Get GLIBC version
    log_info "Checking GLIBC version on Orange Pi..."
    local glibc_version
    glibc_version=$($ssh_cmd "ldd --version | head -n1 | grep -o '[0-9]\+\.[0-9]\+' | head -n1")
    
    echo "Orange Pi GLIBC version: $glibc_version"
    
    # Get available GLIBC symbols
    log_info "Available GLIBC symbols:"
    $ssh_cmd "strings /lib/aarch64-linux-gnu/libc.so.6 | grep 'GLIBC_' | sort -V | tail -10"
    
    # System information
    echo ""
    log_info "Orange Pi system information:"
    $ssh_cmd "uname -a"
    $ssh_cmd "cat /etc/os-release | grep PRETTY_NAME"
    
    return 0
}

# Test binary on remote Orange Pi
test_remote_binary() {
    local binary_path="$1"
    local binary_name
    binary_name=$(basename "$binary_path")
    
    log_info "Testing $binary_name on Orange Pi..."
    
    local ssh_cmd
    ssh_cmd=$(build_ssh_cmd)
    
    # Copy binary to Orange Pi
    local scp_cmd="scp"
    if [[ -n "$SSH_KEY" ]]; then
        scp_cmd="$scp_cmd -i $SSH_KEY"
    fi
    scp_cmd="$scp_cmd -o ConnectTimeout=10 -o StrictHostKeyChecking=no"
    
    log_info "Copying $binary_name to Orange Pi..."
    if ! $scp_cmd "$binary_path" "$SSH_USER@$ORANGE_PI_IP:/tmp/$binary_name"; then
        log_error "Failed to copy $binary_name to Orange Pi"
        return 1
    fi
    
    # Make executable and test
    $ssh_cmd "chmod +x /tmp/$binary_name"
    
    log_info "Testing binary execution..."
    if $ssh_cmd "/tmp/$binary_name --version" 2>/dev/null; then
        log_success "$binary_name runs successfully on Orange Pi"
        return 0
    else
        log_error "$binary_name failed to run on Orange Pi"
        
        # Check ldd output for debugging
        log_info "Checking library dependencies:"
        $ssh_cmd "ldd /tmp/$binary_name" || true
        
        return 1
    fi
}

# Main execution
main() {
    log_step "GLIBC Compatibility Checker for Orange Pi"
    echo "Target GLIBC version: $TARGET_GLIBC"
    echo ""
    
    if [[ "$REMOTE_CHECK" == "true" ]]; then
        check_remote_glibc
        
        # If we have build directories, test some binaries
        local build_dirs
        mapfile -t build_dirs < <(find_build_directories 2>/dev/null || true)
        
        if [[ ${#build_dirs[@]} -gt 0 ]]; then
            echo ""
            log_step "Testing Binaries on Orange Pi"
            
            for build_dir in "${build_dirs[@]}"; do
                # Find a simple executable to test
                local test_exe
                test_exe=$(find "$build_dir" -name "*server*" -type f -executable | head -n1)
                
                if [[ -n "$test_exe" ]]; then
                    test_remote_binary "$test_exe"
                    break
                fi
            done
        fi
    else
        check_local_binaries
    fi
}

# Run main function
main "$@"
