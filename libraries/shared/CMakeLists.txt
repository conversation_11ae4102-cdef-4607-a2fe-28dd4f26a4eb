file(GLOB_RECURSE SHARED_HEADERS
    include/constants/*.hpp
    include/structs/*.hpp
    include/utils/*.hpp
    include/config/*.hpp
)
file(GLOB_RECURSE SHARED_SOURCES
    src/constants/*.cpp
    src/structs/*.cpp
    src/utils/*.cpp
    src/config/*.cpp
)

add_library(shared ${SHARED_HEADERS} ${SHARED_SOURCES})

target_include_directories(shared
    PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# Link with OpenSSL if available (for crypto utils)
# Use PUBLIC to link OpenSSL into the shared library and propagate to consumers
if(USE_OPENSSL)
    target_link_libraries(shared PUBLIC OpenSSL::SSL OpenSSL::Crypto)
    target_compile_definitions(shared PUBLIC CRYPTO_USE_OPENSSL)
endif()

# Link with CURL if available (for http utils)
if(CURL_FOUND)
    if(PKG_CONFIG_FOUND AND CURL_LIBRARIES)
        # Use pkg-config variables
        target_link_libraries(shared PUBLIC ${CURL_LIBRARIES})
        target_include_directories(shared PUBLIC ${CURL_INCLUDE_DIRS})
        target_compile_options(shared PUBLIC ${CURL_CFLAGS_OTHER})
    else()
        # Use find_package variables
        target_link_libraries(shared PUBLIC ${CURL_LIBRARIES})
        target_include_directories(shared PUBLIC ${CURL_INCLUDE_DIRS})
    endif()
endif()

# Unit tests
option(BUILD_SHARED_TESTS "Build tests for shared library" ON)

# Enable testing if not already enabled
if(NOT DEFINED BUILD_TESTING)
    set(BUILD_TESTING ON)
endif()

if(BUILD_SHARED_TESTS AND BUILD_TESTING)
    # Use FetchContent for GoogleTest if not already available
    if(NOT TARGET gtest)
        include(FetchContent)
        FetchContent_Declare(
            googletest
            URL https://github.com/google/googletest/archive/refs/heads/main.zip
        )
        FetchContent_MakeAvailable(googletest)
    endif()

    enable_testing()

    # Find all test files
    file(GLOB_RECURSE TEST_SOURCES test/*.cpp)

    # Create test executables for each test file
    foreach(TEST_SOURCE ${TEST_SOURCES})
        # Get the filename without extension
        get_filename_component(TEST_NAME ${TEST_SOURCE} NAME_WE)

        # Create executable
        add_executable(shared_${TEST_NAME} ${TEST_SOURCE})

        # Link with shared library and GoogleTest
        target_link_libraries(shared_${TEST_NAME}
            PRIVATE
                shared
                gtest_main
                gtest
        )

        # Link with OpenSSL if available (required by shared library)
        if(USE_OPENSSL)
            target_link_libraries(shared_${TEST_NAME} PRIVATE OpenSSL::SSL OpenSSL::Crypto)
        endif()

        # Add test to CTest
        add_test(NAME shared_${TEST_NAME} COMMAND shared_${TEST_NAME})
    endforeach()
endif()
