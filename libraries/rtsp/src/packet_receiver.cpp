#include "rtsp/packet_receiver.hpp"
#include <sched.h>
#include <unistd.h>
#include <sys/syscall.h>
#include <iostream>
#include <thread>
#include <chrono>
#include <algorithm>
#include <stdexcept>

namespace aibox {
namespace rtsp {

// PacketReceiver implementation
PacketReceiver::PacketReceiver(const RTSPConnectionConfig& config)
    : config_(config)
    , transport_mode_(config.transport)
    , should_stop_(false)
    , is_receiving_(false)
    , rga_status_(HardwareAccelStatus::DISABLED)
    , dmabuf_enabled_(false)
    , hardware_processing_(false)
    , last_sequence_number_(0)
    , expected_sequence_number_(0)
    , adaptive_quality_(false)
    , packet_loss_threshold_(0.05f)
    , max_latency_ms_(200)
    , thermal_throttling_(false) {
    
    // Initialize jitter buffer configuration
    jitter_config_.initial_size_ms = config_.jitter_buffer_size_ms;
    jitter_config_.target_latency_ms = 100;
    jitter_config_.adaptive_sizing = true;
    
    // Optimize for RK3588
    optimizeForRK3588();
    
    // Validate configuration
    if (!validateConfig()) {
        throw std::invalid_argument("Invalid packet receiver configuration");
    }
    
    std::cout << "[PacketReceiver] Initialized for stream: " << config_.rtsp_url << std::endl;
}

PacketReceiver::~PacketReceiver() {
    stopReceiving();
    cleanupRGAScaler();
    cleanupDMABuf();
    std::cout << "[PacketReceiver] Destroyed" << std::endl;
}

bool PacketReceiver::startReceiving() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    if (is_receiving_) {
        return true;
    }
    
    std::cout << "[PacketReceiver] Starting packet reception" << std::endl;
    
    // Initialize hardware acceleration if enabled
    if (config_.use_rga_scaler && !initializeRGAScaler()) {
        std::cout << "[PacketReceiver] RGA scaler initialization failed, using software fallback" << std::endl;
    }
    
    if (config_.use_dmabuf_zerocopy && !initializeDMABuf()) {
        std::cout << "[PacketReceiver] DMABUF initialization failed, using regular memory" << std::endl;
    }
    
    // Create jitter buffer
    jitter_buffer_ = std::make_unique<HardwareJitterBuffer>(jitter_config_);
    
    // Start worker threads
    should_stop_ = false;
    receiver_thread_ = std::make_unique<std::thread>(&PacketReceiver::receiverWorker, this);
    processor_thread_ = std::make_unique<std::thread>(&PacketReceiver::processorWorker, this);
    
    is_receiving_ = true;
    
    std::cout << "[PacketReceiver] Packet reception started successfully" << std::endl;
    return true;
}

void PacketReceiver::stopReceiving() {
    if (!is_receiving_) {
        return;
    }
    
    std::cout << "[PacketReceiver] Stopping packet reception" << std::endl;
    
    should_stop_ = true;
    
    // Wait for threads to finish
    if (receiver_thread_ && receiver_thread_->joinable()) {
        receiver_thread_->join();
    }
    if (processor_thread_ && processor_thread_->joinable()) {
        processor_thread_->join();
    }
    
    // Cleanup components
    jitter_buffer_.reset();
    
    is_receiving_ = false;
    
    std::cout << "[PacketReceiver] Packet reception stopped" << std::endl;
}

bool PacketReceiver::isReceiving() const {
    return is_receiving_;
}

void PacketReceiver::updateConfig(const RTSPConnectionConfig& new_config) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    config_ = new_config;
    transport_mode_ = new_config.transport;
    
    // Update jitter buffer if needed
    if (jitter_buffer_) {
        jitter_config_.initial_size_ms = new_config.jitter_buffer_size_ms;
        jitter_buffer_->updateConfig(jitter_config_);
    }
    
    std::cout << "[PacketReceiver] Configuration updated" << std::endl;
}

void PacketReceiver::configureJitterBuffer(const JitterBufferConfig& config) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    jitter_config_ = config;
    
    if (jitter_buffer_) {
        jitter_buffer_->updateConfig(config);
    }
}

const JitterBufferConfig& PacketReceiver::getJitterBufferConfig() const {
    return jitter_config_;
}

void PacketReceiver::setTransportMode(TransportProtocol mode) {
    transport_mode_ = mode;
}

TransportProtocol PacketReceiver::getTransportMode() const {
    return transport_mode_;
}

bool PacketReceiver::enableRGAScaling(bool enable) {
    if (enable && !initializeRGAScaler()) {
        return false;
    } else if (!enable) {
        cleanupRGAScaler();
    }
    return true;
}

bool PacketReceiver::enableDMABufZeroCopy(bool enable) {
    if (enable && !initializeDMABuf()) {
        return false;
    } else if (!enable) {
        cleanupDMABuf();
    }
    dmabuf_enabled_ = enable;
    return true;
}

HardwareAccelStatus PacketReceiver::getRGAStatus() const {
    return rga_status_;
}

bool PacketReceiver::isDMABufEnabled() const {
    return dmabuf_enabled_;
}

void PacketReceiver::setPacketCallback(std::function<void(const std::vector<uint8_t>&, Timestamp)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    packet_callback_ = callback;
}

void PacketReceiver::setNALUnitCallback(std::function<void(const NALUnit&)> callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    nal_callback_ = callback;
}

void PacketReceiver::setErrorCallback(StreamErrorCallback callback) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    error_callback_ = callback;
}

PacketStatistics PacketReceiver::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);

    // Create a copy manually since atomic types can't be copied
    PacketStatistics copy;
    copy.packets_received = statistics_.packets_received.load();
    copy.packets_lost = statistics_.packets_lost.load();
    copy.bytes_received = statistics_.bytes_received.load();
    copy.duplicate_packets = statistics_.duplicate_packets.load();
    copy.out_of_order_packets = statistics_.out_of_order_packets.load();
    copy.rtcp_packets_received = statistics_.rtcp_packets_received.load();
    copy.sender_reports_received = statistics_.sender_reports_received.load();
    copy.receiver_reports_sent = statistics_.receiver_reports_sent.load();
    copy.average_jitter_ms = statistics_.average_jitter_ms.load();
    copy.current_queue_depth = statistics_.current_queue_depth.load();
    copy.max_queue_depth = statistics_.max_queue_depth.load();
    copy.buffer_overruns = statistics_.buffer_overruns.load();
    copy.buffer_underruns = statistics_.buffer_underruns.load();
    copy.hardware_accelerated_count = statistics_.hardware_accelerated_count.load();
    copy.software_fallback_count = statistics_.software_fallback_count.load();
    copy.rga_processing_count = statistics_.rga_processing_count.load();
    copy.dmabuf_operations = statistics_.dmabuf_operations.load();
    copy.current_fps = statistics_.current_fps.load();
    copy.current_bitrate_kbps = statistics_.current_bitrate_kbps.load();
    copy.average_latency_ms = statistics_.average_latency_ms.load();
    copy.parsing_errors = statistics_.parsing_errors.load();
    copy.hardware_errors = statistics_.hardware_errors.load();
    copy.network_errors = statistics_.network_errors.load();
    copy.last_packet_time = statistics_.last_packet_time;
    copy.first_packet_time = statistics_.first_packet_time;

    return copy;
}

float PacketReceiver::getCurrentJitterMs() const {
    if (jitter_buffer_) {
        return jitter_buffer_->getCurrentJitterMs();
    }
    return 0.0f;
}

uint32_t PacketReceiver::getQueueDepth() const {
    if (jitter_buffer_) {
        return jitter_buffer_->getQueueDepth();
    }
    return 0;
}

size_t PacketReceiver::getMemoryUsage() const {
    size_t total = 0;
    
    // Base memory usage
    total += sizeof(*this);
    
    // Jitter buffer memory
    if (jitter_buffer_) {
        total += jitter_buffer_->getMemoryUsage();
    }
    
    // Component memory (estimated)
    total += config_.buffer_size_bytes * 2;  // Input/output buffers
    
    return total;
}

void PacketReceiver::setQualityThresholds(float packet_loss_threshold, uint32_t max_latency_ms) {
    packet_loss_threshold_ = packet_loss_threshold;
    max_latency_ms_ = max_latency_ms;
}

void PacketReceiver::enableAdaptiveQuality(bool enable) {
    adaptive_quality_ = enable;
}

bool PacketReceiver::isAdaptiveQualityEnabled() const {
    return adaptive_quality_;
}

void PacketReceiver::handleThermalThrottling(int temperature) {
    thermal_throttling_ = (temperature >= 80);
    
    if (thermal_throttling_) {
        // Reduce processing load
        hardware_processing_ = false;
        std::cout << "[PacketReceiver] Thermal throttling activated at " << temperature << "°C" << std::endl;
    } else {
        // Restore normal processing
        hardware_processing_ = (rga_status_ == HardwareAccelStatus::HARDWARE_ACTIVE);
    }
}

void PacketReceiver::setPerformanceMode(bool high_performance) {
    if (high_performance && !thermal_throttling_) {
        hardware_processing_ = (rga_status_ == HardwareAccelStatus::HARDWARE_ACTIVE);
    } else {
        hardware_processing_ = false;
    }
}

// Private methods
void PacketReceiver::receiverWorker() {
    setCPUAffinity();
    
    std::cout << "[PacketReceiver] Receiver worker started" << std::endl;
    
    while (!should_stop_) {
        // TODO: Implement actual RTP packet reception
        // This is a skeleton implementation
        
        // Simulate packet reception
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        
        // Update statistics
        updateQueueStatistics();
    }
    
    std::cout << "[PacketReceiver] Receiver worker stopped" << std::endl;
}

void PacketReceiver::processorWorker() {
    setCPUAffinity();
    
    std::cout << "[PacketReceiver] Processor worker started" << std::endl;
    
    while (!should_stop_) {
        // TODO: Implement packet processing
        // This is a skeleton implementation
        
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        
        // Adaptive quality control
        if (adaptive_quality_) {
            adaptQualitySettings();
        }
    }
    
    std::cout << "[PacketReceiver] Processor worker stopped" << std::endl;
}

void PacketReceiver::processRTPPacket(const std::vector<uint8_t>& packet, Timestamp timestamp) {
    if (packet.size() < 12) {  // Minimum RTP header size
        statistics_.parsing_errors++;
        return;
    }

    // Parse RTP header
    RTPPacket rtp_packet;
    if (!parseRTPHeader(packet, rtp_packet)) {
        statistics_.parsing_errors++;
        return;
    }

    // Check for duplicate packets
    bool is_duplicate = checkDuplicatePacket(rtp_packet.header.sequence_number);
    bool is_out_of_order = checkOutOfOrderPacket(rtp_packet.header.sequence_number);

    // Update statistics
    updatePacketStatistics(packet.size(), is_duplicate, is_out_of_order);

    if (is_duplicate) {
        return;  // Skip duplicate packets
    }

    // Process with hardware if available
    std::vector<uint8_t> processed_payload = rtp_packet.payload;
    if (hardware_processing_ && rga_status_ == HardwareAccelStatus::HARDWARE_ACTIVE) {
        std::vector<uint8_t> hardware_processed;
        if (processWithRGA(rtp_packet.payload, hardware_processed)) {
            processed_payload = std::move(hardware_processed);
            statistics_.hardware_accelerated_count++;
            statistics_.rga_processing_count++;
        } else {
            statistics_.software_fallback_count++;
        }
    }

    // Add to jitter buffer
    if (jitter_buffer_) {
        jitter_buffer_->addPacket(processed_payload, rtp_packet.header.sequence_number, timestamp);
    }

    // Extract NAL units if this is a video payload
    if (isVideoPayload(rtp_packet.header.payload_type)) {
        extractNALUnits(rtp_packet, timestamp);
    }

    // Invoke callback
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (packet_callback_) {
        packet_callback_(processed_payload, timestamp);
    }
}

void PacketReceiver::processRTCPPacket(const std::vector<uint8_t>& packet) {
    if (packet.size() < 8) {  // Minimum RTCP header size
        statistics_.parsing_errors++;
        return;
    }

    // Parse RTCP header
    RTCPPacket rtcp_packet;
    if (!parseRTCPHeader(packet, rtcp_packet)) {
        statistics_.parsing_errors++;
        return;
    }

    statistics_.rtcp_packets_received++;

    // Process based on packet type
    switch (rtcp_packet.type) {
        case RTCPPacketType::SENDER_REPORT:
            processSenderReport(rtcp_packet);
            statistics_.sender_reports_received++;
            break;

        case RTCPPacketType::RECEIVER_REPORT:
            processReceiverReport(rtcp_packet);
            break;

        case RTCPPacketType::SOURCE_DESCRIPTION:
            processSourceDescription(rtcp_packet);
            break;

        case RTCPPacketType::BYE:
            processByePacket(rtcp_packet);
            break;

        default:
            // Unknown RTCP packet type
            break;
    }
}

void PacketReceiver::updateJitterBuffer() {
    if (!jitter_buffer_) return;
    
    // Update jitter statistics
    float current_jitter = jitter_buffer_->getCurrentJitterMs();
    updateJitterStatistics(current_jitter);
}

void PacketReceiver::adaptQualitySettings() {
    if (!adaptive_quality_) return;
    
    // Check packet loss rate
    uint64_t packets_received = statistics_.packets_received.load();
    uint64_t packets_lost = statistics_.packets_lost.load();
    float loss_rate = packets_received > 0 ? static_cast<float>(packets_lost) / static_cast<float>(packets_received) : 0.0f;
    
    if (loss_rate > packet_loss_threshold_) {
        // Increase jitter buffer size
        if (jitter_config_.initial_size_ms < jitter_config_.max_size_ms) {
            jitter_config_.initial_size_ms += 50;
            if (jitter_buffer_) {
                jitter_buffer_->updateConfig(jitter_config_);
            }
        }
    }
}

void PacketReceiver::handlePacketLoss() {
    statistics_.packets_lost++;
    
    // Handle packet loss recovery
    if (adaptive_quality_) {
        adaptQualitySettings();
    }
}

void PacketReceiver::optimizeForRK3588() {
    // Set CPU affinity to specific cores
    setCPUAffinity();
    
    // Configure hardware acceleration
    configureHardwareAcceleration();
}

void PacketReceiver::configureHardwareAcceleration() {
    // Initialize RGA if enabled
    if (config_.use_rga_scaler) {
        rga_status_ = HardwareAccelStatus::DISABLED;
    }
    
    // Initialize DMABUF if enabled
    if (config_.use_dmabuf_zerocopy) {
        dmabuf_enabled_ = false;
    }
}

void PacketReceiver::setCPUAffinity() {
    // Set CPU affinity to RK3588 efficiency cores (2-3)
    cpu_set_t cpuset;
    CPU_ZERO(&cpuset);
    CPU_SET(2, &cpuset);  // Cortex-A55 core
    CPU_SET(3, &cpuset);  // Cortex-A55 core
    
    if (sched_setaffinity(0, sizeof(cpuset), &cpuset) != 0) {
        std::cout << "[PacketReceiver] Warning: Failed to set CPU affinity" << std::endl;
    }
}

bool PacketReceiver::initializeRGAScaler() {
    std::cout << "[PacketReceiver] Initializing RGA scaler..." << std::endl;

    // Check if RGA device is available
    if (!checkRGADeviceAvailable()) {
        std::cout << "[PacketReceiver] RGA device not available" << std::endl;
        rga_status_ = HardwareAccelStatus::DISABLED;
        return false;
    }

    try {
        // Initialize RGA context
        rga_context_.reset();

        // For now, simulate RGA initialization
        // In real implementation, this would call RGA library functions
        rga_context_.initialized = true;
        rga_context_.supports_scaling = true;
        rga_context_.supports_format_conversion = true;
        rga_context_.supports_rotation = false;

        rga_status_ = HardwareAccelStatus::HARDWARE_ACTIVE;
        std::cout << "[PacketReceiver] RGA scaler initialized successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cout << "[PacketReceiver] RGA initialization failed: " << e.what() << std::endl;
        rga_status_ = HardwareAccelStatus::HARDWARE_ERROR;
        return false;
    }
}

void PacketReceiver::cleanupRGAScaler() {
    std::cout << "[PacketReceiver] Cleaning up RGA scaler..." << std::endl;

    if (rga_context_.initialized) {
        // In real implementation, this would call RGA cleanup functions
        rga_context_.initialized = false;
        rga_context_.rga_handle = nullptr;
    }

    rga_status_ = HardwareAccelStatus::DISABLED;
    std::cout << "[PacketReceiver] RGA scaler cleanup completed" << std::endl;
}

bool PacketReceiver::initializeDMABuf() {
    std::cout << "[PacketReceiver] Initializing DMABUF..." << std::endl;

    try {
        // Initialize DMABUF context
        dmabuf_context_.reset();

        // For now, simulate DMABUF initialization
        // In real implementation, this would allocate DMABUF file descriptors
        dmabuf_context_.status = HardwareAccelStatus::HARDWARE_ACTIVE;
        dmabuf_context_.buffer_size = 2 * 1024 * 1024;  // 2MB
        dmabuf_context_.max_buffer_count = 16;

        // Simulate buffer pool creation
        for (size_t i = 0; i < dmabuf_context_.max_buffer_count; ++i) {
            dmabuf_context_.available_buffers.push_back(static_cast<int>(i + 100));  // Fake FDs
        }
        dmabuf_context_.total_buffer_count = dmabuf_context_.max_buffer_count;

        dmabuf_enabled_ = true;
        std::cout << "[PacketReceiver] DMABUF initialized with " << dmabuf_context_.max_buffer_count << " buffers" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cout << "[PacketReceiver] DMABUF initialization failed: " << e.what() << std::endl;
        dmabuf_context_.status = HardwareAccelStatus::HARDWARE_ERROR;
        dmabuf_enabled_ = false;
        return false;
    }
}

void PacketReceiver::cleanupDMABuf() {
    std::cout << "[PacketReceiver] Cleaning up DMABUF..." << std::endl;

    if (dmabuf_enabled_) {
        // In real implementation, this would close DMABUF file descriptors
        dmabuf_context_.available_buffers.clear();
        dmabuf_context_.used_buffers.clear();
        dmabuf_context_.total_buffer_count = 0;
        dmabuf_context_.status = HardwareAccelStatus::DISABLED;
    }

    dmabuf_enabled_ = false;
    std::cout << "[PacketReceiver] DMABUF cleanup completed" << std::endl;
}

bool PacketReceiver::processWithRGA(const std::vector<uint8_t>& input, std::vector<uint8_t>& output) {
    if (!rga_context_.initialized || rga_status_ != HardwareAccelStatus::HARDWARE_ACTIVE) {
        return false;
    }

    auto start_time = std::chrono::high_resolution_clock::now();

    try {
        // For now, simulate RGA processing
        // In real implementation, this would call RGA library functions
        output = input;  // Copy input to output

        // Simulate some processing time
        std::this_thread::sleep_for(std::chrono::microseconds(50));

        // Update RGA statistics
        rga_context_.operations_count++;

        auto end_time = std::chrono::high_resolution_clock::now();
        auto processing_time = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);

        uint32_t time_us = static_cast<uint32_t>(processing_time.count());
        rga_context_.average_processing_time_us =
            (rga_context_.average_processing_time_us + time_us) / 2;
        rga_context_.max_processing_time_us = std::max(rga_context_.max_processing_time_us, time_us);

        return true;

    } catch (const std::exception& e) {
        std::cout << "[PacketReceiver] RGA processing failed: " << e.what() << std::endl;
        rga_context_.failed_operations++;

        // Switch to software fallback if too many errors
        if (rga_context_.failed_operations > 5) {
            rga_status_ = HardwareAccelStatus::SOFTWARE_FALLBACK;
        }

        return false;
    }
}

void PacketReceiver::updatePacketStatistics(size_t packet_size, bool is_duplicate, bool is_out_of_order) {
    if (!is_duplicate) {
        statistics_.packets_received++;
        statistics_.bytes_received += packet_size;
    } else {
        statistics_.duplicate_packets++;
    }
    
    if (is_out_of_order) {
        statistics_.out_of_order_packets++;
    }
}

void PacketReceiver::updateJitterStatistics(float jitter_ms) {
    statistics_.average_jitter_ms = jitter_ms;
}

void PacketReceiver::updateQueueStatistics() {
    if (jitter_buffer_) {
        statistics_.current_queue_depth = jitter_buffer_->getQueueDepth();
    }
}

void PacketReceiver::handleReceiveError(ErrorCategory category, const std::string& message) {
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (error_callback_) {
        error_callback_("", category, message);
    }
}

void PacketReceiver::handleHardwareError(const std::string& component, const std::string& error) {
    std::cout << "[PacketReceiver] Hardware error in " << component << ": " << error << std::endl;
    
    if (component == "RGA") {
        rga_status_ = HardwareAccelStatus::HARDWARE_ERROR;
        hardware_processing_ = false;
    }
}

bool PacketReceiver::validateConfig() const {
    return config_.isValid() &&
           jitter_config_.initial_size_ms > 0 &&
           jitter_config_.max_size_ms >= jitter_config_.initial_size_ms;
}

bool PacketReceiver::checkHardwareCapabilities() const {
    // Check RGA availability
    bool rga_available = checkRGADeviceAvailable();

    // Check DMABUF support
    bool dmabuf_available = true;  // Assume available on RK3588

    return rga_available && dmabuf_available;
}

bool PacketReceiver::checkRGADeviceAvailable() const {
    // In real implementation, this would check for RGA device files
    // For now, simulate RGA availability on RK3588
    return true;
}

bool PacketReceiver::parseRTPHeader(const std::vector<uint8_t>& packet, RTPPacket& rtp_packet) {
    if (packet.size() < 12) {
        return false;
    }

    // Parse fixed header (12 bytes)
    const uint8_t* data = packet.data();

    rtp_packet.header.version = (data[0] >> 6) & 0x03;
    rtp_packet.header.padding = (data[0] >> 5) & 0x01;
    rtp_packet.header.extension = (data[0] >> 4) & 0x01;
    rtp_packet.header.csrc_count = data[0] & 0x0F;

    rtp_packet.header.marker = (data[1] >> 7) & 0x01;
    rtp_packet.header.payload_type = data[1] & 0x7F;

    rtp_packet.header.sequence_number = (data[2] << 8) | data[3];
    rtp_packet.header.timestamp = (data[4] << 24) | (data[5] << 16) | (data[6] << 8) | data[7];
    rtp_packet.header.ssrc = (data[8] << 24) | (data[9] << 16) | (data[10] << 8) | data[11];

    // Validate header
    if (!rtp_packet.header.isValid()) {
        return false;
    }

    size_t header_size = rtp_packet.header.getHeaderSize();
    if (packet.size() < header_size) {
        return false;
    }

    // Parse CSRC list if present
    if (rtp_packet.header.csrc_count > 0) {
        rtp_packet.csrc_list.resize(static_cast<size_t>(rtp_packet.header.csrc_count));
        for (uint8_t i = 0; i < rtp_packet.header.csrc_count; ++i) {
            size_t offset = 12 + static_cast<size_t>(i * 4);
            if (offset + 4 > packet.size()) {
                return false;
            }
            rtp_packet.csrc_list[static_cast<size_t>(i)] = (data[offset] << 24) | (data[offset+1] << 16) |
                                     (data[offset+2] << 8) | data[offset+3];
        }
    }

    // Extract payload
    if (packet.size() > header_size) {
        rtp_packet.payload.assign(packet.begin() + static_cast<ptrdiff_t>(header_size), packet.end());
    }

    rtp_packet.receive_time = std::chrono::steady_clock::now();
    return true;
}

bool PacketReceiver::parseRTCPHeader(const std::vector<uint8_t>& packet, RTCPPacket& rtcp_packet) {
    if (packet.size() < 8) {
        return false;
    }

    const uint8_t* data = packet.data();

    // Parse RTCP header
    uint8_t version = (data[0] >> 6) & 0x03;
    if (version != 2) {
        return false;
    }

    uint8_t packet_type = data[1];
    rtcp_packet.type = static_cast<RTCPPacketType>(packet_type);

    // Copy packet data
    rtcp_packet.data = packet;
    rtcp_packet.receive_time = std::chrono::steady_clock::now();

    return true;
}

bool PacketReceiver::checkDuplicatePacket(uint32_t sequence_number) {
    uint32_t last_seq = last_sequence_number_.load();

    // Simple duplicate check - in real implementation, this would use a sliding window
    if (sequence_number == last_seq) {
        return true;
    }

    last_sequence_number_.store(sequence_number);
    return false;
}

bool PacketReceiver::checkOutOfOrderPacket(uint32_t sequence_number) {
    uint32_t expected_seq = expected_sequence_number_.load();

    // Check if packet is out of order
    bool out_of_order = (sequence_number < expected_seq);

    // Update expected sequence number
    if (sequence_number >= expected_seq) {
        expected_sequence_number_.store(sequence_number + 1);
    }

    return out_of_order;
}

bool PacketReceiver::isVideoPayload(uint8_t payload_type) const {
    // Common video payload types
    return (payload_type == 96 ||   // H.264
            payload_type == 97 ||   // H.265
            payload_type == 98 ||   // VP8
            payload_type == 99);    // VP9
}

void PacketReceiver::extractNALUnits(const RTPPacket& rtp_packet, Timestamp timestamp) {
    if (rtp_packet.payload.empty()) {
        return;
    }

    // For H.264/H.265, extract NAL units from RTP payload
    // This is a simplified implementation - real implementation would handle
    // fragmentation units (FU-A, FU-B) and aggregation packets

    NALUnit nal_unit;
    nal_unit.data = rtp_packet.payload;
    nal_unit.timestamp = timestamp;
    nal_unit.sequence_number = rtp_packet.header.sequence_number;

    // Detect NAL unit type (simplified)
    if (!nal_unit.data.empty()) {
        uint8_t nal_header = nal_unit.data[0];

        // H.264 NAL unit type detection
        if (rtp_packet.header.payload_type == 96) {  // H.264
            uint8_t nal_type = nal_header & 0x1F;
            switch (nal_type) {
                case 1: nal_unit.type = NALUnitType::H264_NON_IDR; break;
                case 5: nal_unit.type = NALUnitType::H264_IDR; nal_unit.is_keyframe = true; break;
                case 7: nal_unit.type = NALUnitType::H264_SPS; break;
                case 8: nal_unit.type = NALUnitType::H264_PPS; break;
                default: nal_unit.type = NALUnitType::UNKNOWN; break;
            }
        }
        // H.265 NAL unit type detection
        else if (rtp_packet.header.payload_type == 97) {  // H.265
            uint8_t nal_type = (nal_header >> 1) & 0x3F;
            switch (nal_type) {
                case 19: nal_unit.type = NALUnitType::H265_IDR_W_RADL; nal_unit.is_keyframe = true; break;
                case 20: nal_unit.type = NALUnitType::H265_IDR_N_LP; nal_unit.is_keyframe = true; break;
                case 32: nal_unit.type = NALUnitType::H265_VPS; break;
                case 33: nal_unit.type = NALUnitType::H265_SPS; break;
                case 34: nal_unit.type = NALUnitType::H265_PPS; break;
                default: nal_unit.type = NALUnitType::UNKNOWN; break;
            }
        }
    }

    // Invoke NAL unit callback
    std::lock_guard<std::mutex> lock(callback_mutex_);
    if (nal_callback_) {
        nal_callback_(nal_unit);
    }
}

void PacketReceiver::processSenderReport(const RTCPPacket& packet) {
    // Process RTCP Sender Report
    // Extract timing information, packet counts, etc.
    // This is a placeholder implementation
    std::cout << "[PacketReceiver] Processing RTCP Sender Report" << std::endl;
}

void PacketReceiver::processReceiverReport(const RTCPPacket& packet) {
    // Process RTCP Receiver Report
    // Extract reception statistics, loss information, etc.
    std::cout << "[PacketReceiver] Processing RTCP Receiver Report" << std::endl;
}

void PacketReceiver::processSourceDescription(const RTCPPacket& packet) {
    // Process RTCP Source Description
    // Extract source information
    std::cout << "[PacketReceiver] Processing RTCP Source Description" << std::endl;
}

void PacketReceiver::processByePacket(const RTCPPacket& packet) {
    // Process RTCP BYE packet
    // Handle stream termination
    std::cout << "[PacketReceiver] Processing RTCP BYE packet" << std::endl;
}

// HardwareJitterBuffer implementation
HardwareJitterBuffer::HardwareJitterBuffer(const JitterBufferConfig& config)
    : config_(config)
    , overrun_count_(0)
    , underrun_count_(0)
    , current_jitter_(0.0f)
    , rga_enabled_(false)
    , dmabuf_enabled_(false) {

    std::cout << "[HardwareJitterBuffer] Initialized with " << config_.initial_size_ms << "ms buffer" << std::endl;
}

HardwareJitterBuffer::~HardwareJitterBuffer() {
    flush();
    std::cout << "[HardwareJitterBuffer] Destroyed" << std::endl;
}

bool HardwareJitterBuffer::addPacket(const std::vector<uint8_t>& packet, uint32_t sequence, Timestamp timestamp) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    // Check buffer capacity
    if (buffer_.size() >= static_cast<size_t>(config_.max_size_ms / 10)) {  // Rough estimate
        overrun_count_++;
        if (!config_.adaptive_sizing) {
            return false;  // Drop packet
        }

        // Remove oldest packet
        buffer_.pop();
    }

    // Create buffer entry
    BufferEntry entry;
    entry.data = packet;
    entry.sequence = sequence;
    entry.timestamp = timestamp;
    entry.processed = false;

    // Process with hardware if enabled
    if (rga_enabled_) {
        processWithHardware(entry);
    }

    buffer_.push(entry);
    return true;
}

bool HardwareJitterBuffer::getNextPacket(std::vector<uint8_t>& packet, Timestamp& timestamp) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    if (buffer_.empty()) {
        underrun_count_++;
        return false;
    }

    auto entry = buffer_.front();
    buffer_.pop();

    packet = std::move(entry.data);
    timestamp = entry.timestamp;

    return true;
}

void HardwareJitterBuffer::flush() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    while (!buffer_.empty()) {
        buffer_.pop();
    }

    overrun_count_ = 0;
    underrun_count_ = 0;
    current_jitter_ = 0.0f;
}

void HardwareJitterBuffer::updateConfig(const JitterBufferConfig& config) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    config_ = config;
}

void HardwareJitterBuffer::setTargetLatency(int latency_ms) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    config_.target_latency_ms = latency_ms;
}

void HardwareJitterBuffer::enableAdaptiveSizing(bool enable) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    config_.adaptive_sizing = enable;
}

uint32_t HardwareJitterBuffer::getQueueDepth() const {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    return static_cast<uint32_t>(buffer_.size());
}

float HardwareJitterBuffer::getCurrentJitterMs() const {
    return current_jitter_;
}

uint32_t HardwareJitterBuffer::getOverrunCount() const {
    return overrun_count_;
}

uint32_t HardwareJitterBuffer::getUnderrunCount() const {
    return underrun_count_;
}

size_t HardwareJitterBuffer::getMemoryUsage() const {
    std::lock_guard<std::mutex> lock(buffer_mutex_);

    size_t total = sizeof(*this);
    total += buffer_.size() * (sizeof(BufferEntry) + 1500);  // Estimate 1500 bytes per packet

    return total;
}

bool HardwareJitterBuffer::enableRGAProcessing(bool enable) {
    rga_enabled_ = enable;
    return true;  // TODO: Implement actual RGA initialization
}

bool HardwareJitterBuffer::enableDMABuf(bool enable) {
    dmabuf_enabled_ = enable;
    return true;  // TODO: Implement actual DMABUF initialization
}

void HardwareJitterBuffer::adaptBufferSize() {
    if (!config_.adaptive_sizing) return;

    // Adapt buffer size based on jitter and packet loss
    float target_jitter = static_cast<float>(config_.target_latency_ms);

    if (current_jitter_ > target_jitter * 1.5f) {
        // Increase buffer size
        config_.initial_size_ms = std::min(config_.initial_size_ms + 50, config_.max_size_ms);
    } else if (current_jitter_ < target_jitter * 0.5f) {
        // Decrease buffer size
        config_.initial_size_ms = std::max(config_.initial_size_ms - 25, config_.min_size_ms);
    }
}

float HardwareJitterBuffer::calculateJitter(Timestamp current, Timestamp previous) {
    auto diff = std::chrono::duration_cast<std::chrono::microseconds>(current - previous);
    return static_cast<float>(diff.count()) / 1000.0f;  // Convert to milliseconds
}

bool HardwareJitterBuffer::shouldDropPacket(const BufferEntry& entry) {
    // TODO: Implement packet dropping logic based on latency and importance
    return false;
}

void HardwareJitterBuffer::processWithHardware(BufferEntry& entry) {
    if (!rga_enabled_) return;

    // TODO: Implement hardware processing with RGA
    entry.processed = true;
}

} // namespace rtsp
} // namespace aibox
