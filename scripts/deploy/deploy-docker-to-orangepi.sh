#!/bin/bash

# Deploy Docker Images to Orange Pi 5 Plus Script
# Builds, transfers, and runs Docker containers on Orange Pi target

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
SSH_KEY=""
IMAGE_NAME="c-aibox-arm64"
IMAGE_TAG="latest"
CONTAINER_NAME="c-aibox"
CONTAINER_PORT="8080"
HOST_PORT="8080"
BUILD_ARM64=true
TRANSFER_METHOD="save"  # save, registry, build-remote
DOCKERFILE="docker/Dockerfile.arm64"
BUILD_CONTEXT="."
START_CONTAINER=true
REMOVE_OLD=true
HEALTH_CHECK=true

# Load SSH configuration if available
if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
    source "$PROJECT_ROOT/.orangepi_ssh"
    SSH_KEY="$ORANGE_PI_SSH_KEY"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --port)
            ORANGE_PI_PORT="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --image-name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        --image-tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --container-name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        --container-port)
            CONTAINER_PORT="$2"
            shift 2
            ;;
        --host-port)
            HOST_PORT="$2"
            shift 2
            ;;
        --dockerfile)
            DOCKERFILE="$2"
            shift 2
            ;;
        --no-build)
            BUILD_ARM64=false
            shift
            ;;
        --transfer-method)
            TRANSFER_METHOD="$2"
            shift 2
            ;;
        --no-start)
            START_CONTAINER=false
            shift
            ;;
        --keep-old)
            REMOVE_OLD=false
            shift
            ;;
        --no-health-check)
            HEALTH_CHECK=false
            shift
            ;;
        --help)
            cat << EOF
Deploy Docker Images to Orange Pi 5 Plus Script

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP                 Orange Pi IP address

OPTIONS:
    --user USER             SSH username (default: orangepi)
    --port PORT             SSH port (default: 22)
    --ssh-key PATH          SSH private key file
    --image-name NAME       Docker image name (default: c-aibox-arm64)
    --image-tag TAG         Docker image tag (default: latest)
    --container-name NAME   Container name (default: c-aibox)
    --container-port PORT   Container internal port (default: 8080)
    --host-port PORT        Host port mapping (default: 8080)
    --dockerfile PATH       Dockerfile path (default: docker/Dockerfile.arm64)
    --transfer-method METHOD Transfer method: save, registry, build-remote (default: save)
    --no-build              Skip building image locally
    --no-start              Don't start container after deployment
    --keep-old              Keep old containers/images
    --no-health-check       Skip health check verification
    --help                  Show this help

EXAMPLES:
    $0 --ip ***************                           # Basic deployment
    $0 --ip *************** --host-port 9090          # Custom port
    $0 --ip *************** --transfer-method registry # Use registry
    $0 --ip *************** --no-build --no-start     # Transfer only

TRANSFER METHODS:
    save:         Build locally, save to tar.gz, transfer via SCP
    registry:     Push to registry, pull on device
    build-remote: Transfer source, build on device
EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    log_info "Usage: $0 --ip IP_ADDRESS"
    exit 1
fi

# SSH command builder
build_ssh_cmd() {
    local ssh_cmd="ssh"
    
    if [[ -n "$SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY"
    fi
    
    ssh_cmd="$ssh_cmd -p $ORANGE_PI_PORT $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "$ssh_cmd"
}

# SCP command builder
build_scp_cmd() {
    local scp_cmd="scp"
    
    if [[ -n "$SSH_KEY" ]]; then
        scp_cmd="$scp_cmd -i $SSH_KEY"
    fi
    
    scp_cmd="$scp_cmd -P $ORANGE_PI_PORT"
    echo "$scp_cmd"
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
        exit 1
    fi
}

# Check Docker on Orange Pi
check_docker_on_device() {
    log_step "Checking Docker on Orange Pi"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    if $ssh_cmd "docker --version" >/dev/null 2>&1; then
        local docker_version=$($ssh_cmd "docker --version")
        log_success "Docker found: $docker_version"
    else
        log_error "Docker not found on Orange Pi"
        log_info "Please install Docker on the Orange Pi first"
        exit 1
    fi
}

# Build ARM64 Docker image locally
build_local_image() {
    if [[ "$BUILD_ARM64" != "true" ]]; then
        return
    fi
    
    log_step "Building ARM64 Docker Image Locally"
    
    cd "$PROJECT_ROOT"
    
    if [[ ! -f "$DOCKERFILE" ]]; then
        log_error "Dockerfile not found: $DOCKERFILE"
        exit 1
    fi
    
    log_info "Building image: $IMAGE_NAME:$IMAGE_TAG"
    log_info "Using Dockerfile: $DOCKERFILE"
    
    # Build the image
    if docker build -f "$DOCKERFILE" -t "$IMAGE_NAME:$IMAGE_TAG" "$BUILD_CONTEXT"; then
        log_success "Docker image built successfully"
    else
        log_error "Docker image build failed"
        exit 1
    fi
    
    # Verify image
    local image_info=$(docker images "$IMAGE_NAME:$IMAGE_TAG" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}")
    log_info "Image details:"
    echo "$image_info"
}

# Transfer image using save method
transfer_image_save() {
    log_step "Transferring Docker Image (Save Method)"
    
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    local image_file="${IMAGE_NAME}-${IMAGE_TAG}.tar.gz"
    
    # Save image to compressed tar
    log_info "Saving Docker image to $image_file..."
    docker save "$IMAGE_NAME:$IMAGE_TAG" | gzip > "$image_file"
    
    local file_size=$(ls -lh "$image_file" | awk '{print $5}')
    log_info "Image file size: $file_size"
    
    # Transfer to Orange Pi
    log_info "Transferring image to Orange Pi..."
    $scp_cmd "$image_file" "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/"
    
    # Load image on Orange Pi
    log_info "Loading image on Orange Pi..."
    $ssh_cmd "gunzip -c /home/<USER>/$image_file | docker load"
    
    # Cleanup
    rm -f "$image_file"
    $ssh_cmd "rm -f /home/<USER>/$image_file"
    
    log_success "Image transfer completed"
}

# Build image remotely on Orange Pi
build_remote_image() {
    log_step "Building Docker Image on Orange Pi"
    
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    
    # Create build directory on Orange Pi
    $ssh_cmd "mkdir -p /home/<USER>/docker-build"
    
    # Transfer build context
    log_info "Transferring build context..."
    cd "$PROJECT_ROOT"
    tar czf docker-build-context.tar.gz --exclude='.git' --exclude='build*' --exclude='*.tar.gz' .
    $scp_cmd docker-build-context.tar.gz "$ORANGE_PI_USER@$ORANGE_PI_IP:/home/<USER>/"
    
    # Extract and build on Orange Pi
    log_info "Building image on Orange Pi..."
    $ssh_cmd "cd /home/<USER>"
    
    # Cleanup
    rm -f docker-build-context.tar.gz
    $ssh_cmd "rm -f /home/<USER>/docker-build-context.tar.gz && rm -rf /home/<USER>/docker-build"
    
    log_success "Remote image build completed"
}

# Remove old containers and images
cleanup_old_deployments() {
    if [[ "$REMOVE_OLD" != "true" ]]; then
        return
    fi
    
    log_step "Cleaning Up Old Deployments"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Stop and remove old containers
    log_info "Removing old containers..."
    $ssh_cmd "docker ps -a --filter name=$CONTAINER_NAME --format '{{.Names}}' | xargs -r docker rm -f" || true
    
    # Remove old images (keep latest)
    log_info "Cleaning up old images..."
    $ssh_cmd "docker images $IMAGE_NAME --format '{{.ID}} {{.Tag}}' | grep -v $IMAGE_TAG | awk '{print \$1}' | xargs -r docker rmi" || true
    
    log_success "Cleanup completed"
}

# Start Docker container
start_container() {
    if [[ "$START_CONTAINER" != "true" ]]; then
        return
    fi
    
    log_step "Starting Docker Container"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Run container
    log_info "Starting container: $CONTAINER_NAME"
    log_info "Port mapping: $HOST_PORT:$CONTAINER_PORT"
    
    $ssh_cmd "docker run -d --name $CONTAINER_NAME --restart unless-stopped -p $HOST_PORT:$CONTAINER_PORT $IMAGE_NAME:$IMAGE_TAG"
    
    # Wait for container to start
    sleep 5
    
    # Check container status
    local container_status=$($ssh_cmd "docker ps --filter name=$CONTAINER_NAME --format '{{.Status}}'")
    if [[ -n "$container_status" ]]; then
        log_success "Container started: $container_status"
    else
        log_error "Container failed to start"
        $ssh_cmd "docker logs $CONTAINER_NAME" || true
        exit 1
    fi
}

# Verify deployment
verify_deployment() {
    if [[ "$HEALTH_CHECK" != "true" ]]; then
        return
    fi
    
    log_step "Verifying Deployment"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Wait for service to be ready
    log_info "Waiting for service to be ready..."
    sleep 10
    
    # Test health endpoint
    log_info "Testing health endpoint..."
    if $ssh_cmd "curl -f http://localhost:$HOST_PORT/health" >/dev/null 2>&1; then
        local health_response=$($ssh_cmd "curl -s http://localhost:$HOST_PORT/health")
        log_success "Health check passed"
        log_info "Response: $health_response"
    else
        log_warning "Health check failed"
        log_info "Container logs:"
        $ssh_cmd "docker logs $CONTAINER_NAME | tail -20" || true
    fi
    
    # Show container info
    log_info "Container information:"
    $ssh_cmd "docker ps --filter name=$CONTAINER_NAME"
    $ssh_cmd "docker stats $CONTAINER_NAME --no-stream" || true
}

# Show deployment summary
show_summary() {
    log_step "Deployment Summary"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    echo "Orange Pi: $ORANGE_PI_USER@$ORANGE_PI_IP:$ORANGE_PI_PORT"
    echo "Docker Image: $IMAGE_NAME:$IMAGE_TAG"
    echo "Container: $CONTAINER_NAME"
    echo "Port Mapping: $HOST_PORT:$CONTAINER_PORT"
    echo "Transfer Method: $TRANSFER_METHOD"
    echo ""
    
    # Show deployed images and containers
    log_info "Deployed images:"
    $ssh_cmd "docker images $IMAGE_NAME" || true
    
    echo ""
    log_info "Running containers:"
    $ssh_cmd "docker ps --filter name=$CONTAINER_NAME" || true
    
    echo ""
    log_success "Docker deployment completed successfully!"
    log_info "Access the service at: http://$ORANGE_PI_IP:$HOST_PORT"
    log_info "Health check: http://$ORANGE_PI_IP:$HOST_PORT/health"
    
    echo ""
    log_info "Management commands:"
    log_info "  View logs: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'docker logs $CONTAINER_NAME'"
    log_info "  Stop container: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'docker stop $CONTAINER_NAME'"
    log_info "  Start container: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'docker start $CONTAINER_NAME'"
    log_info "  Remove container: ssh $ORANGE_PI_USER@$ORANGE_PI_IP 'docker rm -f $CONTAINER_NAME'"
}

# Main execution
main() {
    log_step "Deploy Docker Images to Orange Pi 5 Plus"
    
    test_ssh_connection
    check_docker_on_device
    
    case "$TRANSFER_METHOD" in
        "save")
            build_local_image
            transfer_image_save
            ;;
        "build-remote")
            build_remote_image
            ;;
        "registry")
            log_error "Registry transfer method not implemented yet"
            exit 1
            ;;
        *)
            log_error "Unknown transfer method: $TRANSFER_METHOD"
            exit 1
            ;;
    esac
    
    cleanup_old_deployments
    start_container
    verify_deployment
    show_summary
}

# Run main function
main "$@"
