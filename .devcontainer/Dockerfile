FROM nguyendkn/rk3588_development:ubuntu_24.04

# Install essential development tools and dependencies
RUN apt-get update && apt-get install -y \
  build-essential \
  cmake \
  gdb \
  git \
  clang-format \
  clang \
  lldb \
  ninja-build \
  python3 \
  python3-pip \
  libgtest-dev \
  pkg-config \
  curl \
  wget \
  libcurl4-openssl-dev \
  qtbase5-dev \
  qttools5-dev \
  qtmultimedia5-dev \
  qtwebengine5-dev \
  libqt5network5 \
  libqt5widgets5 \
  libqt5gui5 \
  libqt5core5a \
  libqt5webengine5 \
  libqt5webenginewidgets5 \
  && rm -rf /var/lib/apt/lists/*

# Install OpenGL and X11 dependencies for GUI applications
RUN apt-get update && apt-get install -y \
  # X11 server and utilities
  xvfb \
  x11-utils \
  x11-xserver-utils \
  xauth \
  xfonts-base \
  xfonts-75dpi \
  xfonts-100dpi \
  fonts-liberation \
  fonts-dejavu-core \
  x11-apps \
  # Window managers
  fluxbox \
  openbox \
  # Mesa OpenGL libraries
  libgl1-mesa-dev \
  libgl1-mesa-dri \
  libglu1-mesa \
  mesa-utils \
  # X11 libraries
  libx11-6 \
  libxext6 \
  libxrender1 \
  libxtst6 \
  libxi6 \
  libxrandr2 \
  libxss1 \
  libxinerama1 \
  libxcursor1 \
  libxcomposite1 \
  libxdamage1 \
  libxfixes3 \
  # XCB libraries for Qt
  libxcb1 \
  libxcb-glx0 \
  libxcb-keysyms1 \
  libxcb-image0 \
  libxcb-shm0 \
  libxcb-icccm4 \
  libxcb-sync1 \
  libxcb-xfixes0 \
  libxcb-shape0 \
  libxcb-randr0 \
  libxcb-render-util0 \
  libxcb-util1 \
  libxcb-xinerama0 \
  libxcb-xkb1 \
  # Additional Qt dependencies
  libxkbcommon0 \
  libxkbcommon-x11-0 \
  libfontconfig1 \
  libfreetype6 \
  libdbus-1-3 \
  # Audio support (for WebEngine)
  libasound2-dev \
  libpulse0 \
  # Additional WebEngine dependencies
  libnss3 \
  libatk-bridge2.0-0 \
  libdrm2 \
  libgtk-3-0 \
  libatspi2.0-0 \
  && rm -rf /var/lib/apt/lists/*

# Install Docker CLI
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    && curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null \
    && apt-get update \
    && apt-get install -y docker-ce-cli docker-compose-plugin \
    && rm -rf /var/lib/apt/lists/*

# Thiết lập thư mục làm việc
WORKDIR /app
