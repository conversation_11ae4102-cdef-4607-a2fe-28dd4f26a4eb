#include <gtest/gtest.h>
#include "rtsp/stream_multiplexer.hpp"
#include "rtsp/rtsp_config.hpp"
#include "rtsp/rtsp_types.hpp"

using namespace aibox::rtsp;

class StreamMultiplexerTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create test configuration
        config_.max_concurrent_streams = 4;
        config_.max_memory_usage_mb = 100;
        config_.worker_thread_count = 2;
        config_.enable_load_balancing = true;
        config_.enable_thermal_management = true;
        
        // Initialize queue configuration
        config_.queue_config.high_priority_size = 50;
        config_.queue_config.medium_priority_size = 30;
        config_.queue_config.low_priority_size = 20;
        
        multiplexer_ = std::make_unique<StreamMultiplexer>(config_);
    }

    void TearDown() override {
        if (multiplexer_) {
            multiplexer_->stop();
            multiplexer_.reset();
        }
    }

    RTSPConnectionConfig createTestStreamConfig(const std::string& id, StreamPriority priority = StreamPriority::MEDIUM) {
        RTSPConnectionConfig config;
        config.stream_id = id;
        config.rtsp_url = "rtsp://*************:554/stream" + id;
        config.username = "admin";
        config.password = "password";
        config.priority = priority;
        config.enabled = true;
        config.timeout_ms = 5000;
        config.retry_count = 3;
        return config;
    }

    StreamManagementConfig config_;
    std::unique_ptr<StreamMultiplexer> multiplexer_;
};

// Test basic lifecycle
TEST_F(StreamMultiplexerTest, BasicLifecycle) {
    EXPECT_FALSE(multiplexer_->isRunning());
    
    EXPECT_TRUE(multiplexer_->start());
    EXPECT_TRUE(multiplexer_->isRunning());
    
    multiplexer_->stop();
    EXPECT_FALSE(multiplexer_->isRunning());
}

// Test configuration management
TEST_F(StreamMultiplexerTest, ConfigurationManagement) {
    auto initial_config = multiplexer_->getConfig();
    EXPECT_EQ(initial_config.max_concurrent_streams, 4);
    EXPECT_EQ(initial_config.worker_thread_count, 2);
    
    StreamManagementConfig new_config = initial_config;
    new_config.max_concurrent_streams = 8;
    new_config.worker_thread_count = 4;
    
    multiplexer_->updateConfig(new_config);
    
    auto updated_config = multiplexer_->getConfig();
    EXPECT_EQ(updated_config.max_concurrent_streams, 8);
    EXPECT_EQ(updated_config.worker_thread_count, 4);
}

// Test stream management
TEST_F(StreamMultiplexerTest, StreamManagement) {
    auto stream_config = createTestStreamConfig("test1");
    
    // Add stream
    EXPECT_TRUE(multiplexer_->addStream("test1", stream_config));
    
    // Check stream exists
    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 1);
    EXPECT_EQ(stream_ids[0], "test1");
    
    // Get stream info
    auto stream_info = multiplexer_->getStreamInfo("test1");
    EXPECT_EQ(stream_info.id, "test1");
    EXPECT_EQ(stream_info.state, ConnectionState::DISCONNECTED);
    EXPECT_TRUE(stream_info.enabled);
    
    // Remove stream
    EXPECT_TRUE(multiplexer_->removeStream("test1"));
    
    stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 0);
}

// Test multiple streams
TEST_F(StreamMultiplexerTest, MultipleStreams) {
    // Add multiple streams
    for (int i = 1; i <= 3; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }
    
    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 3);
    
    auto stream_infos = multiplexer_->getStreamInfos();
    EXPECT_EQ(stream_infos.size(), 3);
    
    // Check active stream count
    EXPECT_EQ(multiplexer_->getActiveStreamCount(), 0);  // Not connected yet
}

// Test stream limits
TEST_F(StreamMultiplexerTest, StreamLimits) {
    // Try to add more streams than the limit
    for (int i = 1; i <= 5; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        bool result = multiplexer_->addStream(std::to_string(i), config);
        
        if (i <= 4) {
            EXPECT_TRUE(result);  // Should succeed within limit
        } else {
            EXPECT_FALSE(result);  // Should fail beyond limit
        }
    }
    
    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), 4);  // Should be limited to max_concurrent_streams
}

// Test stream enable/disable
TEST_F(StreamMultiplexerTest, StreamEnableDisable) {
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    
    // Initially enabled
    auto info = multiplexer_->getStreamInfo("test1");
    EXPECT_TRUE(info.enabled);
    
    // Disable stream
    EXPECT_TRUE(multiplexer_->enableStream("test1", false));
    info = multiplexer_->getStreamInfo("test1");
    EXPECT_FALSE(info.enabled);
    
    // Re-enable stream
    EXPECT_TRUE(multiplexer_->enableStream("test1", true));
    info = multiplexer_->getStreamInfo("test1");
    EXPECT_TRUE(info.enabled);
}

// Test priority management
TEST_F(StreamMultiplexerTest, PriorityManagement) {
    auto config = createTestStreamConfig("test1", StreamPriority::MEDIUM);
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    
    // Check initial priority
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::MEDIUM);
    
    // Change priority
    EXPECT_TRUE(multiplexer_->setStreamPriority("test1", StreamPriority::HIGH));
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::HIGH);
    
    // Change to low priority
    EXPECT_TRUE(multiplexer_->setStreamPriority("test1", StreamPriority::LOW));
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::LOW);
}

// Test statistics collection
TEST_F(StreamMultiplexerTest, StatisticsCollection) {
    auto stats = multiplexer_->getStatistics();
    
    // Initial statistics
    EXPECT_EQ(stats.total_streams, 0);
    EXPECT_EQ(stats.active_streams, 0);
    EXPECT_EQ(stats.connected_streams, 0);
    
    // Add a stream
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    
    stats = multiplexer_->getStatistics();
    EXPECT_EQ(stats.total_streams, 1);
    EXPECT_EQ(stats.active_streams, 1);  // Enabled by default
    EXPECT_EQ(stats.connected_streams, 0);  // Not connected yet
}

// Test system health monitoring
TEST_F(StreamMultiplexerTest, SystemHealthMonitoring) {
    auto health = multiplexer_->getSystemHealth();
    
    // Initial health should be good
    EXPECT_FALSE(health.status.empty());
    EXPECT_GE(health.score, 0.0f);
    EXPECT_LE(health.score, 1.0f);
    EXPECT_EQ(health.active_streams, 0);
    EXPECT_EQ(health.error_count, 0);
}

// Test load balancing
TEST_F(StreamMultiplexerTest, LoadBalancing) {
    EXPECT_TRUE(multiplexer_->isLoadBalancingEnabled());
    
    multiplexer_->enableLoadBalancing(false);
    EXPECT_FALSE(multiplexer_->isLoadBalancingEnabled());
    
    multiplexer_->enableLoadBalancing(true);
    EXPECT_TRUE(multiplexer_->isLoadBalancingEnabled());
    
    // Test rebalancing (should not crash)
    multiplexer_->rebalanceStreams();
    multiplexer_->redistributeLoad();
}

// Test memory usage tracking
TEST_F(StreamMultiplexerTest, MemoryUsageTracking) {
    size_t initial_memory = multiplexer_->getTotalMemoryUsage();
    
    // Add streams and check memory increases
    for (int i = 1; i <= 3; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }
    
    size_t memory_with_streams = multiplexer_->getTotalMemoryUsage();
    EXPECT_GT(memory_with_streams, initial_memory);
}

// Test error handling
TEST_F(StreamMultiplexerTest, ErrorHandling) {
    // Try to remove non-existent stream
    EXPECT_FALSE(multiplexer_->removeStream("nonexistent"));
    
    // Try to enable non-existent stream
    EXPECT_FALSE(multiplexer_->enableStream("nonexistent", true));
    
    // Try to set priority for non-existent stream
    EXPECT_FALSE(multiplexer_->setStreamPriority("nonexistent", StreamPriority::HIGH));
    
    // Try to get info for non-existent stream
    auto info = multiplexer_->getStreamInfo("nonexistent");
    EXPECT_TRUE(info.id.empty());  // Should return default-constructed info
}

// Test callbacks
TEST_F(StreamMultiplexerTest, CallbackFunctionality) {
    bool nal_callback_called = false;
    bool event_callback_called = false;
    bool error_callback_called = false;
    
    multiplexer_->setNALUnitCallback([&](const StreamId& id, const NALUnit& nal) {
        nal_callback_called = true;
    });
    
    multiplexer_->setStreamEventCallback([&](const StreamId& id, const std::string& event) {
        event_callback_called = true;
    });
    
    multiplexer_->setErrorCallback([&](const StreamId& id, ErrorCategory category, const std::string& message) {
        error_callback_called = true;
    });
    
    // Callbacks are set (actual invocation would require real stream data)
    EXPECT_TRUE(true);  // Test passes if no exceptions thrown
}

// Test thermal management
TEST_F(StreamMultiplexerTest, ThermalManagement) {
    // Add some streams
    for (int i = 1; i <= 2; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }
    
    // Test thermal throttling (should not crash)
    multiplexer_->handleThermalThrottling(75);  // Normal temperature
    multiplexer_->handleThermalThrottling(85);  // High temperature
    
    // Test performance mode changes
    multiplexer_->setPerformanceMode(true);
    multiplexer_->setPerformanceMode(false);
}

// Test resource management
TEST_F(StreamMultiplexerTest, ResourceManagement) {
    auto config = createTestStreamConfig("test1");

    // Check resource availability
    EXPECT_TRUE(multiplexer_->checkResourceAvailability(config));

    // Test resource optimization (should not crash)
    multiplexer_->optimizeResourceUsage();
    multiplexer_->handleMemoryPressure();
}

// Test ThreadSafeQueue functionality
TEST_F(StreamMultiplexerTest, ThreadSafeQueueBasics) {
    ThreadSafeQueue<int> queue(5);

    // Test basic operations
    EXPECT_TRUE(queue.empty());
    EXPECT_FALSE(queue.full());
    EXPECT_EQ(queue.size(), 0);
    EXPECT_EQ(queue.capacity(), 5);

    // Test enqueue
    EXPECT_TRUE(queue.tryEnqueue(1));
    EXPECT_TRUE(queue.tryEnqueue(2));
    EXPECT_EQ(queue.size(), 2);
    EXPECT_FALSE(queue.empty());

    // Test dequeue
    int item;
    EXPECT_TRUE(queue.tryDequeue(item));
    EXPECT_EQ(item, 1);
    EXPECT_EQ(queue.size(), 1);

    // Test clear
    queue.clear();
    EXPECT_TRUE(queue.empty());
    EXPECT_EQ(queue.size(), 0);
}

// Test ThreadSafeQueue capacity limits
TEST_F(StreamMultiplexerTest, ThreadSafeQueueCapacity) {
    ThreadSafeQueue<int> queue(3);

    // Fill to capacity
    EXPECT_TRUE(queue.tryEnqueue(1));
    EXPECT_TRUE(queue.tryEnqueue(2));
    EXPECT_TRUE(queue.tryEnqueue(3));
    EXPECT_TRUE(queue.full());

    // Should fail when full
    EXPECT_FALSE(queue.tryEnqueue(4));

    // Dequeue one and try again
    int item;
    EXPECT_TRUE(queue.tryDequeue(item));
    EXPECT_TRUE(queue.tryEnqueue(4));
}

// Test ThreadSafeQueue timeout operations
TEST_F(StreamMultiplexerTest, ThreadSafeQueueTimeout) {
    ThreadSafeQueue<int> queue(2);

    // Test timeout on empty queue
    int item;
    auto start = std::chrono::steady_clock::now();
    EXPECT_FALSE(queue.dequeue(item, 100));  // 100ms timeout
    auto elapsed = std::chrono::steady_clock::now() - start;
    EXPECT_GE(elapsed, std::chrono::milliseconds(90));

    // Fill queue
    EXPECT_TRUE(queue.tryEnqueue(1));
    EXPECT_TRUE(queue.tryEnqueue(2));

    // Test timeout on full queue
    start = std::chrono::steady_clock::now();
    EXPECT_FALSE(queue.enqueue(3, 100));  // 100ms timeout
    elapsed = std::chrono::steady_clock::now() - start;
    EXPECT_GE(elapsed, std::chrono::milliseconds(90));
}

// Test ThreadSafeQueue stop functionality
TEST_F(StreamMultiplexerTest, ThreadSafeQueueStop) {
    ThreadSafeQueue<int> queue(5);

    // Add some items
    EXPECT_TRUE(queue.tryEnqueue(1));
    EXPECT_TRUE(queue.tryEnqueue(2));

    // Stop the queue
    queue.stop();

    // Should fail to enqueue after stop
    EXPECT_FALSE(queue.tryEnqueue(3));

    // Should still be able to dequeue existing items
    int item;
    EXPECT_TRUE(queue.tryDequeue(item));
    EXPECT_EQ(item, 1);
}

// Test concurrent stream operations
TEST_F(StreamMultiplexerTest, ConcurrentStreamOperations) {
    const int num_streams = 5;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};

    // Start multiplexer
    EXPECT_TRUE(multiplexer_->start());

    // Add streams concurrently
    for (int i = 0; i < num_streams; ++i) {
        threads.emplace_back([this, i, &success_count]() {
            auto config = createTestStreamConfig("stream_" + std::to_string(i));
            if (multiplexer_->addStream("stream_" + std::to_string(i), config)) {
                success_count++;
            }
        });
    }

    // Wait for all threads
    for (auto& thread : threads) {
        thread.join();
    }

    // Check results
    EXPECT_EQ(success_count.load(), std::min(num_streams, 4));  // Limited by max_concurrent_streams

    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_LE(stream_ids.size(), 4);
}

// Test stream state transitions
TEST_F(StreamMultiplexerTest, StreamStateTransitions) {
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));

    // Initial state should be DISCONNECTED
    EXPECT_EQ(multiplexer_->getStreamState("test1"), ConnectionState::DISCONNECTED);

    // Enable/disable should work
    EXPECT_TRUE(multiplexer_->enableStream("test1", false));
    auto info = multiplexer_->getStreamInfo("test1");
    EXPECT_FALSE(info.enabled);

    EXPECT_TRUE(multiplexer_->enableStream("test1", true));
    info = multiplexer_->getStreamInfo("test1");
    EXPECT_TRUE(info.enabled);
}

// Test configuration updates
TEST_F(StreamMultiplexerTest, ConfigurationUpdates) {
    auto initial_config = multiplexer_->getConfig();

    StreamManagementConfig new_config = initial_config;
    new_config.max_concurrent_streams = 8;
    new_config.max_memory_usage_mb = 200;
    new_config.worker_thread_count = 6;

    multiplexer_->updateConfig(new_config);

    auto updated_config = multiplexer_->getConfig();
    EXPECT_EQ(updated_config.max_concurrent_streams, 8);
    EXPECT_EQ(updated_config.max_memory_usage_mb, 200);
    EXPECT_EQ(updated_config.worker_thread_count, 6);
}

// Test stream priority changes
TEST_F(StreamMultiplexerTest, StreamPriorityChanges) {
    auto config = createTestStreamConfig("test1", StreamPriority::LOW);
    EXPECT_TRUE(multiplexer_->addStream("test1", config));

    // Check initial priority
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::LOW);

    // Change to high priority
    EXPECT_TRUE(multiplexer_->setStreamPriority("test1", StreamPriority::HIGH));
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::HIGH);

    // Change to critical priority
    EXPECT_TRUE(multiplexer_->setStreamPriority("test1", StreamPriority::CRITICAL));
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::CRITICAL);
}

// Test statistics accuracy
TEST_F(StreamMultiplexerTest, StatisticsAccuracy) {
    // Add multiple streams with different priorities
    auto high_config = createTestStreamConfig("high", StreamPriority::HIGH);
    auto med_config = createTestStreamConfig("medium", StreamPriority::MEDIUM);
    auto low_config = createTestStreamConfig("low", StreamPriority::LOW);

    EXPECT_TRUE(multiplexer_->addStream("high", high_config));
    EXPECT_TRUE(multiplexer_->addStream("medium", med_config));
    EXPECT_TRUE(multiplexer_->addStream("low", low_config));

    auto stats = multiplexer_->getStatistics();
    EXPECT_EQ(stats.total_streams, 3);
    EXPECT_EQ(stats.active_streams, 3);  // All enabled by default
    EXPECT_EQ(stats.connected_streams, 0);  // Not connected yet

    // Disable one stream
    EXPECT_TRUE(multiplexer_->enableStream("low", false));

    stats = multiplexer_->getStatistics();
    EXPECT_EQ(stats.total_streams, 3);
    EXPECT_EQ(stats.active_streams, 2);  // One disabled
}

// Test memory usage tracking
TEST_F(StreamMultiplexerTest, MemoryUsageTracking) {
    size_t initial_memory = multiplexer_->getTotalMemoryUsage();

    // Add streams and check memory increases
    for (int i = 1; i <= 3; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }

    size_t memory_with_streams = multiplexer_->getTotalMemoryUsage();
    EXPECT_GT(memory_with_streams, initial_memory);

    // Remove streams and check memory decreases
    EXPECT_TRUE(multiplexer_->removeStream("1"));
    EXPECT_TRUE(multiplexer_->removeStream("2"));

    size_t memory_after_removal = multiplexer_->getTotalMemoryUsage();
    EXPECT_LT(memory_after_removal, memory_with_streams);
}

// Test system health monitoring
TEST_F(StreamMultiplexerTest, SystemHealthMonitoring) {
    auto health = multiplexer_->getSystemHealth();

    // Initial health should be reasonable
    EXPECT_FALSE(health.status.empty());
    EXPECT_GE(health.score, 0.0f);
    EXPECT_LE(health.score, 1.0f);
    EXPECT_EQ(health.active_streams, 0);
    EXPECT_EQ(health.error_count, 0);

    // Add some streams
    for (int i = 1; i <= 2; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }

    health = multiplexer_->getSystemHealth();
    EXPECT_EQ(health.active_streams, 2);
}

// Test performance mode changes
TEST_F(StreamMultiplexerTest, PerformanceModeChanges) {
    // Add some streams first
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));

    // Test performance mode changes (should not crash)
    multiplexer_->setPerformanceMode(true);
    multiplexer_->setPerformanceMode(false);

    // Check that configuration might have changed
    auto updated_config = multiplexer_->getConfig();
    EXPECT_TRUE(true);  // Just ensure no crash
}

// Test thermal throttling
TEST_F(StreamMultiplexerTest, ThermalThrottling) {
    // Add multiple streams
    for (int i = 1; i <= 3; ++i) {
        auto config = createTestStreamConfig(std::to_string(i),
                                           i == 1 ? StreamPriority::HIGH : StreamPriority::MEDIUM);
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }

    auto initial_count = multiplexer_->getActiveStreamCount();

    // Simulate high temperature
    multiplexer_->handleThermalThrottling(85);  // High but not critical

    // Simulate critical temperature
    multiplexer_->handleThermalThrottling(95);  // Critical temperature

    // Should not crash and might have disabled some streams
    EXPECT_TRUE(true);
}

// Test queue overflow handling
TEST_F(StreamMultiplexerTest, QueueOverflowHandling) {
    ThreadSafeQueue<int> queue(2);  // Small queue for testing

    // Fill queue to capacity
    EXPECT_TRUE(queue.tryEnqueue(1));
    EXPECT_TRUE(queue.tryEnqueue(2));
    EXPECT_TRUE(queue.full());

    // Try to add more (should fail)
    EXPECT_FALSE(queue.tryEnqueue(3));
    EXPECT_FALSE(queue.tryEnqueue(4));

    // Drain queue
    int item;
    EXPECT_TRUE(queue.tryDequeue(item));
    EXPECT_EQ(item, 1);
    EXPECT_TRUE(queue.tryDequeue(item));
    EXPECT_EQ(item, 2);
    EXPECT_TRUE(queue.empty());
}

// Test adaptive queue sizing
TEST_F(StreamMultiplexerTest, AdaptiveQueueSizing) {
    ThreadSafeQueue<int> queue(10);

    // Enable adaptive sizing
    queue.enableAdaptiveSizing(true);

    // Test size changes
    EXPECT_EQ(queue.capacity(), 10);
    queue.setMaxSize(20);
    EXPECT_EQ(queue.capacity(), 20);

    // Fill partially and resize down
    for (int i = 0; i < 5; ++i) {
        EXPECT_TRUE(queue.tryEnqueue(i));
    }

    queue.setMaxSize(3);  // Smaller than current content
    EXPECT_EQ(queue.capacity(), 3);
    EXPECT_EQ(queue.size(), 5);  // Content remains
}

// Test stream reconnection logic
TEST_F(StreamMultiplexerTest, StreamReconnectionLogic) {
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));

    // Initial state
    EXPECT_EQ(multiplexer_->getStreamState("test1"), ConnectionState::DISCONNECTED);

    // Test reconnection (should not crash even if not connected)
    EXPECT_TRUE(multiplexer_->reconnectStream("test1"));

    // Test with non-existent stream
    EXPECT_FALSE(multiplexer_->reconnectStream("nonexistent"));
}

// Test callback functionality
TEST_F(StreamMultiplexerTest, CallbackFunctionality) {
    std::atomic<int> nal_callback_count{0};
    std::atomic<int> event_callback_count{0};
    std::atomic<int> error_callback_count{0};

    // Set up callbacks
    multiplexer_->setNALUnitCallback([&](const StreamId& id, const NALUnit& nal) {
        nal_callback_count++;
    });

    multiplexer_->setStreamEventCallback([&](const StreamId& id, const std::string& event) {
        event_callback_count++;
    });

    multiplexer_->setErrorCallback([&](const StreamId& id, ErrorCategory category, const std::string& message) {
        error_callback_count++;
    });

    // Callbacks are set (actual invocation would require real stream data)
    // This test just ensures the callbacks can be set without crashing
    EXPECT_TRUE(true);
}

// Test stream update configuration
TEST_F(StreamMultiplexerTest, StreamConfigurationUpdate) {
    auto config = createTestStreamConfig("test1", StreamPriority::LOW);
    config.timeout_ms = 3000;
    config.retry_count = 2;

    EXPECT_TRUE(multiplexer_->addStream("test1", config));

    // Update configuration
    auto new_config = config;
    new_config.priority = StreamPriority::HIGH;
    new_config.timeout_ms = 5000;
    new_config.retry_count = 5;

    EXPECT_TRUE(multiplexer_->updateStreamConfig("test1", new_config));

    // Verify priority was updated
    EXPECT_EQ(multiplexer_->getStreamPriority("test1"), StreamPriority::HIGH);

    // Test with non-existent stream
    EXPECT_FALSE(multiplexer_->updateStreamConfig("nonexistent", new_config));
}

// Test bulk stream operations
TEST_F(StreamMultiplexerTest, BulkStreamOperations) {
    const int num_streams = 3;

    // Add multiple streams
    for (int i = 1; i <= num_streams; ++i) {
        auto config = createTestStreamConfig("stream_" + std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream("stream_" + std::to_string(i), config));
    }

    auto stream_ids = multiplexer_->getStreamIds();
    EXPECT_EQ(stream_ids.size(), num_streams);

    // Connect all streams
    multiplexer_->connectAllStreams();

    // Disconnect all streams
    multiplexer_->disconnectAllStreams();

    // Check states
    for (int i = 1; i <= num_streams; ++i) {
        auto state = multiplexer_->getStreamState("stream_" + std::to_string(i));
        EXPECT_EQ(state, ConnectionState::DISCONNECTED);
    }
}

// Test statistics reset functionality
TEST_F(StreamMultiplexerTest, StatisticsReset) {
    auto stats = multiplexer_->getStatistics();

    // Modify some statistics (in real scenario, these would be updated by stream processing)
    stats.total_packets_processed = 100;
    stats.dropped_packets = 5;
    stats.connection_errors = 2;

    // Reset statistics
    stats.reset();

    // Check all values are reset
    EXPECT_EQ(stats.total_packets_processed, 0);
    EXPECT_EQ(stats.dropped_packets, 0);
    EXPECT_EQ(stats.connection_errors, 0);
    EXPECT_EQ(stats.active_streams, 0);
    EXPECT_EQ(stats.total_streams, 0);
}

// Test packet drop rate calculation
TEST_F(StreamMultiplexerTest, PacketDropRateCalculation) {
    MultiplexerStatistics stats;

    // No packets processed
    EXPECT_EQ(stats.getPacketDropRate(), 0.0);

    // Some packets processed, none dropped
    stats.total_packets_processed = 100;
    stats.dropped_packets = 0;
    EXPECT_EQ(stats.getPacketDropRate(), 0.0);

    // Some packets dropped
    stats.total_packets_processed = 100;
    stats.dropped_packets = 10;
    EXPECT_NEAR(stats.getPacketDropRate(), 0.090909, 0.000001);  // 10/110

    // All packets dropped
    stats.total_packets_processed = 0;
    stats.dropped_packets = 50;
    EXPECT_EQ(stats.getPacketDropRate(), 1.0);
}

// Test multiplexer health scoring
TEST_F(StreamMultiplexerTest, MultiplexerHealthScoring) {
    auto health = multiplexer_->getSystemHealth();

    // Initial health should be good (no streams, no load)
    EXPECT_GE(health.score, 0.8f);  // Should be healthy initially
    EXPECT_EQ(health.status, "HEALTHY");

    // Add streams to change health
    for (int i = 1; i <= 2; ++i) {
        auto config = createTestStreamConfig(std::to_string(i));
        EXPECT_TRUE(multiplexer_->addStream(std::to_string(i), config));
    }

    health = multiplexer_->getSystemHealth();
    EXPECT_EQ(health.active_streams, 2);
}

// Test concurrent queue operations
TEST_F(StreamMultiplexerTest, ConcurrentQueueOperations) {
    ThreadSafeQueue<int> queue(100);
    const int num_producers = 3;
    const int num_consumers = 2;
    const int items_per_producer = 50;

    std::vector<std::thread> producers;
    std::vector<std::thread> consumers;
    std::atomic<int> total_produced{0};
    std::atomic<int> total_consumed{0};

    // Start consumers
    for (int i = 0; i < num_consumers; ++i) {
        consumers.emplace_back([&queue, &total_consumed]() {
            int item;
            while (total_consumed.load() < num_producers * items_per_producer) {
                if (queue.dequeue(item, 10)) {  // 10ms timeout
                    total_consumed++;
                }
            }
        });
    }

    // Start producers
    for (int i = 0; i < num_producers; ++i) {
        producers.emplace_back([&queue, &total_produced, items_per_producer, i]() {
            for (int j = 0; j < items_per_producer; ++j) {
                int item = i * items_per_producer + j;
                while (!queue.enqueue(item, 10)) {  // 10ms timeout, retry
                    std::this_thread::sleep_for(std::chrono::milliseconds(1));
                }
                total_produced++;
            }
        });
    }

    // Wait for all producers
    for (auto& producer : producers) {
        producer.join();
    }

    // Wait for all consumers
    for (auto& consumer : consumers) {
        consumer.join();
    }

    EXPECT_EQ(total_produced.load(), num_producers * items_per_producer);
    EXPECT_EQ(total_consumed.load(), num_producers * items_per_producer);
    EXPECT_TRUE(queue.empty());
}

// Test stream lifecycle with multiplexer running
TEST_F(StreamMultiplexerTest, StreamLifecycleWithRunningMultiplexer) {
    // Start multiplexer
    EXPECT_TRUE(multiplexer_->start());
    EXPECT_TRUE(multiplexer_->isRunning());

    // Add stream while running
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));

    // Change priority while running
    EXPECT_TRUE(multiplexer_->setStreamPriority("test1", StreamPriority::HIGH));

    // Update config while running
    config.timeout_ms = 8000;
    EXPECT_TRUE(multiplexer_->updateStreamConfig("test1", config));

    // Remove stream while running
    EXPECT_TRUE(multiplexer_->removeStream("test1"));

    // Stop multiplexer
    multiplexer_->stop();
    EXPECT_FALSE(multiplexer_->isRunning());
}

// Test edge cases and error conditions
TEST_F(StreamMultiplexerTest, EdgeCasesAndErrorConditions) {
    // Test operations on non-existent streams
    EXPECT_FALSE(multiplexer_->removeStream("nonexistent"));
    EXPECT_FALSE(multiplexer_->enableStream("nonexistent", true));
    EXPECT_FALSE(multiplexer_->setStreamPriority("nonexistent", StreamPriority::HIGH));
    EXPECT_EQ(multiplexer_->getStreamPriority("nonexistent"), StreamPriority::MEDIUM);  // Default

    // Test adding duplicate stream
    auto config = createTestStreamConfig("test1");
    EXPECT_TRUE(multiplexer_->addStream("test1", config));
    EXPECT_FALSE(multiplexer_->addStream("test1", config));  // Should fail

    // Test empty stream ID
    EXPECT_FALSE(multiplexer_->addStream("", config));

    // Clean up
    EXPECT_TRUE(multiplexer_->removeStream("test1"));
}
