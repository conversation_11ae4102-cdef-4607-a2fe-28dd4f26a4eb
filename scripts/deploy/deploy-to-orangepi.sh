#!/bin/bash

# Deploy to Orange Pi 5 Plus Script
# Transfers built applications and dependencies to Orange Pi target

set -e

# Script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BOLD}${CYAN}=== $1 ===${NC}"
}

# Default configuration
ORANGE_PI_IP=""
ORANGE_PI_USER="orangepi"
ORANGE_PI_PORT="22"
BUILD_DIR="build-orangepi"
DEPLOY_DIR="/home/<USER>/c-aibox"
SSH_KEY=""
INSTALL_DEPS=true
START_SERVICES=false
BACKUP_EXISTING=true

# Load SSH configuration if available
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
if [[ -f "$PROJECT_ROOT/.orangepi_ssh" ]]; then
    source "$PROJECT_ROOT/.orangepi_ssh"
    SSH_KEY="$ORANGE_PI_SSH_KEY"
fi

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --ip)
            ORANGE_PI_IP="$2"
            shift 2
            ;;
        --user)
            ORANGE_PI_USER="$2"
            shift 2
            ;;
        --port)
            ORANGE_PI_PORT="$2"
            shift 2
            ;;
        --build-dir)
            BUILD_DIR="$2"
            shift 2
            ;;
        --deploy-dir)
            DEPLOY_DIR="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --no-deps)
            INSTALL_DEPS=false
            shift
            ;;
        --start-services)
            START_SERVICES=true
            shift
            ;;
        --no-backup)
            BACKUP_EXISTING=false
            shift
            ;;
        --help)
            cat << EOF
Deploy to Orange Pi 5 Plus Script

USAGE:
    $0 --ip IP_ADDRESS [OPTIONS]

REQUIRED:
    --ip IP         Orange Pi IP address

OPTIONS:
    --user USER     SSH username (default: orangepi)
    --port PORT     SSH port (default: 22)
    --build-dir DIR Local build directory (default: build-orangepi, use build-container for Docker builds)
    --deploy-dir DIR Remote deployment directory (default: /home/<USER>/c-aibox)
    --ssh-key PATH  SSH private key file
    --no-deps       Skip dependency installation
    --start-services Start services after deployment
    --no-backup     Don't backup existing installation
    --help          Show this help

EXAMPLES:
    $0 --ip *************                    # Basic deployment
    $0 --ip ************* --start-services   # Deploy and start services
    $0 --ip ************* --ssh-key ~/.ssh/orangepi_key  # Use SSH key
    $0 --ip *************** --build-dir build-container  # Deploy Docker build

PREREQUISITES:
    1. Orange Pi 5 Plus with SSH enabled
    2. Cross-compiled build in build-orangepi or build-container directory
    3. SSH access to Orange Pi (password or key-based)
EOF
            exit 0
            ;;
        *)
            if [[ -z "$ORANGE_PI_IP" && "$1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                ORANGE_PI_IP="$1"
            else
                log_error "Unknown option: $1"
                exit 1
            fi
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$ORANGE_PI_IP" ]]; then
    log_error "Orange Pi IP address is required"
    log_info "Usage: $0 --ip IP_ADDRESS"
    exit 1
fi

# SSH command builder
build_ssh_cmd() {
    local ssh_cmd="ssh"

    if [[ -n "$SSH_KEY" ]]; then
        ssh_cmd="$ssh_cmd -i $SSH_KEY"
    fi

    ssh_cmd="$ssh_cmd -p $ORANGE_PI_PORT $ORANGE_PI_USER@$ORANGE_PI_IP"
    echo "$ssh_cmd"
}

# SCP command builder
build_scp_cmd() {
    local scp_cmd="scp"

    if [[ -n "$SSH_KEY" ]]; then
        scp_cmd="$scp_cmd -i $SSH_KEY"
    fi

    scp_cmd="$scp_cmd -P $ORANGE_PI_PORT"
    echo "$scp_cmd"
}

# Check SSH key permissions and setup
check_ssh_key() {
    if [[ -n "$SSH_KEY" && -f "$SSH_KEY" ]]; then
        # Check key file permissions
        local key_perms=$(stat -c "%a" "$SSH_KEY" 2>/dev/null || stat -f "%A" "$SSH_KEY" 2>/dev/null || echo "unknown")
        if [[ "$key_perms" != "600" && "$key_perms" != "400" ]]; then
            log_warning "SSH key permissions are $key_perms, should be 600 or 400"
            log_info "Fixing SSH key permissions..."
            chmod 600 "$SSH_KEY"
        fi

        # Check if key is encrypted (has ENCRYPTED in header)
        if grep -q "ENCRYPTED" "$SSH_KEY" 2>/dev/null; then
            log_warning "SSH key appears to be encrypted - this may cause issues with automated deployment"
        fi

        log_info "SSH key file: $SSH_KEY (permissions: $(stat -c "%a" "$SSH_KEY" 2>/dev/null || stat -f "%A" "$SSH_KEY" 2>/dev/null))"
    fi
}

# Test SSH connection
test_ssh_connection() {
    log_step "Testing SSH Connection"

    local ssh_cmd=$(build_ssh_cmd)

    if $ssh_cmd "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection established"
    else
        log_error "SSH connection failed to $ORANGE_PI_IP"
        exit 1
    fi
}

# Check local build
check_local_build() {
    log_step "Checking Local Build"
    
    if [[ ! -d "$PROJECT_ROOT/$BUILD_DIR" ]]; then
        log_error "Build directory not found: $PROJECT_ROOT/$BUILD_DIR"
        log_info "Run cross-compilation first:"
        log_info "  Native build: ./scripts/build/build-orangepi.sh"
        log_info "  Docker build: ./scripts/cross-build/build-orangepi-docker.sh"
        exit 1
    fi
    
    cd "$PROJECT_ROOT/$BUILD_DIR"
    
    # Check for executables
    local executables=$(find . -type f -executable -name "*" | grep -E "(client_app|server)" | wc -l)
    if [[ "$executables" -eq 0 ]]; then
        log_error "No executables found in build directory"
        log_info "Run cross-compilation first:"
        log_info "  Native build: ./scripts/build/build-orangepi.sh"
        log_info "  Docker build: ./scripts/cross-build/build-orangepi-docker.sh"
        exit 1
    fi
    
    log_success "Found $executables executable(s) in build directory"
    
    # Verify ARM64 architecture
    local sample_exe=$(find . -type f -executable -name "*" | grep -E "(client_app|server)" | head -n1)
    if [[ -n "$sample_exe" ]]; then
        if command -v file >/dev/null 2>&1; then
            if file "$sample_exe" | grep -q "ARM aarch64"; then
                log_success "Verified ARM64 architecture"
            else
                log_warning "Could not verify ARM64 architecture"
            fi
        else
            # Use readelf as fallback if file command is not available
            if command -v readelf >/dev/null 2>&1; then
                if readelf -h "$sample_exe" 2>/dev/null | grep -q "AArch64"; then
                    log_success "Verified ARM64 architecture (using readelf)"
                else
                    log_warning "Could not verify ARM64 architecture"
                fi
            else
                log_info "Architecture verification skipped (file/readelf not available)"
            fi
        fi
    fi
}

# Backup existing installation
backup_existing() {
    if [[ "$BACKUP_EXISTING" != "true" ]]; then
        return
    fi

    log_step "Backing Up Existing Installation"

    local ssh_cmd=$(build_ssh_cmd)

    # Check if deployment directory exists
    if $ssh_cmd "test -d $DEPLOY_DIR" 2>/dev/null; then
        local backup_dir="${DEPLOY_DIR}.backup.$(date +%Y%m%d-%H%M%S)"
        log_info "Backing up existing installation to $backup_dir"
        $ssh_cmd "mv $DEPLOY_DIR $backup_dir"
        log_success "Backup created: $backup_dir"
    else
        log_info "No existing installation found"
    fi
}

# Transfer files to Orange Pi
transfer_files() {
    log_step "Transferring Files to Orange Pi"
    
    local ssh_cmd=$(build_ssh_cmd)
    local scp_cmd=$(build_scp_cmd)
    
    # Create deployment directory
    log_info "Creating deployment directory: $DEPLOY_DIR"
    $ssh_cmd "mkdir -p $DEPLOY_DIR"

    cd "$PROJECT_ROOT/$BUILD_DIR"

    # Transfer executables
    log_info "Transferring executables..."
    find . -type f -executable -name "*" | grep -E "(client_app|server)" | while read -r exe; do
        log_info "  Transferring: $exe"
        $scp_cmd "$exe" "$ORANGE_PI_USER@$ORANGE_PI_IP:$DEPLOY_DIR/" || {
            log_warning "SCP reported error for $exe, but continuing..."
        }
    done

    # Transfer libraries
    log_info "Transferring libraries..."
    find . -name "*.so" | while read -r lib; do
        log_info "  Transferring: $lib"
        $scp_cmd "$lib" "$ORANGE_PI_USER@$ORANGE_PI_IP:$DEPLOY_DIR/" || {
            log_warning "SCP reported error for $lib, but continuing..."
        }
    done

    # Transfer configuration files
    if [[ -f "$PROJECT_ROOT/.env" ]]; then
        log_info "Transferring configuration..."
        $scp_cmd "$PROJECT_ROOT/.env" "$ORANGE_PI_USER@$ORANGE_PI_IP:$DEPLOY_DIR/" || {
            log_warning "SCP reported error for .env, but continuing..."
        }
    fi

    # Transfer deployment scripts
    log_info "Transferring deployment scripts..."
    $scp_cmd -r "$PROJECT_ROOT/scripts/deploy" "$ORANGE_PI_USER@$ORANGE_PI_IP:$DEPLOY_DIR/" || {
        log_warning "SCP reported error for deployment scripts, but continuing..."
    }
    
    log_success "File transfer completed"
}

# Install dependencies on Orange Pi
install_dependencies() {
    if [[ "$INSTALL_DEPS" != "true" ]]; then
        return
    fi
    
    log_step "Installing Dependencies on Orange Pi"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Transfer and run dependency installation script
    if [[ -f "$PROJECT_ROOT/scripts/install-rtsp-deps.sh" ]]; then
        log_info "Installing RTSP dependencies..."
        eval "$ssh_cmd 'cd $DEPLOY_DIR && sudo ./deploy/install-orangepi-deps.sh'" || {
            log_warning "Dependency installation failed, continuing..."
        }
    fi

    # Install runtime libraries
    log_info "Installing runtime libraries..."
    eval "$ssh_cmd 'sudo apt-get update && sudo apt-get install -y libgstreamer1.0-0 libqt5core5a libqt5widgets5 libqt5network5 libqt5webengine5 libqt5webenginewidgets5'" || {
        log_warning "Runtime library installation failed"
    }
    
    log_success "Dependencies installation completed"
}

# Setup Orange Pi environment
setup_environment() {
    log_step "Setting Up Orange Pi Environment"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Set executable permissions
    log_info "Setting executable permissions..."
    eval "$ssh_cmd 'cd $DEPLOY_DIR && chmod +x client_app server 2>/dev/null || true'"
    
    # Create systemd service files (if requested)
    if [[ "$START_SERVICES" == "true" ]]; then
        log_info "Creating systemd service files..."
        eval "$ssh_cmd 'cd $DEPLOY_DIR && ./deploy/setup-services.sh'" || {
            log_warning "Service setup failed"
        }
    fi

    # Set up library paths
    log_info "Setting up library paths..."
    eval "$ssh_cmd 'echo \"$DEPLOY_DIR\" | sudo tee /etc/ld.so.conf.d/c-aibox.conf && sudo ldconfig'" || {
        log_warning "Library path setup failed"
    }
    
    log_success "Environment setup completed"
}

# Test deployment
test_deployment() {
    log_step "Testing Deployment"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    # Test executables
    log_info "Testing executables..."
    
    # Test server
    if eval "$ssh_cmd 'cd $DEPLOY_DIR && ./server --version'" 2>/dev/null; then
        log_success "Server executable working"
    else
        log_warning "Server executable test failed"
    fi

    # Test client
    if eval "$ssh_cmd 'cd $DEPLOY_DIR && ./client_app --version'" 2>/dev/null; then
        log_success "Client executable working"
    else
        log_warning "Client executable test failed"
    fi

    # Check system resources
    log_info "System resources:"
    eval "$ssh_cmd 'free -h && df -h $DEPLOY_DIR'" || true
}

# Show deployment summary
show_summary() {
    log_step "Deployment Summary"
    
    local ssh_cmd=$(build_ssh_cmd)
    
    echo "Orange Pi: $ORANGE_PI_USER@$ORANGE_PI_IP:$ORANGE_PI_PORT"
    echo "Deployment Directory: $DEPLOY_DIR"
    echo "Build Directory: $BUILD_DIR"
    echo ""
    
    # Show deployed files
    log_info "Deployed files:"
    eval "$ssh_cmd 'ls -la $DEPLOY_DIR'" || true
    
    echo ""
    log_success "Deployment to Orange Pi completed successfully!"
    log_info "Next steps:"
    log_info "  1. SSH to Orange Pi: ssh $ORANGE_PI_USER@$ORANGE_PI_IP"
    log_info "  2. Test applications: cd $DEPLOY_DIR && ./server"
    log_info "  3. Run tests: ./scripts/deploy/test-on-orangepi.sh --ip $ORANGE_PI_IP"
    
    if [[ "$START_SERVICES" == "true" ]]; then
        log_info "  4. Check services: systemctl status c-aibox-server"
    fi
}

# Main execution
main() {
    log_step "Deploy to Orange Pi 5 Plus"
    
    test_ssh_connection
    check_local_build
    backup_existing
    transfer_files
    install_dependencies
    setup_environment
    test_deployment
    show_summary
}

# Run main function
main "$@"
