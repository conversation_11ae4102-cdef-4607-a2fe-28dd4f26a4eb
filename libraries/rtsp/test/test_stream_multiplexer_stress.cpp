#include <gtest/gtest.h>
#include "rtsp/stream_multiplexer.hpp"
#include "rtsp/rtsp_config.hpp"
#include "rtsp/rtsp_types.hpp"

#include <thread>
#include <chrono>
#include <atomic>
#include <vector>
#include <random>

using namespace aibox::rtsp;

class StreamMultiplexerStressTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Create configuration for stress testing
        config_.max_concurrent_streams = 10;
        config_.max_memory_usage_mb = 200;
        config_.worker_thread_count = 4;
        config_.enable_load_balancing = true;
        config_.enable_thermal_management = true;
        
        // Larger queues for stress testing
        config_.queue_config.high_priority_size = 100;
        config_.queue_config.medium_priority_size = 80;
        config_.queue_config.low_priority_size = 60;
        
        multiplexer_ = std::make_unique<StreamMultiplexer>(config_);
    }

    void TearDown() override {
        if (multiplexer_) {
            multiplexer_->stop();
            multiplexer_.reset();
        }
    }

    RTSPConnectionConfig createRandomStreamConfig(const std::string& id) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_int_distribution<> priority_dist(0, 3);
        static std::uniform_int_distribution<> timeout_dist(1000, 5000);
        static std::uniform_int_distribution<> retry_dist(1, 5);
        
        RTSPConnectionConfig config;
        config.stream_id = id;
        config.rtsp_url = "rtsp://test" + std::to_string(gen() % 100) + ".example.com:554/stream" + id;
        config.username = "test";
        config.password = "test";
        config.priority = static_cast<StreamPriority>(priority_dist(gen));
        config.enabled = true;
        config.timeout_ms = timeout_dist(gen);
        config.retry_count = retry_dist(gen);
        return config;
    }

    StreamManagementConfig config_;
    std::unique_ptr<StreamMultiplexer> multiplexer_;
};

// Stress test: High-frequency stream addition/removal
TEST_F(StreamMultiplexerStressTest, HighFrequencyStreamOperations) {
    EXPECT_TRUE(multiplexer_->start());
    
    const int num_iterations = 100;
    const int max_concurrent_streams = 8;
    std::atomic<int> operations_completed{0};
    std::atomic<int> errors{0};
    
    auto worker = [&]() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> op_dist(0, 3);  // 0=add, 1=remove, 2=enable/disable, 3=priority
        
        std::vector<std::string> active_streams;
        
        for (int i = 0; i < num_iterations; ++i) {
            try {
                int operation = op_dist(gen);
                
                switch (operation) {
                    case 0: {  // Add stream
                        if (active_streams.size() < max_concurrent_streams) {
                            std::string id = "stress_" + std::to_string(gen() % 1000);
                            auto config = createRandomStreamConfig(id);
                            if (multiplexer_->addStream(id, config)) {
                                active_streams.push_back(id);
                            }
                        }
                        break;
                    }
                    case 1: {  // Remove stream
                        if (!active_streams.empty()) {
                            std::uniform_int_distribution<> idx_dist(0, active_streams.size() - 1);
                            int idx = idx_dist(gen);
                            std::string id = active_streams[idx];
                            if (multiplexer_->removeStream(id)) {
                                active_streams.erase(active_streams.begin() + idx);
                            }
                        }
                        break;
                    }
                    case 2: {  // Enable/disable stream
                        if (!active_streams.empty()) {
                            std::uniform_int_distribution<> idx_dist(0, active_streams.size() - 1);
                            std::uniform_int_distribution<> bool_dist(0, 1);
                            int idx = idx_dist(gen);
                            bool enable = bool_dist(gen);
                            multiplexer_->enableStream(active_streams[idx], enable);
                        }
                        break;
                    }
                    case 3: {  // Change priority
                        if (!active_streams.empty()) {
                            std::uniform_int_distribution<> idx_dist(0, active_streams.size() - 1);
                            std::uniform_int_distribution<> priority_dist(0, 3);
                            int idx = idx_dist(gen);
                            auto priority = static_cast<StreamPriority>(priority_dist(gen));
                            multiplexer_->setStreamPriority(active_streams[idx], priority);
                        }
                        break;
                    }
                }
                
                operations_completed++;
                
                // Brief pause to avoid overwhelming the system
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
                
            } catch (const std::exception& e) {
                errors++;
            }
        }
        
        // Clean up remaining streams
        for (const auto& id : active_streams) {
            multiplexer_->removeStream(id);
        }
    };
    
    // Run multiple worker threads
    const int num_workers = 3;
    std::vector<std::thread> workers;
    
    for (int i = 0; i < num_workers; ++i) {
        workers.emplace_back(worker);
    }
    
    // Wait for all workers to complete
    for (auto& worker_thread : workers) {
        worker_thread.join();
    }
    
    // Check results
    EXPECT_GT(operations_completed.load(), num_iterations * num_workers * 0.8);  // At least 80% success
    EXPECT_LT(errors.load(), num_iterations * num_workers * 0.1);  // Less than 10% errors
    
    // Check final state
    auto final_stats = multiplexer_->getStatistics();
    EXPECT_LE(final_stats.total_streams, max_concurrent_streams);
}

// Stress test: Queue operations under high load
TEST_F(StreamMultiplexerStressTest, HighLoadQueueOperations) {
    const int queue_size = 50;
    const int num_producers = 4;
    const int num_consumers = 3;
    const int items_per_producer = 200;
    
    ThreadSafeQueue<int> queue(queue_size);
    
    std::atomic<int> total_produced{0};
    std::atomic<int> total_consumed{0};
    std::atomic<int> production_errors{0};
    std::atomic<int> consumption_errors{0};
    
    std::vector<std::thread> producers;
    std::vector<std::thread> consumers;
    
    // Start consumers
    for (int i = 0; i < num_consumers; ++i) {
        consumers.emplace_back([&]() {
            int item;
            while (total_consumed.load() < num_producers * items_per_producer) {
                try {
                    if (queue.dequeue(item, 5)) {  // 5ms timeout
                        total_consumed++;
                    }
                } catch (const std::exception& e) {
                    consumption_errors++;
                }
                
                // Brief processing time simulation
                std::this_thread::sleep_for(std::chrono::microseconds(10));
            }
        });
    }
    
    // Start producers
    for (int i = 0; i < num_producers; ++i) {
        producers.emplace_back([&, i]() {
            for (int j = 0; j < items_per_producer; ++j) {
                try {
                    int item = i * items_per_producer + j;
                    
                    // Try multiple times if queue is full
                    int attempts = 0;
                    while (!queue.enqueue(item, 5) && attempts < 10) {  // 5ms timeout
                        attempts++;
                        std::this_thread::sleep_for(std::chrono::microseconds(100));
                    }
                    
                    if (attempts < 10) {
                        total_produced++;
                    } else {
                        production_errors++;
                    }
                } catch (const std::exception& e) {
                    production_errors++;
                }
            }
        });
    }
    
    // Wait for all producers
    for (auto& producer : producers) {
        producer.join();
    }
    
    // Wait for all consumers
    for (auto& consumer : consumers) {
        consumer.join();
    }
    
    // Check results
    EXPECT_EQ(total_produced.load(), total_consumed.load());
    EXPECT_LT(production_errors.load(), items_per_producer * num_producers * 0.05);  // Less than 5% errors
    EXPECT_EQ(consumption_errors.load(), 0);  // No consumption errors expected
}

// Stress test: Memory pressure simulation
TEST_F(StreamMultiplexerStressTest, MemoryPressureSimulation) {
    EXPECT_TRUE(multiplexer_->start());
    
    const int num_streams = 8;
    std::vector<std::string> stream_ids;
    
    // Add streams to create memory pressure
    for (int i = 1; i <= num_streams; ++i) {
        std::string id = "memory_test_" + std::to_string(i);
        auto config = createRandomStreamConfig(id);
        
        if (multiplexer_->addStream(id, config)) {
            stream_ids.push_back(id);
        }
    }
    
    // Monitor memory usage
    size_t initial_memory = multiplexer_->getTotalMemoryUsage();
    
    // Simulate memory pressure
    multiplexer_->handleMemoryPressure();
    
    // Check that memory pressure handling doesn't crash
    auto stats_after_pressure = multiplexer_->getStatistics();
    EXPECT_LE(stats_after_pressure.total_streams, num_streams);
    
    // Test resource optimization
    multiplexer_->optimizeResourceUsage();
    
    size_t memory_after_optimization = multiplexer_->getTotalMemoryUsage();
    
    // Memory should be managed (not necessarily reduced, but system should be stable)
    EXPECT_GT(memory_after_optimization, 0);
    
    // Clean up
    for (const auto& id : stream_ids) {
        multiplexer_->removeStream(id);
    }
}

// Stress test: Thermal throttling simulation
TEST_F(StreamMultiplexerStressTest, ThermalThrottlingSimulation) {
    EXPECT_TRUE(multiplexer_->start());
    
    // Add multiple streams
    const int num_streams = 6;
    std::vector<std::string> stream_ids;
    
    for (int i = 1; i <= num_streams; ++i) {
        std::string id = "thermal_test_" + std::to_string(i);
        auto priority = (i <= 2) ? StreamPriority::HIGH : 
                       (i <= 4) ? StreamPriority::MEDIUM : StreamPriority::LOW;
        
        RTSPConnectionConfig config;
        config.stream_id = id;
        config.rtsp_url = "rtsp://test.example.com:554/stream" + id;
        config.priority = priority;
        config.enabled = true;
        
        if (multiplexer_->addStream(id, config)) {
            stream_ids.push_back(id);
        }
    }
    
    auto initial_active_count = multiplexer_->getActiveStreamCount();
    
    // Simulate gradual temperature increase
    for (int temp = 60; temp <= 95; temp += 5) {
        multiplexer_->handleThermalThrottling(temp);
        
        // Check system stability
        auto stats = multiplexer_->getStatistics();
        auto health = multiplexer_->getSystemHealth();
        
        EXPECT_FALSE(health.status.empty());
        EXPECT_GE(health.score, 0.0f);
        EXPECT_LE(health.score, 1.0f);
        
        // Brief pause between temperature changes
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // At high temperature, some streams might be disabled
    auto final_active_count = multiplexer_->getActiveStreamCount();
    EXPECT_LE(final_active_count, initial_active_count);
    
    // System should still be responsive
    auto final_stats = multiplexer_->getStatistics();
    EXPECT_TRUE(true);  // Just ensure no crash
}

// Stress test: Concurrent configuration changes
TEST_F(StreamMultiplexerStressTest, ConcurrentConfigurationChanges) {
    EXPECT_TRUE(multiplexer_->start());
    
    const int num_threads = 4;
    const int operations_per_thread = 50;
    std::atomic<int> successful_operations{0};
    std::atomic<int> failed_operations{0};
    
    auto worker = [&](int thread_id) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> op_dist(0, 2);
        
        for (int i = 0; i < operations_per_thread; ++i) {
            try {
                int operation = op_dist(gen);
                
                switch (operation) {
                    case 0: {  // Update multiplexer configuration
                        auto current_config = multiplexer_->getConfig();
                        current_config.max_memory_usage_mb = 100 + (gen() % 100);
                        current_config.worker_thread_count = 2 + (gen() % 3);
                        multiplexer_->updateConfig(current_config);
                        break;
                    }
                    case 1: {  // Toggle load balancing
                        bool current_state = multiplexer_->isLoadBalancingEnabled();
                        multiplexer_->enableLoadBalancing(!current_state);
                        break;
                    }
                    case 2: {  // Change performance mode
                        bool high_performance = (gen() % 2) == 0;
                        multiplexer_->setPerformanceMode(high_performance);
                        break;
                    }
                }
                
                successful_operations++;
                
            } catch (const std::exception& e) {
                failed_operations++;
            }
            
            // Brief pause
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    };
    
    // Start worker threads
    std::vector<std::thread> workers;
    for (int i = 0; i < num_threads; ++i) {
        workers.emplace_back(worker, i);
    }
    
    // Wait for all workers
    for (auto& worker_thread : workers) {
        worker_thread.join();
    }
    
    // Check results
    int total_operations = num_threads * operations_per_thread;
    EXPECT_GT(successful_operations.load(), total_operations * 0.9);  // At least 90% success
    EXPECT_LT(failed_operations.load(), total_operations * 0.1);  // Less than 10% failures
    
    // System should still be functional
    auto final_config = multiplexer_->getConfig();
    EXPECT_GT(final_config.max_memory_usage_mb, 0);
    EXPECT_GT(final_config.worker_thread_count, 0);
}

// Stress test: Long-running stability
TEST_F(StreamMultiplexerStressTest, LongRunningStability) {
    EXPECT_TRUE(multiplexer_->start());
    
    const int test_duration_seconds = 5;  // Short for unit test
    const int num_streams = 4;
    
    // Add initial streams
    std::vector<std::string> stream_ids;
    for (int i = 1; i <= num_streams; ++i) {
        std::string id = "stability_test_" + std::to_string(i);
        auto config = createRandomStreamConfig(id);
        
        if (multiplexer_->addStream(id, config)) {
            stream_ids.push_back(id);
        }
    }
    
    auto start_time = std::chrono::steady_clock::now();
    auto last_stats_time = start_time;
    
    // Run for specified duration
    while (true) {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time);
        
        if (elapsed.count() >= test_duration_seconds) {
            break;
        }
        
        // Periodically check system health
        auto stats_elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_stats_time);
        if (stats_elapsed.count() >= 1) {
            auto stats = multiplexer_->getStatistics();
            auto health = multiplexer_->getSystemHealth();
            
            // System should remain stable
            EXPECT_FALSE(health.status.empty());
            EXPECT_GE(health.score, 0.0f);
            EXPECT_LE(health.score, 1.0f);
            
            last_stats_time = current_time;
        }
        
        // Perform some operations
        multiplexer_->rebalanceStreams();
        multiplexer_->optimizeResourceUsage();
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    // Final health check
    auto final_stats = multiplexer_->getStatistics();
    auto final_health = multiplexer_->getSystemHealth();
    
    EXPECT_EQ(final_stats.total_streams, stream_ids.size());
    EXPECT_FALSE(final_health.status.empty());
}
