#pragma once

#include "rtsp_types.hpp"
#include "rtsp_config.hpp"
#include <memory>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>

namespace aibox {
namespace rtsp {

// Forward declarations
class AuthenticationHandler;
class RetryManager;
class GStreamerRTSPClient;

// Connection result structure
struct ConnectionResult {
    bool success;
    std::string error_message;
    ErrorCategory error_category;
    Duration connection_time;
    
    ConnectionResult() : success(false), error_category(ErrorCategory::NETWORK_ERROR) {}
    ConnectionResult(bool s) : success(s), error_category(ErrorCategory::NETWORK_ERROR) {}
    ConnectionResult(const std::string& error, ErrorCategory category) : 
        success(false), error_message(error), error_category(category) {}
    
    operator bool() const { return success; }
};

// Connection health information
struct ConnectionHealth {
    ConnectionState state;
    Timestamp last_activity;
    Duration uptime;
    uint32_t reconnect_count;
    float signal_strength;  // 0.0 - 1.0
    uint32_t packet_loss_rate_percent;
    uint32_t average_latency_ms;
    bool hardware_acceleration_active;
    
    ConnectionHealth() : 
        state(ConnectionState::DISCONNECTED),
        reconnect_count(0),
        signal_strength(0.0f),
        packet_loss_rate_percent(0),
        average_latency_ms(0),
        hardware_acceleration_active(false) {}
    
    bool isHealthy() const {
        return state == ConnectionState::STREAMING &&
               packet_loss_rate_percent < 5 &&
               average_latency_ms < 500;
    }
};

/**
 * @brief Connection Manager for RTSP streams optimized for RK3588
 * 
 * Manages individual RTSP connections using GStreamer with RockChip hardware acceleration.
 * Handles connection lifecycle, authentication, retry logic, and health monitoring.
 */
class ConnectionManager {
public:
    // Constructor
    explicit ConnectionManager(const RTSPConnectionConfig& config);
    
    // Destructor
    ~ConnectionManager();
    
    // Non-copyable, movable
    ConnectionManager(const ConnectionManager&) = delete;
    ConnectionManager& operator=(const ConnectionManager&) = delete;
    ConnectionManager(ConnectionManager&&) = default;
    ConnectionManager& operator=(ConnectionManager&&) = default;
    
    // Connection lifecycle
    ConnectionResult connect();
    void disconnect();
    bool isConnected() const;
    ConnectionState getState() const;
    
    // Configuration management
    void updateConfig(const RTSPConnectionConfig& new_config);
    const RTSPConnectionConfig& getConfig() const;
    
    // Session management
    bool maintainSession();
    void handleKeepAlive();
    bool reconnect();
    
    // Data callbacks
    void setDataCallback(StreamDataCallback callback);
    void setEventCallback(StreamEventCallback callback);
    void setErrorCallback(StreamErrorCallback callback);
    
    // Statistics and monitoring
    StreamStatistics getStatistics() const;
    ConnectionHealth getHealth() const;
    SystemHealth getSystemHealth() const;
    
    // Control operations
    void pause();
    void resume();
    void restart();
    
    // Hardware acceleration control
    bool enableMPPDecoder(bool enable);
    bool enableRGAScaler(bool enable);
    bool enableDMABufZeroCopy(bool enable);
    HardwareAccelStatus getMPPStatus() const;
    HardwareAccelStatus getRGAStatus() const;
    
    // Thermal management
    void handleThermalThrottling(int temperature);

    // Error handling
    void handleError(ErrorCategory category, const std::string& message);

    // Quality control
    void setAdaptiveQuality(bool enable);
    bool canRecover(ErrorCategory category) const;
    
private:
    // Internal state
    RTSPConnectionConfig config_;
    std::atomic<ConnectionState> state_;
    std::unique_ptr<AuthenticationHandler> auth_handler_;
    std::unique_ptr<RetryManager> retry_manager_;
    std::unique_ptr<GStreamerRTSPClient> gstreamer_client_;
    
    // Threading
    std::unique_ptr<std::thread> connection_thread_;
    std::unique_ptr<std::thread> monitoring_thread_;
    std::atomic<bool> should_stop_;
    mutable std::mutex state_mutex_;
    
    // Callbacks
    StreamDataCallback data_callback_;
    StreamEventCallback event_callback_;
    StreamErrorCallback error_callback_;
    
    // Statistics (using atomic types internally)
    mutable AtomicStreamStatistics statistics_;
    mutable std::mutex stats_mutex_;
    
    // Hardware acceleration state
    std::atomic<HardwareAccelStatus> mpp_status_;
    std::atomic<HardwareAccelStatus> rga_status_;
    std::atomic<bool> dmabuf_enabled_;
    
    // Thermal management
    std::atomic<bool> thermal_throttling_;
    std::atomic<bool> adaptive_quality_;
    
    // Internal methods
    void connectionWorker();
    void monitoringWorker();
    void updateStatistics();
    void checkConnectionHealth();
    void handleConnectionLoss();
    void handleHardwareError();
    void adjustQualityForThermal(int temperature);
    
    // GStreamer integration
    bool initializeGStreamerPipeline();
    void cleanupGStreamerPipeline();
    bool configureHardwareAcceleration();
    void handleGStreamerMessage(const std::string& message);
    void handleGStreamerError(const std::string& error);
    
    // Authentication
    bool authenticateConnection();
    bool handleAuthenticationChallenge(const std::string& challenge);
    
    // Retry logic
    bool shouldRetry(ErrorCategory category) const;
    Duration calculateRetryDelay(uint32_t attempt) const;
    void resetRetryState();
    
    // Platform-specific optimizations
    void optimizeForRK3588();
    void setCPUAffinity();
    void configureMemoryLimits();
    
    // Validation
    bool validateConfig() const;
    bool checkSystemResources() const;
    bool checkHardwareAvailability() const;

    // GStreamer callback handlers
    void handleStateChange(ConnectionState new_state);
    void handleBufferReceived(const std::vector<uint8_t>& buffer, Timestamp timestamp);
    void handleNALUnitReceived(const NALUnit& nal_unit);
};

/**
 * @brief Authentication Handler for RTSP connections
 * 
 * Handles various RTSP authentication methods including Basic and Digest authentication.
 */
class AuthenticationHandler {
public:
    enum class AuthMethod {
        NONE,
        BASIC,
        DIGEST
    };
    
    explicit AuthenticationHandler(const std::string& username, const std::string& password);
    
    // Authentication methods
    bool authenticate(AuthMethod method, const std::string& challenge = "");
    std::string generateAuthHeader(AuthMethod method, const std::string& uri, const std::string& challenge = "");
    
    // Credential management
    void updateCredentials(const std::string& username, const std::string& password);
    bool hasCredentials() const;
    
private:
    std::string username_;
    std::string password_;
    std::string realm_;
    std::string nonce_;
    std::string opaque_;
    
    // Helper methods
    std::string generateBasicAuth() const;
    std::string generateDigestAuth(const std::string& uri, const std::string& challenge) const;
    std::string calculateMD5Hash(const std::string& input) const;
    bool parseDigestChallenge(const std::string& challenge);
};

/**
 * @brief Retry Manager for connection failures
 * 
 * Implements exponential backoff with jitter for connection retry logic.
 */
class RetryManager {
public:
    explicit RetryManager(const RTSPConnectionConfig& config);
    
    // Retry logic
    bool shouldRetry(ErrorCategory category) const;
    Duration getNextRetryDelay();
    void recordAttempt(bool success);
    void reset();
    void updateConfig(const RTSPConnectionConfig& config);
    
    // Statistics
    uint32_t getAttemptCount() const;
    uint32_t getSuccessCount() const;
    uint32_t getFailureCount() const;
    Duration getTotalRetryTime() const;
    
private:
    // Configuration
    int max_retry_count_;
    int base_delay_ms_;
    int max_delay_ms_;
    int jitter_ms_;
    
    // State
    std::atomic<uint32_t> attempt_count_;
    std::atomic<uint32_t> success_count_;
    std::atomic<uint32_t> failure_count_;
    Timestamp first_attempt_time_;
    Timestamp last_attempt_time_;
    
    // Helper methods
    Duration calculateExponentialBackoff(uint32_t attempt) const;
    Duration addJitter(Duration base_delay) const;
    bool isRetriableError(ErrorCategory category) const;
};

} // namespace rtsp
} // namespace aibox
